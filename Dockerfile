FROM node:18.20-alpine as builder

# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package*.json ./


RUN npm ci

COPY . .

RUN npm run build

FROM node:slim

ENV NODE_ENV production
USER node

# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package*.json ./

RUN npm ci --production

COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/src/domain/template/export/. ./dist/domain/template/export/.
COPY --from=builder /usr/src/app/src/domain/template/import/. ./dist/domain/template/import/.
COPY --from=builder /usr/src/app/src/domain/template-export/. ./dist/domain/template-export/.

CMD [ "node", "dist/main.js" ]