import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { JwtModule } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import { ServeStaticModule } from '@nestjs/serve-static';
import { resolve } from 'path';
import { ControllersModule } from './controller/controllers.module';
import { EnvironmentConfigModule } from './infrastructure/config/environment-config/environment-config.module';
import { ExceptionsModule } from './infrastructure/exceptions/exceptions.module';
import { LoggerModule } from './infrastructure/logger/logger.module';
import { RepositoriesModule } from './infrastructure/repositories/repositories.module';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: {
        expiresIn: process.env.JWT_EXPIRATION_TIME,
      },
      global: true,
    }),
    ServeStaticModule.forRoot({
      rootPath: resolve(__dirname, '../uploads'),
      // Tell NestJS to serve the files under ~/uploads/
      serveRoot: '/uploads/',
    }),
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    EnvironmentConfigModule,
    LoggerModule,
    ExceptionsModule,
    RepositoriesModule,
    ControllersModule,
  ],
  providers: [],
})
export class AppModule {}
