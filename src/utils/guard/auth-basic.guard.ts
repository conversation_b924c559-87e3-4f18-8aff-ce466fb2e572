import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class AuthBasicGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const verify = this.extractTokenFromHeader(request);
    if (
      process.env.BASIC_USER === verify.username &&
      process.env.BASIC_PASS === verify.password
    ) {
      return true;
    }
    throw new UnauthorizedException();
  }

  private extractTokenFromHeader(request: Request) {
    const b64auth = (request.headers.authorization || '').split(' ')[1] || '';
    const [username, password] = Buffer.from(b64auth, 'base64')
      .toString()
      .split(':');
    return { username, password };
  }
}
