import { CanActivate, ExecutionContext, mixin, Type } from '@nestjs/common';
import { TPermission } from '../constants/permission.enum';
import { parseScopes } from '../common';

const NewPermissionGuard = (
  permissions: (TPermission | TPermission[])[],
): Type<CanActivate> => {
  class PermissionGuardMixin {
    async canActivate(context: ExecutionContext) {
      const request = context.switchToHttp().getRequest();
      const jwtPayload = request['jwtPayload'];

      if (!jwtPayload) {
        return false;
      }

      if (jwtPayload.isSuperAdmin) {
        return true;
      }

      return parseScopes(jwtPayload.scopes, permissions);
    }
  }

  return mixin(PermissionGuardMixin);
};
export default NewPermissionGuard;
