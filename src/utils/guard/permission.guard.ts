// import { CanActivate, ExecutionContext, mixin, Type } from '@nestjs/common';
// import { TPermission } from '../constants/permission.enum';
// import { parseScopes } from '../common';

// const PermissionGuard = (
//   permissions: (TPermission | TPermission[])[],
// ): Type<CanActivate> => {
//   class PermissionGuardMixin {
//     async canActivate(context: ExecutionContext) {
//       const request = context.switchToHttp().getRequest();
//       const token = request.headers['authorization']
//         ? request.headers['authorization']
//         : null;
//       if (token) {
//         const base64Payload = token.split('.')[1];
//         const payloadBuffer = Buffer.from(base64Payload, 'base64');
//         const updatedJwtPayload = JSON.parse(payloadBuffer.toString());
//         // user with admin or super_admin role can pass-by the guard
//         if (updatedJwtPayload.isSuperAdmin) {
//           return true;
//         }

//         return parseScopes(updatedJwtPayload.scopes, permissions);
//       }
//       return false;
//     }
//   }

//   return mixin(PermissionGuardMixin);
// };
// export default PermissionGuard;
