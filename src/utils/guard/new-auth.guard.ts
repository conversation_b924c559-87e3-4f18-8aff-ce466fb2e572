import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { IdentityServiceApiUrlsConst } from '../constants/identity-service-api-url.const';
import { getData } from '../http';

@Injectable()
export class NewAuthGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException();
    }
    try {
      const validate = await getData(
        IdentityServiceApiUrlsConst.VALIDATE_TOKEN(),
        {
          authorization: request.headers['authorization'],
          'x-api-key': request.headers['x-api-key'],
        },
      );

      if (validate?.status == 200 || validate?.status == 201) {
        request['jwtPayload'] = validate?.data?.data;
      } else {
        throw new UnauthorizedException();
      }
    } catch (e) {
      throw new UnauthorizedException();
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
