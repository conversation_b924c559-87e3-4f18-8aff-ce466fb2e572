import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { catchError, map, Observable, throwError } from 'rxjs';
import { MessageResponse } from '../../domain/messages/message.response';
import { AxiosError } from 'axios';

@Injectable()
export class TransformationInterceptor<T>
  implements NestInterceptor<T, MessageResponse<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<MessageResponse<T>> {
    const now = Date.now();
    return next.handle().pipe(
      map((data) => {
        if (Array.isArray(data)) {
          return {
            data,
            message: 'SUCCESSFULLY',
            duration: `${Date.now() - now}ms`,
          };
        } else {
          return {
            data,
            message: data?.message ? data?.message : null,
            duration: `${Date.now() - now}ms`,
          };
        }
      }),
      catchError((error) => {
        let message = 'Internal Server Error';
        let errorCode = '0000';
        let detail;
        let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;

        if (error instanceof HttpException) {
          message = error.getResponse()['message'] || message;
          errorCode = error.getResponse()['errorCode'] || errorCode;
          statusCode = error.getStatus();
          detail = error.getResponse()['detail'] || detail;
          if (error.getResponse() instanceof Array) {
            message = error.getResponse()[0].message || message;
          }
        } else if (error instanceof AxiosError) {
          if (error.response?.data instanceof Object) {
            message =
              error.response?.data?.errors[0]?.message ||
              error.response?.data?.message;
          } else if (error.response?.data?.errors instanceof Array) {
            message = error.response?.data?.errors[0].message || message;
          } else {
            message = message;
          }

          statusCode = error.response?.data?.statusCode || statusCode;
        } else {
          message = error.message || message;
        }

        return throwError(
          () =>
            new HttpException(
              { message, duration: `${Date.now() - now}ms`, errorCode, detail },
              statusCode,
            ),
        );
      }),
    );
  }
}
