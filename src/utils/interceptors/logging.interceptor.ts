import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { AxiosError } from 'axios';
import { catchError, tap } from 'rxjs/operators';
import { CreateApiLogDto } from '../../controller/api-log/dtos/create-api-log.dto';
import { ApiLogUsecases } from '../../usecases/api-log.usecase';

@Injectable()
export class AspectLogger implements NestInterceptor {
  constructor(private readonly apiLogUsecases: ApiLogUsecases) {}

  private maskSensitiveData(
    data: Record<string, string | number> | undefined | null,
  ): Record<string, string | number> {
    if (!data || typeof data !== 'object') {
      return {}; // Trả về một object rỗng nếu data không hợp lệ
    }

    const maskedData: Record<string, string | number> = {};
    for (const [key, value] of Object.entries(data)) {
      maskedData[key] = key.toLowerCase().includes('password')
        ? '******'
        : value;
    }

    return maskedData;
  }
  private logRequest(
    req: any,
    maskedBody: Record<string, string | number>,
  ): void {
    if (process.env.ENV !== 'PRODUCTION') {
      console.log('[MESSAGE-IN]', {
        originalUrl: req.originalUrl,
        // headers: JSON.stringify(req.headers),
        method: req.method,
        params: req.params,
        query: req.query,
        body: JSON.stringify(maskedBody),
      });
    }
  }

  private logResponse(headers: any, statusCode: number, data: any): void {
    if (process.env.ENV !== 'PRODUCTION') {
      console.log('[MESSAGE-OUT]', {
        // headers: JSON.stringify(headers),
        statusCode,
        // data:
        //   statusCode == 207
        //     ? data
        //     : data?.data?.items
        //       ? {
        //           results: data.data.items.length,
        //           length: data.data.items.length,
        //         }
        //       : this.maskSensitiveData(data?.data),
      });
    }
  }

  private extractErrorDetails(error: any): {
    message: any;
    statusCode: number;
  } {
    let message = 'Internal Server Error';
    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;

    if (error instanceof HttpException) {
      const response = error.getResponse();
      message = response['message'] || message;
      statusCode = error.getStatus();
    } else if (error instanceof AxiosError) {
      const responseData = error.response?.data;
      message =
        responseData?.errors?.[0]?.message || responseData?.message || message;
      statusCode = responseData?.statusCode || statusCode;
    } else {
      message = error.message || message;
    }

    return { message, statusCode };
  }

  async intercept(context: ExecutionContext, next: CallHandler) {
    const startTime = Date.now();
    const req = context.switchToHttp().getRequest();
    const res = context.switchToHttp().getResponse();
    const { originalUrl, method, params, query, body, headers } = req;
    const controller = context.getClass().name;
    const maskedBody = this.maskSensitiveData(body);

    // Lấy thông tin token từ Authorization header
    const token = headers.authorization?.split('.')[1];
    let userInfo: Record<string, any> = {};
    if (token) {
      try {
        const payloadBuffer = Buffer.from(token, 'base64');
        userInfo = JSON.parse(payloadBuffer.toString());
      } catch (error) {
        console.error('Invalid Token:', error.message);
      }
    }

    this.logRequest(req, maskedBody);

    return next.handle().pipe(
      tap(async (data) => {
        const logData: CreateApiLogDto = {
          controller,
          method,
          route: originalUrl,
          statusCode: res.statusCode,
          duration: Date.now() - startTime,
          params: params,
          query: query,
          body: maskedBody,
          isSuccess: res.statusCode >= 200 && res.statusCode < 300,
          user: {
            id: userInfo?.userId,
            firstName: userInfo?.firstName,
            lastName: userInfo?.lastName,
            email: userInfo?.email,
            phone: userInfo?.phone,
            staffId: userInfo?.staffId,
            staffCode: userInfo?.staffCode,
          },
          errorMessage: res.statusCode == 207 ? res?.locals?.data : '',
        };
        await this.apiLogUsecases.create(logData);
        this.logResponse(
          headers,
          res.statusCode,
          res.statusCode == 207 ? res?.locals?.data : data,
        );
      }),
      catchError(async (error) => {
        const { message, statusCode } = this.extractErrorDetails(error);

        const logData: CreateApiLogDto = {
          controller,
          method,
          route: originalUrl,
          statusCode,
          duration: Date.now() - startTime,
          params: params,
          query: query,
          body: maskedBody,
          isSuccess: false,
          errorMessage: message,
          user: {
            id: userInfo?.userId,
            firstName: userInfo?.firstName,
            lastName: userInfo?.lastName,
            email: userInfo?.email,
            phone: userInfo?.phone,
            staffId: userInfo?.staffId,
            staffCode: userInfo?.staffCode,
          },
        };

        await this.apiLogUsecases.create(logData);

        throw error; // Re-throw error to maintain existing behavior
      }),
    );
  }
}
