import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const AuthUser = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const req = context.switchToHttp().getRequest();
    // if (process.env.ENV == 'LOCAL') {
    const token = req.headers['authorization']
      ? req.headers['authorization']
      : null;
    if (token) {
      const base64Payload = token.split('.')[1];
      if (!base64Payload) return {};
      const payloadBuffer = Buffer.from(base64Payload, 'base64');
      const updatedJwtPayload = JSON.parse(payloadBuffer.toString());

      const aggregateScopes = (scopes, key) => {
        return scopes
          ?.flatMap((scope) => scope[key]?.split(',') || null)
          .filter(Boolean);
      };

      const sectors = aggregateScopes(updatedJwtPayload.scopes, 'sectors');

      const companies = aggregateScopes(updatedJwtPayload.scopes, 'companies');
      const businessUnits = aggregateScopes(
        updatedJwtPayload.scopes,
        'businessUnits',
      );
      const departments = aggregateScopes(
        updatedJwtPayload.scopes,
        'departments',
      );
      const functionUnits = aggregateScopes(
        updatedJwtPayload.scopes,
        'functionUnits',
      );
      const businessOwners = aggregateScopes(
        updatedJwtPayload.scopes,
        'businessOwners',
      );

      const materials = aggregateScopes(updatedJwtPayload.scopes, 'materials');

      const materialGroups = aggregateScopes(
        updatedJwtPayload.scopes,
        'materialGroups',
      );

      const materialTypes = aggregateScopes(
        updatedJwtPayload.scopes,
        'materialTypes',
      );

      const positions = aggregateScopes(updatedJwtPayload.scopes, 'positions');

      const suppliers = aggregateScopes(updatedJwtPayload.scopes, 'suppliers');

      const purchasingGroups = aggregateScopes(
        updatedJwtPayload.scopes,
        'purchasingGroups',
      );

      const purchasingDepartments = aggregateScopes(
        updatedJwtPayload.scopes,
        'purchasingDepartments',
      );

      const plants = aggregateScopes(updatedJwtPayload.scopes, 'plants');

      const currencyUnits = aggregateScopes(
        updatedJwtPayload.scopes,
        'currencyUnits',
      );

      const prTypeCodes = aggregateScopes(
        updatedJwtPayload.scopes,
        'prTypeCodes',
      );

      const poTypeCodes = aggregateScopes(
        updatedJwtPayload.scopes,
        'poTypeCodes',
      );

      const costs = aggregateScopes(updatedJwtPayload.scopes, 'costs');

      return {
        ...updatedJwtPayload,
        sectors,
        companies,
        businessUnits,
        departments,
        functionUnits,
        businessOwners,
        materials,
        materialGroups,
        materialTypes,
        positions,
        suppliers,
        purchasingGroups,
        purchasingDepartments,
        plants,
        currencyUnits,
        prTypeCodes,
        poTypeCodes,
        costs,
      };
    }
    //throw new BadRequestException('AUTH_USER_NOT_FOUND');
    return {};
    // } else {
    //   const userAuth = req.headers['userauth'];
    //   if (userAuth) {
    //     const userInfo = JSON.parse(userAuth);
    //     console.log(userInfo);
    //     return {
    //       userInfo: {
    //         id: userInfo.UserName,
    //         username: userInfo.UserName,
    //         fullName: userInfo.FullName,
    //         email: userInfo.Email,
    //         phone: userInfo.Phone,
    //         empNo: userInfo.EmpNo,
    //         userType: userInfo.UserType,
    //         industryCode: userInfo.IndustryCode,
    //         roleCode: userInfo.RoleCode,
    //         userId: userInfo.UserId,
    //         farmId: userInfo.FarmId,
    //       },
    //     };
    //   } else {
    //     return {};
    //   }
    // }
  },
);

export interface IUserAuth {
  ID: string;
  UserID: string;
  UserName: string;
  FullName: string;
  FarmID: number;
  IndustryCode: string;
  Email: string;
  Phone: string;
  EmpNo: string;
  UserType: string;
  RoleCode: string;
}
