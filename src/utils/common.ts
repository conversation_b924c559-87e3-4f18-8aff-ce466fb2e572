import * as bcrypt from 'bcryptjs';
import * as crypto from 'crypto';
import * as Excel from 'exceljs';
import { Row, ValueType } from 'exceljs';
import * as jwt from 'jsonwebtoken';
import * as moment from 'moment';
import { v4 as uuidv4 } from 'uuid';
import { CreateBudgetCapexDto } from '../controller/budget/dtos/create-budget-capex.dto';
import { CreateBudgetOpexDto } from '../controller/budget/dtos/create-budget-opex.dto';
import { CreateBudgetDto } from '../controller/budget/dtos/create-budget.dto';
import { inventoryStandardNullValue } from '../domain/config/constant';
import { EBudgetCodeStatus } from '../domain/config/enums/budget-code.enum';
import {
  EBudgetCreateType,
  EBudgetStatus,
  EBudgetType,
} from '../domain/config/enums/budget.enum';
import { EBusinessOwnerStatus } from '../domain/config/enums/business-owner.enum';
import { EBusinessUnitStatus } from '../domain/config/enums/business-unit.enum';
import { ECompanyStatus } from '../domain/config/enums/company.enum';
import { EComparisonType } from '../domain/config/enums/comparision-type.enum';
import { ECostStatus } from '../domain/config/enums/cost.enum';
import { ECostcenterSubaccountStatus } from '../domain/config/enums/costcenter-subaccount.enum';
import { ECurrencyUnitStatus } from '../domain/config/enums/currency-unit.enum';
import { EDepartmentStatus } from '../domain/config/enums/department.enum';
import { EInventoryStandardStatus } from '../domain/config/enums/inventory-standard.enum';
import {
  EMaterialGroupStatus,
  EMaterialStatus,
  EMaterialTypeStatus,
} from '../domain/config/enums/material.enum';
import {
  EPurchasingDepartmentStatus,
  EPurchasingGroupStatus,
} from '../domain/config/enums/purchasing.enum';
import { Status } from '../domain/config/enums/status.enum';
import { ESupplierSectorStatus } from '../domain/config/enums/supplier-sector.enum';
import {
  ESupplierStatus,
  ESupplierType,
} from '../domain/config/enums/supplier.enum';
import { TErrorMessageImport } from '../domain/messages/error-message';
import { BudgetCodeModel } from '../domain/model/budget-code.model';
import { BudgetModel } from '../domain/model/budget.model';
import { BusinessOwnerModel } from '../domain/model/business-owner.model';
import { BusinessUnitModel } from '../domain/model/business-unit.model';
import { CompanyModel } from '../domain/model/company.model';
import { CostModel } from '../domain/model/cost.model';
import { CostcenterSubaccountModel } from '../domain/model/costcenter-subaccount.model';
import { CurrencyUnitModel } from '../domain/model/currency-unit.model';
import { DepartmentModel } from '../domain/model/department.model';
import { InventoryStandardModel } from '../domain/model/inventory-standard.model';
import { MaterialGroupModel } from '../domain/model/material-group.model';
import { MaterialTypeModel } from '../domain/model/material-type.model';
import { SupplierSectorModel } from '../domain/model/supplier-sector.model';
import { SupplierModel } from '../domain/model/supplier.model';
import { TPermission } from './constants/permission.enum';
import * as _ from 'lodash';

export const removeUnicode = (str: string = ''): string => {
  if (!str) {
    return '';
  }

  str = str.toLowerCase();
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  str = str.replace(
    /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\-|\'| |\"|\&|\#|\[|\]|~|$|_/g,
    '',
  );
  str = str.replace(/-+-/g, ''); //thay thế 2- thành 1-
  str = str.replace(/^\-+|\-+$/g, '');
  return str;
};

export const calculateSkip = (pageNumber: number, pageSize: number) =>
  (pageNumber - 1) * pageSize;

export const regexDateString =
  /^\d{4}(-)(((0)[0-9])|((1)[0-2]))(-)([0-2][0-9]|(3)[0-1])$/i;

export const randomId = (length: number) => {
  let result = '';
  const characters = '0123456789';
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return result;
};

export const isValidDate = (dateString: string) => {
  const date = moment(dateString, 'DD/MM/YYYY', true);
  return date.isValid();
};

export const isNumeric = (str: string) => {
  if (typeof str != 'string') return false; // we only process strings!
  return (
    !isNaN(Number(str)) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
    !isNaN(parseFloat(str))
  ); // ...and ensure strings of whitespace fail
};

export const groupByProperty = (arr: any, property: string) => {
  const items = arr.reduce(function (memo: any, x: any) {
    if (!memo[x[property]]) {
      memo[x[property]] = [];
    }
    memo[x[property]].push(x);
    return memo;
  }, {});
  return items;
};

const sha256 = (password: string, salt: string) => {
  const hash = crypto.createHmac('sha256', salt);
  hash.update(password);
  const value = hash.digest('hex');
  return {
    salt: salt,
    passwordHash: value,
  };
};
export function encodePassword(
  origin_password: string,
  salt: string,
  algorithm: string,
) {
  let encoded = '';
  switch (algorithm) {
    case 'sha256':
      if (salt == null)
        salt = crypto
          .randomBytes(Math.ceil(10 / 2))
          .toString('hex')
          .slice(0, 10);
      encoded = sha256(origin_password, salt).passwordHash;
      break;
    case 'bcrypt':
    default:
      if (salt == null) salt = bcrypt.genSaltSync(10);
      encoded = bcrypt.hashSync(origin_password, salt);
  }
  return { encoded: encoded, salt: salt };
}

export function uidToPath(uid4) {
  return (uid4 || '').replaceAll(/-/g, '');
}

export function excelSerialToDate(value: number | string) {
  if (typeof value === 'string') {
    if (moment(value.trim(), moment.ISO_8601, true).isValid()) {
      return moment(value.trim()).toISOString();
    }

    if (moment(value, 'DD/MM/YYYY', true).isValid()) {
      return moment(value, 'DD/MM/YYYY', true).toISOString();
    }

    if (moment(value, 'MM/YYYY', true).isValid()) {
      return moment(value, 'MM/YYYY', true).toISOString();
    }

    if (moment(value, 'DD/MM/YYYY HH:mm', true).isValid()) {
      return moment(value, 'DD/MM/YYYY HH:mm', true).toISOString();
    }
  }

  if (typeof value === 'number') {
    //The Unix time, need to subtract the number of days between January 1, 1900, and January 1, 1970.
    const convertToUnixTime = 25569;
    //Number of seconds in a day.
    const secondsInDay = 86400;

    const utcDays = Math.floor(value - convertToUnixTime);
    const utcValue = utcDays * secondsInDay;

    return moment(utcValue * 1000).toISOString(); // 1000 to convert seconds to milliseconds
  }

  if (moment(value, moment.ISO_8601, true).isValid()) {
    return moment(value).toISOString();
  }

  return null;
}

export function parseScopes(
  scopes: any[],
  permissions: (TPermission | TPermission[])[],
): boolean {
  if (!scopes || !scopes.length) {
    return false;
  }

  let res = false;
  for (const scope of scopes) {
    const userPermissions: TPermission[] = scope.resources?.split(',') || [];

    for (const requirePermission of permissions) {
      let subRes = true;
      if (Array.isArray(requirePermission)) {
        for (const subPermission of requirePermission) {
          subRes = subRes && userPermissions.includes(subPermission);
        }
      } else {
        subRes = userPermissions.includes(requirePermission);
      }
      res = res || subRes;
    }
  }
  return res;
}
///For import excel
export function getStatus<T>(model: T, status?: string) {
  let tranformStatus = 'ACTIVE';
  if (status) {
    if (
      status.toLowerCase() == 'vô hiệu hóa' ||
      status.toLowerCase() == 'in active' ||
      status.toLowerCase() == 'in_active' ||
      status.toLowerCase() == '0' ||
      status.toLowerCase() == 'inactive'
    ) {
      tranformStatus = 'IN_ACTIVE';
    } else {
      tranformStatus = 'ACTIVE';
    }
  }

  switch (model) {
    case BudgetCodeModel:
      return Object.values(EBudgetCodeStatus).find(
        (item) => item == tranformStatus,
      );
    case BudgetModel:
      return Object.values(EBudgetStatus).find(
        (item) => item == tranformStatus,
      );
    case CostcenterSubaccountModel:
      return Object.values(ECostcenterSubaccountStatus).find(
        (item) => item == tranformStatus,
      );
    case SupplierModel:
      return Object.values(ESupplierStatus).find(
        (item) => item == tranformStatus,
      );
    case SupplierSectorModel:
      return Object.values(ESupplierSectorStatus).find(
        (item) => item == tranformStatus,
      );
    case CostModel:
      return Object.values(ECostStatus).find((item) => item == tranformStatus);
    case InventoryStandardModel:
      return Object.values(EInventoryStandardStatus).find(
        (item) => item == tranformStatus,
      );
    case BusinessOwnerModel:
      return Object.values(EBusinessOwnerStatus).find(
        (item) => item == tranformStatus,
      );
    case MaterialGroupModel:
      return Object.values(EMaterialGroupStatus).find(
        (item) => item == tranformStatus,
      );
    case MaterialTypeModel:
      return Object.values(EMaterialTypeStatus).find(
        (item) => item == tranformStatus,
      );
    case DepartmentModel:
      return Object.values(EDepartmentStatus).find(
        (item) => item == tranformStatus,
      );
    case CompanyModel:
      return Object.values(ECompanyStatus).find(
        (item) => item == tranformStatus,
      );
    case BusinessUnitModel:
      return Object.values(EBusinessUnitStatus).find(
        (item) => item == tranformStatus,
      );
    default:
      return null;
  }
}

export function getBudgetCreateType(createType?: string) {
  if (createType) {
    const value = createType.trim().toLowerCase();
    if (value == 'ngân sách mới' || value == 'new') {
      return EBudgetCreateType.NEW;
    }

    if (value == 'tăng ngân sách' || value == 'increase') {
      return EBudgetCreateType.INCREASE;
    }

    if (value == 'giảm ngân sách' || value == 'decrease') {
      return EBudgetCreateType.DECREASE;
    }

    return null;
  } else {
    return null;
  }
}

export function hasDuplicateObjects(arr: object[]): boolean {
  const map = new Map<string, number>();

  for (const obj of arr) {
    const key = JSON.stringify(obj); // Chuyển đối tượng thành chuỗi để làm khóa
    if (map.has(key)) {
      return true; // Có đối tượng trùng lặp
    }
    map.set(key, 1);
  }

  return false; // Không có đối tượng trùng lặp
}

export function getIsLockBudget(isLock?: string) {
  if (isLock) {
    const value = isLock.trim().toLowerCase();

    if (value == 'đã công bố' || Number(value) == 1) {
      return true;
    }

    return false;
  } else {
    return false;
  }
}

export function getPathInventoryStandard(
  materialId: string,
  sectorId: string,
  companyId?: string,
  businessUnitId?: string,
  departmentId?: string,
) {
  let path = '';
  path += uidToPath(materialId);

  path += `.${uidToPath(sectorId)}`;

  if (companyId) {
    path += `.${uidToPath(companyId)}`;
  } else {
    path += `.${inventoryStandardNullValue}`;
  }

  if (businessUnitId) {
    path += `.${uidToPath(businessUnitId)}`;
  } else {
    path += `.${inventoryStandardNullValue}`;
  }

  if (departmentId) {
    path += `.${uidToPath(departmentId)}`;
  } else {
    path += `.${inventoryStandardNullValue}`;
  }

  return path;
}

export const getValueOrResult = (
  cell: Row,
  col: any,
  isNumber = false,
  isDate = false,
) => {
  const targetCell = cell.getCell(col);

  let value;

  // Trường hợp 1: Ô có công thức
  if (typeof targetCell.value === 'object' && targetCell.formula) {
    value = targetCell.result;
  }
  // Trường hợp 2: Ô kiểu ngày tháng
  else if (targetCell.value instanceof Date) {
    value = targetCell.value;
  }
  // Trường hợp 3: Ô có liên kết (Hyperlink)
  else if (targetCell.type === ValueType.Hyperlink) {
    value = targetCell.value?.['text'];
  }
  // Trường hợp 4: Ô có Rich Text
  else if (
    targetCell.value &&
    typeof targetCell.value === 'object' &&
    'richText' in targetCell.value
  ) {
    value = targetCell.value.richText
      .map((textPart: any) => textPart.text)
      .join('');
  }
  // Các trường hợp còn lại: lấy giá trị trực tiếp
  else {
    value = targetCell.value?.toString()?.trim();
  }

  // Xử lý các kiểu dữ liệu đặc biệt nếu cần
  if (isNumber) {
    return isNaN(Number(value)) ? null : Number(value);
  }

  if (isDate) {
    if (value instanceof Date) {
      return value;
    }
    if (!isNaN(Number(value))) {
      const excelStartDate = new Date(Date.UTC(1900, 0, 1)); // Excel bắt đầu từ 01/01/1900
      const daysOffset = Number(value) - 2; // Loại bỏ ngày 0 và bù trừ ngày Excel tính nhầm năm nhuận 1900

      return new Date(
        excelStartDate.getTime() + daysOffset * 24 * 60 * 60 * 1000,
      );
    }
    return value;
  }

  return value;
};

export function getSupplierType(type?: string) {
  if (type) {
    const value = type.trim().toLowerCase();
    if (value == 'tiềm năng' || value == 'potential' || value == '0') {
      return ESupplierType.POTENTIAL;
    }

    if (value == 'chính thức' || value == 'offical' || value == '1') {
      return ESupplierType.OFFICIAL;
    }

    if (value == 'vãng lai' || value == 'walk in' || value == '2') {
      return ESupplierType.WALK_IN;
    }

    return null;
  } else {
    return null;
  }
}

export function isOverlap(
  budget1: CreateBudgetDto,
  budget2: CreateBudgetDto,
): boolean {
  return (
    budget1.budgetCodeId === budget2.budgetCodeId &&
    budget1.costcenterSubaccountId === budget2.costcenterSubaccountId &&
    budget1.effectiveStartDate <= budget2.effectiveEndDate &&
    budget1.effectiveEndDate >= budget2.effectiveStartDate
  );
}

export function checkDuplicateBudget(
  budgets: CreateBudgetOpexDto[] | CreateBudgetCapexDto[],
  type: EBudgetType,
) {
  const matchingObjects: Set<number> = new Set();
  const overlappingPairs: (CreateBudgetOpexDto | CreateBudgetCapexDto)[][] = [];

  for (let i = 0; i < budgets.length; i++) {
    for (let j = i + 1; j < budgets.length; j++) {
      if (isOverlap(budgets[i].budgetData, budgets[j].budgetData)) {
        overlappingPairs.push([budgets[i], budgets[j]]);
        if (
          type == EBudgetType.OPEX &&
          ((budgets[i] as CreateBudgetOpexDto).operations || null) ==
            ((budgets[j] as CreateBudgetOpexDto).operations || null)
        ) {
          matchingObjects.add(i);
          matchingObjects.add(j);
        }

        if (type == EBudgetType.CAPEX) {
          if (
            ((budgets[i] as CreateBudgetCapexDto).investments || []).some(
              (inv1) =>
                ((budgets[j] as CreateBudgetCapexDto).investments || []).some(
                  (inv2) =>
                    inv1.investment == inv2.investment &&
                    inv1.quantity == inv2.quantity &&
                    inv1.price == inv2.price &&
                    inv1.transportationCosts == inv2.transportationCosts,
                ),
            )
          ) {
            matchingObjects.add(i);
            matchingObjects.add(j);
          }
        }
      }
    }
  }

  return Array.from(matchingObjects).map((index) => budgets[index]);
}

export function getBudgetCodeType(type?: string) {
  if (type) {
    const value = type.trim().toLowerCase();
    if (value == 'opex') {
      return EBudgetType.OPEX;
    }

    if (value == 'capex') {
      return EBudgetType.CAPEX;
    }

    return null;
  } else {
    return null;
  }
}

export function getStatusMaterial(status: EMaterialStatus) {
  switch (status) {
    case EMaterialStatus.ACTIVE:
      return 'Hoạt động';
    case EMaterialStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusBudgetCode(status: EBudgetCodeStatus) {
  switch (status) {
    case EBudgetCodeStatus.ACTIVE:
      return 'Hoạt động';
    case EBudgetCodeStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusSupplier(status: ESupplierSectorStatus) {
  switch (status) {
    case ESupplierSectorStatus.ACTIVE:
      return 'Hoạt động';
    case ESupplierSectorStatus.INACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getTypeSupplier(status: ESupplierType) {
  switch (status) {
    case ESupplierType.POTENTIAL:
      return 'Tiềm năng';
    case ESupplierType.OFFICIAL:
      return 'Chính thức';
    case ESupplierType.WALK_IN:
      return 'Vãng lai';
    default:
      return '';
  }
}

export function getStatusCost(status: ECostStatus) {
  switch (status) {
    case ECostStatus.ACTIVE:
      return 'Hoạt động';
    case ECostStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusInventoryStandard(status: EInventoryStandardStatus) {
  switch (status) {
    case EInventoryStandardStatus.ACTIVE:
      return 'Hoạt động';
    case EInventoryStandardStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusBusinessOwner(status: EBusinessOwnerStatus) {
  switch (status) {
    case EBusinessOwnerStatus.ACTIVE:
      return 'Hoạt động';
    case EBusinessOwnerStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusPurchasingDepartment(
  status: EPurchasingDepartmentStatus,
) {
  switch (status) {
    case EPurchasingDepartmentStatus.ACTIVE:
      return 'Hoạt động';
    case EPurchasingDepartmentStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusMaterialGroup(status: EMaterialGroupStatus) {
  switch (status) {
    case EMaterialGroupStatus.ACTIVE:
      return 'Hoạt động';
    case EMaterialGroupStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusPurchasingGroup(status: EPurchasingGroupStatus) {
  switch (status) {
    case EPurchasingGroupStatus.ACTIVE:
      return 'Hoạt động';
    case EPurchasingGroupStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusMaterialType(status: EMaterialTypeStatus) {
  switch (status) {
    case EMaterialTypeStatus.ACTIVE:
      return 'Hoạt động';
    case EMaterialTypeStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusDepartment(status: EDepartmentStatus) {
  switch (status) {
    case EDepartmentStatus.ACTIVE:
      return 'Hoạt động';
    case EDepartmentStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusCompany(status: ECompanyStatus) {
  switch (status) {
    case ECompanyStatus.ACTIVE:
      return 'Hoạt động';
    case ECompanyStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusBusinessUnit(status: EBusinessUnitStatus) {
  switch (status) {
    case EBusinessUnitStatus.ACTIVE:
      return 'Hoạt động';
    case EBusinessUnitStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusCurrencyUnit(status: ECurrencyUnitStatus) {
  switch (status) {
    case ECurrencyUnitStatus.ACTIVE:
      return 'Hoạt động';
    case ECurrencyUnitStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function getStatusCostcenterSubaccount(
  status: ECostcenterSubaccountStatus,
) {
  switch (status) {
    case ECostcenterSubaccountStatus.ACTIVE:
      return 'Hoạt động';
    case ECostcenterSubaccountStatus.IN_ACTIVE:
      return 'Không hoạt động';
    default:
      return '';
  }
}

/**
 * Converts a column number to its corresponding column label (e.g., 1 -> "A", 27 -> "AA").
 *
 * @param {number} colNumber - The column number to convert.
 * @returns {string} The column label corresponding to the given column number.
 */
export function getColumnLabel(colNumber: number): string {
  return colNumber === 0
    ? ''
    : this.getColumnLabel(Math.floor((colNumber - 1) / 26)) +
        String.fromCharCode(65 + ((colNumber - 1) % 26));
}

/**
 * Converts a column label (e.g., "A", "B", "AA") to its corresponding column number.
 *
 * @param {string} columnString - The column label to convert.
 * @param [increase=0] - An optional value to add to the resulting column number.
 * @returns {number} The column number corresponding to the given column label, plus any optional increase.
 */
export function getColumnNumber(columnString: string, increase = 0): number {
  const num = columnString
    .split('')
    .reduce((acc, char) => acc * 26 + char.charCodeAt(0) - 64, 0);
  return num + increase;
}

export async function checkValuesEmptyRowExcel(
  row: Excel.Row,
  firstCell: string,
  lastCell: string,
): Promise<boolean> {
  let isNotEmpty: boolean = false;
  for (
    let i = getColumnNumber(firstCell);
    i <= getColumnNumber(lastCell);
    i++
  ) {
    const value = getValueOrResult(row, i)?.toString();

    if (value) {
      isNotEmpty = true;
    }
  }

  return isNotEmpty;
}

export function startOfDay(time: Date | string) {
  return new Date(new Date(time).setHours(0, 0, 0, 0));
}

export function getIndexOfKeyInObject(data: object, key: string) {
  return Object.keys(data).indexOf(key);
}

// Helper to convert array data to a map
export const codeToIdMap = <T extends { code?: string; id: any }>(
  data: T[],
  key = 'code',
  properties: (keyof T)[] = ['id'], // Array of properties to include
) =>
  data?.reduce(
    (map, item) => {
      const itemKey = item[key as keyof T];
      if (itemKey) {
        // Create an object with specified properties dynamically
        map[itemKey as string] =
          properties?.length > 1
            ? properties.reduce(
                (acc, prop) => {
                  acc[prop as string] = item[prop]; // Explicit type assertion
                  return acc;
                },
                {} as Record<string, any>,
              )
            : item[properties[0]];
      }
      return map;
    },
    {} as Record<string, any>,
  );

export async function checkEffectiveTimeOverlaps(
  start: Date,
  compareStart: Date,
  end?: Date,
  compareEnd?: Date,
): Promise<boolean> {
  if (!start || !compareStart) {
    return false;
  } else {
    if (end) {
      return (
        (compareEnd &&
          new Date(compareStart) <= new Date(start) &&
          new Date(compareEnd) >= new Date(end)) ||
        (!compareEnd &&
          new Date(compareStart) >= new Date(end) &&
          new Date(compareStart) <= new Date(start))
      );
    } else {
      return (
        ((new Date(compareStart) <= new Date(start) ||
          new Date(compareStart) >= new Date(start)) &&
          !compareEnd) ||
        (compareEnd &&
          new Date(compareStart) <= new Date(start) &&
          new Date(compareEnd) >= new Date(start))
      );
    }
  }
}

export function getFirstAndLastDate(month, year) {
  // Lưu ý: Tháng trong JavaScript bắt đầu từ 0 (0 = Tháng 1, 11 = Tháng 12)
  const firstDate = new Date(Date.UTC(year, month, 1)); // Ngày đầu tiên của tháng
  const lastDate = new Date(Date.UTC(year, month + 1, 0)); // Ngày cuối cùng của tháng

  // Định dạng ngày theo YYYY-MM-DD
  const formatDate = (date: Date): string =>
    `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

  return {
    firstDate: formatDate(firstDate),
    lastDate: formatDate(lastDate),
  };
}

export function isDateInRange(
  date: Date,
  startDate: Date,
  endDate: Date,
): boolean {
  const targetDate = moment(date, 'YYYY-MM-DD');
  const start = startDate ? moment(startDate, 'YYYY-MM-DD') : moment();
  const end = endDate ? moment(endDate, 'YYYY-MM-DD') : moment();

  return targetDate.isBetween(start, end, 'days', '[]'); // [] bao gồm cả startDate và endDate
}

export function formatDate(date?: Date, format: string = 'YYYY-MM-DD') {
  return date ? moment(date).format(format) : '';
}

export function handleCurrencyConversion<T>(
  data: T,
  fieldMappings: {
    amountField: keyof T; // Field for docAmount or similar
    dateField: keyof T; // Field for postingDate or similar
    currencyField: keyof T; // Field for currency or similar
    taxCodeField?: keyof T; // Field for taxCode or similar
    taxRateField?: keyof T; // Field for taxRate or similar
  },
  convertCurrency?: CurrencyUnitModel,
) {
  if (!convertCurrency || !convertCurrency.currencyExchanges?.length) return;

  const { amountField, dateField, currencyField, taxCodeField, taxRateField } =
    fieldMappings;

  const taxRate = taxRateField
    ? (data[taxRateField] as any)
      ? Number(data[taxRateField] as any)
      : 0
    : 0;

  (data[amountField] as any) = taxCodeField
    ? (data[taxCodeField] as any)
      ? !(data[taxCodeField] as any).startsWith('A')
        ? Number(data[amountField] as any) * (taxRate / 100 + 1)
        : Number(data[amountField] as any)
      : Number(data[amountField] as any)
    : Number(data[amountField] as any);

  const dataCurrencyExchange = !(data as any).exchangeRate
    ? (data[currencyField] as any)?.currencyExchanges?.find((item: any) =>
        isDateInRange(
          data[dateField] as Date,
          item.effectiveStartDate,
          item.effectiveEndDate,
        ),
      )
    : (data as any).exchangeRate;

  const convertCurrencyExchange = convertCurrency.currencyExchanges.find(
    (item) =>
      isDateInRange(
        data[dateField] as Date,
        item.effectiveStartDate,
        item.effectiveEndDate,
      ),
  );

  if (
    (data[currencyField] as any)?.currencyCode !== 'VND' &&
    convertCurrency.currencyCode === 'VND' &&
    dataCurrencyExchange
  ) {
    (data[amountField] as any) =
      Number(data[amountField] as any) *
      Number(dataCurrencyExchange.exchangeRate);
  } else if (
    (data[currencyField] as any)?.currencyCode === 'VND' &&
    convertCurrency.currencyCode !== 'VND' &&
    convertCurrencyExchange
  ) {
    (data[amountField] as any) =
      Number(data[amountField] as any) /
      Number(convertCurrencyExchange.exchangeRate);
  } else if (
    (data[currencyField] as any)?.currencyCode === 'VND' &&
    convertCurrency.currencyCode === 'VND' &&
    dataCurrencyExchange &&
    convertCurrencyExchange
  ) {
    (data[amountField] as any) =
      (Number(data[amountField] as any) *
        Number(dataCurrencyExchange.exchangeRate)) /
      convertCurrencyExchange.exchangeRate;
  }
}

export async function processInBatches<T>(
  items: T[],
  batchSize: number,
  processFn: (batch: T[]) => Promise<void>,
): Promise<void> {
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    await processFn(batch);
  }
}

export function getStatusPir(status: Status) {
  switch (status) {
    case Status.Active:
      return 'Đang hoạt động';
    case Status.Inactive:
      return 'Không hoạt động';
    default:
      return '';
  }
}

export function convertToGMT7(
  dateString: string | Date,
  format: string = 'YYYY-MM-DD',
) {
  let time = moment(dateString);

  if (!time.isValid()) {
    throw new Error('Thời gian không hợp lệ');
  }

  if (
    time.isUTC() ||
    (typeof dateString === 'string' && dateString.endsWith('Z'))
  ) {
    // Nếu thời gian là UTC, chuyển sang UTC+7 và đặt về 00:00
    time = time.utcOffset(7 * 60);
  } else if (time.format('YYYY-MM-DD') === dateString) {
    // Nếu thời gian là dạng YYYY-MM-DD, giữ nguyên
  } else {
    // Nếu thời gian không phải UTC, chuyển sang UTC+7 và đặt về 00:00
    time = time.utcOffset(7 * 60);
  }

  return time.format(format);
}

export function embedIdInUuid(id: number): string {
  const uuid = uuidv4();
  return `${uuid.substring(0, 24)}${id.toString().padStart(12, '0')}`;
}

export function generateApprovalToken(
  email: string,
  purchase_id: number,
): string {
  const payload = {
    email,
    purchase_id,
  };
  const secret = process.env.JWT_SECRET; // Use a secret from your config
  const options = {
    expiresIn: '8h',
  };

  const token = jwt.sign(payload, secret, options);
  return token;
}

export function formatCurrencyVND(amount) {
  return amount.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' });
}

export function groupByMultipleFields<T>(arr: T[], fields: (keyof T)[]): T[][] {
  const map = new Map<string, T[]>();

  arr.forEach((item) => {
    // Tạo chuỗi key bằng cách nối các giá trị của các field (dùng JSON.stringify cho object)
    const key = fields
      .map((field) => {
        const value = item[field];
        return typeof value === 'object' ? JSON.stringify(value) : value;
      })
      .join('-');

    // Nếu key chưa tồn tại trong map thì thêm một mảng rỗng
    if (!map.has(key)) {
      map.set(key, []);
    }

    // Thêm item vào nhóm tương ứng
    map.get(key)!.push(item);
  });

  // Trả ra mảng các mảng giá trị duy nhất
  return Array.from(map.values());
}

export function comparisonResult(comparisonType, value, target): boolean {
  switch (comparisonType) {
    case EComparisonType.INCLUSION:
      return value.includes(target);
    case EComparisonType.EXCLUSION:
      return !value.includes(target);
    case EComparisonType.EQUAL:
      return value === target;
    case EComparisonType.NOT_EQUAL:
      return value !== target;
    case EComparisonType.GREATER_THAN:
      return value > target;
    case EComparisonType.GREATER_THAN_OR_EQUAL:
      return value >= target;
    case EComparisonType.LESS_THAN:
      return value < target;
    case EComparisonType.LESS_THAN_OR_EQUAL:
      return value <= target;
    case EComparisonType.YES:
      return value == 1;
    case EComparisonType.NO:
      return value == 0;
    default:
      return false;
  }
}

export function mergedErrors(errors: TErrorMessageImport[]) {
  return Object.values(
    errors.reduce<Record<string, TErrorMessageImport>>((acc, curr) => {
      const key = `${curr.row}-${curr.error.errorCode}`;
      if (!acc[key]) {
        acc[key] = curr;
      }
      return acc;
    }, {}),
  );
}

export function getUTCOffset(date: string | Date): string {
  const offsetMinutes = new Date(date).getTimezoneOffset();
  const offsetHours = -offsetMinutes / 60;
  const sign = offsetHours >= 0 ? '+' : '-';
  return `UTC${sign}${Math.abs(offsetHours)}`;
}

export function ensureUTC7(dateInput: string | Date): Date {
  const date = new Date(dateInput);

  // Nếu đang UTC+7 rồi thì return
  if (getUTCOffset(date) === 'UTC+7') {
    return date;
  }

  // Đưa về UTC trước
  const utc = date.getTime() + date.getTimezoneOffset() * 60 * 1000;

  // Thêm 7 tiếng để thành UTC+7
  return new Date(utc + 7 * 60 * 60 * 1000);
}

export function getFlattenColumnValues(rows, column) {
  return [
    ...new Set(
      _.flatten(
        rows
          .map((row) =>
            getValueOrResult(row, column)
              ?.toString()
              ?.split(',')
              ?.map((item) => item.trim()),
          )
          .filter(Boolean) || [],
      ),
    ),
  ];
}
