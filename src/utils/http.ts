import axios, { AxiosBasicCredentials, AxiosRequestConfig } from 'axios';
import axiosRetry from 'axios-retry';
import * as https from 'https';
import * as fs from 'fs';
import { resolve } from 'path';

axiosRetry(axios, { retries: 3 });
const instance = axios.create({
  httpsAgent: new https.Agent({
    requestCert: true,
    rejectUnauthorized: false,
    ca: fs.readFileSync(
      resolve(__dirname, '../domain/template-export/_.greenfeed.com.vn.crt'),
    ),
  }),
  headers: {
    'content-type': 'application/json',
  },
});

export const sendPost = async (
  url: string,
  data: any,
  headers = {},
  auth?: AxiosBasicCredentials,
) => {
  // console.log(6, data);
  // return await axios.request({
  //   url: url,
  //   method: 'POST',
  //   data: data,
  //   headers: { ...{ 'content-type': 'application/json' }, ...headers },
  // });

  try {
    return await instance.post(url, data, { headers, auth });
  } catch (error) {
    console.error('Error in sendPost:', error);
    throw error;
  }
};
// export const sendPost = async (url: string, data: any, headers = {}, auth?: AxiosBasicCredentials) => {
//   const config: AxiosRequestConfig = {
//     url: url,
//     method: 'POST',
//     data: data,
//     headers: { ...{ 'content-type': 'application/json' }, ...headers },
//   };

//   if (auth) {
//     config.auth = auth;
//   }
//   return await axios.request(config);
// };

export const getData = async (url: string, headers = {}, params?: any) => {
  return await axios.request({
    url: url,
    method: 'GET',
    params: params,
    headers: { ...{ 'content-type': 'application/json' }, ...headers },
  });
};

export const sendPatch = (url: string, data: any, headers = {}) => {
  return axios.request({
    url: url,
    method: 'PATCH',
    data: data,
    headers: { ...{ 'content-type': 'application/json' }, ...headers },
  });
};

export const sendDelete = (url: string, headers = {}) => {
  return axios.request({
    url: url,
    method: 'DELETE',
    headers: { ...{ 'content-type': 'application/json' }, ...headers },
  });
};

export const sendPostFormData = (url: string, data: any, headers = {}) => {
  return axios.request({
    url: url,
    method: 'POST',
    data: data,
    headers: { ...{ 'content-type': 'application/json' }, ...headers },
  });
};
