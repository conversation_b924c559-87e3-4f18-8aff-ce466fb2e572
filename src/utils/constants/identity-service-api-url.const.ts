const domain = process.env.IDENTITY_SERVICE_API_URL;

export const IdentityServiceApiUrlsConst = {
  CREATE_DATA_ROLE: () => domain + '/data-role/create',
  UPDATE_DATA_ROLE: (refId) => domain + `/data-role/${refId}`,
  DELETE_DATA_ROLE: (refId) => domain + `/data-role/${refId}`,
  UPDATE_ACCOUNT: (staffId) => domain + `/account/${staffId}`,
  DELETE_ACCOUNT_BY_STAFF_ID: (staffId) => domain + `/account/${staffId}`,
  VALIDATE_TOKEN: () => domain + '/auth/validate-token',
  GET_ACCOUNTS_APPROVER: () => domain + '/user/approver-by-position',
};
