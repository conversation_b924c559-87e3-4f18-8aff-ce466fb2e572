export enum EBusinessOwnerPermission {
  VIEW = 'bo:view',
  CREATE = 'bo:create',
  EDIT = 'bo:edit',
  DELETE = 'bo:delete',
  IMPORT = 'bo:import:execute',
  EXPORT = 'bo:export:execute',
}

export enum EBudgetPermission {
  VIEW = 'budget:view',
  CREATE = 'budget:create',
  EDIT = 'budget:edit',
  DELETE = 'budget:delete',
  IMPORT = 'budget:import:execute',
  EXPORT = 'budget:export:execute',
  REPORT = 'budget:report:execute',
  LOCK_UNLOCK = 'budget:lock_unlock:execute', //resource action
  GET_LIST = 'budget:get_list:execute', //resource action
}

export enum EFunctionUnitPermission {
  VIEW = 'function_unit:view',
  CREATE = 'function_unit:create',
  EDIT = 'function_unit:edit',
  DELETE = 'function_unit:delete',
  IMPORT = 'function_unit:import:execute',
  EXPORT = 'function_unit:export:execute',
}

export enum EDepartmentPermission {
  VIEW = 'department:view',
  CREATE = 'department:create',
  EDIT = 'department:edit',
  DELETE = 'department:delete',
  IMPORT = 'department:import:execute',
  EXPORT = 'department:export:execute',
}

export enum EBusinessUnitPermission {
  VIEW = 'business_unit:view',
  CREATE = 'business_unit:create',
  EDIT = 'business_unit:edit',
  DELETE = 'business_unit:delete',
  IMPORT = 'business_unit:import:execute',
  EXPORT = 'business_unit:export:execute',
}

export enum ECompanyPermission {
  VIEW = 'company:view',
  CREATE = 'company:create',
  EDIT = 'company:edit',
  DELETE = 'company:delete',
  IMPORT = 'company:import:execute',
  EXPORT = 'company:export:execute',
}

export enum ESectorPermission {
  VIEW = 'sector:view',
  CREATE = 'sector:create',
  EDIT = 'sector:edit',
  DELETE = 'sector:delete',
  IMPORT = 'sector:import:execute',
  EXPORT = 'sector:export:execute',
}

export enum ECostcenterSubaccountPermission {
  VIEW = 'costcenter_subaccount:view',
  CREATE = 'costcenter_subaccount:create',
  EDIT = 'costcenter_subaccount:edit',
  DELETE = 'costcenter_subaccount:delete',
  IMPORT = 'costcenter_subaccount:import:execute',
  EXPORT = 'costcenter_subaccount:export:execute',
}

export enum EBudgetCodePermission {
  VIEW = 'budget_code:view',
  CREATE = 'budget_code:create',
  EDIT = 'budget_code:edit',
  DELETE = 'budget_code:delete',
  IMPORT = 'budget_code:import:execute',
  EXPORT = 'budget_code:export:execute',
}

export enum ECurrencyUnitPermission {
  VIEW = 'currency_unit:view',
  CREATE = 'currency_unit:create',
  EDIT = 'currency_unit:edit',
  DELETE = 'currency_unit:delete',
  IMPORT = 'currency_unit:import:execute',
  EXPORT = 'currency_unit:export:execute',
}

export enum ESupplierPermission {
  VIEW = 'supplier:view',
  CREATE = 'supplier:create',
  EDIT = 'supplier:edit',
  DELETE = 'supplier:delete',
  IMPORT = 'supplier:import:execute',
  EXPORT = 'supplier:export:execute',
}

export enum EPurchaseRequestTypePermission {
  VIEW = 'purchase_request_type:view',
  CREATE = 'purchase_request_type:create',
  EDIT = 'purchase_request_type:edit',
  DELETE = 'purchase_request_type:delete',
  IMPORT = 'purchase_request_type:import:execute',
  EXPORT = 'purchase_request_type:export:execute',
}

export enum EPurchaseOrderTypePermission {
  VIEW = 'purchase_order_type:view',
  CREATE = 'purchase_order_type:create',
  EDIT = 'purchase_order_type:edit',
  DELETE = 'purchase_order_type:delete',
  IMPORT = 'purchase_order_type:import:execute',
  EXPORT = 'purchase_order_type:export:execute',
}

export enum EPurchasingGroupPermission {
  VIEW = 'purchasing_group:view',
  CREATE = 'purchasing_group:create',
  EDIT = 'purchasing_group:edit',
  DELETE = 'purchasing_group:delete',
  IMPORT = 'purchasing_group:import:execute',
  EXPORT = 'purchasing_group:export:execute',
}

export enum EPurchasingDepartmentPermission {
  VIEW = 'purchasing_department:view',
  CREATE = 'purchasing_department:create',
  EDIT = 'purchasing_department:edit',
  DELETE = 'purchasing_department:delete',
  IMPORT = 'purchasing_department:import:execute',
  EXPORT = 'purchasing_department:export:execute',
}

export enum EPlantPermission {
  VIEW = 'plant:view',
  CREATE = 'plant:create',
  EDIT = 'plant:edit',
  DELETE = 'plant:delete',
  IMPORT = 'plant:import:execute',
  EXPORT = 'plant:export:execute',
}

export enum EMaterialTypePermission {
  VIEW = 'material_type:view',
  CREATE = 'material_type:create',
  EDIT = 'material_type:edit',
  DELETE = 'material_type:delete',
  IMPORT = 'material_type:import:execute',
  EXPORT = 'material_type:export:execute',
}

export enum EMaterialGroupPermission {
  VIEW = 'material_group:view',
  CREATE = 'material_group:create',
  EDIT = 'material_group:edit',
  DELETE = 'material_group:delete',
  IMPORT = 'material_group:import:execute',
  EXPORT = 'material_group:export:execute',
}

export enum EMaterialPermission {
  VIEW = 'material:view',
  CREATE = 'material:create',
  EDIT = 'material:edit',
  DELETE = 'material:delete',
  IMPORT = 'material:import:execute',
  EXPORT = 'material:export:execute',
}

export enum EPositionPermission {
  VIEW = 'position:view',
  CREATE = 'position:create',
  EDIT = 'position:edit',
  DELETE = 'position:delete',
  IMPORT = 'position:import:execute',
  EXPORT = 'position:export:execute',
}

export enum EStaffPermission {
  VIEW = 'staff:view',
  CREATE = 'staff:create',
  EDIT = 'staff:edit',
  DELETE = 'staff:delete',
  IMPORT = 'staff:import:execute',
  EXPORT = 'staff:export:execute',
}

export enum EInventoryStandardPermission {
  VIEW = 'is:view',
  CREATE = 'is:create',
  EDIT = 'is:edit',
  DELETE = 'is:delete',
  IMPORT = 'is:import:execute',
  EXPORT = 'is:export:execute',
}

export enum ENotificationFormPermission {
  VIEW = 'notif:view',
  CREATE = 'notif:create',
  EDIT = 'notif:edit',
  DELETE = 'notif:delete',
  IMPORT = 'notif:import:execute',
  EXPORT = 'notif:export:execute',
}

export enum EProfitCenterPermission {
  VIEW = 'pc:view',
  CREATE = 'pc:create',
  EDIT = 'pc:edit',
  DELETE = 'pc:delete',
  IMPORT = 'pc:import:execute',
  EXPORT = 'pc:export:execute',
}

export enum ECostPermission {
  VIEW = 'cost:view',
  CREATE = 'cost:create',
  EDIT = 'cost:edit',
  DELETE = 'cost:delete',
  IMPORT = 'cost:import:execute',
  EXPORT = 'cost:export:execute',
}

export enum EActualSpendingPermission {
  VIEW = 'as_ad:view',
  CREATE = 'as_ad:create',
  EDIT = 'as_ad:edit',
  DELETE = 'as_ad:delete',
  CONFIRM = 'as_ad:confirm:execute',
  IMPORT = 'as_ad:import:execute',
  EXPORT = 'as_ad:export:execute',
}

export enum EProcessTypePermission {
  VIEW = 'pt_ad:view',
  CREATE = 'pt_ad:create',
  EDIT = 'pt_ad:edit',
  DELETE = 'pt_ad:delete',
  IMPORT = 'pt_ad:import:execute',
  EXPORT = 'pt_ad:export:execute',
}

export enum EProcessPermission {
  VIEW = 'p_ad:view',
  CREATE = 'p_ad:create',
  EDIT = 'p_ad:edit',
  DELETE = 'p_ad:delete',
  IMPORT = 'p_ad:import:execute',
  EXPORT = 'p_ad:export:execute',
}

export enum EAdmin {
  ADMIN_SUPER = 'is_super_admin',
  ADMIN = 'is_admin',
}

export enum EPurchaseRequestPermission {
  VIEW = 'pr_ad:view',
  CREATE = 'pr_ad:create',
  EDIT = 'pr_ad:edit',
  DELETE = 'pr_ad:delete',
  CANCEL = 'pr_ad:cancel:execute',
  EXPORT = 'pr_ad:export:execute',
  CLOSED = 'pr_ad:closed:execute',
}

export enum ETaxCodePermission {
  VIEW = 'tc:view',
  CREATE = 'tc:create',
  EDIT = 'tc:edit',
  DELETE = 'tc:delete',
  IMPORT = 'tc:import:execute',
  EXPORT = 'tc:export:execute',
}

export enum EPurchaseOrderPermission {
  VIEW = 'po_ad:view',
  CREATE = 'po_ad:create',
  EDIT = 'po_ad:edit',
  DELETE = 'po_ad:delete',
  CANCEL = 'po_ad:cancel:execute',
  EXPORT = 'po_ad:export:execute',
  CLOSED = 'po_ad:closed:execute',
}

export enum EApprovedMaterialPermission {
  VIEW = 'am_ad:view',
  CREATE = 'am_ad:create',
  EDIT = 'am_ad:edit',
  DELETE = 'am_ad:delete',
  EXPORT = 'am_ad:export:execute',
}

export enum EApprovalProcessDetailPermission {
  VIEW = 'apd:view',
  CREATE = 'apd:create',
  EDIT = 'apd:edit',
  DELETE = 'apd:delete',
  IMPORT = 'apd:import:execute',
  EXPORT = 'apd:export:execute',
}

export enum EWarehousePermission {
  VIEW = 'wh:view',
  CREATE = 'wh:create',
  EDIT = 'wh:edit',
  DELETE = 'wh:delete',
  IMPORT = 'wh:import:execute',
  EXPORT = 'wh:export:execute',
}

export enum EMeasurePermission {
  VIEW = 'ms:view',
  CREATE = 'ms:create',
  EDIT = 'ms:edit',
  DELETE = 'ms:delete',
  IMPORT = 'ms:import:execute',
  EXPORT = 'ms:export:execute',
}

export type TPermission =
  | EBusinessOwnerPermission
  | EAdmin
  | EBudgetPermission
  | EFunctionUnitPermission
  | EDepartmentPermission
  | EBusinessUnitPermission
  | ECompanyPermission
  | ESectorPermission
  | ECostcenterSubaccountPermission
  | EBudgetCodePermission
  | ECurrencyUnitPermission
  | ESupplierPermission
  | EPurchaseRequestTypePermission
  | EPurchaseOrderTypePermission
  | EPurchasingGroupPermission
  | EPurchasingDepartmentPermission
  | EPlantPermission
  | EMaterialTypePermission
  | EMaterialGroupPermission
  | EMaterialPermission
  | EPositionPermission
  | EStaffPermission
  | EInventoryStandardPermission
  | ENotificationFormPermission
  | EProfitCenterPermission
  | ECostPermission
  | EPurchaseRequestPermission
  | EPurchaseOrderPermission
  | EActualSpendingPermission
  | EProcessTypePermission
  | EProcessPermission
  | EApprovedMaterialPermission
  | ETaxCodePermission
  | EApprovalProcessDetailPermission
  | EWarehousePermission
  | EMeasurePermission;
