///Domain Purchase Service
const domainPurchaseService = process.env.PURCHASE_SERVICE_API_URL;

export const PurchaseServiceApiUrlsConst = {
  /* NGÂN SÁCH */
  IMPORT_OPEX: () => domainPurchaseService + '/import/import-opex',
  IMPORT_CAPEX: () => domainPurchaseService + '/import/import-capex',
  /* DANH MỤC */
  IMPORT_BUDGET_CODE: () =>
    domainPurchaseService + '/import/import-budget-code',
  IMPORT_COSTCENTER_SUBACCOUNT: () =>
    domainPurchaseService + '/import/import-costcenter-subaccount',
  UPDATE_STATUS_FILE_IMPORT: (refId) =>
    domainPurchaseService + `/file-import-history/update-status/${refId}`,
  IMPORT_SUPPLIER: () => domainPurchaseService + '/import/import-supplier',
  IMPORT_MATERIAL: () => domainPurchaseService + '/import/import-material',
  IMPORT_COST: () => domainPurchaseService + '/import/import-cost',
  IMPORT_CURRENCY_EXCHANGE: () =>
    domainPurchaseService + '/import/import-currency-exchange',
  IMPORT_INVENTORY_STANDARD: () =>
    domainPurchaseService + '/import/import-inventory-standard',
  IMPORT_BUSINESS_OWNER: () =>
    domainPurchaseService + '/import/import-business-owner',
  IMPORT_MATERIAL_GROUP: () =>
    domainPurchaseService + '/import/import-material-group',
  IMPORT_MATERIAL_TYPE: () =>
    domainPurchaseService + '/import/import-material-type',
  IMPORT_DEPARTMENT: () => domainPurchaseService + '/import/import-department',
  IMPORT_COMPANY: () => domainPurchaseService + '/import/import-company',
  IMPORT_BUSINESS_UNIT: () =>
    domainPurchaseService + '/import/import-business-unit',
  /* THỰC CHI */
  IMPORT_ACTUAL_SPENDING: () =>
    domainPurchaseService + '/import/import-actual-spending',
  CREATE_RECEIPT_SOLOMON: () =>
    domainPurchaseService + '/solomon/handle-create-receipt',
};
