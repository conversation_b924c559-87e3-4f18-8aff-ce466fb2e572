///Domain Purchase Service
const domainSAP = process.env.SAP_API_URL;
const sapClient = process.env.SAP_CLIENT;
export const SapApiUrlsConst = {
  /* PO */
  CREATE_PO: () => domainSAP + `/epur/createpo?sap-client=${sapClient}`,
  DELETE_PO: () => domainSAP + `/epur/deletepo?sap-client=${sapClient}`,
};

///Domain SAP Api FOOD
const domainSAPFood = process.env.SAP_API_URL_FOOD;
const sapClientFood = process.env.SAP_CLIENT_FOOD;
export const SapApiUrlsFoodConst = {
  /* PO */
  CREATE_PO: () => domainSAPFood + `/epur/createpo?sap-client=${sapClientFood}`,
  DELETE_PO: () => domainSAPFood + `/epur/deletepo?sap-client=${sapClientFood}`,
};
