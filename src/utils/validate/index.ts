import { HttpException, HttpStatus } from '@nestjs/common';
import { ValidatorOptions, validate } from 'class-validator';
import { fileErrorDetails } from '../../domain/messages/error-details/file';

interface IValidateCustom {
  key: string;
  errorMessage: string;
}

export const validateCustom = async (
  object: object,
  validatorOptions?: ValidatorOptions,
) => {
  const errors: IValidateCustom[] = [];

  const error = await validate(object, validatorOptions);
  error.forEach((error) => {
    errors.push({
      key: error.property,
      errorMessage: Object.values(error.constraints)[0],
    });
  });

  return errors;
};

export const filesValidate = async (
  fileIsRequired: boolean = true,
  files?: Express.Multer.File[],
  mimeTypes?: string[],
  maxSize?: number,
) => {
  if (fileIsRequired && !files?.length) {
    throw new HttpException(
      fileErrorDetails.E_7002(),
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }

  if (files?.length) {
    for (const file of files) {
      if (mimeTypes && !mimeTypes.includes(file.mimetype)) {
        throw new HttpException(
          fileErrorDetails.E_7003(),
          HttpStatus.UNPROCESSABLE_ENTITY,
        );
      }

      if (maxSize && file.size > maxSize) {
        throw new HttpException(
          fileErrorDetails.E_7004(),
          HttpStatus.UNPROCESSABLE_ENTITY,
        );
      }
    }
  }
};
