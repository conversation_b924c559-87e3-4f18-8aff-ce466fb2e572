import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Scope,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { StatusLevel } from '../../controller/approve/dtos/approve.dto';
import { GetPurchaseOrderDto } from '../../controller/purchase-order/dtos/get-all-purchase-order.dto';
import { GetPoDetailReportBudgetDetailDto } from '../../controller/purchase-order/dtos/get-po-detail-report-budget.dto';
import { GetPOWithBudgetDto } from '../../controller/purchase-order/dtos/get-po-with-budget.dto';
import {
  PurchaseOrderDto,
  UpdatePurchaseOrderDto,
} from '../../controller/purchase-order/dtos/purchase-order.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import {
  errorMessage,
  getErrorMessage,
} from '../../domain/messages/error-message';
import { IPurchaseOrderRepository } from '../../domain/repositories/purchaseOrderRepository.repository';
import { convertToGMT7, parseScopes } from '../../utils/common';
import { DataSource, In } from 'typeorm';
import { HistoryApproveEntity } from '../entities/history-approve.entity';
import { PurchaseOrderEntity } from '../entities/purchase_order.entity';
import { PurchaseOrderDetailEntity } from '../entities/purchase_order_detail.entity';
import { BaseRepository } from './base.repository';
import {
  EDisplayStatus,
  State,
  Status,
} from '../../domain/config/enums/purchase-order.enum';
import { PurchaseOrderModel } from '../../domain/model/purchase_order.model';
import { HistoryApproveModel } from '../../domain/model/history-approve.model';
import {
  EBusinessUnitPermission,
  EDepartmentPermission,
  EFunctionUnitPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class purchaseOrderRepository
  extends BaseRepository
  implements IPurchaseOrderRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async findAll(
    paginationDto: GetPurchaseOrderDto,
    jwtPayload,
    isImport: boolean = false,
  ): Promise<ResponseDto<PurchaseOrderEntity>> {
    const repository = this.getRepository(PurchaseOrderEntity);
    const queryBuilder = repository.createQueryBuilder('po');

    queryBuilder.leftJoinAndSelect('po.history', 'history');

    if (paginationDto.businessUnitIds?.length) {
      queryBuilder.andWhere('po.businessUnitId IN (:...businessUnitIds)', {
        businessUnitIds: paginationDto.businessUnitIds,
      });
    }

    if (paginationDto.sectorIds?.length) {
      queryBuilder.andWhere('po.sectorId IN (:...sectorIds)', {
        sectorIds: paginationDto.sectorIds,
      });
    }

    if (paginationDto.typePoIds?.length) {
      queryBuilder.andWhere('po.typePoId IN (:...typePoIds)', {
        typePoIds: paginationDto.typePoIds,
      });
    }

    if (paginationDto.budgetCodeIds?.length) {
      queryBuilder.andWhere('po.budgetCodeId IN (:...budgetCodeIds)', {
        budgetCodeIds: paginationDto.budgetCodeIds,
      });
    }

    if (paginationDto.costCenterIds?.length) {
      queryBuilder.andWhere('po.costCenterId IN (:...costCenterIds)', {
        costCenterIds: paginationDto.costCenterIds,
      });
    }

    if (paginationDto.displayStatusPos?.length) {
      queryBuilder.andWhere('po.displayStatusPo IN (:...displayStatusPos)', {
        displayStatusPos: paginationDto.displayStatusPos,
      });
    }

    if (paginationDto.statePos?.length) {
      queryBuilder.andWhere('po.statePo IN (:...statePos)', {
        statePos: paginationDto.statePos,
      });
    }

    if (paginationDto.idPo) {
      queryBuilder.andWhere('cast(po.id as text) LIKE :idPo', {
        idPo: `%${paginationDto.idPo}%`,
      });
    }

    if (paginationDto.businessUnitIds?.length) {
      queryBuilder.andWhere('po.businessUnitId IN (:...businessUnitIds)', {
        businessUnitIds: paginationDto.businessUnitIds,
      });
    }

    if (paginationDto.sectorIds?.length) {
      queryBuilder.andWhere('po.sectorId IN (:...sectorIds)', {
        sectorIds: paginationDto.sectorIds,
      });
    }

    if (paginationDto.processTypeIds?.length) {
      queryBuilder.andWhere('po.processTypeId IN (:...processTypeIds)', {
        processTypeIds: paginationDto.processTypeIds,
      });
    }

    if (paginationDto.idPr && paginationDto.materialCodeIds?.length) {
      queryBuilder.innerJoinAndSelect(
        'po.details',
        'details',
        'cast(details.prReference as text) LIKE :idPr AND details.materialCodeId IN (:...materialCodeIds)',
        {
          idPr: `%${paginationDto.idPr}%`,
          materialCodeIds: paginationDto.materialCodeIds,
        },
      );
    } else {
      if (paginationDto.idPr) {
        queryBuilder.innerJoinAndSelect(
          'po.details',
          'details',
          'cast(details.prReference as text) LIKE :idPr',
          {
            idPr: `%${paginationDto.idPr}%`,
          },
        );
      } else if (paginationDto.materialCodeIds?.length) {
        queryBuilder.innerJoinAndSelect(
          'po.details',
          'details',
          'details.materialCodeId IN (:...materialCodeIds)',
          {
            materialCodeIds: paginationDto.materialCodeIds,
          },
        );
      } else {
        queryBuilder.innerJoinAndSelect('po.details', 'details');
      }
    }

    queryBuilder
      .leftJoin('details.sapPurchaseOrderItems', 'sapPurchaseOrderItems')
      .addSelect([
        'sapPurchaseOrderItems.id',
        'sapPurchaseOrderItems.messageType',
        'sapPurchaseOrderItems.message',
      ]);

    queryBuilder
      .leftJoin('sapPurchaseOrderItems.sapPurchaseOrder', 'sapPurchaseOrder')
      .addSelect([
        'sapPurchaseOrder.id',
        'sapPurchaseOrder.messageType',
        'sapPurchaseOrder.message',
      ]);

    /// JOIN FOR PO
    queryBuilder
      .leftJoin('po.sector', 'sector')
      .addSelect(['sector.id', 'sector.name', 'sector.code'])
      .leftJoin('po.businessUnit', 'businessUnit')
      .addSelect(['businessUnit.id', 'businessUnit.name', 'businessUnit.code'])
      .leftJoin('po.requester', 'requester')
      .addSelect([
        'requester.id',
        'requester.firstName',
        'requester.lastName',
        'requester.email',
        'requester.code',
      ])
      .leftJoin('po.typePo', 'typePo')
      .addSelect(['typePo.id', 'typePo.name', 'typePo.code', 'typePo.status'])
      .leftJoin('po.budgetCode', 'budgetCode')
      .addSelect(['budgetCode.id', 'budgetCode.name', 'budgetCode.code'])
      .leftJoin('po.costCenter', 'costCenter')
      .addSelect(['costCenter.id', 'costCenter.name', 'costCenter.code'])
      .leftJoin('po.purchaseOrg', 'purchaseOrg')
      .addSelect(['purchaseOrg.id', 'purchaseOrg.name', 'purchaseOrg.code'])
      .leftJoin('po.purchaseGroup', 'purchaseGroup')
      .addSelect([
        'purchaseGroup.id',
        'purchaseGroup.name',
        'purchaseGroup.code',
      ])
      .leftJoin('po.currency', 'currency')
      .addSelect(['currency.id', 'currency.name', 'currency.currencyCode'])
      .leftJoin('po.processType', 'processType')
      .addSelect(['processType.id', 'processType.name', 'processType.code'])
      .leftJoin('po.plant', 'plant')
      .addSelect(['plant.id', 'plant.name', 'plant.code'])
      .leftJoin('po.functionUnit', 'functionUnit')
      .addSelect(['functionUnit.id', 'functionUnit.name', 'functionUnit.code'])
      .leftJoin('po.department', 'department')
      .addSelect(['department.id', 'department.name', 'department.code']);

    if (isImport) {
      /// JOIN FOR DETAILS
      queryBuilder
        .leftJoin('details.budgetCode', 'budgetCodeDetail')
        .addSelect([
          'budgetCodeDetail.id',
          'budgetCodeDetail.name',
          'budgetCodeDetail.code',
          'budgetCodeDetail.budgetType',
        ])
        .leftJoin('details.measure', 'measure')
        .addSelect([
          'measure.id',
          'measure.name',
          'measure.code',
          'measure.description',
        ])
        .leftJoin('details.warehouse', 'warehouse')
        .addSelect([
          'warehouse.id',
          'warehouse.name',
          'warehouse.code',
          'warehouse.description',
        ])
        .leftJoin('details.taxCode', 'taxCode')
        .addSelect([
          'taxCode.id',
          'taxCode.code',
          'taxCode.description',
          'taxCode.taxRate',
        ])
        .leftJoin('details.material', 'material')
        .addSelect(['material.id', 'material.name', 'material.code'])
        .leftJoin('details.costCenter', 'costCenterDetail')
        .addSelect([
          'costCenterDetail.id',
          'costCenterDetail.name',
          'costCenterDetail.code',
        ])
        .leftJoin('details.pir', 'pir')
        .addSelect([
          'pir.id',
          'pir.regularPurchaseQuantity',
          'pir.minimumOrderQuantity',
          'pir.upperTolerance',
          'pir.lowerTolerance',
          'pir.purchasePrice',
          'pir.overPurchaseUnit',
          'pir.unitOfMeasurement',
          'pir.effectiveDate',
          'pir.expirationDate',
        ])
        .leftJoin('details.supplier', 'supplier')
        .addSelect(['supplier.id', 'supplier.name', 'supplier.code'])
        .leftJoin('details.materialGroup', 'materialGroup')
        .addSelect([
          'materialGroup.id',
          'materialGroup.name',
          'materialGroup.code',
        ])
        .leftJoin('details.currency', 'currencyDetail')
        .addSelect([
          'currencyDetail.id',
          'currencyDetail.name',
          'currencyDetail.currencyCode',
        ]);
    }

    if (paginationDto.crudTemplateActiveTab == 'REQUEST') {
      if (jwtPayload?.staffId) {
        queryBuilder.andWhere('po.requester = :requester', {
          requester: jwtPayload?.staffId,
        });
      } else {
        return new ResponseDto([], paginationDto.page, paginationDto.limit, 0);
      }
    }

    if (paginationDto.crudTemplateActiveTab == 'WAITING') {
      if (jwtPayload?.email) {
        queryBuilder.innerJoin(
          'po.levels',
          'levels',
          `levels.email = :email AND levels.status = :status AND ( NOT EXISTS (
          SELECT 1
          FROM "approval-level" l_prev
          WHERE l_prev.level < levels.level
          AND l_prev.status != :approvedStatus
          AND "po"."id" = "l_prev"."purchase_order_id"
        ) OR levels."level" = 1) AND "po"."id" = "levels"."purchase_order_id"`,
          {
            email: jwtPayload?.email,
            status: StatusLevel.Pending,
            approvedStatus: StatusLevel.Approved,
          },
        );
        queryBuilder.andWhere(`po.statusPo IN (:...waitingPoStatus) `, {
          email: jwtPayload?.email,
          waitingPoStatus: [Status.InProgress, Status.WaitingProcess],
        });
      } else {
        return new ResponseDto([], paginationDto.page, paginationDto.limit, 0);
      }
    }

    if (paginationDto.crudTemplateActiveTab == 'APPROVED') {
      if (jwtPayload?.email) {
        queryBuilder.innerJoin(
          'po.levels',
          'levels',
          'levels.email = :email AND levels.status IN (:...status) AND "po"."id" = "levels"."purchase_order_id"',
          {
            email: jwtPayload?.email,
            status: [StatusLevel.Approved, StatusLevel.Rejected],
          },
        );
      } else {
        return new ResponseDto([], paginationDto.page, paginationDto.limit, 0);
      }
    }

    if (paginationDto.searchString) {
      queryBuilder.andWhere(`CAST(po.id AS TEXT) ILIKE :searchString`, {
        searchString: paginationDto.searchString,
      });

      paginationDto.searchString = null;
    }
    if (
      paginationDto.crudTemplateActiveTab != 'REQUEST' &&
      paginationDto.crudTemplateActiveTab != 'WAITING' &&
      paginationDto.crudTemplateActiveTab != 'APPROVED'
    ) {
      if (!jwtPayload?.isSuperAdmin) {
        if (
          !parseScopes(jwtPayload?.scopes, [
            ESectorPermission.CREATE,
            ESectorPermission.EDIT,
          ]) &&
          jwtPayload?.sectors?.length
        ) {
          queryBuilder.andWhere(
            '(sector.id IS NULL OR sector.code IN (:...sectors))',
            {
              sectors: jwtPayload.sectors,
            },
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EBusinessUnitPermission.CREATE,
            EBusinessUnitPermission.EDIT,
          ]) &&
          jwtPayload?.businessUnits?.length
        ) {
          queryBuilder.andWhere(
            '(businessUnit.id IS NULL OR businessUnit.code IN (:...businessUnits))',
            {
              businessUnits: jwtPayload.businessUnits,
            },
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EFunctionUnitPermission.CREATE,
            EFunctionUnitPermission.EDIT,
          ]) &&
          jwtPayload?.functionUnits?.length
        ) {
          queryBuilder.andWhere(
            '(functionUnit.id IS NULL OR functionUnit.code IN (:...functionUnits))',
            {
              functionUnits: jwtPayload.functionUnits,
            },
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EDepartmentPermission.CREATE,
            EDepartmentPermission.EDIT,
          ]) &&
          jwtPayload?.departments?.length
        ) {
          queryBuilder.andWhere(
            '(department.id IS NULL OR department.code IN (:...departments))',
            {
              departments: jwtPayload.departments,
            },
          );
        }
      }
    }

    queryBuilder.orderBy('po.id', 'DESC');

    return await this.pagination(queryBuilder, paginationDto);
  }

  async numberPoCreated(
    id: number,
    poId?: number,
  ): Promise<PurchaseOrderDetailEntity[]> {
    const repository = this.getRepository(PurchaseOrderDetailEntity);
    const queryBuilder = repository
      .createQueryBuilder('poDetails')
      .innerJoin('poDetails.purchaseOrder', 'purchaseOrder')
      .addSelect(['purchaseOrder.id', 'purchaseOrder.status_po']);

    queryBuilder.andWhere(
      'poDetails.prDetailId = :id AND purchaseOrder.statusPo NOT IN (:...status)',
      {
        id,
        status: [Status.Rejected, Status.Cancel],
      },
    );

    if (poId) {
      queryBuilder.andWhere('purchaseOrder.id != :poId', { poId });
    }

    return await queryBuilder.getMany();
  }

  async findOne(id: number): Promise<PurchaseOrderModel> {
    const repository = this.getRepository(PurchaseOrderEntity);
    const repositoryHistory = this.getRepository(HistoryApproveEntity);

    const queryBuilder = repository.createQueryBuilder('po');
    queryBuilder.andWhere('po.id = :id', { id });

    queryBuilder.leftJoinAndSelect('po.details', 'details');
    queryBuilder.leftJoinAndSelect('po.history', 'history');
    queryBuilder.leftJoinAndSelect('po.levels', 'levels');
    queryBuilder.leftJoinAndSelect('po.sapPos', 'sapPos');
    queryBuilder.leftJoinAndSelect('sapPos.items', 'items');
    queryBuilder.leftJoinAndSelect('po.solomonPo', 'solomonPo');
    queryBuilder.leftJoinAndSelect('solomonPo.items', 'solomonPoItems');
    queryBuilder
      .leftJoin('details.sapPurchaseOrderItems', 'sapPurchaseOrderItems')
      .addSelect([
        'sapPurchaseOrderItems.id',
        'sapPurchaseOrderItems.messageType',
        'sapPurchaseOrderItems.message',
      ]);

    queryBuilder
      .leftJoin('sapPurchaseOrderItems.sapPurchaseOrder', 'sapPurchaseOrder')
      .addSelect([
        'sapPurchaseOrder.id',
        'sapPurchaseOrder.messageType',
        'sapPurchaseOrder.message',
      ]);

    /// JOIN FOR PO
    queryBuilder
      .leftJoin('po.sector', 'sector')
      .addSelect(['sector.id', 'sector.name', 'sector.code'])
      .leftJoin('po.businessUnit', 'businessUnit')
      .addSelect(['businessUnit.id', 'businessUnit.name', 'businessUnit.code'])
      .leftJoin('businessUnit.company', 'company')
      .addSelect(['company.id', 'company.name', 'company.code'])
      .leftJoin('po.requester', 'requester')
      .addSelect([
        'requester.id',
        'requester.firstName',
        'requester.lastName',
        'requester.email',
        'requester.code',
      ])
      .leftJoin('po.typePo', 'typePo')
      .addSelect(['typePo.id', 'typePo.name', 'typePo.code', 'typePo.status'])
      .leftJoin('po.budgetCode', 'budgetCode')
      .addSelect(['budgetCode.id', 'budgetCode.name', 'budgetCode.code'])
      .leftJoin('po.costCenter', 'costCenter')
      .addSelect(['costCenter.id', 'costCenter.name', 'costCenter.code'])
      .leftJoin('po.purchaseOrg', 'purchaseOrg')
      .addSelect(['purchaseOrg.id', 'purchaseOrg.name', 'purchaseOrg.code'])
      .leftJoin('po.purchaseGroup', 'purchaseGroup')
      .addSelect([
        'purchaseGroup.id',
        'purchaseGroup.name',
        'purchaseGroup.code',
      ])
      .leftJoin('po.currency', 'currency')
      .addSelect(['currency.id', 'currency.name', 'currency.currencyCode'])
      .leftJoin('po.processType', 'processType')
      .addSelect(['processType.id', 'processType.name', 'processType.code'])
      .leftJoin('po.plant', 'plant')
      .addSelect(['plant.id', 'plant.name', 'plant.code'])
      .leftJoin('po.functionUnit', 'functionUnit')
      .addSelect(['functionUnit.id', 'functionUnit.name', 'functionUnit.code'])
      .leftJoin('po.department', 'department')
      .addSelect(['department.id', 'department.name', 'department.code']);

    /// JOIN FOR DETAILS
    queryBuilder
      .leftJoin('details.budgetCode', 'budgetCodeDetail')
      .addSelect([
        'budgetCodeDetail.id',
        'budgetCodeDetail.name',
        'budgetCodeDetail.code',
        'budgetCodeDetail.budgetType',
      ])
      .leftJoin('details.material', 'material')
      .addSelect(['material.id', 'material.name', 'material.code'])
      .leftJoin('details.costCenter', 'costCenterDetail')
      .addSelect([
        'costCenterDetail.id',
        'costCenterDetail.name',
        'costCenterDetail.code',
      ])
      .leftJoin('details.pir', 'pir')
      .addSelect([
        'pir.id',
        'pir.regularPurchaseQuantity',
        'pir.minimumOrderQuantity',
        'pir.upperTolerance',
        'pir.lowerTolerance',
        'pir.purchasePrice',
        'pir.overPurchaseUnit',
        'pir.unitOfMeasurement',
        'pir.effectiveDate',
        'pir.expirationDate',
      ])
      .leftJoin('details.supplier', 'supplier')
      .addSelect([
        'supplier.id',
        'supplier.name',
        'supplier.code',
        'supplier.type',
      ])
      .leftJoin('details.materialGroup', 'materialGroup')
      .addSelect([
        'materialGroup.id',
        'materialGroup.name',
        'materialGroup.code',
      ])
      .leftJoin('details.currency', 'currencyDetail')
      .addSelect([
        'currencyDetail.id',
        'currencyDetail.name',
        'currencyDetail.currencyCode',
      ])
      .leftJoin('details.measure', 'measure')
      .addSelect([
        'measure.id',
        'measure.name',
        'measure.code',
        'measure.description',
        'measure.codeConversions',
      ])
      .leftJoin('details.warehouse', 'warehouse')
      .addSelect([
        'warehouse.id',
        'warehouse.name',
        'warehouse.code',
        'warehouse.description',
      ])
      .leftJoin('details.taxCode', 'taxCode')
      .addSelect([
        'taxCode.id',
        'taxCode.code',
        'taxCode.description',
        'taxCode.taxRate',
      ]);

    const po = await queryBuilder.getOne();

    const allAdditionalHistory = new Map<number, HistoryApproveEntity>();

    if (po && po?.details?.length > 0) {
      const prReferenceIds = [
        ...new Set(
          (po?.details || []).map((item) => item.prReference).filter(Boolean),
        ),
      ];
      if (prReferenceIds && prReferenceIds?.length) {
        const additionalHistory = await repositoryHistory.find({
          where: { purchaseRequest: { id: In(prReferenceIds) } },
        });

        additionalHistory.forEach((record) => {
          allAdditionalHistory.set(record.id, record);
        });
      }
    }

    po.historyPr = Array.from(allAdditionalHistory.values());

    return po;
  }

  async findOneStatusProcess(status: Status): Promise<PurchaseOrderEntity[]> {
    const repository = this.getRepository(PurchaseOrderEntity);
    return await repository.find({
      where: { statusPo: status },
    });
  }

  async updateStatus(
    id: number,
    status: Status,
    displayStatusPo: EDisplayStatus,
  ): Promise<any> {
    const repository = this.getRepository(PurchaseOrderEntity);
    return await repository.update(id, {
      statusPo: status,
      displayStatusPo: displayStatusPo,
    });
  }

  async updateState(id: number, state: State): Promise<any> {
    const repository = this.getRepository(PurchaseOrderEntity);
    return await repository.update(id, { statePo: state });
  }

  async createPurchaseOrder(
    data: PurchaseOrderDto,
  ): Promise<PurchaseOrderModel> {
    const repository = this.getRepository(PurchaseOrderEntity);
    const repositoryOrderDetail = this.getRepository(PurchaseOrderDetailEntity);
    const repositoryHistory = this.getRepository(HistoryApproveEntity);
    data.details.forEach((detail) => {
      if (!detail.deliveryTime) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_5211()),
          HttpStatus.BAD_REQUEST,
        );
      }

      detail.deliveryTime = new Date(convertToGMT7(detail.deliveryTime));
    });

    const purchaseOrder = repository.create({
      ...data,
      details: data.details.map((detail) =>
        repositoryOrderDetail.create(detail),
      ),
      history:
        data?.history?.map((history) => repositoryHistory.create(history)) ||
        [],
    });
    return await repository.save(purchaseOrder);
  }

  async updatePurchaseOrder(
    id: number,
    updatePurchaseOrder: UpdatePurchaseOrderDto,
  ): Promise<PurchaseOrderEntity> {
    const repository = this.getRepository(PurchaseOrderEntity);
    const repositoryOrderDetail = this.getRepository(PurchaseOrderDetailEntity);
    const repositoryHistory = this.getRepository(HistoryApproveEntity);
    const purchaseOrder = await repository.findOne({
      where: { id },
      relations: ['details'],
    });

    if (!purchaseOrder) {
      return null;
    }

    const poDetailIds = (purchaseOrder.details || []).map(
      (detail) => detail.id,
    );

    Object.assign(purchaseOrder, updatePurchaseOrder);

    if (updatePurchaseOrder.details) {
      await repositoryOrderDetail.softDelete({
        id: In(poDetailIds),
      });

      purchaseOrder.details = updatePurchaseOrder.details.map((detail) =>
        repositoryOrderDetail.create({
          ...detail,
          deliveryTime: new Date(convertToGMT7(detail.deliveryTime)),
        }),
      );
    }

    if (updatePurchaseOrder.history) {
      purchaseOrder.history =
        updatePurchaseOrder.history?.map((history) =>
          repositoryHistory.create(history),
        ) || [];
    }

    return repository.save(purchaseOrder);
  }

  async findPOWithBudget(
    conditions: GetPOWithBudgetDto,
  ): Promise<PurchaseOrderEntity[]> {
    const repository = this.getRepository(PurchaseOrderEntity);
    const queryBuilder = repository.createQueryBuilder('po');

    queryBuilder.andWhere('po.budgetCodeId IN (:...budgetCodeIds)', {
      budgetCodeIds: conditions.budgetCodeIds,
    });

    return await queryBuilder
      .orderBy('po.created_at', 'DESC')
      .limit(1)
      .getMany();
  }

  async materialPurchaseOrder(
    paginationDto: GetPurchaseOrderDto,
  ): Promise<any> {
    const repository = this.getRepository(PurchaseOrderEntity);
    const { page, limit } = paginationDto;
    const skip = (page - 1) * limit;

    // Query các PR đã được duyệt
    const queryBuilder = repository.createQueryBuilder('po');

    // Lọc PR theo status đã được duyệt
    queryBuilder.andWhere('po.statusPo = :status', {
      status: Status.Approved,
    });

    queryBuilder.leftJoinAndSelect('po.details', 'details');

    if (paginationDto.materialCodeIds) {
      queryBuilder.andWhere('details.materialCodeId IN (:...materialCodeIds)', {
        materialCodeIds: paginationDto.materialCodeIds,
      });
    }

    // Điều kiện lọc theo material_name nếu có
    if (paginationDto.materialNames) {
      queryBuilder.andWhere('details.materialName IN (:...materialNames)', {
        materialNames: paginationDto.materialNames,
      });
    }

    queryBuilder.skip(skip).take(limit);

    // Lấy danh sách các PR
    return await queryBuilder.orderBy('po.created_at', 'DESC').getMany();
  }

  async getPoDetailsForRemainingBudget(
    budgetCodeId: string,
    effectiveStartDate: Date,
    effectiveEndDate: Date,
  ): Promise<PurchaseOrderDetailEntity[]> {
    const repository = this.getRepository(PurchaseOrderDetailEntity);
    const queryBuilder = repository.createQueryBuilder('poDetails');

    queryBuilder.innerJoinAndSelect('poDetails.purchaseOrder', 'po');

    queryBuilder.innerJoinAndSelect('po.levels', 'levels');

    // [Tổng số tiền các item (tương ứng) trong PO đã được cấp Kế toán duyệt và trạng thái khác Hủy, khác Từ chối]
    queryBuilder.andWhere(
      '((po.statusPo NOT IN (:...status) AND po.isAccountantApproved = :isAccountantApproved) OR po.statusPo = :statusApproved)',
      {
        status: [Status.Cancel, Status.Rejected],
        isAccountantApproved: true,
        statusApproved: Status.Approved,
      },
    );

    queryBuilder.andWhere('poDetails.budgetCodeId = :budgetCodeId', {
      budgetCodeId: budgetCodeId,
    });

    queryBuilder.andWhere(
      'poDetails.createdAt BETWEEN :startDate AND :endDate',
      { startDate: effectiveStartDate, endDate: effectiveEndDate },
    );

    return await queryBuilder.getMany();
  }

  async findPoWithIds(ids: number[]): Promise<PurchaseOrderEntity[]> {
    if (ids && ids.length) {
      const repository = this.getRepository(PurchaseOrderEntity);
      const purchaseOrders = await repository.find({
        where: { id: In(ids) },
        relations: ['details'],
      });

      return purchaseOrders;
    } else {
      return [];
    }
  }

  async findPoReportBudget(
    conditions: GetPoDetailReportBudgetDetailDto,
    authorization,
  ): Promise<PurchaseOrderDetailEntity[]> {
    const repository = this.getRepository(PurchaseOrderDetailEntity);
    const queryBuilder = repository
      .createQueryBuilder('poDetails')
      .select([
        'poDetails.id',
        'poDetails.budgetCodeId',
        'poDetails.costCenterId',
        'poDetails.totalAmountVat',
        'poDetails.totalAmount',
        'poDetails.vat',
        'poDetails.unitPrice',
        'poDetails.quantity',
      ]);

    queryBuilder
      .innerJoin('poDetails.purchaseOrder', 'purchaseOrder')
      .addSelect([
        'purchaseOrder.id',
        'purchaseOrder.statusPo',
        'purchaseOrder.isAccountantApproved',
        'purchaseOrder.createdAt',
        'purchaseOrder.currency',
      ])
      .innerJoin('purchaseOrder.history', 'history')
      .addSelect([
        'history.id',
        'history.level',
        'history.status',
        'history.updatedAt',
      ]);

    queryBuilder.andWhere('(purchaseOrder.statusPo IN (:...status))', {
      // isAccountantApproved: conditions.isAccountantApproved === 'true',
      status: conditions.statusPo,
    });

    queryBuilder.andWhere('poDetails.budgetCodeId IN (:...budgetCodeIds)', {
      budgetCodeIds: conditions.budgetCodeIds,
    });
    queryBuilder.andWhere('poDetails.costCenterId IN (:...costCenterIds)', {
      costCenterIds: conditions.costCenterIds,
    });
    queryBuilder.andWhere(
      'purchaseOrder.createdAt BETWEEN :startDate AND :endDate',
      {
        startDate: conditions.effectiveStart,
        endDate: conditions.effectiveEnd,
      },
    );
    return await queryBuilder.getMany();
  }

  async findAdjustBudgetIdInPoDetail(
    adjustBudgetIds: string[],
  ): Promise<PurchaseOrderDetailEntity[]> {
    const repository = this.getRepository(PurchaseOrderDetailEntity);
    const queryBuilder = repository
      .createQueryBuilder('poDetails')
      .select(['poDetails.id', 'poDetails.adjustBudgetId']);
    queryBuilder.innerJoin('poDetails.purchaseOrder', 'purchaseOrder');

    queryBuilder.andWhere('poDetails.adjustBudgetId IN (:...adjustBudgetIds)', {
      adjustBudgetIds: adjustBudgetIds,
    });
    return await queryBuilder.getMany();
  }

  async numberPoCreatedByPrDetailIds(
    ids: number[],
    poId?: number,
  ): Promise<PurchaseOrderDetailEntity[]> {
    const repository = this.getRepository(PurchaseOrderDetailEntity);
    const queryBuilder = repository
      .createQueryBuilder('poDetails')
      .innerJoin('poDetails.purchaseOrder', 'purchaseOrder')
      .addSelect(['purchaseOrder.id', 'purchaseOrder.status_po']);
    queryBuilder.andWhere(
      'poDetails.prDetailId IN (:...ids) AND purchaseOrder.statusPo NOT IN (:...status)',
      {
        ids: ids?.length ? ids : [null],
        status: [Status.Rejected, Status.Cancel],
      },
    );
    if (poId) {
      queryBuilder.andWhere('purchaseOrder.id != :poId', { poId });
    }
    return await queryBuilder.getMany();
  }

  async findOneById(id: number): Promise<PurchaseOrderModel> {
    const repository = this.getRepository(PurchaseOrderEntity);

    const queryBuilder = repository.createQueryBuilder('po');
    queryBuilder.andWhere('po.id = :id', { id });

    return await queryBuilder.getOne();
  }

  async getPosForResendEmail(ids: number[]): Promise<PurchaseOrderModel[]> {
    const repository = this.getRepository(PurchaseOrderEntity);

    const queryBuilder = repository.createQueryBuilder('pos');

    queryBuilder.innerJoinAndSelect('pos.details', 'details');
    queryBuilder.leftJoinAndSelect('pos.history', 'history');
    queryBuilder.innerJoinAndSelect('pos.levels', 'levels');
    queryBuilder.leftJoinAndSelect('pos.sapPos', 'sapPos');
    queryBuilder.leftJoinAndSelect('sapPos.items', 'items');
    queryBuilder
      .leftJoin('details.sapPurchaseOrderItems', 'sapPurchaseOrderItems')
      .addSelect([
        'sapPurchaseOrderItems.id',
        'sapPurchaseOrderItems.messageType',
        'sapPurchaseOrderItems.message',
      ]);

    queryBuilder
      .leftJoin('sapPurchaseOrderItems.sapPurchaseOrder', 'sapPurchaseOrder')
      .addSelect([
        'sapPurchaseOrder.id',
        'sapPurchaseOrder.messageType',
        'sapPurchaseOrder.message',
      ]);

    /// JOIN FOR PO
    queryBuilder
      .leftJoin('pos.sector', 'sector')
      .addSelect(['sector.id', 'sector.name', 'sector.code'])
      .leftJoin('pos.businessUnit', 'businessUnit')
      .addSelect(['businessUnit.id', 'businessUnit.name', 'businessUnit.code'])
      .leftJoin('pos.requester', 'requester')
      .addSelect([
        'requester.id',
        'requester.firstName',
        'requester.lastName',
        'requester.email',
        'requester.code',
      ])
      .leftJoin('pos.typePo', 'typePo')
      .addSelect(['typePo.id', 'typePo.name', 'typePo.code', 'typePo.status'])
      .leftJoin('pos.budgetCode', 'budgetCode')
      .addSelect(['budgetCode.id', 'budgetCode.name', 'budgetCode.code'])
      .leftJoin('pos.costCenter', 'costCenter')
      .addSelect(['costCenter.id', 'costCenter.name', 'costCenter.code'])
      .leftJoin('pos.purchaseOrg', 'purchaseOrg')
      .addSelect(['purchaseOrg.id', 'purchaseOrg.name', 'purchaseOrg.code'])
      .leftJoin('pos.purchaseGroup', 'purchaseGroup')
      .addSelect([
        'purchaseGroup.id',
        'purchaseGroup.name',
        'purchaseGroup.code',
      ])
      .leftJoin('pos.currency', 'currency')
      .addSelect(['currency.id', 'currency.name', 'currency.currencyCode'])
      .leftJoin('pos.processType', 'processType')
      .addSelect(['processType.id', 'processType.name', 'processType.code'])
      .leftJoin('pos.plant', 'plant')
      .addSelect(['plant.id', 'plant.name', 'plant.code'])
      .leftJoin('pos.functionUnit', 'functionUnit')
      .addSelect(['functionUnit.id', 'functionUnit.name', 'functionUnit.code'])
      .leftJoin('pos.department', 'department')
      .addSelect(['department.id', 'department.name', 'department.code']);

    /// JOIN FOR DETAILS
    queryBuilder
      .leftJoin('details.budgetCode', 'budgetCodeDetail')
      .addSelect([
        'budgetCodeDetail.id',
        'budgetCodeDetail.name',
        'budgetCodeDetail.code',
        'budgetCodeDetail.budgetType',
      ])
      .leftJoin('details.material', 'material')
      .addSelect(['material.id', 'material.name', 'material.code'])
      .leftJoin('details.costCenter', 'costCenterDetail')
      .addSelect([
        'costCenterDetail.id',
        'costCenterDetail.name',
        'costCenterDetail.code',
      ])
      .leftJoin('details.pir', 'pir')
      .addSelect([
        'pir.id',
        'pir.regularPurchaseQuantity',
        'pir.minimumOrderQuantity',
        'pir.upperTolerance',
        'pir.lowerTolerance',
        'pir.purchasePrice',
        'pir.overPurchaseUnit',
        'pir.unitOfMeasurement',
        'pir.effectiveDate',
        'pir.expirationDate',
      ])
      .leftJoin('details.supplier', 'supplier')
      .addSelect(['supplier.id', 'supplier.name', 'supplier.code'])
      .leftJoin('details.materialGroup', 'materialGroup')
      .addSelect([
        'materialGroup.id',
        'materialGroup.name',
        'materialGroup.code',
      ])
      .leftJoin('details.currency', 'currencyDetail')
      .addSelect([
        'currencyDetail.id',
        'currencyDetail.name',
        'currencyDetail.currencyCode',
      ])
      .leftJoin('details.measure', 'measure')
      .addSelect([
        'measure.id',
        'measure.name',
        'measure.code',
        'measure.description',
      ])
      .leftJoin('details.warehouse', 'warehouse')
      .addSelect([
        'warehouse.id',
        'warehouse.name',
        'warehouse.code',
        'warehouse.description',
      ])
      .leftJoin('details.taxCode', 'taxCode')
      .addSelect([
        'taxCode.id',
        'taxCode.code',
        'taxCode.description',
        'taxCode.taxRate',
      ]);

    queryBuilder.andWhere('pos.id IN (:...ids)', {
      ids: [...new Set(ids)],
    });

    queryBuilder.andWhere('levels.status = :status', {
      status: StatusLevel.Pending,
    });

    return await queryBuilder.getMany();
  }
}
