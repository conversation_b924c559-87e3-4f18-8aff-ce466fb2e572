import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { CreateReceiptSolomonDto } from '../../controller/receipt-solomon/dtos/create-receipt-solomon.dto';
import { ReceiptSolomonModel } from '../../domain/model/receipt-solomon.model';
import { IReceiptSolomonRepository } from '../../domain/repositories/receipt-solomon.repository';
import { ReceiptSolomonEntity } from '../entities/receipt-solomon.entity';
import { BaseRepository } from './base.repository';
import { GetListReceiptDto } from '../../controller/receipt-solomon/dtos/get-list-receipt.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';

@Injectable({ scope: Scope.REQUEST })
export class ReceiptSolomonRepository
  extends BaseRepository
  implements IReceiptSolomonRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createReceipt(
    data: CreateReceiptSolomonDto[],
  ): Promise<ReceiptSolomonModel[]> {
    const repository = this.getRepository(ReceiptSolomonEntity);
    const receipts = repository.create(
      data.map((item) => {
        return {
          ...item,
          budgetCode: undefined,
        };
      }),
    );

    return await repository.save(receipts);
  }

  async getReceipts(
    param: GetListReceiptDto,
  ): Promise<ResponseDto<ReceiptSolomonModel>> {
    const repository = this.getRepository(ReceiptSolomonEntity);
    const queryBuilder = repository.createQueryBuilder('receipts');

    queryBuilder
      .leftJoin('receipts.purchaseOrder', 'purchaseOrder')
      .addSelect(['purchaseOrder.id']);

    queryBuilder
      .leftJoin('receipts.budgetCode', 'budgetCode')
      .addSelect([
        'budgetCode.id',
        'budgetCode.code',
        'budgetCode.name',
        'budgetCode.budgetType',
      ]);

    queryBuilder.orderBy('receipts.createdAt', 'DESC');

    if (param.budgetCodeIds?.length) {
      queryBuilder.andWhere('receipts.budgetCodeId IN (:...budgetCodeIds)', {
        budgetCodeIds: param.budgetCodeIds,
      });
    }

    if (param.poIds?.length) {
      queryBuilder.andWhere('receipts.poId IN (:...poIds)', {
        poIds: param.poIds,
      });
    }

    return await this.pagination(queryBuilder, param);
  }

  async getDetailReceipt(param: GetUuidDto): Promise<ReceiptSolomonModel> {
    const repository = this.getRepository(ReceiptSolomonEntity);
    const queryBuilder = repository.createQueryBuilder('receipts');

    queryBuilder.where('receipts.id = :id', { id: param.id });

    queryBuilder
      .leftJoin('receipts.purchaseOrder', 'purchaseOrder')
      .addSelect(['purchaseOrder.id']);

    queryBuilder
      .leftJoin('receipts.budgetCode', 'budgetCode')
      .addSelect([
        'budgetCode.id',
        'budgetCode.code',
        'budgetCode.name',
        'budgetCode.budgetType',
      ]);

    return await queryBuilder.getOne();
  }
}
