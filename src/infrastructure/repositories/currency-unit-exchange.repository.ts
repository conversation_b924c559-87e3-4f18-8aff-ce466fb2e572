import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { ICurrencyUnitExchangeRepository } from '../../domain/repositories/currency-unit-exchange.repository';
import { DataSource, In, Not } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { REQUEST } from '@nestjs/core';
import { CurrencyUnitExchangeModel } from '../../domain/model/currency-unit-exchange.model';
import { CurrencyUnitExchangeEntity } from '../entities/currency-unit-exchange.entity';

@Injectable({ scope: Scope.REQUEST })
export class CurrencyUnitExchangeRepository
  extends BaseRepository
  implements ICurrencyUnitExchangeRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createManyCurrencyUnitExchange(
    data: CurrencyUnitExchangeModel[],
  ): Promise<CurrencyUnitExchangeModel[]> {
    const repository = this.getRepository(CurrencyUnitExchangeEntity);
    const newCurrencyExchanges = repository.create(data);

    return await repository.save(newCurrencyExchanges);
  }

  async updateManyCurrencyUnitExchange(
    data: CurrencyUnitExchangeModel[],
  ): Promise<CurrencyUnitExchangeModel[]> {
    const repository = this.getRepository(CurrencyUnitExchangeEntity);
    const updateCurrencyUnitExchanges = repository.create(data);
    return await repository.save(updateCurrencyUnitExchanges);
  }

  async deleteCurrencyUnitExchangesNotIn(
    ids: string[],
    currencyId: string,
  ): Promise<void> {
    const repository = this.getRepository(CurrencyUnitExchangeEntity);
    await repository.delete({
      id: Not(In(ids)),
      currencyUnitId: currencyId,
    });
  }
}
