import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, FindOneOptions } from 'typeorm';
import {
  ApprovalLevelEntity,
  ApproveType,
} from '../entities/approval-level.entity';
import { HistoryApproveEntity } from '../entities/history-approve.entity';
import { BaseRepository } from './base.repository';
import { IPrApprovalFlowRepository } from '../../domain/repositories/prApprovalFlowRepository.repository';
import { ApprovalLevelModel } from '../../domain/model/approval-level.model';
import {
  ApprovalLevelDto,
  ConditionDto,
  StatusLevel,
} from '../../controller/approve/dtos/approve.dto';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class prApprovalRepository
  extends BaseRepository
  implements IPrApprovalFlowRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async findOne(id: number): Promise<ApprovalLevelModel> {
    const repository = this.getRepository(ApprovalLevelEntity);
    return await repository.findOne({ where: { id } });
    // const approvalFlow = await this.prApprovalFlowRepository.findOne({
    //   where: { id }, // Use an object with `where` clause to find by ID
    //   relations: ['conditions', 'levels'], // Specify relations to be loaded
    // });

    // console.log(42, approvalFlow);

    // if (!approvalFlow) {
    //   throw new Error(`ApprovalFlow with id ${id} not found`);
    // }

    // // const rootNode = this.buildTree(approvalFlow.conditions);

    // return {
    //   ...approvalFlow,
    //   // tree: rootNode,
    // };
  }

  async findOneByLevel(
    purchaseId: number,
    level: number,
    approveType: ApproveType,
  ): Promise<any> {
    const repository = this.getRepository(ApprovalLevelEntity);
    const findOneOptions: FindOneOptions<ApprovalLevelEntity> = {
      where: { level: level, approveType: approveType },
    };

    if (approveType === ApproveType.PO) {
      findOneOptions.where['purchaseOrderId'] = purchaseId;
    } else if (approveType === ApproveType.PR) {
      findOneOptions.where['purchaseRequestId'] = purchaseId;
    }
    return await repository.findOne(findOneOptions);
  }

  // async findActive(): Promise<ApprovalFlowModel[]> {
  //   const repository = this.getRepository(ApprovalFlowEntity);
  //   const queryBuilder = repository.createQueryBuilder('flows');

  //   return await queryBuilder
  //     .leftJoinAndSelect('flows.conditions', 'conditions')
  //     .leftJoinAndSelect('flows.levels', 'levels')
  //     .where(`flows.is_active = :is_active and levels.approve_type = 'PO'`, {
  //       is_active: true,
  //     })
  //     .orderBy('flows.created_at', 'DESC')
  //     .getMany();
  // }

  // async findPrActive(): Promise<ApprovalFlowEntity[]> {
  //   const repository = this.getRepository(ApprovalFlowEntity);
  //   const queryBuilder = repository.createQueryBuilder('flows');

  //   return await queryBuilder
  //     .leftJoinAndSelect('flows.conditions', 'conditions')
  //     .leftJoinAndSelect('flows.levels', 'levels')
  //     .where(`flows.is_active = :is_active and levels.approve_type = 'PR'`, {
  //       is_active: true,
  //     })
  //     .orderBy('flows.created_at', 'DESC')
  //     .getMany();
  // }

  // async create(
  //   rootNode: ApprovalNode,
  //   approval: ApproveDto,
  //   jwtPayload,
  // ): Promise<any> {
  //   console.log(74, rootNode);
  //   const approvalFlow = new ApprovalFlowEntity();
  //   approvalFlow.name = approval.name;
  //   approvalFlow.submitted_person_name = approval.submitted_person_name;
  //   approvalFlow.notified_person = approval.notified_person;
  //   approvalFlow.is_active = approval.is_active ? approval.is_active : false;
  //   approvalFlow.isSendMail = approval.isSendMail
  //     ? approval.isSendMail
  //     : false;
  //   approvalFlow.comment = approval.comment;
  //   approvalFlow.forward = approval.forward;
  //   approvalFlow.rollback = approval.rollback;
  //   approvalFlow.conditions = this.flattenConditions(rootNode);
  //   approvalFlow.levels = approval.levels.map((level) => {
  //     const lvl = new ApprovaLevelEntity();
  //     lvl.level = level.level;
  //     lvl.role = level.role;
  //     if (jwtPayload?.userInfo?.userName)
  //       lvl.user_id = jwtPayload?.userInfo?.id;
  //     lvl.isSendMail = level.isSendMail;
  //     return lvl;
  //   });
  //   const repository = this.getRepository(ApprovalFlowEntity);
  //   return await repository.save(approvalFlow);
  // }

  // async update(
  //   id: number,
  //   rootNode: ApprovalNode,
  //   approval: ApproveDto,
  //   jwtPayload,
  // ): Promise<any> {
  //   console.log(80, rootNode);
  //   console.log(81, approval);
  //   const repository = this.getRepository(ApprovalFlowEntity);
  //   const approvalFlow = await repository.findOne({
  //     where: { id },
  //   });
  //   if (!approvalFlow) {
  //     throw new Error('Approval flow not found');
  //   }

  //   approvalFlow.name = approval.name;
  //   approvalFlow.submitted_person_name = approval.submitted_person_name;
  //   approvalFlow.notified_person = approval.notified_person;
  //   approvalFlow.is_active = approval.is_active ? approval.is_active : false;
  //   approvalFlow.isSendMail = approval.isSendMail
  //     ? approval.isSendMail
  //     : false;
  //   approvalFlow.comment = approval.comment;
  //   approvalFlow.forward = approval.forward;
  //   approvalFlow.rollback = approval.rollback;
  //   approvalFlow.conditions = this.flattenConditions(rootNode);
  //   approvalFlow.levels = approval.levels.map((level) => {
  //     const lvl = new ApprovaLevelEntity();
  //     lvl.level = level.level;
  //     lvl.role = level.role;
  //     if (jwtPayload?.userInfo?.userName)
  //       lvl.user_id = jwtPayload?.userInfo?.id;
  //     lvl.isSendMail = level.isSendMail;
  //     return lvl;
  //   });

  //   return await repository.save(approvalFlow);
  // }

  // async checkConditions(
  //   conditions: ConditionDto[],
  //   prData: any,
  // ): Promise<boolean> {
  //   if (!Array.isArray(conditions)) {
  //     throw new TypeError('conditions must be an array');
  //   }

  //   for (const condition of conditions) {
  //     const prValue = prData[condition.field];
  //     console.log(`Checking condition: ${JSON.stringify(condition)}`);
  //     console.log(`PR value: ${prValue}`);

  //     switch (condition.comparisonType) {
  //       case 'Equal':
  //         if (prValue !== condition.value) {
  //           console.log(
  //             `Condition failed: ${condition.field} (${prValue}) is not equal to ${condition.value}`,
  //           );
  //           return false;
  //         }
  //         break;
  //       case 'GreaterThan':
  //         if (!(prValue > condition.value)) {
  //           console.log(
  //             `Condition failed: ${condition.field} (${prValue}) is not greater than ${condition.value}`,
  //           );
  //           return false;
  //         }
  //         break;
  //       case 'GreaterThanOrEqual':
  //         if (!(prValue >= condition.value)) {
  //           console.log(
  //             `Condition failed: ${condition.field} (${prValue}) is not greater than or equal to ${condition.value}`,
  //           );
  //           return false;
  //         }
  //         break;
  //       case 'LessThan':
  //         if (!(prValue < condition.value)) {
  //           console.log(
  //             `Condition failed: ${condition.field} (${prValue}) is not less than ${condition.value}`,
  //           );
  //           return false;
  //         }
  //         break;
  //       case 'LessThanOrEqual':
  //         if (!(prValue <= condition.value)) {
  //           console.log(
  //             `Condition failed: ${condition.field} (${prValue}) is not less than or equal to ${condition.value}`,
  //           );
  //           return false;
  //         }
  //         break;
  //       case 'NotEqual':
  //         if (prValue === condition.value) {
  //           console.log(
  //             `Condition failed: ${condition.field} (${prValue}) is equal to ${condition.value}`,
  //           );
  //           return false;
  //         }
  //         break;
  //       case 'Includes':
  //         if (
  //           typeof prValue !== 'string' ||
  //           !prValue.includes(condition.value)
  //         ) {
  //           console.log(
  //             `Condition failed: ${condition.field} (${prValue}) does not include ${condition.value}`,
  //           );
  //           return false;
  //         }
  //         break;
  //       case 'NotIncludes':
  //         if (
  //           typeof prValue === 'string' &&
  //           prValue.includes(condition.value)
  //         ) {
  //           console.log(
  //             `Condition failed: ${condition.field} (${prValue}) includes ${condition.value}`,
  //           );
  //           return false;
  //         }
  //         break;
  //       default:
  //         throw new Error(
  //           `Unknown comparison type: ${condition.comparisonType}`,
  //         );
  //     }
  //   }
  //   return true;
  // }

  // private flattenConditions(
  //   node: ApprovalNode,
  //   parentId: number | null = null,
  // ): ConditionFlowEntity[] {
  //   console.log(
  //     `Flattening conditions for node with id: ${node.id} and parentId: ${parentId}`,
  //   );

  //   console.log(195, node);
  //   let conditions = node.conditions.map((condition) => {
  //     const cond = new ConditionFlowEntity();
  //     cond.field = condition.field;
  //     cond.comparison_type = condition.comparison_type;
  //     cond.value = condition.value;
  //     cond.parent_id = parentId;
  //     return cond;
  //   });

  //   // Recursively flatten conditions for child nodes
  //   for (const child of node.children) {
  //     conditions = conditions.concat(this.flattenConditions(child, node.id));
  //   }

  //   return conditions;
  // }

  async changeStatus(flow, id: number): Promise<any> {
    const repository = this.getRepository(ApprovalLevelEntity);
    for (const level of flow.levels) {
      await repository.update(level.id, {
        status: StatusLevel.Pending,
        purchaseRequest: { id },
      });
    }
  }

  async changeStatusPurchaseOrder(flow, id: number): Promise<any> {
    const repository = this.getRepository(ApprovalLevelEntity);
    for (const level of flow.levels) {
      await repository.update(level.id, {
        status: StatusLevel.Pending,
        purchaseOrder: { id },
      });
    }
  }

  async updateApprovalLevel(status: string, id: number): Promise<void> {
    const repository = this.getRepository(ApprovalLevelEntity);
    await repository.update(id, { status });
  }

  async changeStatusHistory(
    data: { status: string; reason: string },
    id: number,
  ): Promise<any> {
    const repository = this.getRepository(HistoryApproveEntity);
    return await repository.update(id, {
      status: data.status,
      reason: data.reason,
    });
  }

  async createApprovalLevels(
    approvalLevelDtos: ApprovalLevelDto[],
  ): Promise<any> {
    const repository = this.getRepository(ApprovalLevelEntity);

    const entities = repository.create(approvalLevelDtos);

    return await repository.save(entities);
  }

  async findApprovalLevels(
    prId: number,
    poId: number,
    greaterThanLevel?: number,
  ): Promise<ApprovalLevelEntity[]> {
    const repository = this.getRepository(ApprovalLevelEntity);
    const queryBuilder = repository.createQueryBuilder('levels');
    // .andWhere('levels.status = :status', { status: StatusLevel.Pending })
    // .orderBy('levels.created_at', 'DESC');

    if (prId) {
      queryBuilder.andWhere('levels.purchase_request_id = :prId', { prId });
    }

    if (poId) {
      queryBuilder.andWhere('levels.purchase_order_id = :poId', { poId });
    }

    if (greaterThanLevel && !isNaN(Number(greaterThanLevel))) {
      queryBuilder.andWhere('levels.level > :greaterThanLevel', {
        greaterThanLevel,
      });
    }

    return await queryBuilder.getMany();
  }

  async deleteApprovalLevels(prId: number, poId: number): Promise<void> {
    const repository = this.getRepository(ApprovalLevelEntity);
    if (prId) {
      await repository.delete({ purchaseRequestId: prId });
    }

    if (poId) {
      await repository.delete({ purchaseOrderId: poId });
    }
  }
}
