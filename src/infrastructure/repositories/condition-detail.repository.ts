import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource } from 'typeorm';

import { BaseRepository } from './base.repository';
import { CreateConditionDetailDto } from '../../controller/process/dtos/create-condition-detail.dto';
import { ConditionDetailModel } from '../../domain/model/condition-detail.model';
import { IConditionDetailRepository } from '../../domain/repositories/condition-detail.repository';
import { ConditionDetailEntity } from '../entities/condition-detail.entity';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ConditionDetailRepository
  extends BaseRepository
  implements IConditionDetailRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createConditionDetails(
    data: CreateConditionDetailDto[],
  ): Promise<ConditionDetailModel[]> {
    const repository = this.getRepository(ConditionDetailEntity);

    const conditions = repository.create(data);

    return await repository.save(conditions);
  }

  async deleteConditionDetailByConditionId(id: string): Promise<void> {
    const repository = this.getRepository(ConditionDetailEntity);
    await repository.delete(id);
  }
}
