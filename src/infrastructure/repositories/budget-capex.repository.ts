import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { BudgetCapexModel } from '../../domain/model/budget-capex.model';
import { IBudgetCapexRepository } from '../../domain/repositories/budget-capex.repository';
import { BudgetCapexEntity } from '../entities/budget-capex.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class BudgetCapexRepository
  extends BaseRepository
  implements IBudgetCapexRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createBudgetCapex(data: BudgetCapexModel): Promise<BudgetCapexModel> {
    const repository = this.getRepository(BudgetCapexEntity);
    const newBudgetCapex = repository.create(data);
    return await repository.save(newBudgetCapex);
  }

  async updateBudgetCapex(data: BudgetCapexModel): Promise<BudgetCapexModel> {
    const repository = this.getRepository(BudgetCapexEntity);
    const newBudgetCapex = repository.create(data);
    return await repository.save(newBudgetCapex);
  }
  async getBudgetCapexById(id: string): Promise<BudgetCapexModel> {
    const repository = this.getRepository(BudgetCapexEntity);
    return await repository.findOne({ where: { id: id } });
  }
}
