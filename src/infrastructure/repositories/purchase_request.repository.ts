import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
  Scope,
} from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { PurchaseRequestDto } from '../../controller/purchase-request/dtos/purchase-request.dto';
import { IPurchaseRequestRepository } from '../../domain/repositories/purchaseRequestRepository.repository';
import { DataSource, In } from 'typeorm';
import { PurchaseRequestEntity } from '../entities/purchase_request.entity';
import { PurchaseRequestDetailEntity } from '../entities/purchase_request_detail.entity';

import { REQUEST } from '@nestjs/core';
import { StatusLevel } from '../../controller/approve/dtos/approve.dto';
import { GetPurchaseRequestDto } from '../../controller/purchase-request/dtos/get-all-purchase-request.dto';
import { GetPRWithBudgetDto } from '../../controller/purchase-request/dtos/get-pr-with-budget.dto';
import { PriceMaterialDto } from '../../controller/purchase-request/dtos/price-material.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import {
  errorMessage,
  getErrorMessage,
} from '../../domain/messages/error-message';
import { HistoryApproveEntity } from '../entities/history-approve.entity';
import { BaseRepository } from './base.repository';
import { PurchaseRequestModel } from '../../domain/model/purchase_request.model';
import { PurchaseRequestDetailModel } from '../../domain/model/purchase_request_detail.model';
import { State, Status } from '../../domain/config/enums/purchase-request.enum';
import {
  EApprovedMaterialPermission,
  EBusinessUnitPermission,
  EDepartmentPermission,
  EFunctionUnitPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { parseScopes } from '../../utils/common';
import * as _ from 'lodash';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class purchaseRequestRepository
  extends BaseRepository
  implements IPurchaseRequestRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async findAll(
    paginationDto: GetPurchaseRequestDto,
    jwtPayload: any,
    isNeedDetails: boolean,
  ): Promise<ResponseDto<PurchaseRequestModel>> {
    const repository = this.getRepository(PurchaseRequestEntity);
    const queryBuilder = repository
      .createQueryBuilder('pr')
      .innerJoinAndSelect('pr.details', 'details')
      .leftJoinAndSelect('pr.history', 'history');

    if (paginationDto.departmentIds?.length) {
      queryBuilder.andWhere('pr.departmentId IN (:...departmentIds)', {
        departmentIds: paginationDto.departmentIds,
      });
    }

    if (paginationDto.functionUnitIds?.length) {
      queryBuilder.andWhere('pr.functionUnitId IN (:...functionUnitIds)', {
        functionUnitIds: paginationDto.functionUnitIds,
      });
    }

    if (paginationDto.typePrIds?.length) {
      queryBuilder.andWhere('pr.typePrId IN (:...typePrIds)', {
        typePrIds: paginationDto.typePrIds,
      });
    }

    if (paginationDto.budgetCodeIds?.length) {
      queryBuilder.andWhere('pr.budgetCodeId IN (:...budgetCodeIds)', {
        budgetCodeIds: paginationDto.budgetCodeIds,
      });
    }

    if (paginationDto.statusPrs?.length) {
      queryBuilder.andWhere('pr.statusPr IN (:...statusPrs)', {
        statusPrs: paginationDto.statusPrs,
      });
    }

    if (paginationDto.statePrs?.length) {
      queryBuilder.andWhere('pr.statePr IN (:...statePrs)', {
        statePrs: paginationDto.statePrs,
      });
    }

    if (paginationDto.businessUnitIds?.length) {
      queryBuilder.andWhere('pr.businessUnitId IN (:...businessUnitIds)', {
        businessUnitIds: paginationDto.businessUnitIds,
      });
    }

    if (paginationDto.sectorIds?.length) {
      queryBuilder.andWhere('pr.sectorId IN (:...sectorIds)', {
        sectorIds: paginationDto.sectorIds,
      });
    }

    ///Details
    if (paginationDto.materialGroupIds?.length) {
      queryBuilder.andWhere(
        'details.materialGroupId IN (:...materialGroupIds)',
        {
          materialGroupIds: paginationDto.materialGroupIds,
        },
      );
    }

    if (paginationDto.startDate && paginationDto.endDate) {
      queryBuilder.andWhere(
        'details.deliveryTime BETWEEN :startDate AND :endDate',
        {
          startDate: paginationDto.startDate,
          endDate: paginationDto.endDate,
        },
      );
    }

    if (paginationDto.costCenterIds?.length) {
      queryBuilder.andWhere('details.costCenterId IN (:...costCenterIds)', {
        costCenterIds: paginationDto.costCenterIds,
      });
    }

    if (paginationDto.isMaterial) {
      queryBuilder.andWhere('pr.statusPr = :status', {
        status: Status.Approved,
      });
    }

    if (paginationDto.processTypeIds?.length) {
      queryBuilder.andWhere('pr.processTypeId IN (:...processTypeIds)', {
        processTypeIds: paginationDto.processTypeIds,
      });
    }

    if (paginationDto.crudTemplateActiveTab == 'REQUEST') {
      if (jwtPayload?.staffId) {
        queryBuilder.andWhere('pr.requesterId = :requesterId', {
          requesterId: jwtPayload?.staffId,
        });
      } else {
        return new ResponseDto([], paginationDto.page, paginationDto.limit, 0);
      }
    }

    if (paginationDto.crudTemplateActiveTab == 'WAITING') {
      if (jwtPayload?.email) {
        queryBuilder.innerJoin(
          'pr.levels',
          'levels',
          ` ( NOT EXISTS (
          SELECT 1
          FROM "approval-level" l_prev
          WHERE l_prev.level < levels.level
          AND l_prev.status != :approvedStatus
          AND "pr"."id" = "l_prev"."purchase_request_id"
        ) OR levels."level" = 1) AND levels.status = :status AND "pr"."id" = "levels"."purchase_request_id"`,
          { status: StatusLevel.Pending, approvedStatus: StatusLevel.Approved },
        );
        queryBuilder.andWhere(
          `levels.email = :email  AND pr.statusPr IN (:...waitingPrStatus) `,
          {
            email: jwtPayload?.email,
            waitingPrStatus: [Status.InProgress, Status.WaitingProcess],
          },
        );
      } else {
        return new ResponseDto([], paginationDto.page, paginationDto.limit, 0);
      }
    }

    if (paginationDto.crudTemplateActiveTab == 'APPROVED') {
      if (jwtPayload?.email) {
        queryBuilder.innerJoin(
          'pr.levels',
          'levels',
          'levels.email = :email AND levels.status IN (:...status) AND "pr"."id" = "levels"."purchase_request_id"',
          {
            email: jwtPayload?.email,
            status: [StatusLevel.Approved, StatusLevel.Rejected],
          },
        );
      } else {
        return new ResponseDto([], paginationDto.page, paginationDto.limit, 0);
      }
    }

    if (paginationDto.searchString) {
      queryBuilder.andWhere(`CAST(pr.id AS TEXT) ILIKE :searchString`, {
        searchString: paginationDto.searchString,
      });

      paginationDto.searchString = null;
    }

    /// JOIN FOR PR
    queryBuilder
      .leftJoin('pr.sector', 'sector')
      .addSelect(['sector.id', 'sector.name', 'sector.code'])
      .leftJoin('pr.businessUnit', 'businessUnit')
      .addSelect(['businessUnit.id', 'businessUnit.name', 'businessUnit.code'])
      .leftJoin('pr.requester', 'requester')
      .addSelect([
        'requester.id',
        'requester.firstName',
        'requester.lastName',
        'requester.email',
        'requester.code',
      ])
      .leftJoin('pr.typePr', 'typePr')
      .addSelect(['typePr.id', 'typePr.name', 'typePr.code', 'typePr.status'])
      .leftJoin('pr.budgetCode', 'budgetCode')
      .addSelect(['budgetCode.id', 'budgetCode.name', 'budgetCode.code'])
      .leftJoin('pr.costCenter', 'costCenter')
      .addSelect(['costCenter.id', 'costCenter.name', 'costCenter.code'])
      .leftJoin('pr.purchaseOrg', 'purchaseOrg')
      .addSelect(['purchaseOrg.id', 'purchaseOrg.name', 'purchaseOrg.code'])
      .leftJoin('pr.purchaseGroup', 'purchaseGroup')
      .addSelect([
        'purchaseGroup.id',
        'purchaseGroup.name',
        'purchaseGroup.code',
      ])
      .leftJoin('pr.purchaser', 'purchaser')
      .addSelect([
        'purchaser.id',
        'purchaser.firstName',
        'purchaser.lastName',
        'purchaser.email',
        'purchaser.code',
      ])
      .leftJoin('pr.processType', 'processType')
      .addSelect(['processType.id', 'processType.name', 'processType.code'])
      .leftJoin('pr.plant', 'plant')
      .addSelect(['plant.id', 'plant.name', 'plant.code'])
      .leftJoin('pr.functionUnit', 'functionUnit')
      .addSelect(['functionUnit.id', 'functionUnit.name', 'functionUnit.code'])
      .leftJoin('pr.department', 'department')
      .addSelect(['department.id', 'department.name', 'department.code']);

    /// JOIN FOR DETAILS
    queryBuilder
      .leftJoin('details.budgetCode', 'budgetCodeDetail')
      .addSelect([
        'budgetCodeDetail.id',
        'budgetCodeDetail.name',
        'budgetCodeDetail.code',
        'budgetCodeDetail.budgetType',
        'budgetCodeDetail.internalOrder',
      ])
      .leftJoin('details.measure', 'measure')
      .addSelect([
        'measure.id',
        'measure.name',
        'measure.code',
        'measure.description',
      ])
      .leftJoin('details.warehouse', 'warehouse')
      .addSelect([
        'warehouse.id',
        'warehouse.name',
        'warehouse.code',
        'warehouse.description',
      ])
      .leftJoin('details.material', 'material')
      .addSelect(['material.id', 'material.name', 'material.code'])
      .leftJoin('details.costCenter', 'costCenterDetail')
      .addSelect([
        'costCenterDetail.id',
        'costCenterDetail.name',
        'costCenterDetail.code',
      ])
      .leftJoin('details.materialGroup', 'materialGroup')
      .addSelect([
        'materialGroup.id',
        'materialGroup.name',
        'materialGroup.code',
      ]);

    if (
      paginationDto.crudTemplateActiveTab != 'REQUEST' &&
      paginationDto.crudTemplateActiveTab != 'WAITING' &&
      paginationDto.crudTemplateActiveTab != 'APPROVED'
    ) {
      if (!jwtPayload?.isSuperAdmin) {
        if (
          !parseScopes(jwtPayload?.scopes, [
            ESectorPermission.CREATE,
            ESectorPermission.EDIT,
          ]) &&
          jwtPayload?.sectors?.length
        ) {
          queryBuilder.andWhere(
            '(sector.id IS NULL OR sector.code IN (:...sectors))',
            {
              sectors: jwtPayload.sectors,
            },
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EBusinessUnitPermission.CREATE,
            EBusinessUnitPermission.EDIT,
          ]) &&
          jwtPayload?.businessUnits?.length
        ) {
          queryBuilder.andWhere(
            '(businessUnit.id IS NULL OR businessUnit.code IN (:...businessUnits))',
            {
              businessUnits: jwtPayload.businessUnits,
            },
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EFunctionUnitPermission.CREATE,
            EFunctionUnitPermission.EDIT,
          ]) &&
          jwtPayload?.functionUnits?.length
        ) {
          queryBuilder.andWhere(
            '(functionUnit.id IS NULL OR functionUnit.code IN (:...functionUnits))',
            {
              functionUnits: jwtPayload.functionUnits,
            },
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EDepartmentPermission.CREATE,
            EDepartmentPermission.EDIT,
          ]) &&
          jwtPayload?.departments?.length
        ) {
          queryBuilder.andWhere(
            '(department.id IS NULL OR department.code IN (:...departments))',
            {
              departments: jwtPayload.departments,
            },
          );
        }
      }
    }

    if (isNeedDetails) {
      if (
        !jwtPayload?.isSuperAdmin &&
        parseScopes(jwtPayload?.scopes, [EApprovedMaterialPermission.CREATE])
      ) {
        queryBuilder.andWhere('"purchaser".id = :staffId', {
          staffId: jwtPayload?.staffId,
        });
      }
    }

    queryBuilder.orderBy('pr.id', 'DESC');

    return await this.pagination(queryBuilder, paginationDto);
  }

  async findOne(
    id: number,
    isNeedPo: boolean = false,
  ): Promise<PurchaseRequestModel> {
    const repository = this.getRepository(PurchaseRequestEntity);

    const queryBuilder = repository
      .createQueryBuilder('pr')
      .innerJoinAndSelect('pr.details', 'details')
      .leftJoinAndSelect('pr.history', 'history');

    queryBuilder.andWhere('pr.id = :id', { id });

    /// JOIN FOR PR
    queryBuilder
      .leftJoin('pr.sector', 'sector')
      .addSelect(['sector.id', 'sector.name', 'sector.code'])
      .leftJoin('pr.businessUnit', 'businessUnit')
      .addSelect(['businessUnit.id', 'businessUnit.name', 'businessUnit.code'])
      .leftJoin('pr.requester', 'requester')
      .addSelect([
        'requester.id',
        'requester.firstName',
        'requester.lastName',
        'requester.email',
        'requester.code',
      ])
      .leftJoin('pr.typePr', 'typePr')
      .addSelect(['typePr.id', 'typePr.name', 'typePr.code', 'typePr.status'])
      .leftJoin('pr.budgetCode', 'budgetCode')
      .addSelect(['budgetCode.id', 'budgetCode.name', 'budgetCode.code'])
      .leftJoin('pr.costCenter', 'costCenter')
      .addSelect(['costCenter.id', 'costCenter.name', 'costCenter.code'])
      .leftJoin('pr.purchaseOrg', 'purchaseOrg')
      .addSelect(['purchaseOrg.id', 'purchaseOrg.name', 'purchaseOrg.code'])
      .leftJoin('pr.purchaseGroup', 'purchaseGroup')
      .addSelect([
        'purchaseGroup.id',
        'purchaseGroup.name',
        'purchaseGroup.code',
      ])
      .leftJoin('pr.purchaser', 'purchaser')
      .addSelect([
        'purchaser.id',
        'purchaser.firstName',
        'purchaser.lastName',
        'purchaser.email',
        'purchaser.code',
      ])
      .leftJoin('pr.processType', 'processType')
      .addSelect(['processType.id', 'processType.name', 'processType.code'])
      .leftJoin('pr.plant', 'plant')
      .addSelect(['plant.id', 'plant.name', 'plant.code'])
      .leftJoin('pr.functionUnit', 'functionUnit')
      .addSelect(['functionUnit.id', 'functionUnit.name', 'functionUnit.code'])
      .leftJoin('pr.department', 'department')
      .addSelect(['department.id', 'department.name', 'department.code']);

    /// JOIN FOR DETAILS
    queryBuilder
      .leftJoin('details.budgetCode', 'budgetCodeDetail')
      .addSelect([
        'budgetCodeDetail.id',
        'budgetCodeDetail.name',
        'budgetCodeDetail.code',
        'budgetCodeDetail.budgetType',
      ])
      .leftJoin('details.material', 'material')
      .addSelect(['material.id', 'material.name', 'material.code'])
      .leftJoin('details.costCenter', 'costCenterDetail')
      .addSelect([
        'costCenterDetail.id',
        'costCenterDetail.name',
        'costCenterDetail.code',
      ])
      .leftJoin('details.materialGroup', 'materialGroup')
      .addSelect([
        'materialGroup.id',
        'materialGroup.name',
        'materialGroup.code',
      ])
      .leftJoin('details.measure', 'measure')
      .addSelect([
        'measure.id',
        'measure.name',
        'measure.code',
        'measure.description',
      ])
      .leftJoin('details.warehouse', 'warehouse')
      .addSelect([
        'warehouse.id',
        'warehouse.name',
        'warehouse.code',
        'warehouse.description',
      ]);

    if (isNeedPo) {
      queryBuilder
        .leftJoin('details.poDetails', 'poDetails')
        .addSelect(['poDetails.id'])
        .leftJoin('poDetails.purchaseOrder', 'purchaseOrder')
        .addSelect(['purchaseOrder.id']);
    }

    return await queryBuilder.getOne();
  }

  async findOneBy(data): Promise<PurchaseRequestModel> {
    const repository = this.getRepository(PurchaseRequestEntity);
    return await repository.findOneBy(data);
  }

  async findOneStatusProcess(status: Status): Promise<PurchaseRequestModel[]> {
    const repository = this.getRepository(PurchaseRequestEntity);
    return await repository.find({
      where: { statusPr: status },
    });
  }

  async updateStatus(id: number, status: Status): Promise<any> {
    const repository = this.getRepository(PurchaseRequestEntity);
    return await repository.update(id, {
      statusPr: status,
    });
  }

  async updateState(id: number, state: State): Promise<any> {
    const repository = this.getRepository(PurchaseRequestEntity);
    return await repository.update(id, { statePr: state });
  }

  async createPurchaseRequest(
    data: PurchaseRequestDto,
    jwtPayload,
    authorization: any,
  ): Promise<PurchaseRequestModel> {
    const repository = this.getRepository(PurchaseRequestEntity);
    const repositoryRequestDetail = this.getRepository(
      PurchaseRequestDetailEntity,
    );
    const repositoryHistory = this.getRepository(HistoryApproveEntity);

    data.details.forEach((detail) => {
      if (
        !detail.quantity ||
        (!detail.unitPrice && detail.unitPrice < 0) ||
        !detail.deliveryTime
      ) {
        throw new BadRequestException('Missing required fields in details');
      }
    });

    const purchaseRequest = repository.create({
      ...data,
      details: data.details.map((detail) =>
        repositoryRequestDetail.create(detail),
      ),
      history:
        data?.history?.map((history) => repositoryHistory.create(history)) ||
        [],
    });

    return await repository.save(purchaseRequest);
  }

  async updatePurchaseRequest(
    id: number,
    updatePurchaseRequest: PurchaseRequestDto,
  ): Promise<PurchaseRequestModel> {
    const repository = this.getRepository(PurchaseRequestEntity);
    const repositoryRequestDetail = this.getRepository(
      PurchaseRequestDetailEntity,
    );
    const purchaseRequest = await repository.findOne({
      where: { id },
      relations: ['details'],
    });

    if (!purchaseRequest) {
      return null;
    }

    const prDetailIds = (purchaseRequest.details || []).map(
      (detail) => detail.id,
    );

    Object.assign(purchaseRequest, updatePurchaseRequest);

    if (updatePurchaseRequest.details) {
      await repositoryRequestDetail.softDelete({
        id: In(prDetailIds),
      });

      purchaseRequest.details = updatePurchaseRequest.details.map((detail) =>
        repositoryRequestDetail.create(detail),
      );
    }

    return repository.save(purchaseRequest);
  }

  async approvePurchaseRequest(checkConditionsDto): Promise<any> {
    const repository = this.getRepository(PurchaseRequestEntity);
    const purchaseRequest = await repository.findOne({
      where: { id: checkConditionsDto.id },
    });
    if (!purchaseRequest) {
      throw new NotFoundException(
        `Purchase Request with ID ${checkConditionsDto.id} not found`,
      );
    }

    purchaseRequest.statusPr = Status.Approved;
    await repository.save(purchaseRequest);
    return purchaseRequest;
  }

  async checkIsPO(prReferenceIds?: number[]): Promise<void> {
    if (prReferenceIds && prReferenceIds?.length) {
      const repository = this.getRepository(PurchaseRequestEntity);
      const purchaseRequestes = await repository.find({
        where: { id: In(prReferenceIds) },
      });

      const queryPrReferenceIds = (purchaseRequestes || []).map(
        (item) => item.id,
      );

      const diffPrReferenceIds = _.difference(
        prReferenceIds,
        queryPrReferenceIds,
      );

      if (diffPrReferenceIds?.length) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_5110()),
          HttpStatus.NOT_FOUND,
        );
      }
      await repository.update({ id: In(prReferenceIds) }, { isPo: true });
    }
  }

  async findPRWithBudget(
    conditions: GetPRWithBudgetDto,
  ): Promise<PurchaseRequestModel[]> {
    const repository = this.getRepository(PurchaseRequestEntity);
    const queryBuilder = repository.createQueryBuilder('pr');
    queryBuilder.leftJoinAndSelect('pr.details', 'details');

    if (conditions.budget_codes?.length) {
      queryBuilder.andWhere('pr.budgetCodeId IN (:...budget_codes)', {
        budget_codes: conditions.budget_codes,
      });
    }

    if (conditions.business_units?.length) {
      queryBuilder.andWhere('pr.businessUnitId IN (:...business_units)', {
        business_units: conditions.business_units,
      });
    }

    if (conditions.statuses?.length) {
      queryBuilder.andWhere('pr.statusPr IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    return await queryBuilder.orderBy('pr.created_at', 'DESC').getMany();
  }

  async materialPurchase(
    paginationDto: GetPurchaseRequestDto,
  ): Promise<ResponseDto<PurchaseRequestModel>> {
    const repository = this.getRepository(PurchaseRequestEntity);

    const queryBuilder = repository.createQueryBuilder('pr');

    queryBuilder.andWhere('pr.statusPr = :status', {
      status: Status.Approved,
    });

    queryBuilder.leftJoinAndSelect('pr.details', 'details');

    // Điều kiện lọc theo material_code nếu có
    if (paginationDto.materialCodeIds) {
      queryBuilder.andWhere('details.materialCodeId IN (:...materialCodeIds)', {
        materialCodeIds: paginationDto.materialCodeIds,
      });
    }

    // Điều kiện lọc theo material_name nếu có
    if (paginationDto.materialNames) {
      queryBuilder.andWhere('details.materialName IN (:...materialNames)', {
        materialNames: paginationDto.materialNames,
      });
    }

    return await this.pagination(queryBuilder, paginationDto);
  }

  async countPr(): Promise<any> {
    const repository = this.getRepository(PurchaseRequestEntity);
    const queryBuilder = repository
      .createQueryBuilder('pr')
      .select(['pr.id'])
      .innerJoin('pr.details', 'details')
      .innerJoin('details.poDetails', 'poDetails')
      .having('details.quantity > SUM(poDetails.quantity)')
      .groupBy('pr.id, details.quantity');

    return (await queryBuilder.getMany())?.length;
  }

  async findPrWithPo(prIds: number[]): Promise<PurchaseRequestModel[]> {
    const repository = this.getRepository(PurchaseRequestEntity);
    const queryBuilder = repository.createQueryBuilder('prs');

    queryBuilder.innerJoin('prs.details', 'details');
    queryBuilder.leftJoin('details.poDetails', 'poDetails');
    queryBuilder.leftJoin(
      'poDetails.purchaseOrder',
      'purchaseOrder',
      'purchaseOrder.statusPo NOT IN (:...status)',
      {
        status: [Status.Cancel, Status.Rejected],
      },
    );

    queryBuilder.andWhere('prs.id IN (:...prIds)', {
      prIds,
    });

    queryBuilder.select([
      'prs.id',
      'prs.statePr',
      'details.id',
      'poDetails.id',
      'purchaseOrder.id',
      'purchaseOrder.statusPo',
    ]);

    return await queryBuilder.getMany();
  }

  async getPrDetails(ids: number[]): Promise<PurchaseRequestDetailModel[]> {
    const repository = this.getRepository(PurchaseRequestDetailEntity);

    if (ids?.length) {
      const queryBuilder = repository
        .createQueryBuilder('prDetails')
        .innerJoinAndSelect('prDetails.poDetails', 'poDetails')
        .innerJoin(
          'poDetails.purchaseOrder',
          'purchaseOrder',
          'purchaseOrder.statusPo NOT IN (:...status)',
          {
            status: [Status.Cancel, Status.Rejected],
          },
        )
        .andWhere('prDetails.id IN (:...ids)', {
          ids: [...new Set(ids)],
        });

      return await queryBuilder.getMany();
    }

    return [];
  }

  async getPrDetailByIds(ids: number[]): Promise<PurchaseRequestDetailModel[]> {
    const repository = this.getRepository(PurchaseRequestDetailEntity);

    if (ids?.length) {
      const queryBuilder = repository
        .createQueryBuilder('prDetails')
        .andWhere('prDetails.id IN (:...ids)', {
          ids: [...new Set(ids)],
        });

      return await queryBuilder.getMany();
    }

    return [];
  }

  async priceMaterial(
    conditions: PriceMaterialDto,
  ): Promise<PurchaseRequestDetailModel> {
    const repository = this.getRepository(PurchaseRequestDetailEntity);
    const queryBuilder = repository
      .createQueryBuilder('prDetails')
      .orderBy('prDetails.deliveryTime', 'DESC');

    queryBuilder.innerJoin('prDetails.purchaseRequest', 'purchaseRequest');

    queryBuilder.andWhere('prDetails.materialCodeId = :materialCodeId', {
      materialCodeId: conditions.materialCodeId,
    });
    queryBuilder.andWhere('purchaseRequest.statusPr = :status', {
      status: Status.Approved,
    });

    return await queryBuilder.getOne();
  }

  async getPrListByIds(ids: number[]): Promise<PurchaseRequestModel[]> {
    const repository = this.getRepository(PurchaseRequestEntity);

    const queryBuilder = repository
      .createQueryBuilder('prs')
      .leftJoinAndSelect('prs.levels', 'levels')
      .leftJoinAndSelect('prs.details', 'details')
      .andWhere('prs.id IN (:...ids)', {
        ids: [...new Set(ids)],
      });

    return await queryBuilder.getMany();
  }

  async findOneById(id: number): Promise<PurchaseRequestModel> {
    const repository = this.getRepository(PurchaseRequestEntity);

    const queryBuilder = repository.createQueryBuilder('pr');

    queryBuilder.andWhere('pr.id = :id', { id });

    return await queryBuilder.getOne();
  }

  async getPrsForResendEmail(ids: number[]): Promise<PurchaseRequestModel[]> {
    const repository = this.getRepository(PurchaseRequestEntity);

    const queryBuilder = repository
      .createQueryBuilder('prs')
      .innerJoinAndSelect('prs.levels', 'levels')
      .innerJoinAndSelect('prs.details', 'details');

    /// JOIN FOR PR
    queryBuilder
      .leftJoin('prs.sector', 'sector')
      .addSelect(['sector.id', 'sector.name', 'sector.code'])
      .leftJoin('prs.businessUnit', 'businessUnit')
      .addSelect(['businessUnit.id', 'businessUnit.name', 'businessUnit.code'])
      .leftJoin('prs.requester', 'requester')
      .addSelect([
        'requester.id',
        'requester.firstName',
        'requester.lastName',
        'requester.email',
        'requester.code',
      ])
      .leftJoin('prs.typePr', 'typePr')
      .addSelect(['typePr.id', 'typePr.name', 'typePr.code', 'typePr.status'])
      .leftJoin('prs.budgetCode', 'budgetCode')
      .addSelect(['budgetCode.id', 'budgetCode.name', 'budgetCode.code'])
      .leftJoin('prs.costCenter', 'costCenter')
      .addSelect(['costCenter.id', 'costCenter.name', 'costCenter.code'])
      .leftJoin('prs.purchaseOrg', 'purchaseOrg')
      .addSelect(['purchaseOrg.id', 'purchaseOrg.name', 'purchaseOrg.code'])
      .leftJoin('prs.purchaseGroup', 'purchaseGroup')
      .addSelect([
        'purchaseGroup.id',
        'purchaseGroup.name',
        'purchaseGroup.code',
      ])
      .leftJoin('prs.purchaser', 'purchaser')
      .addSelect([
        'purchaser.id',
        'purchaser.firstName',
        'purchaser.lastName',
        'purchaser.email',
        'purchaser.code',
      ])
      .leftJoin('prs.processType', 'processType')
      .addSelect(['processType.id', 'processType.name', 'processType.code'])
      .leftJoin('prs.plant', 'plant')
      .addSelect(['plant.id', 'plant.name', 'plant.code'])
      .leftJoin('prs.functionUnit', 'functionUnit')
      .addSelect(['functionUnit.id', 'functionUnit.name', 'functionUnit.code'])
      .leftJoin('prs.department', 'department')
      .addSelect(['department.id', 'department.name', 'department.code']);

    /// JOIN FOR DETAILS
    queryBuilder
      .leftJoin('details.budgetCode', 'budgetCodeDetail')
      .addSelect([
        'budgetCodeDetail.id',
        'budgetCodeDetail.name',
        'budgetCodeDetail.code',
        'budgetCodeDetail.budgetType',
      ])
      .leftJoin('details.material', 'material')
      .addSelect(['material.id', 'material.name', 'material.code'])
      .leftJoin('details.costCenter', 'costCenterDetail')
      .addSelect([
        'costCenterDetail.id',
        'costCenterDetail.name',
        'costCenterDetail.code',
      ])
      .leftJoin('details.materialGroup', 'materialGroup')
      .addSelect([
        'materialGroup.id',
        'materialGroup.name',
        'materialGroup.code',
      ])
      .leftJoin('details.measure', 'measure')
      .addSelect([
        'measure.id',
        'measure.name',
        'measure.code',
        'measure.description',
      ])
      .leftJoin('details.warehouse', 'warehouse')
      .addSelect([
        'warehouse.id',
        'warehouse.name',
        'warehouse.code',
        'warehouse.description',
      ]);

    queryBuilder.andWhere('prs.id IN (:...ids)', {
      ids: [...new Set(ids)],
    });

    queryBuilder.andWhere('levels.status = :status', {
      status: StatusLevel.Pending,
    });

    return await queryBuilder.getMany();
  }
}
