import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetSectorListDto } from '../../controller/sector/dtos/get-sector-list.dto';
import { UpdateSectorDto } from '../../controller/sector/dtos/update-sector.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { SectorModel } from '../../domain/model/sector.model';
import { ISectorRepository } from '../../domain/repositories/sector.repository';
import { SectorEntity } from '../entities/sector.entity';
import { BaseRepository } from './base.repository';
import { GetDetailSectorDto } from '../../controller/sector/dtos/get-detail-sector.dto';
import { parseScopes } from '../../utils/common';
import { ESectorPermission } from '../../utils/constants/permission.enum';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class SectorRepository
  extends BaseRepository
  implements ISectorRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createSector(data: SectorModel): Promise<SectorModel> {
    const repository = this.getRepository(SectorEntity);

    const sector = repository.create({
      code: data.code,
      description: data.description,
      name: data.name,
      status: data.status,
    });

    return await repository.save(sector);
  }
  async updateSector(
    id,
    updateSectorDto: UpdateSectorDto,
  ): Promise<SectorModel> {
    const repository = this.getRepository(SectorEntity);
    const sector = repository.create({ id, ...updateSectorDto });
    return await repository.save(sector);
  }
  async deleteSector(id: string): Promise<void> {
    const repository = this.getRepository(SectorEntity);
    await repository.delete(id);
  }

  async getSectorById(id: string): Promise<SectorModel> {
    const repository = this.getRepository(SectorEntity);
    return await repository.findOneBy({ id: id });
  }

  async getSectors(
    conditions: GetSectorListDto,
    jwtPayload,
  ): Promise<ResponseDto<SectorModel>> {
    const repository = this.getRepository(SectorEntity);
    let queryBuilder = repository.createQueryBuilder('sectors');

    if (conditions.statuses) {
      queryBuilder.andWhere('sectors.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('sectors.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('sectors.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getSectorByCode(code: string, id?: string): Promise<SectorModel> {
    const repository = this.getRepository(SectorEntity);

    const query: FindOneOptions<SectorEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getDetailSector(
    conditions: GetDetailSectorDto,
    jwtPayload: any,
  ): Promise<SectorModel> {
    const repository = this.getRepository(SectorEntity);
    let queryBuilder = repository
      .createQueryBuilder('sector')
      .select([
        'sector.id',
        'sector.code',
        'sector.name',
        'sector.description',
        'sector.status',
        'sector.createdAt',
      ]);

    queryBuilder.where('sector.id = :id', { id: conditions.id });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  async getSectorsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<SectorModel[]> {
    const repository = this.getRepository(SectorEntity);
    let queryBuilder = repository.createQueryBuilder('sectors');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetSectorListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('sectors.code IN (:...codes)', { codes: codes });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('sectors.code IN (:...codes)', { codes: codes });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }

  async getSectorByIds(ids: string[], jwtPayload: any): Promise<SectorModel[]> {
    const repository = this.getRepository(SectorEntity);
    let queryBuilder = repository.createQueryBuilder('sectors');

    if (ids.length) {
      queryBuilder.where('sectors.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetSectorListDto({}),
    );

    return await queryBuilder.getMany();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<SectorEntity>,
    jwtPayload: any,
    conditions: GetSectorListDto | GetDetailSectorDto,
  ) {
    conditions.codes = jwtPayload?.sectors; // Data role
    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        ESectorPermission.CREATE,
        ESectorPermission.EDIT,
      ]) &&
      conditions.codes?.length
    ) {
      queryBuilder.andWhere(
        '(sectors.id IS NULL OR sectors.code IN (:...codes))',
        {
          codes: conditions.codes,
        },
      );
    }

    return queryBuilder;
  }
}
