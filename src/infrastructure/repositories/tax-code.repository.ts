import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { ITaxCodeRepository } from '../../domain/repositories/tax-code.repository';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { TaxCodeModel } from '../../domain/model/tax-code.model';
import { TaxCodeEntity } from '../entities/tax-code.entity';
import { GetTaxCodeListDto } from '../../controller/tax-code/dtos/get-tax-code-list.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { GetDetailTaxCodeDto } from '../../controller/tax-code/dtos/get-detail-tax-code.dto';

@Injectable({ scope: Scope.REQUEST })
export class TaxCodeRepository
  extends BaseRepository
  implements ITaxCodeRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createTaxCode(data: TaxCodeModel): Promise<TaxCodeModel> {
    const repository = this.getRepository(TaxCodeEntity);
    const newTaxCode = repository.create(data);
    return await repository.save(newTaxCode);
  }

  async updateTaxCode(data: TaxCodeModel): Promise<TaxCodeModel> {
    const repository = this.getRepository(TaxCodeEntity);
    const updateTaxCode = repository.create(data);
    return await repository.save(updateTaxCode);
  }

  async getTaxCodes(
    conditions: GetTaxCodeListDto,
    jwtPayload,
  ): Promise<ResponseDto<TaxCodeModel>> {
    const repository = this.getRepository(TaxCodeEntity);
    let queryBuilder = repository.createQueryBuilder('taxCodes');

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.andWhere('taxCodes.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    queryBuilder.orderBy('taxCodes.createdAt', 'DESC');

    return await this.pagination<TaxCodeEntity>(queryBuilder, conditions);
  }

  async deleteTaxCode(id: string): Promise<void> {
    const repository = this.getRepository(TaxCodeEntity);
    await repository.delete(id);
  }

  async getTaxCodeByCode(code: string, id?: string): Promise<TaxCodeModel> {
    const repository = this.getRepository(TaxCodeEntity);
    const query: FindOneOptions<TaxCodeEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getTaxCodeDetail(
    conditions: GetDetailTaxCodeDto,
    jwtPayload,
  ): Promise<TaxCodeModel> {
    const repository = this.getRepository(TaxCodeEntity);
    let queryBuilder = repository.createQueryBuilder('taxCodes');

    queryBuilder.where('taxCodes.id = :id', { id: conditions.id });

    return await queryBuilder.getOne();
  }

  async getTaxCodesByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<TaxCodeModel[]> {
    const repository = this.getRepository(TaxCodeEntity);

    const queryBuilder = repository.createQueryBuilder('taxCodes');

    if (codes?.length) {
      queryBuilder.andWhere('taxCodes.code IN (:...codes)', {
        codes: codes,
      });
    } else {
      return [];
    }

    return await queryBuilder.getMany();
  }
}
