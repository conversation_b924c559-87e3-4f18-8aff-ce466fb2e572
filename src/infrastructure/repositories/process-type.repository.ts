import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, FindOneOptions, Not } from 'typeorm';
import { CreateProcessTypeDto } from '../../controller/process-type/dtos/create-process-type.dto';
import { GetDetailProcessTypeDto } from '../../controller/process-type/dtos/get-detail-process-type.dto';
import { GetProcessTypeListDto } from '../../controller/process-type/dtos/get-process-type-list.dto';
import { UpdateProcessTypeDto } from '../../controller/process-type/dtos/update-process-type.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { ProcessTypeModel } from '../../domain/model/process-type.model';
import { IProcessTypeRepository } from '../../domain/repositories/process-type.repository';
import { ProcessTypeEntity } from '../entities/process-type.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ProcessTypeRepository
  extends BaseRepository
  implements IProcessTypeRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createProcessType(
    data: CreateProcessTypeDto,
  ): Promise<ProcessTypeModel> {
    const repository = this.getRepository(ProcessTypeEntity);

    const entity = repository.create(data);

    return await repository.save(entity);
  }
  async updateProcessType(
    id,
    updateProcessTypeDto: UpdateProcessTypeDto,
  ): Promise<ProcessTypeModel> {
    const repository = this.getRepository(ProcessTypeEntity);

    const entity = repository.create({ id, ...updateProcessTypeDto });

    return await repository.save(entity);
  }
  async deleteProcessType(id: string): Promise<void> {
    const repository = this.getRepository(ProcessTypeEntity);
    await repository.softDelete(id);
  }

  async getProcessTypeById(id: string): Promise<ProcessTypeModel> {
    const repository = this.getRepository(ProcessTypeEntity);
    return await repository.findOneBy({ id: id });
  }

  async getProcessTypes(
    conditions: GetProcessTypeListDto,
  ): Promise<ResponseDto<ProcessTypeModel>> {
    const repository = this.getRepository(ProcessTypeEntity);
    const queryBuilder = repository.createQueryBuilder('processType');

    if (conditions.statuses) {
      queryBuilder.andWhere('processType.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (
      conditions.hasInventoryStandard !== undefined &&
      conditions.hasInventoryStandard !== null
    ) {
      queryBuilder.andWhere('processType.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }
    //TODO: Hiện tại chưa yêu cầu cho danh mục này nên tạm thời đóng
    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('processType.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getProcessTypeByCode(
    code: string,
    id?: string,
  ): Promise<ProcessTypeModel> {
    const repository = this.getRepository(ProcessTypeEntity);

    const query: FindOneOptions<ProcessTypeEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getDetailProcessType(
    conditions: GetDetailProcessTypeDto,
  ): Promise<ProcessTypeModel> {
    const repository = this.getRepository(ProcessTypeEntity);
    const queryBuilder = repository
      .createQueryBuilder('processType')
      .select([
        'processType.id',
        'processType.code',
        'processType.name',
        'processType.description',
        'processType.status',
        'processType.createdAt',
        'processType.hasInventoryStandard',
      ]);

    queryBuilder.where('processType.id = :id', { id: conditions.id });
    //TODO: Hiện tại chưa yêu cầu cho danh mục này nên tạm thời đóng
    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  async getProcessTypesByCodesWithRole(
    codes: string[],
    isNeedPermission: boolean = true,
  ): Promise<ProcessTypeModel[]> {
    const repository = this.getRepository(ProcessTypeEntity);
    const queryBuilder = repository.createQueryBuilder('processType');

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     new GetProcessTypeListDto({}),
    //   );
    // } else {
    if (codes?.length) {
      queryBuilder.andWhere('processType.code IN (:...codes)', {
        codes: codes,
      });
    } else {
      return [];
    }
    // }

    return await queryBuilder.getMany();
  }

  async getProcessTypeByIds(ids: string[]): Promise<ProcessTypeModel[]> {
    const repository = this.getRepository(ProcessTypeEntity);
    const queryBuilder = repository.createQueryBuilder('processType');

    if (ids.length) {
      queryBuilder.where('processType.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }
    //TODO: Hiện tại chưa yêu cầu cho danh mục này nên tạm thời đóng
    // queryBuilder = this.queryWithDataRole(
    //   queryBuilder,
    //   jwtPayload,
    //   new GetProcessTypeListDto({}),
    // );

    return await queryBuilder.getMany();
  }
  //TODO: Hiện tại chưa yêu cầu cho danh mục này nên tạm thời đóng
  // queryWithDataRole(
  //   queryBuilder: SelectQueryBuilder<ProcessTypeEntity>,
  //   jwtPayload: any,
  //   conditions: GetProcessTypeListDto | GetDetailProcessTypeDto,
  // ) {
  //   conditions.codes = jwtPayload?.processTypes; // Data role
  //   if (
  //     !jwtPayload?.isSuperAdmin &&
  //     !parseScopes(jwtPayload?.scopes, [
  //       EProcessTypePermission.CREATE,
  //       EProcessTypePermission.EDIT,
  //     ])
  //   ) {
  //     queryBuilder.andWhere('processType.code IN (:...codes)', {
  //       codes: conditions.codes,
  //     });
  //   }

  //   return queryBuilder;
  // }
}
