import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { IWarehouseRepository } from '../../domain/repositories/warehouse.repository';
import { CreateWarehouseDto } from '../../controller/warehouse/dtos/create-warehouse.dto';
import { WarehouseModel } from '../../domain/model/warehouse.model';
import { WarehouseEntity } from '../entities/warehouse.entity';
import { UpdateWarehouseDto } from '../../controller/warehouse/dtos/update-warehouse.dto';
import { GetWarehouseListDto } from '../../controller/warehouse/dtos/get-warehouse-list.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { GetDetailWarehouseDto } from '../../controller/warehouse/dtos/get-detail-warehouse.dto';
import { parseScopes } from '../../utils/common';
import { ESectorPermission } from '../../utils/constants/permission.enum';

@Injectable({ scope: Scope.REQUEST })
export class WarehouseRepository
  extends BaseRepository
  implements IWarehouseRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createWarehouse(data: CreateWarehouseDto): Promise<WarehouseModel> {
    const repository = this.getRepository(WarehouseEntity);

    const warehouse = repository.create(data);

    return await repository.save(warehouse);
  }
  async updateWarehouse(
    id: string,
    updateWarehouseDto: UpdateWarehouseDto,
  ): Promise<WarehouseModel> {
    const repository = this.getRepository(WarehouseEntity);
    const warehouse = repository.create({ id, ...updateWarehouseDto });
    return await repository.save(warehouse);
  }
  async deleteWarehouse(id: string): Promise<void> {
    const repository = this.getRepository(WarehouseEntity);
    await repository.softDelete(id);
  }

  async getWarehouseById(id: string): Promise<WarehouseModel> {
    const repository = this.getRepository(WarehouseEntity);
    return await repository.findOneBy({ id: id });
  }

  async getWarehouses(
    conditions: GetWarehouseListDto,
    jwtPayload,
  ): Promise<ResponseDto<WarehouseModel>> {
    const repository = this.getRepository(WarehouseEntity);
    let queryBuilder = repository.createQueryBuilder('warehouses');

    queryBuilder
      .leftJoin('warehouses.sectors', 'sectors')
      .addSelect([
        'sectors.id',
        'sectors.code',
        'sectors.name',
        'sectors.description',
        'sectors.status',
      ]);

    if (conditions.statuses) {
      queryBuilder.andWhere('warehouses.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('warehouses.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('warehouses.updatedAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getWarehouseByCode(code: string, id?: string): Promise<WarehouseModel> {
    const repository = this.getRepository(WarehouseEntity);

    const queryBuilder = repository.createQueryBuilder('warehouses');

    queryBuilder.where('warehouses.code = :code', { code });

    if (id) {
      queryBuilder.andWhere('warehouses.id != :id', { id });
    }

    return await queryBuilder.getOne();
  }

  async getDetailWarehouse(
    conditions: GetDetailWarehouseDto,
    jwtPayload: any,
  ): Promise<WarehouseModel> {
    const repository = this.getRepository(WarehouseEntity);
    let queryBuilder = repository
      .createQueryBuilder('warehouse')
      .select([
        'warehouse.id',
        'warehouse.code',
        'warehouse.name',
        'warehouse.description',
        'warehouse.status',
        'warehouse.createdAt',
        'warehouse.updatedAt',
      ]);

    queryBuilder
      .leftJoin('warehouse.sectors', 'sectors')
      .addSelect([
        'sectors.id',
        'sectors.code',
        'sectors.name',
        'sectors.description',
        'sectors.status',
      ]);

    queryBuilder.where('warehouse.id = :id', { id: conditions.id });

    return await queryBuilder.getOne();
  }

  async getWarehouseByIds(
    conditions: GetWarehouseListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<WarehouseModel>> {
    const repository = this.getRepository(WarehouseEntity);
    let queryBuilder = repository.createQueryBuilder('warehouses');

    if (conditions.ids?.length) {
      queryBuilder.where('warehouses.id IN (:...ids)', { ids: conditions.ids });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetWarehouseListDto({}),
    );

    return await this.pagination<WarehouseEntity>(queryBuilder, conditions);
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<WarehouseEntity>,
    jwtPayload: any,
    conditions: GetWarehouseListDto | GetDetailWarehouseDto,
  ) {
    conditions.sectorCodes = jwtPayload?.sectors; // Data role
    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        ESectorPermission.CREATE,
        ESectorPermission.EDIT,
      ]) &&
      conditions.sectorCodes?.length
    ) {
      queryBuilder.andWhere('sectors.code IN (:...codes)', {
        codes: conditions.sectorCodes,
      });
    }

    return queryBuilder;
  }

  async validateDeleteWarehouse(id: string): Promise<WarehouseModel> {
    const repository = this.getRepository(WarehouseEntity);
    const queryBuilder = repository
      .createQueryBuilder('warehouse')
      .select('warehouse.id');

    queryBuilder.where('warehouse.id = :id', { id });

    queryBuilder
      .leftJoin('warehouse.purchaseRequestDetails', 'purchaseRequestDetails')
      .leftJoin('warehouse.purchaseOrderDetails', 'purchaseOrderDetails');

    queryBuilder.addSelect(
      'COUNT(DISTINCT purchaseRequestDetails.id)',
      'totalPrDetails',
    );
    queryBuilder.addSelect(
      'COUNT(DISTINCT purchaseOrderDetails.id)',
      'totalPoDetails',
    );

    queryBuilder.groupBy('warehouse.id');

    return await queryBuilder.getRawOne();
  }

  async getWarehousesByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<WarehouseModel[]> {
    const repository = this.getRepository(WarehouseEntity);
    let queryBuilder = repository
      .createQueryBuilder('warehouses')
      .leftJoin('warehouses.sectors', 'sectors')
      .addSelect([
        'sectors.id',
        'sectors.code',
        'sectors.name',
        'sectors.description',
        'sectors.status',
      ]);

    if (codes?.length) {
      queryBuilder.andWhere('warehouses.code IN (:...codes)', {
        codes: codes,
      });
    } else {
      return [];
    }

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetWarehouseListDto({}),
      );
    }

    return await queryBuilder.getMany();
  }
}
