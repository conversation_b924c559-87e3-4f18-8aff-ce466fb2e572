import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { GetPriceInformationRecordDto } from '../../controller/price-information-record/dtos/get-all-price-information-record.dto';
import {
  PriceInformationRecordDto,
  UpdatePirStatusDto,
} from '../../controller/price-information-record/dtos/price-information-record.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { IPriceInformationRecordRepository } from '../../domain/repositories/priceInformationRecordRepository.repository';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { PriceInformationRecordEntity } from '../entities/price_information_record.entity';
import { BaseRepository } from './base.repository';
import { PriceMaterialDto } from '../../controller/purchase-request/dtos/price-material.dto';
import { PriceInformationRecordModel } from '../../domain/model/price_information_record.model';
import { BusinessUnitModel } from '../../domain/model/business-unit.model';
import { GetPIRListByIdsDto } from '../../controller/price-information-record/dtos/get-price-information-record-list-by-ids.dto';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class priceInformationRecordRepository
  extends BaseRepository
  implements IPriceInformationRecordRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async findAll(
    paginationDto: GetPriceInformationRecordDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PriceInformationRecordModel>> {
    const queryBuilder = await this.createQueryBuilderPir(
      paginationDto,
      jwtPayload,
    );

    return await this.pagination(queryBuilder, paginationDto);
  }

  async createQueryBuilderPir(
    paginationDto: GetPriceInformationRecordDto,
    jwtPayload: any,
  ) {
    const repository = this.getRepository(PriceInformationRecordEntity);
    let queryBuilder = repository
      .createQueryBuilder('pir')
      .orderBy('pir.createdAt', 'DESC');

    queryBuilder
      .leftJoin('pir.vendor', 'vendor')
      .addSelect(['vendor.id', 'vendor.name', 'vendor.code', 'vendor.type'])
      .leftJoin('pir.material', 'material')
      .addSelect(['material.id', 'material.name', 'material.code'])
      .leftJoin('material.industries', 'industries')
      .addSelect(['industries.id', 'industries.codeSAP', 'industries.status'])
      .leftJoin('industries.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.code',
        'sector.name',
        'sector.description',
        'sector.status',
      ])
      .leftJoin('material.functionUnits', 'functionUnits')
      .addSelect([
        'functionUnits.id',
        'functionUnits.name',
        'functionUnits.code',
        'functionUnits.description',
        'functionUnits.status',
      ])
      .leftJoin('pir.plant', 'plant')
      .addSelect(['plant.id', 'plant.name', 'plant.code'])
      .leftJoin('pir.purchaseOrganization', 'purchaseOrganization')
      .addSelect([
        'purchaseOrganization.id',
        'purchaseOrganization.name',
        'purchaseOrganization.code',
      ])
      .leftJoin('pir.purchaseGroup', 'purchaseGroup')
      .addSelect([
        'purchaseGroup.id',
        'purchaseGroup.name',
        'purchaseGroup.code',
      ])
      .leftJoin('pir.currency', 'currency')
      .addSelect(['currency.id', 'currency.name', 'currency.currencyCode'])
      .leftJoin('pir.businessUnits', 'businessUnits')
      .addSelect([
        'businessUnits.id',
        'businessUnits.name',
        'businessUnits.code',
      ]);

    if (paginationDto.vendorCodeIds) {
      queryBuilder.andWhere('pir.vendorCodeId IN (:...vendorCodeIds)', {
        vendorCodeIds: paginationDto.vendorCodeIds,
      });
    }

    if (paginationDto.materialCodeIds) {
      queryBuilder.andWhere('pir.materialCodeId IN (:...materialCodeIds)', {
        materialCodeIds: paginationDto.materialCodeIds,
      });
    }

    if (paginationDto.purchaseOrganizationIds) {
      queryBuilder.andWhere(
        'pir.purchaseOrganizationId IN (:...purchaseOrganizationIds)',
        {
          purchaseOrganizationIds: paginationDto.purchaseOrganizationIds,
        },
      );
    }

    if (paginationDto.purchaseGroupIds) {
      queryBuilder.andWhere('pir.purchaseGroupId IN (:...purchaseGroupIds)', {
        purchaseGroupIds: paginationDto.purchaseGroupIds,
      });
    }

    if (paginationDto.currencyIds) {
      queryBuilder.andWhere('pir.currencyId IN (:...currencyIds)', {
        currencyIds: paginationDto.currencyIds,
      });
    }

    if (paginationDto.statuses) {
      queryBuilder.andWhere('pir.status IN (:...statuses)', {
        statuses: paginationDto.statuses,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        paginationDto,
      );
    }

    return queryBuilder;
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<PriceInformationRecordEntity>,
    jwtPayload: any,
    conditions: GetPriceInformationRecordDto,
  ) {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.functionUnitCodes = jwtPayload?.functionUnits;

    if (conditions.sectorCodes?.length) {
      queryBuilder.andWhere(
        '(sector.id IS NULL OR sector.code IN (:...sectorCodes))',
        {
          sectorCodes: conditions.sectorCodes,
        },
      );
    }

    if (conditions.functionUnitCodes?.length) {
      queryBuilder.andWhere(
        '(functionUnits.id IS NULL OR functionUnits.code IN (:...functionUnitCodes))',
        {
          functionUnitCodes: conditions.functionUnitCodes,
        },
      );
    }

    return queryBuilder;
  }

  async findOne(id: number): Promise<PriceInformationRecordModel> {
    const repository = this.getRepository(PriceInformationRecordEntity);
    const queryBuilder = repository
      .createQueryBuilder('pir')
      .orderBy('pir.createdAt', 'DESC');

    queryBuilder
      .leftJoin('pir.vendor', 'vendor')
      .addSelect(['vendor.id', 'vendor.name', 'vendor.code', 'vendor.type'])
      .leftJoin('pir.material', 'material')
      .addSelect(['material.id', 'material.name', 'material.code'])
      .leftJoin('pir.plant', 'plant')
      .addSelect(['plant.id', 'plant.name', 'plant.code'])
      .leftJoin('pir.purchaseOrganization', 'purchaseOrganization')
      .addSelect([
        'purchaseOrganization.id',
        'purchaseOrganization.name',
        'purchaseOrganization.code',
      ])
      .leftJoin('pir.purchaseGroup', 'purchaseGroup')
      .addSelect([
        'purchaseGroup.id',
        'purchaseGroup.name',
        'purchaseGroup.code',
      ])
      .leftJoin('pir.currency', 'currency')
      .addSelect(['currency.id', 'currency.name', 'currency.currencyCode'])
      .leftJoin('pir.businessUnits', 'businessUnits')
      .addSelect([
        'businessUnits.id',
        'businessUnits.name',
        'businessUnits.code',
      ]);

    if (!isNaN(id)) {
      queryBuilder.andWhere('pir.id = :id', { id });
      return await queryBuilder.getOne();
    }

    return null;
  }

  async createPriceInformationRecord(
    data: PriceInformationRecordDto,
  ): Promise<PriceInformationRecordModel> {
    const repository = this.getRepository(PriceInformationRecordEntity);
    const createPIR = repository.create({
      ...data,
    });

    return await repository.save(createPIR);
  }

  async updatePriceInformationRecord(
    id: number,
    price: PriceInformationRecordDto,
  ): Promise<PriceInformationRecordEntity> {
    const repository = this.getRepository(PriceInformationRecordEntity);
    const updatePir = repository.create({
      id,
      ...price,
    });

    return await repository.save(updatePir);
  }

  async updateStatusPriceInformationRecord(
    id: number,
    updateStatus: UpdatePirStatusDto,
  ): Promise<PriceInformationRecordEntity> {
    const repository = this.getRepository(PriceInformationRecordEntity);
    await repository.update(id, updateStatus);

    return await repository.findOneBy({ id });
  }

  async activePirs(): Promise<any> {
    const now = new Date();
    const repository = this.getRepository(PriceInformationRecordEntity);
    return await repository
      .createQueryBuilder('pir')
      .where('pir.effectiveDate <= :now AND pir.expirationDate >= :now', {
        now,
      })
      .getMany();
  }

  async inactivePirs(): Promise<any> {
    const now = new Date();
    const repository = this.getRepository(PriceInformationRecordEntity);
    return await repository
      .createQueryBuilder('pir')
      .where('pir.expirationDate < :now OR pir.effectiveDate > :now', { now })
      .getMany();
  }

  async createMultiplePriceInformationRecord(
    prices: PriceInformationRecordDto[],
  ): Promise<PriceInformationRecordModel[]> {
    const repository = this.getRepository(PriceInformationRecordEntity);
    const createPIRs = repository.create(prices);

    return await repository.save(createPIRs);
  }

  async priceMaterial(
    conditions: PriceMaterialDto,
  ): Promise<PriceInformationRecordModel> {
    const repository = this.getRepository(PriceInformationRecordEntity);
    const queryBuilder = repository
      .createQueryBuilder('pir')
      .orderBy('pir.createdAt', 'DESC');

    queryBuilder.andWhere('pir.materialCodeId = :materialCodeId', {
      materialCodeId: conditions.materialCodeId,
    });
    queryBuilder.andWhere(
      'pir.effectiveDate <= :deliveryTime AND pir.expirationDate >= :deliveryTime',
      { deliveryTime: conditions.deliveryTime },
    );
    return await queryBuilder.getOne();
  }

  async migrationRelationPIR(
    id: number,
    businessUnits: BusinessUnitModel[],
  ): Promise<void> {
    const repository = this.getRepository(PriceInformationRecordEntity);
    const dataUpdate = repository.create({ id, businessUnits });
    await repository.save(dataUpdate);
  }

  async getListByIds(
    conditions: GetPIRListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<PriceInformationRecordModel>> {
    const repository = this.getRepository(PriceInformationRecordEntity);
    let queryBuilder = repository.createQueryBuilder('pirs');

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.where('pirs.id IN (:...ids)', {
        ids: conditions.ids,
      });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder.orderBy('pirs.createdAt', 'DESC');

    return await this.pagination<PriceInformationRecordEntity>(
      queryBuilder,
      conditions,
    );
  }
}
