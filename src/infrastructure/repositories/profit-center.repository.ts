import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not } from 'typeorm';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { BaseRepository } from './base.repository';
import { parseScopes } from '../../utils/common';
import { IProfitCenterRepository } from '../../domain/repositories/profit-center.repository';
import { ProfitCenterModel } from '../../domain/model/profit-center.model';
import { ProfitCenterEntity } from '../entities/profit-center.entity';
import { GetProfitCenterListDto } from '../../controller/profit-center/dtos/get-profit-center-list.dto';
import { GetDetailProfitCenterDto } from '../../controller/profit-center/dtos/get-detail-profit-center.dto';

@Injectable({ scope: Scope.REQUEST })
export class ProfitCenterRepository
  extends BaseRepository
  implements IProfitCenterRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createProfitCenter(
    data: ProfitCenterModel,
  ): Promise<ProfitCenterModel> {
    const repository = this.getRepository(ProfitCenterEntity);
    const newProfitCenter = repository.create(data);
    return await repository.save(newProfitCenter);
  }

  async updateProfitCenter(
    data: ProfitCenterModel,
  ): Promise<ProfitCenterModel> {
    const repository = this.getRepository(ProfitCenterEntity);
    const updateProfitCenter = repository.create(data);
    return await repository.save(updateProfitCenter);
  }

  async getProfitCenters(
    conditions: GetProfitCenterListDto,
  ): Promise<ResponseDto<ProfitCenterModel>> {
    const repository = this.getRepository(ProfitCenterEntity);
    const queryBuilder = repository.createQueryBuilder('profitCenters');

    if (conditions.statuses) {
      queryBuilder.where('profitCenters.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    queryBuilder.orderBy('profitCenters.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async deleteProfitCenter(id: string): Promise<void> {
    const repository = this.getRepository(ProfitCenterEntity);
    await repository.softDelete(id);
  }

  async getProfitCenterByCode(
    code: string,
    id?: string,
  ): Promise<ProfitCenterModel> {
    const repository = this.getRepository(ProfitCenterEntity);
    const query: FindOneOptions<ProfitCenterEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getDetailProfitCenter(
    conditions: GetDetailProfitCenterDto,
  ): Promise<ProfitCenterModel> {
    const repository = this.getRepository(ProfitCenterEntity);

    const queryBuilder = repository.createQueryBuilder('profitCenters');
    queryBuilder.where('profitCenters.id = :id', { id: conditions.id });

    return await queryBuilder.getOne();
  }

  async getProfitCenterByCodes(codes: string[]): Promise<ProfitCenterModel[]> {
    const repository = this.getRepository(ProfitCenterEntity);
    const queryBuilder = repository.createQueryBuilder('profitCenters');

    if (codes?.length) {
      queryBuilder.where('profitCenters.code IN (:...codes)', { codes });
    } else {
      return [];
    }

    queryBuilder.orderBy('profitCenters.createdAt', 'DESC');

    return await queryBuilder.getMany();
  }
}
