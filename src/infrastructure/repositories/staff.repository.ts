import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { GetDetailStaffDto } from '../../controller/staff/dtos/get-detail-staff.dto';
import { GetStaffByCodesDto } from '../../controller/staff/dtos/get-staff-by-codes.dto';
import { GetStaffByEmailsDto } from '../../controller/staff/dtos/get-staff-by-emails.dto';
import { GetStaffListDto } from '../../controller/staff/dtos/get-staff-list.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { StaffModel } from '../../domain/model/staff.model';
import { IStaffRepository } from '../../domain/repositories/staff.repository';
import { parseScopes } from '../../utils/common';
import {
  EBusinessOwnerPermission,
  EBusinessUnitPermission,
  ECompanyPermission,
  EDepartmentPermission,
  EFunctionUnitPermission,
  EPositionPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { StaffEntity } from '../entities/staff.entity';
import { BaseRepository } from './base.repository';
import { GetApproverByPositionDto } from '../../controller/staff/dtos/get-approver-by-position.dto';
import { EStatus } from '../../domain/config/enums/status.enum';

@Injectable({ scope: Scope.REQUEST })
export class StaffRepository
  extends BaseRepository
  implements IStaffRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createStaff(data: StaffModel): Promise<StaffModel> {
    const repository = this.getRepository(StaffEntity);
    const newStaff = repository.create(data);

    return await repository.save(newStaff);
  }

  async updateStaff(staff: StaffModel): Promise<StaffModel> {
    const repository = this.getRepository(StaffEntity);
    const updateStaff = repository.create({ ...staff });

    return await repository.save(updateStaff);
  }

  async getStaffList(
    conditions: GetStaffListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<StaffModel>> {
    //For data role
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.companyCodes = jwtPayload?.companies;
    conditions.businessUnitCodes = jwtPayload?.businessUnits;
    conditions.departmentCodes = jwtPayload?.departments;
    conditions.functionUnitCodes = jwtPayload?.functionUnits;
    conditions.positionCodes = jwtPayload?.positions;

    const repository = this.getRepository(StaffEntity);
    let queryBuilder = repository
      .createQueryBuilder('staffs')
      .select([
        'staffs.id',
        'staffs.createdAt',
        'staffs.updatedAt',
        'staffs.deletedAt',
        'staffs.createdBy',
        'staffs.updatedBy',
        'staffs.code',
        'staffs.email',
        'staffs.firstName',
        'staffs.lastName',
        'staffs.phone',
        'staffs.status',
      ]);

    if (conditions.statuses && conditions.statuses?.length) {
      queryBuilder.andWhere('staffs.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    //Query join for staff
    queryBuilder.orderBy('staffs.createdAt', 'DESC');

    /// Cái nào cần quẻy theo role thì join vào ở đây, không thì khi nào cần filter mới join
    queryBuilder
      .leftJoin('staffs.sectors', 'sectors')
      .leftJoin('staffs.companies', 'companies')
      .leftJoin('staffs.businessUnits', 'businessUnits');

    if (conditions.notStaffIds && conditions.notStaffIds.length) {
      queryBuilder.andWhere('staffs.id NOT IN (:...notStaffIds)', {
        notStaffIds: conditions.notStaffIds,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('staffs.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.sectorIds && conditions.sectorIds.length) {
      queryBuilder.andWhere('sectors.id IN (:...sectorIds)', {
        sectorIds: conditions.sectorIds,
      });
    }

    if (conditions.companyIds && conditions.companyIds.length) {
      queryBuilder.andWhere('companies.id IN (:...companyIds)', {
        companyIds: conditions.companyIds,
      });
    }

    if (conditions.departmentIds && conditions.departmentIds.length) {
      queryBuilder.leftJoin('staffs.departments', 'departments');

      queryBuilder.andWhere('departments.id IN (:...departmentIds)', {
        departmentIds: conditions.departmentIds,
      });
    }

    if (conditions.businessUnitIds && conditions.businessUnitIds.length) {
      queryBuilder.andWhere('businessUnits.id IN (:...businessUnitIds)', {
        businessUnitIds: conditions.businessUnitIds,
      });
    }

    if (conditions.functionUnitIds && conditions.functionUnitIds.length) {
      queryBuilder.leftJoin('staffs.functionUnits', 'functionUnits');

      queryBuilder.andWhere('functionUnits.id IN (:...functionUnitIds)', {
        functionUnitIds: conditions.functionUnitIds,
      });
    }

    if (conditions.businessOwnerIds && conditions.businessOwnerIds.length) {
      queryBuilder.leftJoin('staffs.businessOwners', 'businessOwners');

      queryBuilder.andWhere('businessOwners.id IN (:...businessOwnerIds)', {
        businessOwnerIds: conditions.businessOwnerIds,
      });
    }

    if (conditions.positionIds && conditions.positionIds.length) {
      queryBuilder.leftJoin('staffs.position', 'position');

      queryBuilder.andWhere('position.id IN (:...positionIds)', {
        positionIds: conditions.positionIds,
      });
    }

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await this.pagination(queryBuilder, conditions);
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<StaffEntity>,
    jwtPayload: any,
    conditions: GetStaffListDto | GetDetailStaffDto | GetStaffByCodesDto,
  ) {
    if (
      !parseScopes(jwtPayload.scopes, [
        ESectorPermission.CREATE,
        ESectorPermission.EDIT,
      ]) &&
      conditions.sectorCodes?.length
    ) {
      queryBuilder.andWhere(
        '(sectors.id IS NULL OR sectors.code IN (:...sectorCodes))',
        {
          sectorCodes: conditions.sectorCodes,
        },
      );
    }

    if (
      !parseScopes(jwtPayload.scopes, [
        ECompanyPermission.CREATE,
        ECompanyPermission.EDIT,
      ]) &&
      conditions.companyCodes?.length
    ) {
      queryBuilder.andWhere(
        '(companies.id IS NULL OR companies.code IN (:...companyCodes))',
        {
          companyCodes: conditions.companyCodes,
        },
      );
    }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EDepartmentPermission.CREATE,
    //     EDepartmentPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('departments.code IN (:...departmentCodes)', {
    //     departmentCodes: conditions.departmentCodes,
    //   });
    // }

    if (
      !parseScopes(jwtPayload.scopes, [
        EBusinessUnitPermission.CREATE,
        EBusinessUnitPermission.EDIT,
      ]) &&
      conditions.businessUnitCodes?.length
    ) {
      queryBuilder.andWhere(
        '(businessUnits.id IS NULL OR businessUnits.code IN (:...businessUnitCodes))',
        {
          businessUnitCodes: conditions.businessUnitCodes,
        },
      );
    }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EFunctionUnitPermission.CREATE,
    //     EFunctionUnitPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('functionUnits.code IN (:...functionUnitCodes)', {
    //     functionUnitCodes: conditions.functionUnitCodes,
    //   });
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EBusinessOwnerPermission.CREATE,
    //     EBusinessOwnerPermission.EDIT,
    //   ]) &&
    //   conditions.businessOwnerCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(businessOwners.id IS NULL OR businessOwners.code IN (:...businessOwnerCodes))',
    //     {
    //       businessOwnerCodes: conditions.businessOwnerCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EPositionPermission.CREATE,
    //     EPositionPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('position.code IN (:...positionCodes)', {
    //     positionCodes: conditions.positionCodes,
    //   });
    // }

    return queryBuilder;
  }

  async getStaffByCode(code: string): Promise<StaffModel> {
    const repository = this.getRepository(StaffEntity);
    const detail = await repository.findOne({
      where: { code },
    });
    return detail;
  }

  async getStaffByEmail(email: string): Promise<StaffModel> {
    const repository = this.getRepository(StaffEntity);
    const detail = await repository.findOne({
      where: { email },
    });
    return detail;
  }

  async getDetailStaff(
    conditions: GetDetailStaffDto,
    jwtPayload: any,
  ): Promise<StaffModel> {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.companyCodes = jwtPayload?.companies;
    conditions.departmentCodes = jwtPayload?.departments;
    conditions.businessUnitCodes = jwtPayload?.businessUnits;
    conditions.functionUnitCodes = jwtPayload?.functionUnits;
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    conditions.positionCodes = jwtPayload?.positions;

    const repository = this.getRepository(StaffEntity);
    let queryBuilder = repository
      .createQueryBuilder('staff')
      .select([
        'staff.id',
        'staff.code',
        'staff.firstName',
        'staff.lastName',
        'staff.email',
        'staff.phone',
        'staff.status',
        'staff.poCreatorId',
        'staff.purchaserId',
      ]);

    queryBuilder.where('staff.id = :id', { id: conditions.staffId });

    ///Query join for staff
    queryBuilder
      .leftJoin('staff.sectors', 'sectors')
      .addSelect([
        'sectors.id',
        'sectors.code',
        'sectors.name',
        'sectors.status',
      ]);
    queryBuilder
      .leftJoin('staff.companies', 'companies')
      .addSelect([
        'companies.id',
        'companies.code',
        'companies.name',
        'companies.status',
        'companies.description',
      ]);
    queryBuilder
      .leftJoin('staff.departments', 'departments')
      .addSelect([
        'departments.id',
        'departments.code',
        'departments.name',
        'departments.status',
        'departments.description',
      ]);
    queryBuilder
      .leftJoin('staff.functionUnits', 'functionUnits')
      .addSelect([
        'functionUnits.id',
        'functionUnits.code',
        'functionUnits.name',
        'functionUnits.status',
        'functionUnits.description',
      ]);
    queryBuilder
      .leftJoin('staff.businessUnits', 'businessUnits')
      .addSelect([
        'businessUnits.id',
        'businessUnits.code',
        'businessUnits.name',
        'businessUnits.status',
        'businessUnits.description',
      ]);
    queryBuilder
      .leftJoin('staff.businessOwners', 'businessOwners')
      .addSelect([
        'businessOwners.id',
        'businessOwners.code',
        'businessOwners.name',
        'businessOwners.status',
        'businessOwners.description',
      ]);
    queryBuilder
      .leftJoin('staff.position', 'position')
      .addSelect([
        'position.id',
        'position.code',
        'position.name',
        'position.status',
        'position.description',
      ]);
    queryBuilder
      .leftJoinAndSelect('staff.managerHierarchies', 'managerHierarchies')
      .leftJoinAndSelect('managerHierarchies.manager', 'manager');
    queryBuilder
      .leftJoin('staff.purchasers', 'purchasers')
      .addSelect([
        'purchasers.id',
        'purchasers.code',
        'purchasers.firstName',
        'purchasers.lastName',
        'purchasers.email',
        'purchasers.phone',
      ])
      .leftJoin('staff.poCreator', 'poCreator')
      .addSelect([
        'poCreator.id',
        'poCreator.code',
        'poCreator.firstName',
        'poCreator.lastName',
        'poCreator.email',
        'poCreator.phone',
      ]);

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    const detail = await queryBuilder.getOne();
    return detail;
  }

  async deleteStaff(staffId: string): Promise<void> {
    const repository = this.getRepository(StaffEntity);

    await repository.softDelete({ id: staffId });
  }

  async getStaffByCodes(
    conditions: GetStaffByCodesDto,
    jwtPayload: any,
  ): Promise<StaffModel[]> {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.companyCodes = jwtPayload?.companies;
    conditions.departmentCodes = jwtPayload?.departments;
    conditions.businessUnitCodes = jwtPayload?.businessUnits;
    conditions.functionUnitCodes = jwtPayload?.functionUnits;
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    conditions.positionCodes = jwtPayload?.positions;

    const repository = this.getRepository(StaffEntity);
    let queryBuilder = repository.createQueryBuilder('staff');

    ///Query join for staff
    queryBuilder.leftJoinAndSelect('staff.sectors', 'sectors');
    queryBuilder.leftJoinAndSelect('staff.companies', 'companies');
    queryBuilder.leftJoinAndSelect('staff.departments', 'departments');
    queryBuilder.leftJoinAndSelect('staff.functionUnits', 'functionUnits');
    queryBuilder.leftJoinAndSelect('staff.businessUnits', 'businessUnits');
    queryBuilder.leftJoinAndSelect('staff.businessOwners', 'businessOwners');
    queryBuilder.leftJoinAndSelect('staff.position', 'position');
    queryBuilder
      .leftJoinAndSelect('staff.managerHierarchies', 'managerHierarchies')
      .leftJoinAndSelect('managerHierarchies.manager', 'manager');

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    } else {
      if (!conditions.codes?.length) {
        return [];
      } else {
        queryBuilder.where('staff.code IN (:...codes)', {
          codes: conditions.codes,
        });
      }
    }

    const staffs = await queryBuilder.getMany();
    return staffs;
  }

  async getStaffByIds(
    conditions: GetStaffListDto,
    jwtPayload: any,
  ): Promise<StaffModel[]> {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.companyCodes = jwtPayload?.companies;
    conditions.departmentCodes = jwtPayload?.departments;
    conditions.businessUnitCodes = jwtPayload?.businessUnits;
    conditions.functionUnitCodes = jwtPayload?.functionUnits;
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    conditions.positionCodes = jwtPayload?.positions;

    const repository = this.getRepository(StaffEntity);
    let queryBuilder = repository.createQueryBuilder('staff');

    ///Query join for staff
    queryBuilder.leftJoin('staff.sectors', 'sectors');
    queryBuilder.leftJoin('staff.companies', 'companies');
    queryBuilder.leftJoin('staff.departments', 'departments');
    queryBuilder.leftJoin('staff.functionUnits', 'functionUnits');
    queryBuilder.leftJoin('staff.businessUnits', 'businessUnits');
    queryBuilder.leftJoin('staff.businessOwners', 'businessOwners');
    queryBuilder.leftJoin('staff.position', 'position');

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('staff.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.statuses) {
      queryBuilder.andWhere('staff.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    return await queryBuilder.getMany();
  }

  async getStaffByEmails(
    conditions: GetStaffByEmailsDto,
    jwtPayload: any,
  ): Promise<StaffModel[]> {
    // conditions.sectorCodes = jwtPayload?.sectors;
    // conditions.companyCodes = jwtPayload?.companies;
    // conditions.departmentCodes = jwtPayload?.departments;
    // conditions.businessUnitCodes = jwtPayload?.businessUnits;
    // conditions.functionUnitCodes = jwtPayload?.functionUnits;
    // conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    // conditions.positionCodes = jwtPayload?.positions;

    const repository = this.getRepository(StaffEntity);
    let queryBuilder = repository.createQueryBuilder('staff');

    ///Query join for staff
    // queryBuilder.leftJoinAndSelect('staff.sectors', 'sectors');
    // queryBuilder.leftJoinAndSelect('staff.companies', 'companies');
    // queryBuilder.leftJoinAndSelect('staff.departments', 'departments');
    // queryBuilder.leftJoinAndSelect('staff.functionUnits', 'functionUnits');
    // queryBuilder.leftJoinAndSelect('staff.businessUnits', 'businessUnits');
    // queryBuilder.leftJoinAndSelect('staff.businessOwners', 'businessOwners');
    // queryBuilder.leftJoinAndSelect('staff.position', 'position');
    // queryBuilder
    //   .leftJoinAndSelect('staff.managerHierarchies', 'managerHierarchies')
    //   .leftJoinAndSelect('managerHierarchies.manager', 'manager');

    if (!conditions.emails?.length) {
      return [];
    } else {
      queryBuilder.andWhere('staff.email IN (:...emails)', {
        emails: conditions.emails,
      });
    }

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    const staffs = await queryBuilder.getMany();
    return staffs;
  }

  async approverByPosition(
    conditions: GetApproverByPositionDto,
  ): Promise<StaffModel[]> {
    const repository = this.getRepository(StaffEntity);
    const queryBuilder = repository
      .createQueryBuilder('staffs')
      .select([
        'staffs.id',
        'staffs.email',
        'staffs.code',
        'staffs.firstName',
        'staffs.lastName',
        'staffs.status',
      ]);

    queryBuilder
      .innerJoin('staffs.position', 'position')
      .addSelect(['position.id', 'position.name']);

    queryBuilder.andWhere(
      '(staffs.status IN (:...statuses) AND position.id IN (:...positionIds)) OR (staffs.status IN (:...statuses) AND position.id IN (:...positionIds) AND staffs.id IN (:...staffIds))',
      {
        statuses: conditions.statuses || [EStatus.ACTIVE],
        positionIds: conditions.positionIds || [null],
        staffIds: conditions.staffIds?.length ? conditions.staffIds : [null],
      },
    );

    queryBuilder.orderBy('staffs.createdAt', 'DESC');

    return await queryBuilder.getMany();
  }
}
