import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import * as moment from 'moment';
import { DataSource, In, SelectQueryBuilder } from 'typeorm';
import { ActualSpendingForReportDto } from '../../controller/actual-spending/dtos/actual-spending-for-report.dto';
import { CreateActualSpendingDto } from '../../controller/actual-spending/dtos/create-actual-spending.dto';
import { GetActualSpendingListDto } from '../../controller/actual-spending/dtos/get-list-actual-spending.dto';
import { UpdateActualSpendingDto } from '../../controller/actual-spending/dtos/update-actual-spending.dto';
import { EBudgetType } from '../../domain/config/enums/budget.enum';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { ActualSpendingModel } from '../../domain/model/actual-spending.model';
import { IActualSpendingRepository } from '../../domain/repositories/actual-spending.repository';
import { parseScopes } from '../../utils/common';
import {
  EActualSpendingPermission,
  EBusinessOwnerPermission,
  EBusinessUnitPermission,
  ECompanyPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { ActualSpendingEntity } from '../entities/actual-spending.entity';
import { BaseRepository } from './base.repository';
import { GetActualSpendingListForSyncSapDto } from '../../controller/actual-spending/dtos/get-list-actual-spending-for-sync-sap.dto';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ActualSpendingRepository
  extends BaseRepository
  implements IActualSpendingRepository
{
  constructor(
    private myDataSource: DataSource,
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
    this.myDataSource = dataSource;
  }

  async findExistingRecords(conditions: any): Promise<ActualSpendingModel[]> {
    const repository = this.getRepository(ActualSpendingEntity);

    // Xử lý điều kiện truy vấn động
    const queryBuilder = repository.createQueryBuilder('actualSpending');

    if (
      conditions.invoiceConditions?.length &&
      conditions.assetConditions?.length &&
      conditions.functionalAreaConditions?.length
    ) {
      return [];
    }

    if (conditions.invoiceConditions?.length) {
      queryBuilder.orWhere(
        '(actualSpending.documentNumber IN (:...docNumbers) AND actualSpending.postingDate IN (:...postingDates) AND actualSpending.entryDate IN (:...entryDates))',
        {
          docNumbers: conditions.invoiceConditions.map((c) => c.documentNumber),
          postingDates: conditions.invoiceConditions.map((c) => c.postingDate),
          entryDates: conditions.invoiceConditions.map((c) => c.entryDate),
        },
      );
    }

    if (conditions.assetConditions?.length) {
      queryBuilder.orWhere(
        '(actualSpending.assetCode IN (:...assetCodes) AND actualSpending.postingDate IN (:...postingDates))',
        {
          assetCodes: conditions.assetConditions.map((c) => c.assetCode),
          postingDates: conditions.assetConditions.map((c) => c.postingDate),
        },
      );
    }

    if (conditions.functionalAreaConditions?.length) {
      queryBuilder.orWhere(
        '(actualSpending.documentNumber IN (:...docNumbers) AND actualSpending.postingDate IN (:...postingDates))',
        {
          docNumbers: conditions.functionalAreaConditions.map(
            (c) => c.documentNumber,
          ),
          postingDates: conditions.functionalAreaConditions.map(
            (c) => c.postingDate,
          ),
        },
      );
    }

    return queryBuilder.getMany();
  }

  async createActualSpendingList(
    createActualSpendingDtos: CreateActualSpendingDto[],
  ): Promise<ActualSpendingModel[]> {
    const repository = this.getRepository(ActualSpendingEntity);

    const entities = repository.create(createActualSpendingDtos);

    return await repository.save(entities);
  }

  async updateActualSpendingList(
    updateActualSpendingDtos: UpdateActualSpendingDto[],
  ): Promise<ActualSpendingModel[]> {
    const repository = this.getRepository(ActualSpendingEntity);

    const entities = repository.create(updateActualSpendingDtos);

    return await repository.save(entities);
  }

  async getDetailActualSpending(
    actualSpendingId: string,
    jwtPayload: any,
  ): Promise<ActualSpendingModel> {
    const repository = this.getRepository(ActualSpendingEntity);

    const queryBuilder = repository
      .createQueryBuilder('actual')
      // .leftJoin('actual.budgetCode', 'budgetCode')
      // .leftJoin('budgetCode.businessOwner', 'businessOwner')
      // .leftJoin('budgetCode.cost', 'cost')
      .leftJoin('actual.company', 'company')
      .leftJoin('actual.costCenter', 'costCenter')
      .leftJoin('costCenter.sector', 'sector')
      .leftJoin('actual.bu', 'bu')
      .leftJoin('actual.supplier', 'supplier')
      .leftJoin('actual.currency', 'currency')
      .leftJoin('actual.localCurrency', 'localCurrency');

    const select = [
      'sector.id',
      'sector.code',
      'sector.name',
      'company.id',
      'company.code',
      'company.name',
      'bu.id',
      'bu.code',
      'bu.name',
      // 'businessOwner.id',
      // 'businessOwner.code',
      // 'businessOwner.name',
      'costCenter.id',
      'costCenter.code',
      'costCenter.name',
      'costCenter.note2',
      'costCenter.note3',
      'costCenter.note4',
      // 'budgetCode.id',
      // 'budgetCode.code',
      // 'budgetCode.name',
      // 'budgetCode.budgetType',
      'supplier.id',
      'supplier.code',
      'supplier.name',
      'actual.poSapId',
      'actual.docDate',
      'actual.postingDate',
      'actual.entryDate',
      'actual.paymentDoc',
      'actual.eInvoiceNumber',
      'actual.documentNumber',
      'actual.inventoryDoc',
      'actual.invoiceNumber',
      // 'actual.transactionType',
      'actual.glAccount',
      'actual.taxCode',
      'actual.taxRate',
      'actual.docAmount',
      'actual.receiverCode',
      'actual.receiverName',
      'actual.profitCenter',
      'actual.profitCenterDescription',
      'actual.profitCenterGroup',
      'actual.profitCenterGroupDescription',
      'actual.status',
      'actual.actualType',
      //CAPEX
      // 'actual.fiscalYear',
      // 'actual.periodMonth',
      // 'actual.periodYear',
      'actual.internalOrder',
      'actual.internalOrderName',
      //OPEX
      // 'cost.id',
      // 'cost.code',
      // 'cost.name',
      // 'cost.groupCost',
      'actual.documentType',
      'actual.invocieBusinessTransaction',
      'actual.paymentDate',
      'actual.paymentBusinessTransaction',
      'actual.payementDocType',
      'actual.inventoryDocDate',
      'actual.poItem',
      'actual.poDate',
      'actual.internalOrderType',
      'actual.costCenterName',
      'actual.functionalArea',
      'actual.functionalAreaName',
      'actual.taxCodeName',
      'actual.docPaymentAmount',
      'actual.localCurrencyPaymentAmount',
      'actual.debitCreditInd',
      'actual.accountType',
      'actual.description',
      'actual.note',
      'actual.companyCode',
      'actual.costCenterCode',
      'actual.buCode',
      'actual.currencyCode',
      'actual.localCurrencyCode',
      'actual.supplierCode',
    ];

    queryBuilder
      .select(select)
      .andWhere('actual.id = :id', { id: actualSpendingId });

    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [EActualSpendingPermission.IMPORT])
    ) {
      this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetActualSpendingListDto(),
      );
    }

    return await queryBuilder.getOne();
  }

  async getListActualSpending(
    conditions: GetActualSpendingListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<ActualSpendingModel>> {
    const repository = this.getRepository(ActualSpendingEntity);
    const queryBuilder = repository
      .createQueryBuilder('actual')
      .leftJoin('actual.budgetCode', 'budgetCode')
      .leftJoin('budgetCode.businessOwner', 'businessOwner')
      // .leftJoin('budgetCode.cost', 'cost')
      .leftJoin('actual.company', 'company')
      .leftJoin('actual.costCenter', 'costCenter')
      .leftJoin('costCenter.sector', 'sector')
      .leftJoin('actual.bu', 'bu')
      .leftJoin('actual.supplier', 'supplier')
      .leftJoin('actual.currency', 'currency')
      .leftJoin('currency.currencyExchanges', 'currencyExchanges')
      .leftJoin('actual.localCurrency', 'localCurrency')
      .leftJoin('localCurrency.currencyExchanges', 'localCurrencyExchanges');

    let select = [
      'company.id',
      'company.code',
      'company.name',
      /* BU */
      'bu.id',
      'bu.code',
      'bu.name',

      'actual.id',
      'actual.internalOrder', //Để lấy khối chủ sở hữu
      'actual.functionalArea',
      'actual.receiverCode',
      'actual.receiverName',
      'actual.profitCenter',
      'actual.profitCenterDescription',
      'actual.profitCenterGroup',
      'actual.profitCenterGroupDescription',
      'actual.entryDate',
      'actual.documentNumber',
      'actual.invoiceNumber',
      'actual.eInvoiceNumber',
      'actual.docDate',
      'actual.postingDate',
      'actual.documentType',
      'actual.invocieBusinessTransaction',
      'actual.inventoryDoc',
      'actual.inventoryDocDate',
      'actual.poSapId',
      'actual.poItem',
      'actual.poDate',
      'actual.internalOrderType',
      'actual.actualType',
      'actual.companyCode',
      'actual.costCenterCode',
      'actual.buCode',
      'actual.currencyCode',
      'actual.localCurrencyCode',
      /* Cost Center */
      'costCenter.id',
      'costCenter.code',
      'costCenter.name',
      /* Budget Code */
      'budgetCode.id',
      'budgetCode.code',
      'budgetCode.name',
      /* Business Owner */
      'businessOwner.id',
      'businessOwner.code',
      'businessOwner.name',

      'actual.costCenterName',
      /* Supplier */
      'supplier.id',
      'supplier.code',
      'supplier.name',

      'actual.supplierCode',
      'actual.supplierName',
      'actual.glAccount',
      'actual.taxCode',
      'actual.taxCodeName',
      'actual.taxRate',
      'actual.docAmount',
      /* Currency */
      'currency.id',
      'currency.currencyCode',
      'currency.description',
      'currencyExchanges.id',
      'currencyExchanges.effectiveStartDate',
      'currencyExchanges.effectiveEndDate',
      'currencyExchanges.exchangeRate',

      /* Local Currency */
      'localCurrency.id',
      'localCurrency.currencyCode',
      'localCurrency.description',
      'localCurrencyExchanges.id',
      'localCurrencyExchanges.effectiveStartDate',
      'localCurrencyExchanges.effectiveEndDate',
      'localCurrencyExchanges.exchangeRate',

      'actual.localCurrencyAmount',
      'actual.exchangeRate',
      'actual.debitCreditInd',
      'actual.accountType',
      'actual.description',
      'actual.note',
      'actual.status',
    ];

    // Filter theo ngày đầu tiên của năm (YYYY) đến ngày cuối cùng của tháng (MM) của năm đó (YYYY)
    const from = conditions.period
      ? moment(conditions.period, 'YYYY-MM')
          .startOf('year')
          .format('YYYY-MM-DD')
      : moment().startOf('year').format('YYYY-MM-DD');
    const to = conditions.period
      ? moment(conditions.period, 'YYYY-MM').endOf('month').format('YYYY-MM-DD')
      : moment().endOf('month').format('YYYY-MM-DD');

    queryBuilder.andWhere('actual.budgetCodeId IS NOT NULL');

    if (conditions.budgetCodeType == EBudgetType.CAPEX) {
      select = [
        ...select,
        'actual.paymentDoc',
        'actual.paymentDate',
        'actual.paymentBusinessTransaction',
        'actual.payementDocType',
        'actual.internalOrderName',
        'actual.docPaymentAmount',
        'actual.localCurrencyPaymentAmount',
      ];

      queryBuilder
        .andWhere(
          "actual.internalOrder IS NOT NULL AND actual.internalOrder != ''",
        )
        .andWhere(
          '(actual.postingDate BETWEEN :from AND :to OR actual.paymentDate BETWEEN :from AND :to)',
          {
            from,
            to,
          },
        );

      if (
        conditions.businessOwnerIds?.length &&
        conditions.budgetCodeCodes?.length
      ) {
        queryBuilder.andWhere('actual.internalOrder IN (:...budgetCodeCodes)', {
          budgetCodeCodes: conditions.budgetCodeCodes,
        });
      }
    } else if (conditions.budgetCodeType == EBudgetType.OPEX) {
      select = [
        ...select,
        'actual.functionalArea',
        'actual.functionalAreaName',
      ];

      queryBuilder
        .andWhere(
          "actual.functionalArea IS NOT NULL AND actual.functionalArea != ''",
        )
        .andWhere('actual.postingDate BETWEEN :from AND :to', {
          from,
          to,
        });

      if (
        conditions.businessOwnerIds?.length &&
        conditions.budgetCodeCodes?.length
      ) {
        queryBuilder.andWhere(
          'actual.functionalArea IN (:...budgetCodeCodes)',
          {
            budgetCodeCodes: conditions.budgetCodeCodes,
          },
        );
      }
    }

    queryBuilder.select(select);

    if (conditions.budgetCodeType) {
      queryBuilder.andWhere('budgetCode.budgetType = :budgetCodeType', {
        budgetCodeType: conditions.budgetCodeType,
      });
    }

    // if (conditions.transactionTypes?.length) {
    //   queryBuilder.andWhere(
    //     'actual.transactionType IN (:...transactionTypes)',
    //     {
    //       transactionTypes: conditions.transactionTypes,
    //     },
    //   );
    // }

    if (conditions.statuses?.length) {
      queryBuilder.andWhere('actual.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.budgetCodeIds?.length) {
      queryBuilder.andWhere('actual.budgetCodeId IN (:...budgetCodeIds)', {
        budgetCodeIds: conditions.budgetCodeIds,
      });
    }

    if (conditions.costCenterIds?.length) {
      queryBuilder.andWhere('actual.costCenterId IN (:...costCenterIds)', {
        costCenterIds: conditions.costCenterIds,
      });
    }

    if (conditions.businessUnitIds?.length) {
      queryBuilder.andWhere('bu.id IN (:...businessUnitIds)', {
        businessUnitIds: conditions.businessUnitIds,
      });
    }

    // if (conditions.sectorIds?.length) {
    //   queryBuilder.andWhere('sector.id IN (:...sectorIds)', {
    //     sectorIds: conditions.sectorIds,
    //   });
    // }

    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        EActualSpendingPermission.CREATE,
        EActualSpendingPermission.EDIT,
      ])
    ) {
      this.queryWithDataRole(queryBuilder, jwtPayload, conditions);
    }

    return await this.pagination(queryBuilder, {
      ...conditions,
      from: null,
      to: null,
    });
  }

  async updateActualSpending(
    updateActualSpendingDto: UpdateActualSpendingDto,
  ): Promise<ActualSpendingModel> {
    const queryRunner = this.myDataSource.createQueryRunner();
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      const actualSpending = await queryRunner.manager.save(
        ActualSpendingEntity,
        {
          id: updateActualSpendingDto.id,
          status: updateActualSpendingDto.status,
          updatedBy: updateActualSpendingDto.updatedBy,
        },
      );

      await queryRunner.commitTransaction();

      return actualSpending;
    } catch (err) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<ActualSpendingEntity>,
    jwtPayload: any,
    conditions: GetActualSpendingListDto,
  ) {
    conditions.companyCodes = jwtPayload?.companies;
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.businessUnitCodes = jwtPayload?.businessUnits;

    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        ECompanyPermission.CREATE,
        ECompanyPermission.EDIT,
      ]) &&
      conditions.companyCodes?.length
    ) {
      queryBuilder.andWhere(
        '(company.id IS NULL OR company.code IN (:...companyCodes))',
        {
          companyCodes: conditions.companyCodes,
        },
      );
    }

    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        EBusinessOwnerPermission.CREATE,
        EBusinessOwnerPermission.EDIT,
      ])
    ) {
      queryBuilder.andWhere(
        '(businessOwner.id IS NULL OR businessOwner.code IN (:...businessOwnerCodes))',
        {
          businessOwnerCodes: conditions.businessOwnerCodes,
        },
      );
    }

    // if (
    //   !jwtPayload?.isSuperAdmin &&
    //   !parseScopes(jwtPayload?.scopes, [
    //     ESectorPermission.CREATE,
    //     ESectorPermission.EDIT,
    //   ]) &&
    //   conditions.sectorCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(sector.id IS NULL OR sector.code IN (:...sectorCodes))',
    //     {
    //       sectorCodes: conditions.sectorCodes,
    //     },
    //   );
    // }

    // if (
    //   !jwtPayload?.isSuperAdmin &&
    //   !parseScopes(jwtPayload?.scopes, [
    //     EBusinessUnitPermission.CREATE,
    //     EBusinessUnitPermission.EDIT,
    //   ]) &&
    //   conditions.businessUnitCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(bu.id IS NULL OR bu.code IN (:...businessUnitCodes))',
    //     {
    //       businessUnitCodes: conditions.businessUnitCodes,
    //     },
    //   );
    // }

    return queryBuilder;
  }

  async getListActualSpendingById(
    ids: string[],
  ): Promise<ActualSpendingModel[]> {
    const repository = this.getRepository(ActualSpendingEntity);

    return await repository.find({ where: { id: In(ids) } });
  }

  async actualSpendingForReport(
    conditions: ActualSpendingForReportDto,
  ): Promise<ActualSpendingModel[]> {
    const repository = this.getRepository(ActualSpendingEntity);
    const queryBuilder = repository
      .createQueryBuilder('actual')
      // .leftJoin('budgetCode.businessOwner', 'businessOwner')
      .leftJoin('actual.budgetCode', 'budgetCode')
      .leftJoin('actual.company', 'company')
      .leftJoin('actual.costCenter', 'costCenter')
      .leftJoin('costCenter.sector', 'sector')
      .leftJoin('actual.bu', 'bu')
      .leftJoin('actual.supplier', 'supplier')
      .leftJoin('actual.currency', 'currency')
      .leftJoin('currency.currencyExchanges', 'currencyExchanges')
      .leftJoin('actual.localCurrency', 'localCurrency')
      .leftJoin('localCurrency.currencyExchanges', 'localCurrencyExchanges');

    const select = [
      'sector.id',
      'sector.code',
      'sector.name',
      'company.id',
      'company.code',
      'company.name',
      'bu.id',
      'bu.code',
      'bu.name',
      // 'businessOwner.id',
      // 'businessOwner.code',
      // 'businessOwner.name',
      'costCenter.id',
      'costCenter.code',
      'costCenter.name',
      'costCenter.note2',
      'costCenter.note3',
      'costCenter.note4',
      'budgetCode.id',
      'budgetCode.code',
      'budgetCode.name',
      'budgetCode.budgetType',
      'supplier.id',
      'supplier.code',
      'supplier.name',
      'actual.id',
      'actual.poSapId',
      'actual.docDate',
      'actual.postingDate',
      'actual.entryDate',
      'actual.paymentDoc',
      'actual.eInvoiceNumber',
      'actual.documentNumber',
      'actual.inventoryDoc',
      'actual.invoiceNumber',
      'actual.companyCode',
      'actual.costCenterCode',
      'actual.buCode',
      'actual.currencyCode',
      'actual.localCurrencyCode',
      'actual.supplierCode',
      // 'actual.transactionType',
      'actual.glAccount',
      'actual.taxCode',
      'actual.taxRate',
      'actual.docAmount',
      'actual.receiverCode',
      'actual.receiverName',
      'actual.profitCenter',
      'actual.profitCenterDescription',
      'actual.profitCenterGroup',
      'actual.profitCenterGroupDescription',
      'actual.status',
      // 'actual.fiscalYear',
      // 'actual.periodMonth',
      // 'actual.periodYear',
      'actual.currencyId',
      'currency.id',
      'currency.currencyCode',
      'currency.description',
      'currencyExchanges.id',
      'currencyExchanges.effectiveStartDate',
      'currencyExchanges.effectiveEndDate',
      'currencyExchanges.exchangeRate',
      'localCurrency.id',
      'localCurrency.currencyCode',
      'localCurrency.description',
      'localCurrencyExchanges.id',
      'localCurrencyExchanges.effectiveStartDate',
      'localCurrencyExchanges.effectiveEndDate',
      'localCurrencyExchanges.exchangeRate',
      'actual.documentType',
      'actual.invocieBusinessTransaction',
      'actual.paymentDate',
      'actual.paymentBusinessTransaction',
      'actual.payementDocType',
      'actual.inventoryDocDate',
      'actual.poItem',
      'actual.poDate',
      'actual.internalOrderType',
      'actual.costCenterName',
      'actual.functionalArea',
      'actual.functionalAreaName',
      'actual.taxCodeName',
      'actual.docPaymentAmount',
      'actual.localCurrencyPaymentAmount',
      'actual.debitCreditInd',
      'actual.accountType',
      'actual.description',
      'actual.note',
      'actual.internalOrder',
      'actual.internalOrderName',
      'actual.exchangeRate',
    ];

    queryBuilder.andWhere(
      'actual.postingDate BETWEEN :startDate AND :endDate',
      {
        startDate: conditions.postingDateFrom,
        endDate: conditions.postingDateTo,
      },
    );

    queryBuilder.andWhere('actual.status IN (:...status)', {
      status: conditions.statuses,
    });

    if (conditions.internalOrder || conditions.functionalArea) {
      queryBuilder.andWhere(
        'actual.internalOrder = :internalOrder OR actual.functionalArea = :functionalArea',
        {
          internalOrder: conditions.internalOrder,
          functionalArea: conditions.functionalArea,
        },
      );
    }

    queryBuilder.andWhere('budgetCode.code = :budgetCodeCode', {
      budgetCodeCode: conditions.budgetCodeCode,
    });

    // queryBuilder.andWhere('actual.paymentDate IS NULL');

    queryBuilder.select(select);

    return await queryBuilder.getMany();
  }

  async getListActualSpedingForUpdateSyncSap(
    conditions: GetActualSpendingListForSyncSapDto,
  ): Promise<ActualSpendingModel[]> {
    const repository = this.getRepository(ActualSpendingEntity);
    const queryBuilder = repository.createQueryBuilder('actuals');

    if (conditions.statuses?.length) {
      queryBuilder.andWhere('actuals.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.buCode) {
      queryBuilder.andWhere(
        'actuals.buCode = :buCode AND actuals.buId IS NULL',
        {
          buCode: conditions.buCode,
        },
      );
    }

    if (conditions.companyCode) {
      queryBuilder.andWhere(
        'actuals.companyCode = :companyCode AND actuals.companyId IS NULL',
        {
          companyCode: conditions.companyCode,
        },
      );
    }

    if (conditions.costCenterCode) {
      queryBuilder.andWhere(
        'actuals.costCenterCode = :costCenterCode AND actuals.costCenterId IS NULL',
        {
          costCenterCode: conditions.costCenterCode,
        },
      );
    }

    if (conditions.supplierCode) {
      queryBuilder.andWhere(
        'actuals.supplierCode = :supplierCode AND actuals.supplierId IS NULL',
        {
          supplierCode: conditions.supplierCode,
        },
      );
    }

    if (conditions.currencyCode) {
      queryBuilder.andWhere(
        '((actuals.currencyCode = :currencyCode AND actuals.currencyId IS NULL) OR (actuals.localCurrencyCode = :currencyCode AND actuals.localCurrencyId IS NULL))',
        {
          currencyCode: conditions.currencyCode || null,
        },
      );
    }

    if (conditions.budgetCodeCode) {
      if (conditions.budgetType == EBudgetType.CAPEX) {
        queryBuilder.andWhere(
          'actuals.internalOrder = :budgetCodeCode AND actuals.budgetCodeId IS NULL',
          {
            budgetCodeCode: conditions.budgetCodeCode,
          },
        );
      } else if (conditions.budgetType == EBudgetType.OPEX) {
        queryBuilder.andWhere(
          'actuals.functionalArea = :budgetCodeCode AND actuals.budgetCodeId IS NULL',
          {
            budgetCodeCode: conditions.budgetCodeCode,
          },
        );
      } else {
        return [];
      }
    }

    return await queryBuilder.getMany();
  }
}
