import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, In } from 'typeorm';
import { GetListPurchaseOrderDto } from '../../controller/purchase-order/dtos/get-list-purchase-order.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PurchaseOrderModel } from '../../domain/model/purchase_order.model';
import { IPurchaseOrderRepository } from '../../domain/repositories/purchase-order.repository';
import { parseScopes } from '../../utils/common';
import {
  EBusinessUnitPermission,
  EDepartmentPermission,
  EFunctionUnitPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { PurchaseOrderEntity } from '../entities/purchase_order.entity';
import { BaseRepository } from './base.repository';
import { HistoryApproveEntity } from '../entities/history-approve.entity';

@Injectable({ scope: Scope.REQUEST })
export class PurchaseOrderRepository
  extends BaseRepository
  implements IPurchaseOrderRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async getListPurchaseOrder(
    conditions: GetListPurchaseOrderDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PurchaseOrderModel>> {
    const repository = this.getRepository(PurchaseOrderEntity);

    // const select = `SELECT
    //   "po".*,
    //   jsonb_build_object('id', "sector".id , 'name', "sector".name,'code', "sector".code) AS sector,
    //   jsonb_build_object('id', "requester".id , 'firstName', "requester".first_name, 'lastName', "requester".last_name,'code', "requester".code) AS requester,
    //   jsonb_build_object('id', "business_unit".id , 'name', "business_unit".name,'code', "business_unit".code) AS business_unit,
    //   jsonb_build_object('id', "type_po".id , 'name', "type_po".name,'code', "type_po".code) AS type_po,
    //   jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code) AS budget_code,
    //   jsonb_build_object('id', "function_unit".id , 'name', "function_unit".name,'code', "function_unit".code) AS function_unit,
    //   jsonb_build_object('id', "department".id , 'name', "department".name,'code', "department".code) AS department,
    //   (SELECT jsonb_agg(jsonb_build_object(
    //   'id', "details".id ,
    //   'total_amount', "details".total_amount,
    //   'unit_price', "details".unit_price,
    //   'vat', "details".vat,
    //   'total_amount_vat', "details".total_amount_vat,
    //   'estimated_price', "details".estimated_price,
    //   'purchase_price', "details".purchase_price,
    //   'standard_quantity', "details".standard_quantity,
    //   'quantity', "details".quantity,
    //   'sapCreatedQuantity', "details".sap_created_quantity
    //   ))
    //   FROM "purchase_order_details" "details" WHERE "details"."purchaseOrderId" = "po"."id" AND "details".deleted_at IS NULL) AS details
    // `;

    // let from = `
    //   FROM "purchase_orders" "po"
    //   INNER JOIN "purchase_order_details" "details" ON "details"."purchaseOrderId" = "po"."id" AND "details".deleted_at IS NULL
    //   LEFT JOIN "sectors" "sector" ON "sector"."id" = "po"."sector"::UUID
    //   LEFT JOIN "staffs" "requester" ON "requester"."id" = "po"."requester"::UUID
    //   LEFT JOIN "business_unit" "business_unit" ON "business_unit"."id" = "po"."business_unit"::UUID
    //   LEFT JOIN "purchase_order_type" "type_po" ON "type_po"."id" = "po"."type_po"::UUID
    //   LEFT JOIN "process_types" "process_type" ON "process_type"."id" = "po"."process_type"::UUID
    //   LEFT JOIN "budget_code" "budget_code" ON "budget_code"."id" = "po"."budget_code"::UUID
    //   LEFT JOIN "function_unit" "function_unit" ON "function_unit"."id" = "po"."function_unit"::UUID
    //   LEFT JOIN "department" "department" ON "department"."id" = "po"."department"::UUID`;

    // let groupBy = ` GROUP BY "po".id, "sector".id, "requester".id, "business_unit".id, "type_po".id, "budget_code".id, "function_unit".id, "department".id `;

    // let orderBy = `ORDER BY "po"."created_at" DESC `;

    // let where = ` WHERE "po"."created_at" NOTNULL `;

    const select = `SELECT
      "po".*,
      jsonb_build_object('id', "sector".id , 'name', "sector".name,'code', "sector".code) AS sector,
      jsonb_build_object('id', "requester".id , 'firstName', "requester".first_name, 'lastName', "requester".last_name,'code', "requester".code) AS requester,
      jsonb_build_object('id', "business_unit".id , 'name', "business_unit".name,'code', "business_unit".code) AS business_unit,
      jsonb_build_object('id', "type_po".id , 'name', "type_po".name,'code', "type_po".code) AS type_po,
      jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code) AS budget_code,
      jsonb_build_object('id', "function_unit".id , 'name', "function_unit".name,'code', "function_unit".code) AS function_unit,
      jsonb_build_object('id', "department".id , 'name', "department".name,'code', "department".code) AS department,
      jsonb_build_object('id', "currency".id , 'name', "currency".name,'currencyCode', "currency".currency_code) AS currency,
      jsonb_build_object('id', "process_type".id , 'name', "process_type".name,'code', "process_type".code) AS process_type,
      jsonb_build_object('id', "purchase_group".id , 'name', "purchase_group".name,'code', "purchase_group".code) AS purchase_group,
      jsonb_build_object('id', "purchase_org".id , 'name', "purchase_org".name,'code', "purchase_org".code) AS purchase_org,
      (SELECT jsonb_agg(jsonb_build_object('id', "history".id , 'level', "history".level, 'status', "history".status,'name', "history".name,'created_at', "history".created_at,'updated_at', "history".updated_at))
      FROM "history-approve" "history"
      WHERE "history"."purchaseOrderId" = "po"."id") AS history,
      (SELECT jsonb_agg(jsonb_build_object(
      'id', "details".id , 
      'total_amount', "details".total_amount,
      'unit_price', "details".unit_price,
      'unit', "details".unit,
      'material_name', "details".material_name,
      'material_code', "details".material_code,
      'remaining_budget', "details".remaining_budget,
      'budget', "details".budget,
      'created_at', "details".created_at, 
      'budget_id', "details".budget_id, 
      'adjust_budget_id', "details".adjust_budget_id,
      'remaining_actual_budget', "details".remaining_actual_budget,
      'account_assignment', "details".account_assignment,
      'account_gl', "details".account_gl,
      'requested_quantity', "details".requested_quantity,
      'total_converted_amount', "details".total_converted_amount,
      'exchange_rate', "details".exchange_rate,
      'wbs', "details".wbs,
      'property', "details".property,
      'internal_order', "details".internal_order,
      'note', "details".note,
      'inventory_number', "details".inventory_number,
      'delivery_time', "details".delivery_time,
      'vat', "details".vat,
      'total_amount_vat', "details".total_amount_vat,
      'estimated_price', "details".estimated_price,
      'purchase_price', "details".purchase_price,
      'standard_quantity', "details".standard_quantity,
      'quantity', "details".quantity,
      'supplierInfo', "details".supplier_info,
      'pr_reference', "details".pr_reference,
      'pr_detail_id', "details".pr_detail_id,
      'sapCreatedQuantity', "details".sap_created_quantity,
      'material', (SELECT jsonb_build_object('id', "material".id , 'name', "material".name,'code', "material".code) FROM "materials" "material" WHERE "material"."id" = "details"."material_code"::UUID),
      'budget_code', (SELECT jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code) FROM "budget_code" "budget_code" WHERE "budget_code"."id" = "details"."budget_code"::UUID),
      'cost_center', (SELECT jsonb_build_object('id', "cost_center".id , 'name', "cost_center".name,'code', "cost_center".code) FROM "costcenter_subaccount" "cost_center" WHERE "cost_center"."id" = "details"."cost_center"::UUID),
      'supplier', (SELECT jsonb_build_object('id', "supplier".id , 'name', "supplier".name,'code', "supplier".code, 'type', "supplier".type) FROM "suppliers" "supplier" WHERE "supplier"."id" = "details"."supplier"::UUID),
      'pir', (SELECT jsonb_build_object('id', "pir".id , 'purchase_unit', "pir".purchase_unit,'vendor_leadtime', "pir".vendor_leadtime,'regular_purchase_quantity', "pir".regular_purchase_quantity,'minimum_order_quantity', "pir".minimum_order_quantity,'upper_tolerance', "pir".upper_tolerance,'lower_tolerance', "pir".lower_tolerance,'purchase_price', "pir".purchase_price,'over_purchase_unit', "pir".over_purchase_unit) FROM "price_information_records" "pir" WHERE "pir"."id" = "details"."pir"),
      'material_group', (SELECT jsonb_build_object('id', "material_group".id , 'name', "material_group".name,'code', "material_group".code) FROM "material_groups" "material_group" WHERE "material_group"."id" = "details"."material_group"::UUID),
      'currency', (SELECT jsonb_build_object('id', "currency".id , 'name', "currency".name,'currency_code', "currency".currency_code) FROM "currency_unit" "currency" WHERE "currency"."id" = "details"."currency"::UUID),
      'measure_code', (SELECT jsonb_build_object('id', "measure_code".id , 'name', "measure_code".name,'code', "measure_code".code) FROM "measures" "measure_code" WHERE "measure_code"."id" = "details"."measure_code"::UUID)
      ))
      FROM "purchase_order_details" "details" WHERE "details"."purchaseOrderId" = "po"."id" AND "details".deleted_at IS NULL) AS details
    `;

    let from = `
      FROM "purchase_orders" "po"
      INNER JOIN "purchase_order_details" "details" ON "details"."purchaseOrderId" = "po"."id" AND "details".deleted_at IS NULL
      LEFT JOIN "sectors" "sector" ON "sector"."id" = "po"."sector"::UUID
      LEFT JOIN "staffs" "requester" ON "requester"."id" = "po"."requester"::UUID
      LEFT JOIN "business_unit" "business_unit" ON "business_unit"."id" = "po"."business_unit"::UUID
      LEFT JOIN "purchase_order_type" "type_po" ON "type_po"."id" = "po"."type_po"::UUID
      LEFT JOIN "process_types" "process_type" ON "process_type"."id" = "po"."process_type"::UUID
      LEFT JOIN "budget_code" "budget_code" ON "budget_code"."id" = "po"."budget_code"::UUID
      LEFT JOIN "function_unit" "function_unit" ON "function_unit"."id" = "po"."function_unit"::UUID
      LEFT JOIN "currency_unit" "currency" ON "currency"."id" = "po"."currency"::UUID
      LEFT JOIN "purchasing_groups" "purchase_group" ON "purchase_group"."id" = "po"."purchase_group"::UUID
      LEFT JOIN "purchasing_departments" "purchase_org" ON "purchase_org"."id" = "po"."purchase_org"::UUID
      LEFT JOIN "department" "department" ON "department"."id" = "po"."department"::UUID`;

    let groupBy = ` GROUP BY "po".id, "sector".id, "requester".id, "business_unit".id, "type_po".id, "budget_code".id, "function_unit".id, "department".id, "currency".id, "process_type".id, "purchase_group".id, "purchase_org".id `;

    let orderBy = `ORDER BY "po"."created_at" DESC `;

    let where = ` WHERE "po"."created_at" NOTNULL `;

    const parameters = [];

    let parameterIndex = 1; // Bắt đầu từ $1

    if (conditions.crudTemplateActiveTab == 'APPROVED') {
      if (jwtPayload?.email) {
        from += ` INNER JOIN "approval-level" "levels" 
                    ON "levels"."email" = $${parameters.length + 1} 
                    AND "levels"."status" IN ('Approved', 'Rejected') AND "po"."id" = "levels"."purchase_order_id" `;
        parameters.push(jwtPayload?.email);
        parameterIndex++;
      } else {
        return new ResponseDto([], conditions.page, conditions.limit, 0);
      }
    }

    const addCondition = (condition: string, value: any) => {
      if (Array.isArray(value)) {
        // Tạo chuỗi các `$n` tương ứng
        const placeholders = value.map(() => `$${parameterIndex++}`).join(', ');
        where += condition.replace('?', placeholders);

        // Thêm tất cả giá trị vào parameters
        parameters.push(...value);
      } else {
        where += condition.replace('?', `$${parameterIndex}`);
        parameters.push(value);
        parameterIndex++;
      }
    };

    if (conditions.department?.length) {
      addCondition(
        ` AND "po"."department" IN (?)`,
        `${conditions.department.join("', '")}`,
      );
    }

    if (conditions.function_unit?.length) {
      addCondition(
        ` AND "po"."function_unit" IN (?)`,
        `${conditions.function_unit.join("', '")}`,
      );
    }

    if (conditions.type_po?.length) {
      addCondition(
        ` AND "po"."type_po" IN (?)`,
        `${conditions.type_po.join("', '")}`,
      );
    }

    if (conditions.budget_code?.length) {
      addCondition(
        ` AND "po"."budget_code" IN (?)`,
        `${conditions.budget_code.join("', '")}`,
      );
    }

    if (conditions.cost_center?.length) {
      addCondition(
        ` AND "po"."cost_center" IN (?)`,
        `${conditions.cost_center.join("', '")}`,
      );
    }

    if (conditions.display_status_po) {
      addCondition(
        ` AND "po"."display_status_po" IN (?)`,
        conditions.display_status_po,
      );
    }

    if (conditions.state_po?.length) {
      addCondition(` AND "po"."state_po" IN (?)`, conditions.state_po);
    }

    if (conditions.business_unit?.length) {
      addCondition(
        ` AND "po"."business_unit" IN (?)`,
        `${conditions.business_unit.join("', '")}`,
      );
    }

    if (conditions.sector?.length) {
      addCondition(
        ` AND "po"."sector" IN (?)`,
        `${conditions.sector.join("', '")}`,
      );
    }

    if (conditions.idPr && conditions.material_code) {
      addCondition(
        ' AND CAST(details.pr_reference as text) LIKE ? ',
        `%${conditions.idPr}%`,
      );
      addCondition(
        ' AND details.material_code IN (?)',
        `${conditions.material_code.join("', '")}`,
      );
    } else {
      if (conditions.idPr) {
        addCondition(
          ' AND CAST(details.pr_reference as text) LIKE ? ',
          `%${conditions.idPr}%`,
        );
      } else if (conditions.material_code) {
        addCondition(
          ' AND details.material_code IN (?)',
          `${conditions.material_code.join("', '")}`,
        );
      }
    }

    if (conditions.process_type?.length) {
      addCondition(
        ` AND "po"."process_type" IN (?)`,
        `${conditions.process_type.join("', '")}`,
      );
    }

    if (conditions.crudTemplateActiveTab == 'REQUEST') {
      if (jwtPayload?.staffId) {
        addCondition(` AND "po"."requester" = ?`, jwtPayload.staffId);
      } else {
        return new ResponseDto([], conditions.page, conditions.limit, 0);
      }
    }

    if (conditions.crudTemplateActiveTab == 'WAITING') {
      if (jwtPayload?.email) {
        from += ` INNER JOIN "approval-level" "levels" ON  ( NOT EXISTS (
            SELECT 1
            FROM "approval-level" l_prev
            WHERE l_prev.level < levels.level
            AND l_prev.status != 'Approved'
            AND "po"."id" = "l_prev"."purchase_order_id"
          ) OR levels."level" = 1) AND levels.status = 'Pending' AND "po"."id" = "levels"."purchase_order_id" `;

        addCondition(
          ` AND "levels"."email" = ? AND "po"."status_po" IN ('Waiting to process', 'In-Progress')`,
          jwtPayload.email,
        );
      } else {
        return new ResponseDto([], conditions.page, conditions.limit, 0);
      }
    }

    if (conditions.searchString) {
      addCondition(
        ` AND CAST("po"."id" AS TEXT) ILIKE ?`,
        `%${conditions.searchString}%`,
      );
    }
    if (
      conditions.crudTemplateActiveTab != 'REQUEST' &&
      conditions.crudTemplateActiveTab != 'WAITING' &&
      conditions.crudTemplateActiveTab != 'APPROVED'
    ) {
      if (!jwtPayload?.isSuperAdmin) {
        if (
          !parseScopes(jwtPayload?.scopes, [
            ESectorPermission.CREATE,
            ESectorPermission.EDIT,
          ]) &&
          jwtPayload?.sectors?.length
        ) {
          addCondition(` AND "sector".code IN (?)`, jwtPayload.sectors);
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EBusinessUnitPermission.CREATE,
            EBusinessUnitPermission.EDIT,
          ]) &&
          jwtPayload?.businessUnits?.length
        ) {
          addCondition(
            ` AND "business_unit".code IN (?)`,
            jwtPayload.businessUnits,
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EFunctionUnitPermission.CREATE,
            EFunctionUnitPermission.EDIT,
          ]) &&
          jwtPayload?.functionUnits?.length
        ) {
          addCondition(
            ` AND ("function_unit".id IS NULL OR "function_unit".code IN (?))`,
            jwtPayload.functionUnits,
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EDepartmentPermission.CREATE,
            EDepartmentPermission.EDIT,
          ]) &&
          jwtPayload?.departments?.length
        ) {
          addCondition(
            ` AND ("department".id IS NULL OR "department".code IN (?))`,
            jwtPayload.departments,
          );
        }
      }
    }

    const countSelect = ` SELECT COUNT(DISTINCT "po"."id") `;

    const countRecords = await repository.query(
      countSelect + from + where,
      parameters,
    );

    if (conditions.getAll !== 1) {
      //paginate
      const offset = Number(conditions.limit) * (Number(conditions.page) - 1);
      const limit = Number(conditions.limit);

      orderBy += ` LIMIT ${limit} OFFSET ${offset}`;
    }

    const results = await repository.query(
      select + from + where + groupBy + orderBy,
      parameters,
    );

    return new ResponseDto(
      results.map((item) => {
        return { ...item, attachments: item?.files?.split(',') };
      }),
      conditions.page,
      conditions.limit,
      Number(countRecords[0].count),
    );
  }

  async findOne(id: number): Promise<PurchaseOrderModel> {
    const repository = this.getRepository(PurchaseOrderEntity);

    const select = `SELECT
      "po".*,
      jsonb_build_object('id', "sector".id , 'name', "sector".name,'code', "sector".code) AS sector,
      jsonb_build_object('id', "requester".id , 'firstName', "requester".first_name, 'lastName', "requester".last_name,'code', "requester".code) AS requester,
      jsonb_build_object('id', "business_unit".id , 'name', "business_unit".name,'code', "business_unit".code) AS business_unit,
      jsonb_build_object('id', "type_po".id , 'name', "type_po".name,'code', "type_po".code) AS type_po,
      jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code) AS budget_code,
      jsonb_build_object('id', "function_unit".id , 'name', "function_unit".name,'code', "function_unit".code) AS function_unit,
      jsonb_build_object('id', "department".id , 'name', "department".name,'code', "department".code) AS department,
      jsonb_build_object('id', "currency".id , 'name', "currency".name,'currencyCode', "currency".currency_code) AS currency,
      jsonb_build_object('id', "process_type".id , 'name', "process_type".name,'code', "process_type".code) AS process_type,
      jsonb_build_object('id', "purchase_group".id , 'name', "purchase_group".name,'code', "purchase_group".code) AS purchase_group,
      jsonb_build_object('id', "purchase_org".id , 'name', "purchase_org".name,'code', "purchase_org".code) AS purchase_org,
      (SELECT jsonb_agg(jsonb_build_object(
      'id', "details".id , 
      'total_amount', "details".total_amount,
      'unit_price', "details".unit_price,
      'unit', "details".unit,
      'material_name', "details".material_name,
      'material_code', "details".material_code,
      'remaining_budget', "details".remaining_budget,
      'budget', "details".budget,
      'created_at', "details".created_at, 
      'budget_id', "details".budget_id, 
      'adjust_budget_id', "details".adjust_budget_id,
      'remaining_actual_budget', "details".remaining_actual_budget,
      'account_assignment', "details".account_assignment,
      'account_gl', "details".account_gl,
      'requested_quantity', "details".requested_quantity,
      'total_converted_amount', "details".total_converted_amount,
      'exchange_rate', "details".exchange_rate,
      'wbs', "details".wbs,
      'property', "details".property,
      'internal_order', "details".internal_order,
      'note', "details".note,
      'inventory_number', "details".inventory_number,
      'delivery_time', "details".delivery_time,
      'vat', "details".vat,
      'total_amount_vat', "details".total_amount_vat,
      'estimated_price', "details".estimated_price,
      'purchase_price', "details".purchase_price,
      'standard_quantity', "details".standard_quantity,
      'quantity', "details".quantity,
      'supplierInfo', "details".supplier_info,
      'pr_reference', "details".pr_reference,
      'pr_detail_id', "details".pr_detail_id,
      'sapCreatedQuantity', "details".sap_created_quantity,
      'material', (SELECT jsonb_build_object('id', "material".id , 'name', "material".name,'code', "material".code) FROM "materials" "material" WHERE "material"."id" = "details"."material_code"::UUID),
      'budget_code', (SELECT jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code) FROM "budget_code" "budget_code" WHERE "budget_code"."id" = "details"."budget_code"::UUID),
      'cost_center', (SELECT jsonb_build_object('id', "cost_center".id , 'name', "cost_center".name,'code', "cost_center".code) FROM "costcenter_subaccount" "cost_center" WHERE "cost_center"."id" = "details"."cost_center"::UUID),
      'supplier', (SELECT jsonb_build_object('id', "supplier".id , 'name', "supplier".name,'code', "supplier".code, 'type', "supplier".type) FROM "suppliers" "supplier" WHERE "supplier"."id" = "details"."supplier"::UUID),
      'pir', (SELECT jsonb_build_object('id', "pir".id , 'purchase_unit', "pir".purchase_unit,'vendor_leadtime', "pir".vendor_leadtime,'regular_purchase_quantity', "pir".regular_purchase_quantity,'minimum_order_quantity', "pir".minimum_order_quantity,'upper_tolerance', "pir".upper_tolerance,'lower_tolerance', "pir".lower_tolerance,'purchase_price', "pir".purchase_price,'over_purchase_unit', "pir".over_purchase_unit) FROM "price_information_records" "pir" WHERE "pir"."id" = "details"."pir"),
      'material_group', (SELECT jsonb_build_object('id', "material_group".id , 'name', "material_group".name,'code', "material_group".code) FROM "material_groups" "material_group" WHERE "material_group"."id" = "details"."material_group"::UUID),
      'currency', (SELECT jsonb_build_object('id', "currency".id , 'name', "currency".name,'currencyCode', "currency".currency_code) FROM "currency_unit" "currency" WHERE "currency"."id" = "details"."currency"::UUID),
      'measure_code', (SELECT jsonb_build_object('id', "measure_code".id , 'name', "measure_code".name,'code', "measure_code".code) FROM "measures" "measure_code" WHERE "measure_code"."id" = "details"."measure_code"::UUID)
      ))
      FROM "purchase_order_details" "details" WHERE "details"."purchaseOrderId" = "po"."id" AND "details".deleted_at IS NULL) AS details,
      (SELECT jsonb_agg(DISTINCT 
      jsonb_build_object(
      'id', "history".id , 
      'created_at', "history".created_at,
      'email', "history".email,
      'id', "history".id,
      'level', "history".level,
      'name', "history".name,
      'reason', "history".reason,
      'role', "history".role,
      'status', "history".status,
      'updated_at', "history".updated_at,
      'user_id', "history".user_id
      ))
      FROM "history-approve" "history" 
      WHERE "history"."purchaseOrderId" = "po"."id" ) AS history,
      (SELECT jsonb_agg(DISTINCT 
      jsonb_build_object(
      'companyCode', "sapPos".company_code,
      'companyId', "sapPos".company_id,
      'createdAt', "sapPos"."createdAt",
      'createdBy', "sapPos"."createdBy",
      'currencyCode', "sapPos".currency_code,
      'deletedAt', "sapPos"."deletedAt",
      'deletedBy', "sapPos"."deletedBy",
      'ebeln', "sapPos".ebeln,
      'exchangeRate', "sapPos".exchange_rate,
      'id', "sapPos".id,
      'message', "sapPos".message,
      'messageType', "sapPos".message_type,
      'poCreatedAt', "sapPos".po_created_at,
      'poId', "sapPos".po_id,
      'poTypeCode', "sapPos".po_type_code,
      'poTypeId', "sapPos".po_type_id,
      'purchasingDepartmentCode', "sapPos".purchasing_department_code,
      'purchasingDepartmentId', "sapPos".purchasing_department_id,
      'purchasingGroupCode', "sapPos"."purchasing_group_Code",
      'purchasingGroupId', "sapPos".purchasing_group_id,
      'status', "sapPos".status,
      'supplierCode', "sapPos".supplier_code,
      'supplierId', "sapPos".supplier_id,
      'supplierInfo', "sapPos"."supplierInfo",
      'updatedAt', "sapPos"."updatedAt",
      'updatedBy', "sapPos"."updatedBy",
      'items', (SELECT jsonb_agg(DISTINCT 
      jsonb_build_object(
      'accountAssignment', "items".account_assignment,
      'asset', "items".asset,
      'buCode', "items".bu_code,
      'buId', "items".bu_id,
      'costCenterCode', "items".cost_center_code,
      'costCenterId', "items".cost_center_id,
      'createdAt', "items"."createdAt",
      'createdBy', "items"."createdBy",
      'currency', "items".currency,
      'currencyCode', "items".currency_code,
      'deletedAt', "items"."deletedAt",
      'deletedBy', "items"."deletedBy",
      'deliveryDate', "items".delivery_date,
      'eprId', "items".eprno,
      'functionalArea', "items".functional_area,
      'glAccount', "items".gl_account,
      'id', "items".id,
      'internalOrder', "items".internal_order,
      'materialCode', "items".material_code,
      'materialGroupCode', "items".material_group_code,
      'materialGroupId', "items".material_group_id,
      'materialGroupName', "items".material_group_name,
      'materialId', "items".material_id,
      'materialName', "items".material_name,
      'message', "items".message,
      'messageType', "items".message_type,
      'price', "items".price,
      'purchaseOrderDetailId', "items".purchase_order_detail_id,
      'quantity', "items".quantity,
      'sapPurchaseOrderId', "items".sap_purchase_order_id,
      'unit', "items".unit,
      'updatedAt', "items"."updatedAt",
      'updatedBy', "items"."updatedBy",
      'wbs', "items".wbs
      ))
      FROM "sap_purchase_order_items" "items" 
      WHERE "sapPos"."id" = "items"."sap_purchase_order_id" AND "items"."deletedAt" IS NULL)
      ))
      FROM "sap_purchase_orders" "sapPos" 
      WHERE "sapPos"."po_id" = "po"."id" AND "sapPos"."deletedAt" IS NULL ) AS "sapPos"
    `;

    let from = `
      FROM "purchase_orders" "po"
      INNER JOIN "purchase_order_details" "details" ON "details"."purchaseOrderId" = "po"."id" AND "details".deleted_at IS NULL
      LEFT JOIN "sectors" "sector" ON "sector"."id" = "po"."sector"::UUID
      LEFT JOIN "staffs" "requester" ON "requester"."id" = "po"."requester"::UUID
      LEFT JOIN "business_unit" "business_unit" ON "business_unit"."id" = "po"."business_unit"::UUID
      LEFT JOIN "purchase_order_type" "type_po" ON "type_po"."id" = "po"."type_po"::UUID
      LEFT JOIN "process_types" "process_type" ON "process_type"."id" = "po"."process_type"::UUID
      LEFT JOIN "budget_code" "budget_code" ON "budget_code"."id" = "po"."budget_code"::UUID
      LEFT JOIN "function_unit" "function_unit" ON "function_unit"."id" = "po"."function_unit"::UUID
      LEFT JOIN "currency_unit" "currency" ON "currency"."id" = "po"."currency"::UUID
      LEFT JOIN "purchasing_groups" "purchase_group" ON "purchase_group"."id" = "po"."purchase_group"::UUID
      LEFT JOIN "purchasing_departments" "purchase_org" ON "purchase_org"."id" = "po"."purchase_org"::UUID
      LEFT JOIN "department" "department" ON "department"."id" = "po"."department"::UUID`;

    let groupBy = ` GROUP BY "po".id, "sector".id, "requester".id, "business_unit".id, "type_po".id, "budget_code".id, "function_unit".id, "department".id, "currency".id, "process_type".id, "purchase_group".id, "purchase_org".id `;

    let orderBy = `ORDER BY "po"."created_at" DESC `;

    let where = ` WHERE "po"."created_at" NOTNULL `;

    const parameters = [];

    let parameterIndex = 1; // Bắt đầu từ $1

    const addCondition = (condition: string, value: any) => {
      if (Array.isArray(value)) {
        // Tạo chuỗi các `$n` tương ứng
        const placeholders = value.map(() => `$${parameterIndex++}`).join(', ');
        where += condition.replace('?', placeholders);

        // Thêm tất cả giá trị vào parameters
        parameters.push(...value);
      } else {
        where += condition.replace('?', `$${parameterIndex}`);
        parameters.push(value);
        parameterIndex++;
      }
    };

    addCondition(` AND "po"."id" = ?`, `${id}`);

    const limit = Number(1);

    orderBy += ` LIMIT ${limit}`;

    const results = await repository.query(
      select + from + where + groupBy + orderBy,
      parameters,
    );

    const data = results[0];

    const allAdditionalHistory = new Map<number, HistoryApproveEntity>();

    if (data && data?.details?.length > 0) {
      const prReferenceIds = [
        ...new Set(
          (data?.details || [])
            .map((item) => item.pr_reference)
            .filter(Boolean),
        ),
      ];

      if (prReferenceIds && prReferenceIds?.length) {
        const repositoryHistory = this.getRepository(HistoryApproveEntity);
        const queryBuilder = repositoryHistory
          .createQueryBuilder('history')
          .select([
            'id',
            'level',
            'role',
            'user_id',
            'email',
            'status',
            'name',
            'reason',
            'created_at',
            'updated_at',
            '"purchaseRequestId"',
            '"purchaseOrderId"',
          ]);

        queryBuilder.andWhere(
          'history."purchaseRequestId" IN (:...prReferenceIds)',
          {
            prReferenceIds: prReferenceIds,
          },
        );

        const additionalHistory = await queryBuilder.getRawMany();

        additionalHistory.forEach((record) => {
          allAdditionalHistory.set(record.id, record);
        });
      }

      data['history_pr'] = Array.from(allAdditionalHistory.values());
    }

    if (data) {
      data.attachments = data?.files.split(',');
    }

    return data;
  }
}
