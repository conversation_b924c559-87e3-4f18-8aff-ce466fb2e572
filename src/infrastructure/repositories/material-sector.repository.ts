import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, In, Not } from 'typeorm';
import { BaseRepository } from './base.repository';
import { IMaterialSectorRepository } from '../../domain/repositories/material-sector.repository';
import { MaterialSectorModel } from '../../domain/model/material-sector.model';
import { MaterialSectorEntity } from '../entities/material-sector.entity';

@Injectable({ scope: Scope.REQUEST })
export class MaterialSectorRepository
  extends BaseRepository
  implements IMaterialSectorRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createMaterialSectors(
    conditions: MaterialSectorModel[],
  ): Promise<MaterialSectorModel[]> {
    const repository = this.getRepository(MaterialSectorEntity);
    const newMaterialSectors = repository.create(conditions);

    return await repository.save(newMaterialSectors);
  }

  async updateMaterialSectors(
    conditions: MaterialSectorModel[],
  ): Promise<MaterialSectorModel[]> {
    const repository = this.getRepository(MaterialSectorEntity);
    const updateMaterialSectors = repository.create(conditions);
    return await repository.save(updateMaterialSectors);
  }

  async deleteMaterialSectors(materialId: string): Promise<void> {
    const repository = this.getRepository(MaterialSectorEntity);
    const queryBuilder = repository.createQueryBuilder('materialSectors');
    queryBuilder.leftJoinAndSelect('materialSectors.material', 'material');
    queryBuilder.andWhere('material.id = :materialId', { materialId });
    await queryBuilder.delete().execute();
  }

  async getMaterialSectorByMaterialId(
    materialId: string,
  ): Promise<MaterialSectorModel[]> {
    const repository = this.getRepository(MaterialSectorEntity);
    const queryBuilder = repository.createQueryBuilder('materialSectors');
    queryBuilder
      .leftJoin('materialSectors.sector', 'sector')
      .addSelect(['sector.id']);
    queryBuilder.leftJoin('materialSectors.material', 'material');
    queryBuilder.andWhere('material.id = :materialId', { materialId });

    return await queryBuilder.getMany();
  }
}
