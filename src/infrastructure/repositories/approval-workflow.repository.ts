import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource } from 'typeorm';
import { CreateApprovalWorkflowDto } from '../../controller/process/dtos/create-approval-workflow.dto';
import { ApprovalWorkflowModel } from '../../domain/model/approval-workflow.model';
import { IApprovalWorkflowRepository } from '../../domain/repositories/approval-workflow.repository';
import { ApprovalWorkflowEntity } from '../entities/approval-workflow.entity';
import { BaseRepository } from './base.repository';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ApprovalWorkflowRepository
  extends BaseRepository
  implements IApprovalWorkflowRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createApprovalWorkflow(
    data: CreateApprovalWorkflowDto,
  ): Promise<ApprovalWorkflowModel> {
    const repository = this.getRepository(ApprovalWorkflowEntity);

    const approvalWorkflow = repository.create(data);

    return await repository.save(approvalWorkflow);
  }

  async deleteApprovalWorkflowByParentProcessId(
    parentProcessId: string,
  ): Promise<void> {
    const repository = this.getRepository(ApprovalWorkflowEntity);

    await repository.softDelete({ parentProcessId });
  }

  async getApprovalWorkflowByParentProcessId(
    parentProcessId: string,
  ): Promise<ApprovalWorkflowModel[]> {
    const repository = this.getRepository(ApprovalWorkflowEntity);

    return await repository.find({ where: { parentProcessId } });
  }
}
