import {
  Between,
  DataSource,
  EntityManager,
  FindManyOptions,
  FindOptionsWhere,
  ILike,
  LessThanOrEqual,
  MoreThanOrEqual,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { PaginationDto } from '../../domain/dtos/pagination.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { ENTITY_MANAGER_KEY } from '../config/typeorm/tracsaction.interceptor';

export const MAX_LIMIT = 100;
export const DEFAULT_LIMIT = 15;
export class BaseRepository {
  constructor(
    private dataSource: DataSource,
    private request: Request,
  ) {}

  protected getRepository<T>(entityCls: new () => T): Repository<T> {
    const entityManager: EntityManager =
      this.request[ENTITY_MANAGER_KEY]?.manager ?? this.dataSource.manager;

    return entityManager.getRepository(entityCls);
  }

  public async pagination<T>(
    repo: SelectQueryBuilder<T> | Repository<T>,
    options: Partial<PaginationDto> & { tableName?: string },
    findParams?: FindOptionsWhere<T> | FindManyOptions<T>,
  ): Promise<ResponseDto<T>> {
    let results = [];
    let total = 0;

    options = await this.prepareOptions(options);

    if (repo instanceof SelectQueryBuilder) {
      if (!options.tableName) {
        options.tableName = repo.alias;
      }
      [results, total] = await this._paginateQueryBuilder<T>(
        repo,
        options,
        findParams,
      );
    } else {
      [results, total] = await this._paginateRepository(
        repo,
        options,
        findParams,
      );
    }

    return new ResponseDto<T>(results, options.page, options.limit, total);
  }

  async prepareOptions(
    options: Partial<PaginationDto> & { tableName?: string },
  ): Promise<Partial<PaginationDto> & { tableName?: string }> {
    if (typeof options.limit === 'string') {
      options.limit = parseInt(options.limit);
    }
    if (typeof options.limit === 'string') {
      options.limit = parseInt(options.limit);
    }
    let limit: number = options.limit || DEFAULT_LIMIT;
    if (options.limit > MAX_LIMIT) {
      limit = MAX_LIMIT;
    }

    return {
      ...options,
      limit,
      page: options.page < 1 ? 0 : options.page,
    };
  }

  private async _paginateQueryBuilder<T>(
    qb: SelectQueryBuilder<T>,
    options: Partial<PaginationDto> & { tableName?: string },
    findParams: FindOptionsWhere<T> | FindManyOptions<T> = {},
  ) {
    if (!options.tableName) {
      options.tableName = qb.alias;
    }

    const { from, searchString, tableName, to } = options;

    if (from && to) {
      qb.andWhere(`${tableName}.created_at BETWEEN :from AND :to`, {
        from,
        to,
      });
    } else if (from) {
      qb.andWhere(`${tableName}.created_at >= :from`, { from });
    } else if (to) {
      qb.andWhere(`${tableName}.created_at <= :to`, { to });
    }

    if (searchString) {
      qb.andWhere(`${tableName}.search_value ILIKE :searchString`, {
        searchString,
      });
    }

    qb.setFindOptions(findParams);

    if (options.getAll !== 1) {
      qb.take(options.limit).skip((options.page - 1) * options.limit);
    }

    return qb.getManyAndCount();
  }

  private async _paginateRepository<T>(
    repo: Repository<T>,
    options: Partial<PaginationDto> & { tableName?: string },
    findParams: FindOptionsWhere<T> | FindManyOptions<T> = {},
  ) {
    const { from, searchString, tableName, to } = options;

    if (typeof findParams === 'object' && 'where' in findParams) {
      if (from && to) {
        findParams.where = {
          ...findParams.where,
          [`${tableName}.created_at`]: Between(from, to),
        };
      } else if (from) {
        findParams.where = {
          ...findParams.where,
          [`${tableName}.created_at`]: MoreThanOrEqual(from),
        };
      } else if (to) {
        findParams.where = {
          ...findParams.where,
          [`${tableName}.created_at`]: LessThanOrEqual(to),
        };
      }

      if (searchString) {
        findParams.where = {
          ...findParams.where,
          [`${tableName}.search_value`]: ILike(searchString),
        };
      }
    } else {
      if (from && to) {
        findParams = {
          ...findParams,
          [`${tableName}.created_at`]: Between(from, to),
        };
      } else if (from) {
        findParams = {
          ...findParams,
          [`${tableName}.created_at`]: MoreThanOrEqual(from),
        };
      } else if (to) {
        findParams = {
          ...findParams,
          [`${tableName}.created_at`]: LessThanOrEqual(to),
        };
      }

      if (searchString) {
        findParams = {
          ...findParams,
          [`${tableName}.search_value`]: ILike(searchString),
        };
      }
    }

    return repo.findAndCount({
      skip: (options.page - 1) * options.limit,
      take: options.limit,
      ...findParams,
    });
  }
}
