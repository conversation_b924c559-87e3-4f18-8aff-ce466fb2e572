import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetBudgetCodeListByIdsDto } from '../../controller/budget-code/dtos/get-budget-code-list-by-ids.dto';
import { GetBudgetCodeListDto } from '../../controller/budget-code/dtos/get-budget-code-list.dto';
import { GetDetailBudgetCodeDto } from '../../controller/budget-code/dtos/get-detail-budget-code.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { BudgetCodeModel } from '../../domain/model/budget-code.model';
import { IBudgetCodeRepository } from '../../domain/repositories/budget-code.repository';
import { parseScopes } from '../../utils/common';
import {
  EBusinessOwnerPermission,
  ECostPermission,
} from '../../utils/constants/permission.enum';
import { BudgetCodeEntity } from '../entities/budget-code.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class BudgetCodeRepository
  extends BaseRepository
  implements IBudgetCodeRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createBudgetCode(data: BudgetCodeModel): Promise<BudgetCodeModel> {
    const repository = this.getRepository(BudgetCodeEntity);
    const newBudgetCode = repository.create(data);
    return await repository.save(newBudgetCode);
  }

  async updateBudgetCode(data: BudgetCodeModel): Promise<BudgetCodeModel> {
    const repository = this.getRepository(BudgetCodeEntity);
    const updateBudgetCode = repository.create(data);
    return await repository.save(updateBudgetCode);
  }

  async getBudgetCodes(
    conditions: GetBudgetCodeListDto,
    jwtPayload,
  ): Promise<ResponseDto<BudgetCodeModel>> {
    const repository = this.getRepository(BudgetCodeEntity);
    let queryBuilder = repository.createQueryBuilder('budgetCodes');
    queryBuilder
      .innerJoinAndSelect('budgetCodes.businessOwner', 'businessOwner')
      .leftJoinAndSelect('budgetCodes.cost', 'cost');

    // queryBuilder.leftJoinAndSelect(
    //   'budgetCodes.costcenterSubaccount',
    //   'costcenterSubaccount',
    // );

    //Join for sector / company/ business unit / department in const center / sub account
    // queryBuilder
    //   .leftJoinAndSelect('costcenterSubaccount.sector', 'sector')
    //   .leftJoinAndSelect('costcenterSubaccount.company', 'company')
    //   .leftJoinAndSelect('costcenterSubaccount.businessUnit', 'businessUnit')
    //   .leftJoinAndSelect('costcenterSubaccount.department', 'department');

    if (conditions.statuses) {
      queryBuilder.where('budgetCodes.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.businessOwnerIds) {
      queryBuilder.andWhere('businessOwner.id IN (:...businessOwnerIds)', {
        businessOwnerIds: conditions.businessOwnerIds,
      });
    }

    if (conditions.costIds?.length) {
      queryBuilder.where('budgetCodes.costId IN (:...costIds)', {
        costIds: conditions.costIds,
      });
    }

    if (conditions.budgetTypes?.length) {
      queryBuilder.where('budgetCodes.budgetType IN (:...budgetTypes)', {
        budgetTypes: conditions.budgetTypes,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('budgetCodes.createdAt', 'DESC');

    return await this.pagination<BudgetCodeEntity>(queryBuilder, conditions);
  }

  async deleteBudgetCode(id: string): Promise<void> {
    const repository = this.getRepository(BudgetCodeEntity);
    await repository.delete(id);
  }

  async getBudgetCodeById(id: string): Promise<BudgetCodeModel> {
    const repository = this.getRepository(BudgetCodeEntity);
    return await repository.findOne({
      where: { id: id },
      relations: ['businessOwner'],
    });
  }

  async getBudgetCodeByCode(
    code: string,
    id?: string,
  ): Promise<BudgetCodeModel> {
    const repository = this.getRepository(BudgetCodeEntity);
    const query: FindOneOptions<BudgetCodeEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getBudgetCodeDetail(
    conditions: GetDetailBudgetCodeDto,
    jwtPayload,
  ): Promise<BudgetCodeModel> {
    const repository = this.getRepository(BudgetCodeEntity);
    let queryBuilder = repository
      .createQueryBuilder('budgetCodes')
      .select([
        'budgetCodes.id',
        'budgetCodes.code',
        'budgetCodes.name',
        'budgetCodes.status',
        'budgetCodes.budgetType',
        'budgetCodes.internalOrder',
        'budgetCodes.costId',
        'budgetCodes.description',
        'budgetCodes.createdAt',
      ]);
    queryBuilder
      .innerJoin('budgetCodes.businessOwner', 'businessOwner')
      .addSelect([
        'businessOwner.id',
        'businessOwner.code',
        'businessOwner.name',
        'businessOwner.status',
        'businessOwner.description',
      ])
      .leftJoin('budgetCodes.cost', 'cost')
      .addSelect([
        'cost.id',
        'cost.name',
        'cost.groupCost',
        'cost.description',
        'cost.status',
      ]);

    // queryBuilder.leftJoinAndSelect(
    //   'budgetCodes.costcenterSubaccount',
    //   'costcenterSubaccount',
    // );

    // //Join for sector / company/ business unit / department in const center / sub account
    // queryBuilder
    //   .leftJoinAndSelect('costcenterSubaccount.sector', 'sector')
    //   .leftJoinAndSelect('costcenterSubaccount.company', 'company')
    //   .leftJoinAndSelect('costcenterSubaccount.businessUnit', 'businessUnit')
    //   .leftJoinAndSelect('costcenterSubaccount.department', 'department');

    queryBuilder.where('budgetCodes.id = :id', { id: conditions.id });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  async getBudgetCodesByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<BudgetCodeModel[]> {
    const repository = this.getRepository(BudgetCodeEntity);
    let queryBuilder = repository.createQueryBuilder('budgetCodes');
    queryBuilder
      .innerJoinAndSelect('budgetCodes.businessOwner', 'businessOwner')
      .leftJoinAndSelect('budgetCodes.cost', 'cost');

    // queryBuilder.leftJoinAndSelect(
    //   'budgetCodes.costcenterSubaccount',
    //   'costcenterSubaccount',
    // );

    // //Join for sector / company/ business unit / department in const center / sub account
    // queryBuilder
    //   .leftJoinAndSelect('costcenterSubaccount.sector', 'sector')
    //   .leftJoinAndSelect('costcenterSubaccount.company', 'company')
    //   .leftJoinAndSelect('costcenterSubaccount.businessUnit', 'businessUnit')
    //   .leftJoinAndSelect('costcenterSubaccount.department', 'department');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetBudgetCodeListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('budgetCodes.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('budgetCodes.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<BudgetCodeEntity>,
    jwtPayload: any,
    conditions: GetBudgetCodeListDto | GetDetailBudgetCodeDto,
  ) {
    //For filter budget code
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    //For filter cost center / sub account
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.companyCodes = jwtPayload?.companies;
    conditions.businessUnitCodes = jwtPayload?.businessUnits;
    conditions.departmentCodes = jwtPayload?.departments;

    conditions.costCodes = jwtPayload?.costs;

    if (!(jwtPayload?.isSuperAdmin ?? false)) {
      if (
        !parseScopes(jwtPayload?.scopes, [
          EBusinessOwnerPermission.CREATE,
          EBusinessOwnerPermission.EDIT,
        ]) &&
        conditions.businessOwnerCodes?.length
      ) {
        queryBuilder.andWhere(
          `(businessOwner.id IS NULL OR businessOwner.code IN (:...businessOwnerCodes))`,
          {
            businessOwnerCodes: conditions.businessOwnerCodes,
          },
        );
      }

      if (
        !parseScopes(jwtPayload?.scopes, [
          ECostPermission.CREATE,
          ECostPermission.EDIT,
        ]) &&
        conditions.costCodes?.length
      ) {
        queryBuilder.andWhere(
          `(cost.id IS NULL OR cost.code IN (:...costCodes))`,
          {
            costCodes: conditions.costCodes,
          },
        );
      }

      // if (
      //   !parseScopes(jwtPayload?.scopes, [
      //     ESectorPermission.CREATE,
      //     ESectorPermission.EDIT,
      //   ])
      // ) {
      //   queryRolesCode.push(`sector.code IN (:...sectorCodes)`);
      //   params.sectorCodes = conditions.sectorCodes;
      // }

      // if (
      //   !parseScopes(jwtPayload?.scopes, [
      //     ECompanyPermission.CREATE,
      //     ECompanyPermission.EDIT,
      //   ])
      // ) {
      //   queryRolesCode.push(`company.code IN (:...companyCodes)`);
      //   params.companyCodes = conditions.companyCodes;
      // }

      // if (
      //   !parseScopes(jwtPayload?.scopes, [
      //     EBusinessUnitPermission.CREATE,
      //     EBusinessUnitPermission.EDIT,
      //   ])
      // ) {
      //   queryRolesCode.push(`businessUnit.code IN (:...businessUnitCodes)`);
      //   params.businessUnitCodes = conditions.businessUnitCodes;
      // }

      // if (
      //   !parseScopes(jwtPayload?.scopes, [
      //     EDepartmentPermission.CREATE,
      //     EDepartmentPermission.EDIT,
      //   ])
      // ) {
      //   queryRolesCode.push(`department.code IN (:...departmentCodes)`);
      //   params.departmentCodes = conditions.departmentCodes;
      // }

      // if (queryBusinessOwners) {
      //   queryBuilder.andWhere(
      //     `${queryBusinessOwners} AND (budgetCodes.costcenterSubaccountId IS NULL OR (${queryRolesCode.join(' AND ')}))`,
      //     params,
      //   );
      // } else {
      //   queryBuilder.andWhere(
      //     `budgetCodes.costcenterSubaccountId IS NULL OR (${queryRolesCode.join(' AND ')})`,
      //     params,
      //   );
      // }
    }

    return queryBuilder;
  }

  async getBudgetCodeByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<BudgetCodeModel[]> {
    const repository = this.getRepository(BudgetCodeEntity);
    let queryBuilder = repository.createQueryBuilder('budgetCodes');

    if (ids.length) {
      queryBuilder.where('budgetCodes.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetBudgetCodeListDto({}),
    );

    return await queryBuilder.getMany();
  }

  async getListByIds(
    conditions: GetBudgetCodeListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<BudgetCodeModel>> {
    const repository = this.getRepository(BudgetCodeEntity);
    let queryBuilder = repository
      .createQueryBuilder('budgetCodes')
      .select([
        'budgetCodes.id',
        'budgetCodes.code',
        'budgetCodes.name',
        'budgetCodes.status',
        'budgetCodes.budgetType',
        'budgetCodes.internalOrder',
        'budgetCodes.costId',
        'budgetCodes.description',
        'budgetCodes.createdAt',
      ]);

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.where('budgetCodes.id IN (:...ids)', {
        ids: conditions.ids,
      });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder.orderBy('budgetCodes.createdAt', 'DESC');

    return await this.pagination<BudgetCodeEntity>(queryBuilder, conditions);
  }

  async getBudgetCodeByInternalOrderAndFunctionalArea(
    jwtPayload: any,
    functionalAreas?: string[],
    internalOrders?: string[],
  ): Promise<BudgetCodeModel[]> {
    const repository = this.getRepository(BudgetCodeEntity);
    let queryBuilder = repository.createQueryBuilder('budgetCodes');

    queryBuilder.where(
      'budgetCodes.code IN (:...functionalAreas) OR budgetCodes.code IN (:...internalOrders)',
      {
        functionalAreas: functionalAreas?.length ? functionalAreas : [null],
        internalOrders: internalOrders?.length ? internalOrders : [null],
      },
    );

    return await queryBuilder.getMany();
  }
}
