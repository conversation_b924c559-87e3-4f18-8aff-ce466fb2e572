import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetDetailPurchaseRequestTypeDto } from '../../controller/purchase-request-type/dtos/get-detail-purchase-request-type.dto';
import { GetPurchaseRequestTypeListDto } from '../../controller/purchase-request-type/dtos/get-purchase-request-type-list.dto';
import { UpdatePurchaseRequestTypeDto } from '../../controller/purchase-request-type/dtos/update-purchase-request-type.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PurchaseRequestTypeModel } from '../../domain/model/purchase-request-type.model';
import { IPurchaseRequestTypeRepository } from '../../domain/repositories/purchase-request-type.repository';
import { parseScopes } from '../../utils/common';
import { EPurchasingDepartmentPermission } from '../../utils/constants/permission.enum';
import { PurchaseRequestTypeEntity } from '../entities/purchase-request-type.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class PurchaseRequestTypeRepository
  extends BaseRepository
  implements IPurchaseRequestTypeRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createPrType(
    data: PurchaseRequestTypeModel,
  ): Promise<PurchaseRequestTypeModel> {
    const repository = this.getRepository(PurchaseRequestTypeEntity);

    const prType = repository.create(data);

    return await repository.save(prType);
  }
  async updatePrType(
    id: string,
    updatePrTypeDto: UpdatePurchaseRequestTypeDto,
  ): Promise<PurchaseRequestTypeModel> {
    const repository = this.getRepository(PurchaseRequestTypeEntity);
    const prType = repository.create({
      id,
      ...updatePrTypeDto,
    });
    return await repository.save(prType);
  }

  async deletePrType(id: string): Promise<void> {
    const repository = this.getRepository(PurchaseRequestTypeEntity);
    await repository.delete(id);
  }

  async getPrTypes(
    conditions: GetPurchaseRequestTypeListDto,
    jwtPayload,
  ): Promise<ResponseDto<PurchaseRequestTypeModel>> {
    const repository = this.getRepository(PurchaseRequestTypeEntity);
    let queryBuilder = repository.createQueryBuilder('prTypes');

    if (conditions.statuses) {
      queryBuilder.andWhere('prTypes.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.codes) {
      queryBuilder.andWhere('prTypes.code IN (:...codes)', {
        codes: conditions.codes,
      });
    }

    if (conditions.forms) {
      queryBuilder.andWhere('prTypes.form IN (:...forms)', {
        forms: conditions.forms,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    queryBuilder.orderBy('prTypes.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getPrTypeByCode(
    code: string,
    id?: string,
  ): Promise<PurchaseRequestTypeModel> {
    const repository = this.getRepository(PurchaseRequestTypeEntity);

    const query: FindOneOptions<PurchaseRequestTypeEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getPrTypeDetail(
    conditions: GetDetailPurchaseRequestTypeDto,
    jwtPayload,
  ): Promise<PurchaseRequestTypeModel> {
    const repository = this.getRepository(PurchaseRequestTypeEntity);
    let queryBuilder = repository
      .createQueryBuilder('prTypes')
      .select([
        'prTypes.id',
        'prTypes.code',
        'prTypes.name',
        'prTypes.description',
        'prTypes.status',
        'prTypes.form',
      ]);

    queryBuilder.where('prTypes.id = :id', {
      id: conditions.id,
    });

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<PurchaseRequestTypeEntity>,
    jwtPayload: any,
    conditions: GetPurchaseRequestTypeListDto | GetDetailPurchaseRequestTypeDto,
  ) {
    // conditions.codes = jwtPayload?.prTypeCodes; // Data role
    // if (
    //   !jwtPayload?.isSuperAdmin &&
    //   !parseScopes(jwtPayload?.scopes, [
    //     EPurchasingDepartmentPermission.CREATE,
    //     EPurchasingDepartmentPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('prTypes.code IN (:...codes)', {
    //     codes: conditions?.codes?.length ? conditions.codes : [null],
    //   });
    // }

    return queryBuilder;
  }

  async getPrTypeByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<PurchaseRequestTypeModel[]> {
    const repository = this.getRepository(PurchaseRequestTypeEntity);
    let queryBuilder = repository.createQueryBuilder('prTypes');

    if (ids.length) {
      queryBuilder.andWhere('prTypes.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetPurchaseRequestTypeListDto({}),
    );

    return await queryBuilder.getMany();
  }
}
