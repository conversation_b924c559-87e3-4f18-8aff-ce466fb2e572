import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { CreateSapPurchaseOrderItemDto } from '../../controller/sap-purchase-order/dtos/create-sap-purchase-order-item.dto';
import { SapPurchaseOrderItemModel } from '../../domain/model/sap_purchase_order_item.model';
import { ISapPurchaseOrderItemRepository } from '../../domain/repositories/sap-purchase-order-item.repository';
import { DataSource, In, Repository } from 'typeorm';
import { SapPurchaseOrderItemEntity } from '../entities/sap_purchase_order_item.entity';
import { BaseRepository } from './base.repository';

@Injectable({
  scope: Scope.REQUEST,
})
export class SapPurchaseOrderItemRepository
  extends BaseRepository
  implements ISapPurchaseOrderItemRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
    @InjectRepository(SapPurchaseOrderItemEntity)
    private readonly sapPurchaseOrderItemEntityRepository: Repository<SapPurchaseOrderItemEntity>,
  ) {
    super(dataSource, req);
  }
  async createSapPurchaseOrderItem(
    data: CreateSapPurchaseOrderItemDto[],
  ): Promise<SapPurchaseOrderItemModel[]> {
    // const repository = this.getRepository(SapPurchaseOrderItemEntity);
    const dataCreate = this.sapPurchaseOrderItemEntityRepository.create({
      ...data,
    });

    return await this.sapPurchaseOrderItemEntityRepository.save(dataCreate);
  }
  async getSapPOItemsByPoDetailId(
    poDetailId: number,
  ): Promise<SapPurchaseOrderItemModel[]> {
    const repository = this.getRepository(SapPurchaseOrderItemEntity);

    return await repository.find({
      where: { purchaseOrderDetailId: poDetailId },
    });
  }

  async deleteSapPOItemsBySapPurchaseOrderId(
    sapPurchaseOrderIds: number[],
  ): Promise<void> {
    await this.sapPurchaseOrderItemEntityRepository.softDelete({
      sapPurchaseOrderId: In(sapPurchaseOrderIds),
    });
  }
}
