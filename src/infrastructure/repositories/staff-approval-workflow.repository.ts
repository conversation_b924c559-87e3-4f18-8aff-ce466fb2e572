import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, In } from 'typeorm';
import { CreateStaffApprovalWorkflowDto } from '../../controller/process/dtos/create-staff-approval-workflow.dto';
import { StaffApprovalWorkflowModel } from '../../domain/model/staff-approval-workflow.model';
import { StaffModel } from '../../domain/model/staff.model';
import { IStaffApprovalWorkflowRepository } from '../../domain/repositories/staff-approval-workflow.repository';
import { StaffApprovalWorkflowEntity } from '../entities/staff-approval-workflow.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class StaffApprovalWorkflowRepository
  extends BaseRepository
  implements IStaffApprovalWorkflowRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createStaffApprovalWorkflow(
    data: CreateStaffApprovalWorkflowDto,
  ): Promise<StaffApprovalWorkflowModel> {
    const repository = this.getRepository(StaffApprovalWorkflowEntity);
    const newStaff = repository.create(data);

    return await repository.save(newStaff);
  }

  async deleteStaffApprovalWorkflow(
    approvalWorkflowIds: string[],
  ): Promise<void> {
    const repository = this.getRepository(StaffApprovalWorkflowEntity);

    await repository.softDelete({
      approvalWorkflowId: In(approvalWorkflowIds),
    });
  }

  async getSelectedStaffs(
    staffApprovalWorkflowId: string,
  ): Promise<StaffModel[]> {
    const repository = this.getRepository(StaffApprovalWorkflowEntity);

    const queryBuilder = repository
      .createQueryBuilder('staffApprovalWorkflow')
      .innerJoinAndSelect(
        'staffApprovalWorkflow.selectedStaffs',
        'selectedStaffs',
      )
      .where(
        'staffApprovalWorkflow.id = :id AND staffApprovalWorkflow.allowSelect = TRUE',
        { id: staffApprovalWorkflowId },
      )
      .withDeleted();

    const staffApprovalWorkflow = await queryBuilder.getOne();

    return staffApprovalWorkflow?.selectedStaffs?.length
      ? staffApprovalWorkflow.selectedStaffs
      : [];
  }
}
