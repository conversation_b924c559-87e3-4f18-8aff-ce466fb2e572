import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { CurrencyUnitModel } from '../../domain/model/currency-unit.model';
import { ICurrencyUnitRepository } from '../../domain/repositories/currency-unit.repository';
import { CurrencyUnitEntity } from '../entities/currency-unit.entity';
import { BaseRepository } from './base.repository';
import { GetCurrencyUnitListDto } from '../../controller/currency-unit/dtos/get-currency-unit-list.dto';
import { GetDetailCurrencyUnitDto } from '../../controller/currency-unit/dtos/get-detail-currency-unit.dto';
import { parseScopes } from '../../utils/common';
import { ECurrencyUnitPermission } from '../../utils/constants/permission.enum';
import { GetCurrencyListByIdsDto } from '../../controller/currency-unit/dtos/get-currency-unit-list-by-ids.dto';

@Injectable({ scope: Scope.REQUEST })
export class CurrencyUnitRepository
  extends BaseRepository
  implements ICurrencyUnitRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createCurrencyUnit(
    data: CurrencyUnitModel,
  ): Promise<CurrencyUnitModel> {
    const repository = this.getRepository(CurrencyUnitEntity);
    const newCurrencyUnit = repository.create(data);
    return await repository.save(newCurrencyUnit);
  }

  async updateCurrencyUnit(
    data: CurrencyUnitModel,
  ): Promise<CurrencyUnitModel> {
    const repository = this.getRepository(CurrencyUnitEntity);
    const updateCurrencyUnit = repository.create(data);
    return await repository.save(updateCurrencyUnit);
  }

  async getCurrencyUnits(
    conditions: GetCurrencyUnitListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<CurrencyUnitModel>> {
    const repository = this.getRepository(CurrencyUnitEntity);
    let queryBuilder = repository
      .createQueryBuilder('currencyUnits')
      .select([
        'currencyUnits.id',
        'currencyUnits.name',
        'currencyUnits.currencyCode',
        'currencyUnits.description',
        'currencyUnits.status',
        'currencyUnits.createdAt',
      ]);

    queryBuilder
      .leftJoin('currencyUnits.currencyExchanges', 'currencyExchanges')
      .addSelect([
        'currencyExchanges.id',
        'currencyExchanges.effectiveStartDate',
        'currencyExchanges.effectiveEndDate',
        'currencyExchanges.exchangeRate',
        'currencyExchanges.exchangeBudget',
        'currencyExchanges.currencyUnitId',
      ]);

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('currencyUnits.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.statuses) {
      queryBuilder.where('currencyUnits.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('currencyUnits.createdAt', 'DESC');
    queryBuilder.addOrderBy('currencyExchanges.effectiveStartDate', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async deleteCurrencyUnit(id: string): Promise<void> {
    const repository = this.getRepository(CurrencyUnitEntity);
    await repository.delete(id);
  }

  // async getCurrencyUnitById(id: string): Promise<CurrencyUnitModel> {
  //   const repository = this.getRepository(CurrencyUnitEntity);
  //   const detail = await repository
  //     .createQueryBuilder('currencyUnit')
  //     .where('currencyUnit.id = :id', { id })
  //     .leftJoinAndSelect('currencyUnit.currencyExchanges', 'currencyExchanges')
  //     .getOne();

  //   return detail;
  // }

  async findCurrencyUnitByCode(code: string): Promise<CurrencyUnitModel> {
    const repository = this.getRepository(CurrencyUnitEntity);
    return await repository.findOne({ where: { currencyCode: code } });
  }

  async getCurrencyUnitsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<CurrencyUnitModel[]> {
    const repository = this.getRepository(CurrencyUnitEntity);

    let queryBuilder = repository.createQueryBuilder('currencyUnits');

    queryBuilder.leftJoinAndSelect(
      'currencyUnits.currencyExchanges',
      'currencyExchanges',
    );

    queryBuilder.orderBy('currencyExchanges.effectiveStartDate', 'DESC');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetCurrencyUnitListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.where('currencyUnits.currencyCode IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.where('currencyUnits.currencyCode IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }

  async getCurrencyUnitDetail(
    conditions: GetDetailCurrencyUnitDto,
    jwtPayload: any,
  ): Promise<CurrencyUnitModel> {
    const repository = this.getRepository(CurrencyUnitEntity);
    let queryBuilder = repository
      .createQueryBuilder('currencyUnits')
      .select([
        'currencyUnits.id',
        'currencyUnits.currencyCode',
        'currencyUnits.name',
        'currencyUnits.description',
        'currencyUnits.status',
        'currencyUnits.createdAt',
      ]);

    queryBuilder
      .leftJoin('currencyUnits.currencyExchanges', 'currencyExchanges')
      .addSelect([
        'currencyExchanges.id',
        'currencyExchanges.effectiveStartDate',
        'currencyExchanges.effectiveEndDate',
        'currencyExchanges.exchangeRate',
        'currencyExchanges.currencyUnitId',
        'currencyExchanges.exchangeBudget',
      ]);

    queryBuilder.andWhere('currencyUnits.id = :id', { id: conditions.id });

    queryBuilder.orderBy('currencyExchanges.effectiveStartDate', 'DESC');

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<CurrencyUnitEntity>,
    jwtPayload: any,
    conditions: GetCurrencyUnitListDto | GetDetailCurrencyUnitDto,
  ) {
    conditions.codes = jwtPayload?.currencyUnits;
    if (!jwtPayload?.isSuperAdmin) {
      if (
        !parseScopes(jwtPayload?.scopes, [
          ECurrencyUnitPermission.CREATE,
          ECurrencyUnitPermission.EDIT,
        ]) &&
        conditions.codes?.length
      ) {
        queryBuilder.andWhere(
          '(currencyUnits.id IS NULL OR currencyUnits.currencyCode IN (:...codes))',
          {
            codes: conditions.codes,
          },
        );
      }
    }

    return queryBuilder;
  }
  async getCurrencyUnitVND(): Promise<CurrencyUnitModel> {
    const repository = this.getRepository(CurrencyUnitEntity);
    let queryBuilder = repository
      .createQueryBuilder('currencyUnits')
      .select([
        'currencyUnits.id',
        'currencyUnits.currencyCode',
        'currencyUnits.name',
        'currencyUnits.description',
        'currencyUnits.status',
        'currencyUnits.createdAt',
      ]);

    queryBuilder
      .leftJoin('currencyUnits.currencyExchanges', 'currencyExchanges')
      .addSelect([
        'currencyExchanges.id',
        'currencyExchanges.effectiveStartDate',
        'currencyExchanges.effectiveEndDate',
        'currencyExchanges.exchangeRate',
        'currencyExchanges.currencyUnitId',
        'currencyExchanges.exchangeBudget',
      ]);

    queryBuilder.andWhere('currencyUnits.currencyCode = :currencyCode', {
      currencyCode: 'VND',
    });

    queryBuilder.orderBy('currencyExchanges.effectiveStartDate', 'DESC');

    return await queryBuilder.getOne();
  }

  async getListByIds(
    conditions: GetCurrencyListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<CurrencyUnitModel>> {
    const repository = this.getRepository(CurrencyUnitEntity);
    let queryBuilder = repository.createQueryBuilder('currencyUnits');

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.where('currencyUnits.id IN (:...ids)', {
        ids: conditions.ids,
      });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder.orderBy('currencyUnits.createdAt', 'DESC');

    return await this.pagination<CurrencyUnitEntity>(queryBuilder, conditions);
  }
}
