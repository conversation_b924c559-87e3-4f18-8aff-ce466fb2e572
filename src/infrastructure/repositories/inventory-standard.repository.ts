import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { CreateInventoryStandardDto } from '../../controller/inventory-standard/dtos/create-inventory-standard.dto';
import { GetDetailInventoryStandardDto } from '../../controller/inventory-standard/dtos/get-detail-inventory-standard.dto';
import { GetInventoryStandardListDto } from '../../controller/inventory-standard/dtos/get-inventory-standard-list.dto';
import { UpdateInventoryStandardDto } from '../../controller/inventory-standard/dtos/update-inventory-standard.dto';
import { inventoryStandardNullValue } from '../../domain/config/constant';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { InventoryStandardModel } from '../../domain/model/inventory-standard.model';
import { IInventoryStandardRepository } from '../../domain/repositories/inventory-standard.repository';
import {
  getPathInventoryStandard,
  parseScopes,
  uidToPath,
} from '../../utils/common';
import {
  EBusinessUnitPermission,
  ECompanyPermission,
  EDepartmentPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { InventoryStandardEntity } from '../entities/inventory-standard.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class InventoryStandardRepository
  extends BaseRepository
  implements IInventoryStandardRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createInventoryStandard(
    data: InventoryStandardModel,
  ): Promise<InventoryStandardModel> {
    const repository = this.getRepository(InventoryStandardEntity);
    const newInventoryStandard = repository.create(data);
    return await repository.save(newInventoryStandard);
  }

  async updateInventoryStandard(
    data: InventoryStandardModel,
  ): Promise<InventoryStandardModel> {
    const repository = this.getRepository(InventoryStandardEntity);
    const updateInventoryStandard = repository.create(data);
    return await repository.save(updateInventoryStandard);
  }

  async getInventoryStandards(
    conditions: GetInventoryStandardListDto,
    jwtPayload,
  ): Promise<ResponseDto<InventoryStandardModel>> {
    const repository = this.getRepository(InventoryStandardEntity);
    let queryBuilder = repository.createQueryBuilder('inventoryStandards');

    queryBuilder.leftJoinAndSelect('inventoryStandards.material', 'material');
    queryBuilder.leftJoinAndSelect('inventoryStandards.sector', 'sector');
    queryBuilder.leftJoinAndSelect('inventoryStandards.company', 'company');
    queryBuilder.leftJoinAndSelect(
      'inventoryStandards.businessUnit',
      'businessUnit',
    );
    queryBuilder.leftJoinAndSelect(
      'inventoryStandards.department',
      'department',
    );
    ///Join for material
    queryBuilder
      .leftJoin('material.industries', 'industries')
      .addSelect([
        'industries.id',
        'industries.codeSAP',
        'industries.status',
        'industries.sector',
      ]);
    queryBuilder
      .leftJoin('industries.sector', 'industrySector')
      .addSelect([
        'industrySector.id',
        'industrySector.code',
        'industrySector.name',
        'industrySector.description',
        'industrySector.status',
      ]);
    queryBuilder.leftJoinAndSelect('material.materialType', 'materialType');
    queryBuilder.leftJoinAndSelect('material.materialGroup', 'materialGroup');

    if (conditions.materialIds) {
      queryBuilder.andWhere(
        'inventoryStandards.materialId IN (:...materialIds)',
        {
          materialIds: conditions.materialIds,
        },
      );
    }

    if (conditions.statuses) {
      queryBuilder.andWhere('inventoryStandards.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.sectorIds) {
      queryBuilder.andWhere('inventoryStandards.sectorId IN (:...sectorIds)', {
        sectorIds: conditions.sectorIds,
      });
    }

    if (conditions.companyIds) {
      queryBuilder.andWhere(
        'inventoryStandards.companyId IN (:...companyIds)',
        {
          companyIds: conditions.companyIds,
        },
      );
    }

    if (conditions.businessUnitIds) {
      queryBuilder.andWhere(
        'inventoryStandards.businessUnitId IN (:...businessUnitIds)',
        {
          businessUnitIds: conditions.businessUnitIds,
        },
      );
    }

    if (conditions.departmentIds) {
      queryBuilder.andWhere(
        'inventoryStandards.departmentId IN (:...departmentIds)',
        {
          departmentIds: conditions.departmentIds,
        },
      );
    }

    if (conditions.searchString) {
      queryBuilder.andWhere('material.searchValue ILIKE :searchString', {
        searchString: conditions.searchString,
      });

      conditions.searchString = '';
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('inventoryStandards.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async deleteInventoryStandard(id: string): Promise<void> {
    const repository = this.getRepository(InventoryStandardEntity);
    await repository.softDelete(id);
  }

  async getInventoryStandardDetail(
    conditions: GetDetailInventoryStandardDto,
    jwtPayload,
  ): Promise<InventoryStandardModel> {
    const repository = this.getRepository(InventoryStandardEntity);
    let queryBuilder = repository.createQueryBuilder('inventoryStandards');
    queryBuilder
      .leftJoinAndSelect('inventoryStandards.material', 'material')
      .leftJoinAndSelect('inventoryStandards.sector', 'sector')
      .leftJoinAndSelect('inventoryStandards.company', 'company')
      .leftJoinAndSelect('inventoryStandards.businessUnit', 'businessUnit')
      .leftJoinAndSelect('inventoryStandards.department', 'department');

    ///Join for material
    queryBuilder
      .leftJoin('material.industries', 'industries')
      .addSelect([
        'industries.id',
        'industries.codeSAP',
        'industries.status',
        'industries.sector',
      ]);
    queryBuilder
      .leftJoin('industries.sector', 'industrySector')
      .addSelect([
        'industrySector.id',
        'industrySector.code',
        'industrySector.name',
        'industrySector.description',
        'industrySector.status',
      ]);
    queryBuilder.leftJoinAndSelect('material.materialType', 'materialType');
    queryBuilder.leftJoinAndSelect('material.materialGroup', 'materialGroup');

    queryBuilder.where('inventoryStandards.id = :id', {
      id: conditions.id,
    });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<InventoryStandardEntity>,
    jwtPayload: any,
    conditions: GetInventoryStandardListDto | GetDetailInventoryStandardDto,
  ) {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.companyCodes = jwtPayload?.companies;
    conditions.businessUnitCodes = jwtPayload?.businessUnits;
    conditions.departmentCodes = jwtPayload?.departments;
    conditions.materialGroupCodes = jwtPayload?.materialGroups;
    conditions.materialTypeCodes = jwtPayload?.materialTypes;
    conditions.materialCodes = jwtPayload?.materials;

    if (!jwtPayload?.isSuperAdmin) {
      if (
        !parseScopes(jwtPayload?.scopes, [
          ESectorPermission.CREATE,
          ESectorPermission.EDIT,
        ]) &&
        conditions?.sectorCodes?.length
      ) {
        queryBuilder.andWhere(
          '(sector.id IS NULL OR sector.code IN (:...sectorCodes))',
          {
            sectorCodes: conditions?.sectorCodes,
          },
        );
      }

      if (
        !parseScopes(jwtPayload?.scopes, [
          ECompanyPermission.CREATE,
          ECompanyPermission.EDIT,
        ]) &&
        conditions?.companyCodes?.length
      ) {
        queryBuilder.andWhere(
          '(inventoryStandards.companyId IS NULL OR company.code IN (:...companyCodes))',
          {
            companyCodes: conditions?.companyCodes,
          },
        );
      }

      if (
        !parseScopes(jwtPayload?.scopes, [
          EBusinessUnitPermission.CREATE,
          EBusinessUnitPermission.EDIT,
        ]) &&
        conditions?.businessUnitCodes?.length
      ) {
        queryBuilder.andWhere(
          '(inventoryStandards.businessUnitId IS NULL OR businessUnit.code IN (:...businessUnitCodes))',
          {
            businessUnitCodes: conditions?.businessUnitCodes,
          },
        );
      }

      if (
        !parseScopes(jwtPayload?.scopes, [
          EDepartmentPermission.CREATE,
          EDepartmentPermission.EDIT,
        ]) &&
        conditions?.departmentCodes?.length
      ) {
        queryBuilder.andWhere(
          '(inventoryStandards.departmentId IS NULL OR department.code IN (:...departmentCodes))',
          {
            departmentCodes: conditions?.departmentCodes,
          },
        );
      }

      // if (
      //   !parseScopes(jwtPayload.scopes, [
      //     EMaterialPermission.CREATE,
      //     EMaterialPermission.EDIT,
      //   ])
      // ) {
      //   queryBuilder.andWhere('material.code IN (:...materialCodes)', {
      //     materialCodes: conditions?.materialCodes?.length
      //       ? conditions?.materialCodes
      //       : [null],
      //   });
      // }

      ///Filter for material
      // if (
      //   !parseScopes(jwtPayload.scopes, [
      //     EMaterialGroupPermission.CREATE,
      //     EMaterialGroupPermission.EDIT,
      //   ])
      // ) {
      //   queryBuilder.andWhere(
      //     'materialGroup.code IN (:...materialGroupCodes)',
      //     {
      //       materialGroupCodes: conditions?.materialGroupCodes?.length
      //         ? conditions?.materialGroupCodes
      //         : [null],
      //     },
      //   );
      // }

      // if (
      //   !parseScopes(jwtPayload.scopes, [
      //     EMaterialTypePermission.CREATE,
      //     EMaterialTypePermission.EDIT,
      //   ])
      // ) {
      //   queryBuilder.andWhere('materialType.code IN (:...materialTypeCodes)', {
      //     materialTypeCodes: conditions?.materialTypeCodes?.length
      //       ? conditions?.materialTypeCodes
      //       : [null],
      //   });
      // }
    }

    return queryBuilder;
  }

  async checkDuplicatePathInventoryStandard(
    conditions: CreateInventoryStandardDto | UpdateInventoryStandardDto,
    id?: string,
  ): Promise<InventoryStandardModel[]> {
    const repository = this.getRepository(InventoryStandardEntity);
    let queryBuilder = repository.createQueryBuilder('inventoryStandards');

    queryBuilder = this.queryWithPath(queryBuilder, conditions);

    if (id) {
      queryBuilder.andWhere('inventoryStandards.id != :id', { id });
    }

    return await queryBuilder.getMany();
  }

  queryWithPath(
    queryBuilder: SelectQueryBuilder<InventoryStandardEntity>,
    conditions: CreateInventoryStandardDto | UpdateInventoryStandardDto,
  ) {
    let query = '';

    ///Trường hợp không có giá trị NULL
    const queryParams = this.getParamsQueryPathInventoryStandard(
      conditions.materialId,
      conditions.sectorId,
      conditions.companyId,
      conditions.businessUnitId,
      conditions.departmentId,
    );
    const findQuery = [];
    for (const param of queryParams) {
      findQuery.push(`inventoryStandards.path = '${param}'`);
    }

    if (
      !conditions.companyId ||
      !conditions.businessUnitId ||
      !conditions.departmentId
    ) {
      ///Trường hợp có giá trị NULL
      const path = getPathInventoryStandard(
        conditions.materialId,
        conditions.sectorId,
        conditions.companyId,
        conditions.businessUnitId,
        conditions.departmentId,
      );
      let regex = new RegExp(inventoryStandardNullValue, 'g');
      const replacePath = path.replace(regex, '*');

      findQuery.push(`inventoryStandards.path ~ '${replacePath}'`);
    }

    query = findQuery.join(' OR ');

    queryBuilder.andWhere(`(${query})`);

    return queryBuilder;
  }

  getParamsQueryPathInventoryStandard(
    materialId: string,
    sectorId: string,
    companyId?: string,
    businessUnitId?: string,
    departmentId?: string,
  ) {
    const queryParams = [];

    const combinations = this.generateCombinations(
      `${companyId ? uidToPath(companyId) : inventoryStandardNullValue}.${businessUnitId ? uidToPath(businessUnitId) : inventoryStandardNullValue}.${departmentId ? uidToPath(departmentId) : inventoryStandardNullValue}`,
      inventoryStandardNullValue,
    );

    for (const query of combinations) {
      queryParams.push(
        `${uidToPath(materialId)}.${uidToPath(sectorId)}.` + query,
      );
    }

    return [...new Set(queryParams)];
  }

  generateCombinations(input: string, replacement: string): string[] {
    const parts = input.split('.');
    const results: string[] = [];

    // Gọi hàm đệ quy để tạo các kết hợp
    this.generateCombinationsRecursive(parts, replacement, 0, results);
    return results;
  }

  generateCombinationsRecursive(
    parts: string[],
    replacement: string,
    index: number,
    results: string[],
  ) {
    if (index === parts.length) {
      results.push(parts.join('.'));
      return;
    }

    // Giữ nguyên phần tử hiện tại
    this.generateCombinationsRecursive(parts, replacement, index + 1, results);

    // Thay thế phần tử hiện tại bằng replacement
    const original = parts[index];
    parts[index] = replacement;
    this.generateCombinationsRecursive(parts, replacement, index + 1, results);

    // Phục hồi phần tử hiện tại
    parts[index] = original;
  }

  async getInventoryStandardByPath(
    path: string,
  ): Promise<InventoryStandardModel> {
    const repository = this.getRepository(InventoryStandardEntity);
    return await repository.findOne({ where: { path } });
  }

  async getParamsCheckkDuplicatesInventoryStandard(
    materialId: string,
    sectorId: string,
    companyId?: string,
    businessUnitId?: string,
    departmentId?: string,
  ) {
    const params = this.getParamsQueryPathInventoryStandard(
      materialId,
      sectorId,
      companyId,
      businessUnitId,
      departmentId,
    );

    // if (!companyId || !businessUnitId || !departmentId) {
    //   ///Trường hợp có giá trị NULL
    //   const path = getPathInventoryStandard(
    //     materialId,
    //     sectorId,
    //     companyId,
    //     businessUnitId,
    //     departmentId,
    //   );
    // }

    return params;
  }
}
