import { Inject, Injectable, Scope } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { BudgetOpexModel } from '../../domain/model/budget-opex.model';
import { IBudgetOpexRepository } from '../../domain/repositories/budget-opex.repository';
import { REQUEST } from '@nestjs/core';
import { BaseRepository } from './base.repository';
import { BudgetOpexEntity } from '../entities/budget-opex.entity';

@Injectable({ scope: Scope.REQUEST })
export class BudgetOpexRepository
  extends BaseRepository
  implements IBudgetOpexRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createBudgetOpex(data: BudgetOpexModel): Promise<BudgetOpexModel> {
    const repository = this.getRepository(BudgetOpexEntity);
    const newBudgetOpex = repository.create(data);
    return await repository.save(newBudgetOpex);
  }

  async updateBudgetOpex(data: BudgetOpexModel): Promise<BudgetOpexModel> {
    const repository = this.getRepository(BudgetOpexEntity);
    const newBudgetOpex = repository.create(data);
    return await repository.save(newBudgetOpex);
  }
  async getBudgetOpexById(id: string): Promise<BudgetOpexModel> {
    const repository = this.getRepository(BudgetOpexEntity);
    return await repository.findOne({ where: { id: id } });
  }
}
