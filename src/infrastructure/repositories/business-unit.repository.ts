import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetBusinessUnitListDto } from '../../controller/business-unit/dtos/get-business-unit-list.dto';
import { GetDetailBusinessUnitDto } from '../../controller/business-unit/dtos/get-detail-business-unit.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { BusinessUnitModel } from '../../domain/model/business-unit.model';
import { IBusinessUnitRepository } from '../../domain/repositories/business-unit.repository';
import { parseScopes } from '../../utils/common';
import {
  EBusinessUnitPermission,
  ECompanyPermission,
} from '../../utils/constants/permission.enum';
import { BusinessUnitEntity } from '../entities/business-unit.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class BusinessUnitRepository
  extends BaseRepository
  implements IBusinessUnitRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createBusinessUnit(
    data: BusinessUnitModel,
  ): Promise<BusinessUnitModel> {
    const repository = this.getRepository(BusinessUnitEntity);
    const newBusinessUnit = repository.create(data);
    return await repository.save(newBusinessUnit);
  }

  async updateBusinessUnit(
    data: BusinessUnitModel,
  ): Promise<BusinessUnitModel> {
    const repository = this.getRepository(BusinessUnitEntity);
    const updateBusinessUnit = repository.create(data);
    return await repository.save(updateBusinessUnit);
  }

  async getBusinessUnits(
    conditions: GetBusinessUnitListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<BusinessUnitModel>> {
    const repository = this.getRepository(BusinessUnitEntity);
    let queryBuilder = repository.createQueryBuilder('businessUnits');
    queryBuilder.leftJoinAndSelect('businessUnits.company', 'company');

    if (conditions.statuses) {
      queryBuilder.where('businessUnits.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('businessUnits.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.companyIds && conditions.companyIds.length) {
      queryBuilder.andWhere('company.id IN (:...companyIds)', {
        companyIds: conditions.companyIds,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('businessUnits.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async deleteBusinessUnit(id: string): Promise<void> {
    const repository = this.getRepository(BusinessUnitEntity);
    await repository.softDelete(id);
  }

  async getBusinessUnitById(id: string): Promise<BusinessUnitModel> {
    const repository = this.getRepository(BusinessUnitEntity);
    const detail = await repository.findOne({ where: { id: id } });
    return detail;
  }

  async getBusinessUnitByCode(
    code: string,
    id?: string,
  ): Promise<BusinessUnitModel> {
    const repository = this.getRepository(BusinessUnitEntity);
    const query: FindOneOptions<BusinessUnitEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getDetailBusinessUnit(
    conditions: GetDetailBusinessUnitDto,
    jwtPayload: any,
  ): Promise<BusinessUnitModel> {
    const repository = this.getRepository(BusinessUnitEntity);
    let queryBuilder = repository
      .createQueryBuilder('businessUnits')
      .select([
        'businessUnits.id',
        'businessUnits.code',
        'businessUnits.name',
        'businessUnits.description',
        'businessUnits.status',
        'businessUnits.createdAt',
      ]);
    queryBuilder
      .leftJoin('businessUnits.company', 'company')
      .addSelect([
        'company.id',
        'company.name',
        'company.code',
        'company.description',
        'company.status',
      ]);

    queryBuilder.where('businessUnits.id = :id', {
      id: conditions.id,
    });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  async getBusinessUnitsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<BusinessUnitModel[]> {
    const repository = this.getRepository(BusinessUnitEntity);
    let queryBuilder = repository.createQueryBuilder('businessUnits');
    queryBuilder.leftJoinAndSelect('businessUnits.company', 'company');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetBusinessUnitListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.where('businessUnits.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.where('businessUnits.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<BusinessUnitEntity>,
    jwtPayload: any,
    conditions: GetBusinessUnitListDto | GetDetailBusinessUnitDto,
  ) {
    conditions.codes = jwtPayload?.businessUnits; // Data role
    conditions.companyCodes = jwtPayload?.companies;

    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        EBusinessUnitPermission.CREATE,
        EBusinessUnitPermission.EDIT,
      ]) &&
      conditions.codes?.length
    ) {
      queryBuilder.andWhere(
        '(businessUnits.id IS NULL OR businessUnits.code IN (:...codes))',
        {
          codes: conditions.codes,
        },
      );
    }

    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        ECompanyPermission.CREATE,
        ECompanyPermission.EDIT,
      ]) &&
      conditions?.companyCodes?.length
    ) {
      queryBuilder.andWhere(
        '(businessUnits.company_id IS NULL OR company.code IN (:...companyCodes))',
        {
          companyCodes: conditions?.companyCodes,
        },
      );
    }

    return queryBuilder;
  }

  async getBuByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<BusinessUnitModel[]> {
    const repository = this.getRepository(BusinessUnitEntity);
    let queryBuilder = repository.createQueryBuilder('businessUnits');
    queryBuilder.leftJoinAndSelect('businessUnits.company', 'company');

    if (ids.length) {
      queryBuilder.where('businessUnits.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetBusinessUnitListDto({}),
    );

    return await queryBuilder.getMany();
  }
}
