import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, In, Not } from 'typeorm';
import { BaseRepository } from './base.repository';
import { ISupplierSectorRepository } from '../../domain/repositories/supplier-sector.repository';
import { SupplierSectorModel } from '../../domain/model/supplier-sector.model';
import { SupplierSectorEntity } from '../entities/supplier-sector.entity';

@Injectable({ scope: Scope.REQUEST })
export class SupplierSectorRepository
  extends BaseRepository
  implements ISupplierSectorRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createSupplierSectors(
    conditions: SupplierSectorModel[],
  ): Promise<SupplierSectorModel[]> {
    const repository = this.getRepository(SupplierSectorEntity);
    const newSupplierSectors = repository.create(conditions);

    return await repository.save(newSupplierSectors);
  }

  async updateSupplierSectors(
    conditions: SupplierSectorModel[],
  ): Promise<SupplierSectorModel[]> {
    const repository = this.getRepository(SupplierSectorEntity);
    const updateSupplierSectors = repository.create(conditions);
    return await repository.save(updateSupplierSectors);
  }

  async deleteSupplierSectors(supplierId: string): Promise<void> {
    const repository = this.getRepository(SupplierSectorEntity);
    const queryBuilder = repository.createQueryBuilder('supplierSectors');
    queryBuilder.leftJoinAndSelect('supplierSectors.supplier', 'supplier');
    queryBuilder.andWhere('supplier.id = :supplierId', { supplierId });
    await queryBuilder.delete().execute();
  }

  async getSupplierSectorBySupplierId(
    supplierId: string,
  ): Promise<SupplierSectorModel[]> {
    const repository = this.getRepository(SupplierSectorEntity);
    const queryBuilder = repository.createQueryBuilder('supplierSectors');
    queryBuilder
      .leftJoin('supplierSectors.sector', 'sector')
      .addSelect(['sector.id']);
    queryBuilder.leftJoin('supplierSectors.supplier', 'supplier');
    queryBuilder.andWhere('supplier.id = :supplierId', { supplierId });

    return await queryBuilder.getMany();
  }
}
