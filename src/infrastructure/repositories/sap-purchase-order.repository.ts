import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { CreateSapPurchaseOrderDto } from '../../controller/sap-purchase-order/dtos/create-sap-purchase-order.dto';
import { SapPurchaseOrderModel } from '../../domain/model/sap_purchase_order.model';
import { ISapPurchaseOrderRepository } from '../../domain/repositories/sap-purchase-order.repository';
import { DataSource, In, Repository } from 'typeorm';
import { SapPurchaseOrderEntity } from '../entities/sap_purchase_order.entity';
import { BaseRepository } from './base.repository';

@Injectable({
  scope: Scope.REQUEST,
})
export class SapPurchaseOrderRepository
  extends BaseRepository
  implements ISapPurchaseOrderRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
    @InjectRepository(SapPurchaseOrderEntity)
    private readonly sapPurchaseOrderEntityRepository: Repository<SapPurchaseOrderEntity>,
  ) {
    super(dataSource, req);
  }

  async createSapPurchaseOrders(
    createSapPurchaseOrderDtos: CreateSapPurchaseOrderDto[],
  ): Promise<SapPurchaseOrderModel[]> {
    // const repository = this.getRepository(SapPurchaseOrderEntity);

    return await this.sapPurchaseOrderEntityRepository.save(
      createSapPurchaseOrderDtos,
    );
  }

  async updateSapPurchaseOrders(
    models: SapPurchaseOrderModel[],
  ): Promise<SapPurchaseOrderModel[]> {
    // const repository = this.getRepository(SapPurchaseOrderEntity);

    return await this.sapPurchaseOrderEntityRepository.save(models);
  }

  async getListSapPurchaseOrderByIds(
    ids: number[],
  ): Promise<SapPurchaseOrderModel[]> {
    const repository = this.getRepository(SapPurchaseOrderEntity);

    return ids && ids.length
      ? await repository.find({ where: { id: In(ids), messageType: 'S' } })
      : [];
  }

  async deleteSapPurchaseOrdersById(ids: number[]): Promise<void> {
    await this.sapPurchaseOrderEntityRepository.softDelete({ id: In(ids) });
  }
}
