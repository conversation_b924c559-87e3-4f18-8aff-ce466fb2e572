import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, In, SelectQueryBuilder } from 'typeorm';
import { GetBudgetListDto } from '../../controller/budget/dtos/get-budget-list.dto';
import { GetDetailBudgetDto } from '../../controller/budget/dtos/get-detail-budget.dto';
import { ReportBudgetDto } from '../../controller/budget/dtos/report-budget.dto';
import {
  EBudgetCreateType,
  EBudgetType,
} from '../../domain/config/enums/budget.enum';
import { ResponseDto } from '../../domain/dtos/response.dto';
import {
  IGroupBudgetByBudgetCodeAndCostCenter,
  IGroupBudgetByBusinessOwnerAndCostCenter,
} from '../../domain/interface/budget.interface';
import { BudgetModel } from '../../domain/model/budget.model';
import { IBudgetRepository } from '../../domain/repositories/budget.repository';
import { getIndexOfKeyInObject, parseScopes } from '../../utils/common';
import { EBusinessOwnerPermission } from '../../utils/constants/permission.enum';
import { BudgetEntity } from '../entities/budget.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class BudgetRepository
  extends BaseRepository
  implements IBudgetRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createBudget(data: BudgetModel): Promise<BudgetModel> {
    const repository = this.getRepository(BudgetEntity);
    const newBudget = repository.create(data);

    return await repository.save(newBudget);
  }

  async updateBudget(id: string, data: BudgetModel): Promise<BudgetModel> {
    const repository = this.getRepository(BudgetEntity);
    const updateBudget = repository.create({ id, ...data });

    return await repository.save(updateBudget);
  }

  async getBudgetById(id: string): Promise<BudgetModel> {
    const repository = this.getRepository(BudgetEntity);
    const detail = await repository
      .createQueryBuilder('budget')
      .leftJoinAndSelect('budget.parent', 'parent')
      .leftJoinAndSelect('budget.children', 'children')
      .leftJoinAndSelect('budget.budgetOpex', 'budgetOpex')
      .leftJoinAndSelect('budget.budgetCapex', 'budgetCapex')
      .leftJoinAndSelect('budgetCapex.budgetInvestments', 'budgetInvestments')
      .leftJoinAndSelect('budget.currencyUnit', 'currencyUnit')
      .leftJoinAndSelect('budget.budgetCode', 'budgetCode')
      .leftJoinAndSelect('budget.costcenterSubaccount', 'costcenterSubaccount')
      .where('budget.id = :id', { id: id })
      .getOne();
    return detail;
  }

  async getBudgetList(
    conditions: GetBudgetListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<BudgetModel>> {
    const repository = this.getRepository(BudgetEntity);
    let queryBuilder = repository.createQueryBuilder('budgets');

    queryBuilder.where('budgets.budgetType = :budgetType', {
      budgetType: conditions.budgetType,
    });

    if (typeof conditions.isLock == 'boolean') {
      queryBuilder.andWhere('budgets.isLock = :isLock', {
        isLock: conditions.isLock,
      });
    }

    if (conditions.ids) {
      queryBuilder.andWhere('budgets.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.createTypes) {
      queryBuilder.andWhere('budgets.createType IN (:...createTypes)', {
        createTypes: conditions.createTypes,
      });
    }

    if (conditions.adjustBudgetIds) {
      queryBuilder.andWhere('budgets.adjustBudgetId IN (:...adjustBudgetIds)', {
        adjustBudgetIds: conditions.adjustBudgetIds,
      });
    }

    if (conditions.currencyUnitIds) {
      queryBuilder.andWhere('budgets.currencyUnitId IN (:...currencyUnitIds)', {
        currencyUnitIds: conditions.currencyUnitIds,
      });
    }

    if (conditions.budgetCodeIds) {
      queryBuilder.andWhere('budgets.budgetCodeId IN (:...budgetCodeIds)', {
        budgetCodeIds: conditions.budgetCodeIds,
      });
    }

    if (conditions.costcenterSubaccountIds) {
      queryBuilder.andWhere(
        'budgets.costcenterSubaccountId IN (:...costcenterSubaccountIds)',
        {
          costcenterSubaccountIds: conditions.costcenterSubaccountIds,
        },
      );
    }

    if (conditions.minValue && conditions.maxValue) {
      queryBuilder.andWhere(
        'budgets.totalValue BETWEEN :minValue AND :maxValue',
        {
          minValue: conditions.minValue,
          maxValue: conditions.maxValue,
        },
      );
    } else if (conditions.minValue) {
      queryBuilder.andWhere('budgets.totalValue >= :minValue ', {
        minValue: conditions.minValue,
      });
    } else if (conditions.maxValue) {
      queryBuilder.andWhere('budgets.totalValue <= :maxValue ', {
        maxValue: conditions.maxValue,
      });
    }

    if (conditions.effectiveStartDate && conditions.effectiveEndDate) {
      queryBuilder.andWhere(
        'budgets.effectiveStartDate BETWEEN :effectiveStartDate AND :effectiveEndDate',
        {
          effectiveStartDate: conditions.effectiveStartDate,
          effectiveEndDate: conditions.effectiveEndDate,
        },
      );
    } else if (conditions.effectiveStartDate) {
      queryBuilder.andWhere(
        'budgets.effectiveStartDate >= :effectiveStartDate',
        {
          effectiveStartDate: conditions.effectiveStartDate,
        },
      );
    } else if (conditions.effectiveEndDate) {
      queryBuilder.andWhere('budgets.effectiveEndDate <= :effectiveEndDate', {
        effectiveEndDate: conditions.effectiveEndDate,
      });
    }

    if (conditions.createdAt) {
      queryBuilder.andWhere(
        'budgets.effectiveStartDate <= :createdAt AND budgets.effectiveEndDate >= :createdAt',
        {
          createdAt: conditions.createdAt,
        },
      );
    }

    if (conditions.searchID) {
      queryBuilder.andWhere('remove_unicode(budgets.code) ILIKE :searchID', {
        searchID: conditions.searchID,
      });
    }

    if (
      conditions.budgetType == EBudgetType.CAPEX &&
      conditions.searchClassify
    ) {
      queryBuilder.andWhere(
        'remove_unicode(budgetCapex.classify) ILIKE :searchClassify',
        {
          searchClassify: conditions.searchClassify,
        },
      );
    }

    ///Query join for budget
    queryBuilder
      .leftJoinAndSelect('budgets.parent', 'parent')
      .leftJoinAndSelect('budgets.children', 'children')
      .leftJoinAndSelect('budgets.budgetOpex', 'budgetOpex')
      .leftJoinAndSelect('budgets.budgetCapex', 'budgetCapex')
      .leftJoinAndSelect('budgetCapex.budgetInvestments', 'budgetInvestments')
      .leftJoinAndSelect('budgets.currencyUnit', 'currencyUnit')
      .leftJoinAndSelect('currencyUnit.currencyExchanges', 'currencyExchanges')
      .leftJoinAndSelect('budgets.budgetCode', 'budgetCode')
      .leftJoinAndSelect('budgets.costcenterSubaccount', 'costcenterSubaccount')
      .orderBy('budgets.createdAt', 'DESC');

    // ///Query join for budget - businessOwner
    queryBuilder.leftJoinAndSelect('budgetCode.businessOwner', 'businessOwner');

    queryBuilder.leftJoinAndSelect('budgetCode.cost', 'cost');

    // ///Query join for budget - cost center / sub account
    queryBuilder.leftJoinAndSelect('costcenterSubaccount.sector', 'sector');
    // .leftJoinAndSelect('costcenterSubaccount.company', 'company')
    // .leftJoinAndSelect('costcenterSubaccount.businessUnit', 'businessUnit')
    // .leftJoinAndSelect('costcenterSubaccount.department', 'department');

    ///Query join for budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'budgetCode.costcenterSubaccount',
    //   'budgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.sector',
    //   'budgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.company',
    //   'budgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.businessUnit',
    //   'budgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.department',
    //   'budgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent
    queryBuilder
      .leftJoinAndSelect('parent.budgetCode', 'parentBudgetCode')
      .leftJoinAndSelect(
        'parent.costcenterSubaccount',
        'parentCostcenterSubaccount',
      );

    ///Query join for parent - businessOwner
    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.businessOwner',
      'parentBusinessOwner',
    );

    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.cost',
      'parentBudgetCodeCost',
    );

    ///Query join for parent budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCode.costcenterSubaccount',
    //   'parentBudgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.sector',
    //   'parentBudgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.company',
    //   'parentBudgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.businessUnit',
    //   'parentBudgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.department',
    //   'parentBudgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent - cost center / sub account
    queryBuilder.leftJoinAndSelect(
      'parentCostcenterSubaccount.sector',
      'parentCostcenterSubaccountSector',
    );
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.company',
    //   'parentCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.businessUnit',
    //   'parentCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.department',
    //   'parentCostcenterSubaccountDepartment',
    // );

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    return await this.pagination(queryBuilder, conditions);
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<BudgetEntity>,
    jwtPayload: any,
    conditions: GetBudgetListDto | GetDetailBudgetDto,
  ) {
    //cho budget code
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    //cho cost center / sub account
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.companyCodes = jwtPayload?.companies;
    conditions.businessUnitCodes = jwtPayload?.businessUnits;
    conditions.departmentCodes = jwtPayload?.departments;
    conditions.costCodes = jwtPayload?.costs;
    //For Parent Budget
    let parentCostcenterQuery = '';
    // let parentBudgetCodeCostcenterQuery = '';
    //For current Budget
    let budgetQuery = [];
    // let budgetCodeCostcenterQuery = '';

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     ECostPermission.CREATE,
    //     ECostPermission.EDIT,
    //   ])
    // ) {
    //   parentCostcenterQuery += `AND (parentBudgetCodeCost.id IS NULL OR parentBudgetCodeCost.code IN (:...costCodes))`;
    //   budgetQuery.push(`(cost.id IS NULL OR cost.code IN (:...costCodes))`);
    // }

    if (
      !parseScopes(jwtPayload.scopes, [
        EBusinessOwnerPermission.CREATE,
        EBusinessOwnerPermission.EDIT,
      ]) &&
      conditions.businessOwnerCodes?.length
    ) {
      parentCostcenterQuery += `AND (parentBusinessOwner.id IS NULL OR parentBusinessOwner.code IN (:...businessOwnerCodes))`;
      budgetQuery.push(
        `(businessOwner.id IS NULL OR businessOwner.code IN (:...businessOwnerCodes))`,
      );
    }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     ESectorPermission.CREATE,
    //     ESectorPermission.EDIT,
    //   ])
    // ) {
    //   parentCostcenterQuery += `AND parentCostcenterSubaccountSector.code IN (:...sectorCodes)`;
    //   // parentBudgetCodeCostcenterQuery += `AND parentBudgetCodeCostcenterSubaccountSector.code IN (:...sectorCodes)`;

    //   budgetQuery.push(`sector.code IN (:...sectorCodes)`);
    //   // budgetCodeCostcenterQuery += ` AND budgetCodeCostcenterSubaccountSector.code IN (:...sectorCodes)`;
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     ECompanyPermission.CREATE,
    //     ECompanyPermission.EDIT,
    //   ])
    // ) {
    //   parentCostcenterQuery += `AND parentCostcenterSubaccountCompany.code IN (:...companyCodes)`;
    //   parentBudgetCodeCostcenterQuery += `AND parentBudgetCodeCostcenterSubaccountCompany.code IN (:...companyCodes)`;

    //   budgetQuery += `company.code IN (:...companyCodes) AND `;
    //   budgetCodeCostcenterQuery += ` AND budgetCodeCostcenterSubaccountCompany.code IN (:...companyCodes)`;
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EBusinessUnitPermission.CREATE,
    //     EBusinessUnitPermission.EDIT,
    //   ])
    // ) {
    //   parentCostcenterQuery += `AND parentCostcenterSubaccountBusinessUnit.code IN (:...businessUnitCodes)`;
    //   parentBudgetCodeCostcenterQuery += `AND parentBudgetCodeCostcenterSubaccountBusinessUnit.code IN (:...businessUnitCodes)`;

    //   budgetQuery += `businessUnit.code IN (:...businessUnitCodes) AND `;
    //   budgetCodeCostcenterQuery += ` AND budgetCodeCostcenterSubaccountBusinessUnit.code IN (:...businessUnitCodes)`;
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EDepartmentPermission.CREATE,
    //     EDepartmentPermission.EDIT,
    //   ])
    // ) {
    //   parentCostcenterQuery += `AND parentCostcenterSubaccountDepartment.code IN (:...departmentCodes)`;
    //   parentBudgetCodeCostcenterQuery += `AND parentBudgetCodeCostcenterSubaccountDepartment.code IN (:...departmentCodes)`;

    //   budgetQuery += `department.code IN (:...departmentCodes) AND `;
    //   budgetCodeCostcenterQuery += ` AND budgetCodeCostcenterSubaccountDepartment.code IN (:...departmentCodes)`;
    // }

    queryBuilder.andWhere(
      `(${queryBuilder.alias}.adjustBudgetId IS NULL 
      OR (${queryBuilder.alias}.adjustBudgetId IS NOT NULL ${parentCostcenterQuery}))`,
      {
        businessOwnerCodes: conditions.businessOwnerCodes,
        // sectorCodes: conditions.sectorCodes,
        // costCodes: conditions.costCodes,
        // companyCodes: conditions.companyCodes,
        // businessUnitCodes: conditions.businessUnitCodes,
        // departmentCodes: conditions.departmentCodes,
      },
    );

    //Query for code in budget
    if (budgetQuery.length) {
      queryBuilder.andWhere(`${budgetQuery.join(' AND ')}`, {
        businessOwnerCodes: conditions.businessOwnerCodes,
        // sectorCodes: conditions.sectorCodes,
        // costCodes: conditions.costCodes,
        // companyCodes: conditions.companyCodes,
        // businessUnitCodes: conditions.businessUnitCodes,
        // departmentCodes: conditions.departmentCodes,
      });
    }

    return queryBuilder;
  }

  async getBudgetByIdAndBudgetType(
    id: string,
    budgetType: EBudgetType,
  ): Promise<BudgetModel> {
    const repository = this.getRepository(BudgetEntity);
    const detail = await repository.findOne({
      where: { id: id, budgetType: budgetType },
    });
    return detail;
  }

  async lockBudget(ids: string[]): Promise<void> {
    const repository = this.getRepository(BudgetEntity);
    await repository.update({ id: In(ids) }, { isLock: true });
  }

  async unlockBudget(ids: string[]): Promise<void> {
    const repository = this.getRepository(BudgetEntity);
    await repository.update({ id: In(ids) }, { isLock: false });
  }

  async getBudgetByCode(code: string): Promise<BudgetModel> {
    const repository = this.getRepository(BudgetEntity);
    const detail = await repository.findOne({
      where: { code: code },
    });
    return detail;
  }

  async getBudgetDetail(
    conditions: GetDetailBudgetDto,
    jwtPayload: any,
  ): Promise<BudgetModel> {
    const repository = this.getRepository(BudgetEntity);
    let queryBuilder = repository.createQueryBuilder('budgets');

    queryBuilder.where('budgets.id = :id', { id: conditions.id });

    ///Query join for budget
    queryBuilder
      .leftJoinAndSelect('budgets.parent', 'parent')
      .leftJoinAndSelect('budgets.budgetOpex', 'budgetOpex')
      .leftJoinAndSelect('budgets.budgetCapex', 'budgetCapex')
      .leftJoinAndSelect('budgetCapex.budgetInvestments', 'budgetInvestments')
      .leftJoinAndSelect('budgets.currencyUnit', 'currencyUnit')
      .leftJoinAndSelect('budgets.budgetCode', 'budgetCode')
      .leftJoinAndSelect('budgets.costcenterSubaccount', 'costcenterSubaccount')
      .orderBy('budgets.createdAt', 'DESC');

    // ///Query join for budget - businessOwner
    queryBuilder.leftJoinAndSelect('budgetCode.businessOwner', 'businessOwner');

    queryBuilder.leftJoinAndSelect('budgetCode.cost', 'cost');

    // ///Query join for budget - cost center / sub account
    queryBuilder.leftJoinAndSelect('costcenterSubaccount.sector', 'sector');
    // .leftJoinAndSelect('costcenterSubaccount.company', 'company')
    // .leftJoinAndSelect('costcenterSubaccount.businessUnit', 'businessUnit')
    // .leftJoinAndSelect('costcenterSubaccount.department', 'department');

    ///Query join for budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'budgetCode.costcenterSubaccount',
    //   'budgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.sector',
    //   'budgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.company',
    //   'budgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.businessUnit',
    //   'budgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.department',
    //   'budgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent
    queryBuilder
      .leftJoinAndSelect('parent.budgetCode', 'parentBudgetCode')
      .leftJoinAndSelect(
        'parent.costcenterSubaccount',
        'parentCostcenterSubaccount',
      );

    ///Query join for parent - businessOwner
    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.businessOwner',
      'parentBusinessOwner',
    );

    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.cost',
      'parentBudgetCodeCost',
    );

    ///Query join for parent budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCode.costcenterSubaccount',
    //   'parentBudgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.sector',
    //   'parentBudgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.company',
    //   'parentBudgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.businessUnit',
    //   'parentBudgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.department',
    //   'parentBudgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent - cost center / sub account
    queryBuilder.leftJoinAndSelect(
      'parentCostcenterSubaccount.sector',
      'parentCostcenterSubaccountSector',
    );
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.company',
    //   'parentCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.businessUnit',
    //   'parentCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.department',
    //   'parentCostcenterSubaccountDepartment',
    // );

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    const detail = await queryBuilder.getOne();
    return detail;
  }

  async getBudgetsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    budgetType: EBudgetType,
  ): Promise<BudgetModel[]> {
    const repository = this.getRepository(BudgetEntity);
    let queryBuilder = repository.createQueryBuilder('budgets');

    queryBuilder.where('budgets.budgetType = :type', { type: budgetType });

    ///Query join for budget
    queryBuilder
      .leftJoinAndSelect('budgets.parent', 'parent')
      .leftJoinAndSelect('budgets.budgetOpex', 'budgetOpex')
      .leftJoinAndSelect('budgets.budgetCapex', 'budgetCapex')
      .leftJoinAndSelect('budgetCapex.budgetInvestments', 'budgetInvestments')
      .leftJoinAndSelect('budgets.currencyUnit', 'currencyUnit')
      .leftJoinAndSelect('budgets.budgetCode', 'budgetCode')
      .leftJoinAndSelect('budgets.costcenterSubaccount', 'costcenterSubaccount')
      .orderBy('budgets.createdAt', 'DESC');

    // ///Query join for budget - businessOwner
    queryBuilder.leftJoinAndSelect('budgetCode.businessOwner', 'businessOwner');

    queryBuilder.leftJoinAndSelect('budgetCode.cost', 'cost');

    // ///Query join for budget - cost center / sub account
    queryBuilder.leftJoinAndSelect('costcenterSubaccount.sector', 'sector');
    // .leftJoinAndSelect('costcenterSubaccount.company', 'company')
    // .leftJoinAndSelect('costcenterSubaccount.businessUnit', 'businessUnit')
    // .leftJoinAndSelect('costcenterSubaccount.department', 'department');

    ///Query join for budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'budgetCode.costcenterSubaccount',
    //   'budgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.sector',
    //   'budgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.company',
    //   'budgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.businessUnit',
    //   'budgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.department',
    //   'budgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent
    queryBuilder
      .leftJoinAndSelect('parent.budgetCode', 'parentBudgetCode')
      .leftJoinAndSelect(
        'parent.costcenterSubaccount',
        'parentCostcenterSubaccount',
      );

    ///Query join for parent - businessOwner
    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.businessOwner',
      'parentBusinessOwner',
    );

    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.cost',
      'parentBudgetCodeCost',
    );

    ///Query join for parent budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCode.costcenterSubaccount',
    //   'parentBudgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.sector',
    //   'parentBudgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.company',
    //   'parentBudgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.businessUnit',
    //   'parentBudgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.department',
    //   'parentBudgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent - cost center / sub account
    queryBuilder.leftJoinAndSelect(
      'parentCostcenterSubaccount.sector',
      'parentCostcenterSubaccountSector',
    );
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.company',
    //   'parentCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.businessUnit',
    //   'parentCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.department',
    //   'parentCostcenterSubaccountDepartment',
    // );

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetBudgetListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('budgets.code IN (:...codes)', { codes: codes });
      } else {
        return [];
      }
    }

    return await queryBuilder.getMany();
  }

  async getBudgetByIds(ids: string[], jwtPayload: any): Promise<BudgetModel[]> {
    const repository = this.getRepository(BudgetEntity);
    let queryBuilder = repository.createQueryBuilder('budgets');

    if (ids.length) {
      queryBuilder.andWhere('budgets.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    ///Query join for budget
    queryBuilder
      .leftJoinAndSelect('budgets.parent', 'parent')
      .leftJoinAndSelect('budgets.budgetOpex', 'budgetOpex')
      .leftJoinAndSelect('budgets.budgetCapex', 'budgetCapex')
      .leftJoinAndSelect('budgetCapex.budgetInvestments', 'budgetInvestments')
      .leftJoinAndSelect('budgets.currencyUnit', 'currencyUnit')
      .leftJoinAndSelect('budgets.budgetCode', 'budgetCode')
      .leftJoinAndSelect('budgets.costcenterSubaccount', 'costcenterSubaccount')
      .orderBy('budgets.createdAt', 'DESC');

    // ///Query join for budget - businessOwner
    queryBuilder.leftJoinAndSelect('budgetCode.businessOwner', 'businessOwner');

    queryBuilder.leftJoinAndSelect('budgetCode.cost', 'cost');

    // ///Query join for budget - cost center / sub account
    queryBuilder.leftJoinAndSelect('costcenterSubaccount.sector', 'sector');
    // .leftJoinAndSelect('costcenterSubaccount.company', 'company')
    // .leftJoinAndSelect('costcenterSubaccount.businessUnit', 'businessUnit')
    // .leftJoinAndSelect('costcenterSubaccount.department', 'department');

    ///Query join for budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'budgetCode.costcenterSubaccount',
    //   'budgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.sector',
    //   'budgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.company',
    //   'budgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.businessUnit',
    //   'budgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.department',
    //   'budgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent
    queryBuilder
      .leftJoinAndSelect('parent.budgetCode', 'parentBudgetCode')
      .leftJoinAndSelect(
        'parent.costcenterSubaccount',
        'parentCostcenterSubaccount',
      );

    ///Query join for parent - businessOwner
    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.businessOwner',
      'parentBusinessOwner',
    );

    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.cost',
      'parentBusinessCost',
    );

    ///Query join for parent budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCode.costcenterSubaccount',
    //   'parentBudgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.sector',
    //   'parentBudgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.company',
    //   'parentBudgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.businessUnit',
    //   'parentBudgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.department',
    //   'parentBudgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent - cost center / sub account
    queryBuilder.leftJoinAndSelect(
      'parentCostcenterSubaccount.sector',
      'parentCostcenterSubaccountSector',
    );
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.company',
    //   'parentCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.businessUnit',
    //   'parentCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.department',
    //   'parentCostcenterSubaccountDepartment',
    // );

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetBudgetListDto({}),
      );
    }

    return await queryBuilder.getMany();
  }

  async getBudgetsForImportAdjustBudgetWithRole(
    budgetCodes: string[],
    costcenterCodes: string[],
    effectiveStartDates: string[],
    jwtPayload: any,
    budgetType: EBudgetType,
  ): Promise<BudgetModel[]> {
    const repository = this.getRepository(BudgetEntity);
    let queryBuilder = repository.createQueryBuilder('budgets');

    ///Query join for budget
    queryBuilder
      .leftJoinAndSelect('budgets.parent', 'parent')
      .leftJoinAndSelect('budgets.budgetOpex', 'budgetOpex')
      .leftJoinAndSelect('budgets.budgetCapex', 'budgetCapex')
      .leftJoinAndSelect('budgetCapex.budgetInvestments', 'budgetInvestments')
      .leftJoinAndSelect('budgets.currencyUnit', 'currencyUnit')
      .leftJoinAndSelect('budgets.budgetCode', 'budgetCode')
      .leftJoinAndSelect('budgets.costcenterSubaccount', 'costcenterSubaccount')
      .orderBy('budgets.createdAt', 'DESC');

    // ///Query join for budget - businessOwner
    queryBuilder.leftJoinAndSelect('budgetCode.businessOwner', 'businessOwner');

    queryBuilder.leftJoinAndSelect('budgetCode.cost', 'cost');

    // ///Query join for budget - cost center / sub account
    queryBuilder.leftJoinAndSelect('costcenterSubaccount.sector', 'sector');
    // .leftJoinAndSelect('costcenterSubaccount.company', 'company')
    // .leftJoinAndSelect('costcenterSubaccount.businessUnit', 'businessUnit')
    // .leftJoinAndSelect('costcenterSubaccount.department', 'department');

    ///Query join for budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'budgetCode.costcenterSubaccount',
    //   'budgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.sector',
    //   'budgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.company',
    //   'budgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.businessUnit',
    //   'budgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'budgetCodeCostcenterSubaccount.department',
    //   'budgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent
    queryBuilder
      .leftJoinAndSelect('parent.budgetCode', 'parentBudgetCode')
      .leftJoinAndSelect(
        'parent.costcenterSubaccount',
        'parentCostcenterSubaccount',
      );

    ///Query join for parent - businessOwner
    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.businessOwner',
      'parentBusinessOwner',
    );

    queryBuilder.leftJoinAndSelect(
      'parentBudgetCode.cost',
      'parentBudgetCodeCost',
    );

    ///Query join for parent budget code - cost center / sub account
    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCode.costcenterSubaccount',
    //   'parentBudgetCodeCostcenterSubaccount',
    // );

    // queryBuilder.leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.sector',
    //   'parentBudgetCodeCostcenterSubaccountSector',
    // );
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.company',
    //   'parentBudgetCodeCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.businessUnit',
    //   'parentBudgetCodeCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentBudgetCodeCostcenterSubaccount.department',
    //   'parentBudgetCodeCostcenterSubaccountDepartment',
    // );

    ///Query join for parent - cost center / sub account
    queryBuilder.leftJoinAndSelect(
      'parentCostcenterSubaccount.sector',
      'parentCostcenterSubaccountSector',
    );
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.company',
    //   'parentCostcenterSubaccountCompany',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.businessUnit',
    //   'parentCostcenterSubaccountBusinessUnit',
    // )
    // .leftJoinAndSelect(
    //   'parentCostcenterSubaccount.department',
    //   'parentCostcenterSubaccountDepartment',
    // );

    queryBuilder.andWhere('budgets.createType = :createType', {
      createType: EBudgetCreateType.NEW,
    });
    queryBuilder.andWhere('budgets.budgetType = :type', { type: budgetType });

    if (budgetCodes?.length) {
      queryBuilder.andWhere('budgetCode.code IN (:...budgetCodes)', {
        budgetCodes: budgetCodes,
      });
    } else {
      return [];
    }

    if (costcenterCodes?.length) {
      queryBuilder.andWhere(
        'costcenterSubaccount.code IN (:...costcenterCodes)',
        { costcenterCodes: costcenterCodes },
      );
    } else {
      return [];
    }

    if (effectiveStartDates?.length) {
      queryBuilder.andWhere(
        'budgets.effectiveStartDate IN (:...effectiveStartDates)',
        { effectiveStartDates: effectiveStartDates },
      );
    } else {
      return [];
    }

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     new GetBudgetListDto({}),
    //   );
    // }

    return await queryBuilder.getMany();
  }

  async checkDuplicateBudget(data: BudgetModel): Promise<BudgetModel[]> {
    const repository = this.getRepository(BudgetEntity);
    let queryBuilder = repository.createQueryBuilder('budgets');

    queryBuilder.leftJoinAndSelect('budgets.budgetOpex', 'budgetOpex');

    queryBuilder.leftJoinAndSelect('budgets.budgetCapex', 'budgetCapex');
    queryBuilder.leftJoinAndSelect(
      'budgetCapex.budgetInvestments',
      'budgetInvestments',
    );

    queryBuilder
      .andWhere('budgets.budgetCodeId = :budgetCodeId', {
        budgetCodeId: data.budgetCodeId,
      })
      .andWhere('budgets.costcenterSubaccountId = :costcenterSubaccountId', {
        costcenterSubaccountId: data.costcenterSubaccountId,
      })
      .andWhere(
        '(budgets.effectiveStartDate <= :endDate AND budgets.effectiveEndDate >= :startDate)',
        {
          startDate: data.effectiveStartDate,
          endDate: data.effectiveEndDate,
        },
      )
      .andWhere('budgets.createType = :createType', {
        createType: EBudgetCreateType.NEW,
      });
    return await queryBuilder.getMany();
  }

  async reportBudgetDetail(
    conditions: ReportBudgetDto,
    jwtPayload: any,
  ): Promise<ResponseDto<IGroupBudgetByBudgetCodeAndCostCenter>> {
    const groupBudgetCodeAndCostcenter =
      await this.queryBudgetCodeAndCostCenterOfBudget(conditions, jwtPayload);

    const queryReportBudgetDetails = await this.queryReportBudgets(
      conditions,
      jwtPayload,
    );
    const budgets = await queryReportBudgetDetails.getMany();

    const budgetsByBudgetCodeAndCostcenter: IGroupBudgetByBudgetCodeAndCostCenter[] =
      [];

    for (let i = 0; i < groupBudgetCodeAndCostcenter?.results?.length; i++) {
      budgetsByBudgetCodeAndCostcenter.push({
        budgetCodeId: groupBudgetCodeAndCostcenter?.results[i].budgetCodeId,
        costcenterSubaccountId:
          groupBudgetCodeAndCostcenter?.results[i].costcenterSubaccountId,
        budgets: budgets.filter(
          (item) =>
            item.budgetCodeId ===
              groupBudgetCodeAndCostcenter?.results[i].budgetCodeId &&
            item.costcenterSubaccountId ===
              groupBudgetCodeAndCostcenter?.results[i].costcenterSubaccountId,
        ),
      });
    }

    return new ResponseDto<IGroupBudgetByBudgetCodeAndCostCenter>(
      budgetsByBudgetCodeAndCostcenter,
      conditions.page,
      conditions.limit,
      groupBudgetCodeAndCostcenter?.total || 0,
    );
  }

  private async queryBudgetCodeAndCostCenterOfBudget(
    conditions: ReportBudgetDto,
    jwtPayload: any,
  ): Promise<ResponseDto<any>> {
    const repository = this.getRepository(BudgetEntity);

    const { queryFromWhere, limitOffset, paramsObject, paramsObjectCount } =
      await this.getQueryFromWhereForBudget(conditions, jwtPayload);

    /// SUB QUERY vì cần order by theo created_at
    const query = `
    SELECT 
        "sub"."budgetCodeId",
        "sub"."costcenterSubaccountId"
    FROM (
        SELECT
            "budgets"."budget_code_id" AS "budgetCodeId",
            "budgets"."costcenter_subaccount_id" AS "costcenterSubaccountId"
        ${queryFromWhere}
    ) sub
    GROUP BY
        "sub"."budgetCodeId",
        "sub"."costcenterSubaccountId"
    ${limitOffset}
    `;

    const queryCount = `
    SELECT
        "sub"."budgetCodeId",
        "sub"."costcenterSubaccountId"
    FROM (
        SELECT
            "budgets"."budget_code_id" AS "budgetCodeId",
            "budgets"."costcenter_subaccount_id" AS "costcenterSubaccountId"
        ${queryFromWhere}
    ) sub
    GROUP BY
        "sub"."budgetCodeId",
        "sub"."costcenterSubaccountId"
    `;

    const [data, total] = await Promise.all([
      repository.query(query, Object.values(paramsObject)),
      await repository.query(queryCount, Object.values(paramsObjectCount)),
    ]);

    return new ResponseDto<any>(
      data,
      conditions.page,
      conditions.limit,
      total?.length,
    );
  }

  private async queryReportBudgets(
    conditions: ReportBudgetDto,
    jwtPayload: any,
  ) {
    const repository = this.getRepository(BudgetEntity);
    let queryBuilder = repository
      .createQueryBuilder('budgets')
      .select([
        'budgets.id',
        'budgets.code',
        'budgets.createType',
        'budgets.budgetType',
        'budgets.totalValue',
        'budgets.note',
        'budgets.note2',
        'budgets.budgetCodeId',
        'budgets.costcenterSubaccountId',
        'budgets.currencyUnitId',
        'budgets.effectiveStartDate',
        'budgets.effectiveEndDate',
        'budgets.createdAt',
      ]);

    /// OPEX
    queryBuilder
      .leftJoin('budgets.budgetOpex', 'budgetOpex')
      .addSelect(['budgetOpex.id', 'budgetOpex.form', 'budgetOpex.operations']);

    /// CAPEX
    queryBuilder
      .leftJoin('budgets.budgetCapex', 'budgetCapex')
      .addSelect([
        'budgetCapex.id',
        'budgetCapex.startDate',
        'budgetCapex.expectedAcceptanceTime',
        'budgetCapex.classify',
        'budgetCapex.priority',
        'budgetCapex.investmentPurpose',
      ]);

    queryBuilder
      .leftJoin('budgetCapex.budgetInvestments', 'budgetInvestments')
      .addSelect([
        'budgetInvestments.investment',
        'budgetInvestments.quantity',
        'budgetInvestments.price',
        'budgetInvestments.transportationCosts',
      ]);

    /// CURRENCY
    queryBuilder
      .leftJoin('budgets.currencyUnit', 'currencyUnit')
      .addSelect([
        'currencyUnit.id',
        'currencyUnit.name',
        'currencyUnit.currencyCode',
        'currencyUnit.description',
        'currencyUnit.status',
      ]);

    queryBuilder
      .leftJoin('currencyUnit.currencyExchanges', 'currencyExchanges')
      .addSelect([
        'currencyExchanges.id',
        'currencyExchanges.exchangeRate',
        'currencyExchanges.effectiveStartDate',
        'currencyExchanges.effectiveEndDate',
        'currencyExchanges.exchangeBudget',
      ]);

    /// BUDGET CODE
    queryBuilder
      .innerJoin('budgets.budgetCode', 'budgetCode')
      .addSelect([
        'budgetCode.id',
        'budgetCode.code',
        'budgetCode.name',
        'budgetCode.description',
        'budgetCode.status',
        'budgetCode.budgetType',
        'budgetCode.costId',
      ]);

    queryBuilder
      .innerJoin('budgetCode.businessOwner', 'businessOwner')
      .addSelect([
        'businessOwner.id',
        'businessOwner.code',
        'businessOwner.name',
        'businessOwner.description',
        'businessOwner.status',
      ]);

    queryBuilder
      .leftJoin('budgetCode.cost', 'cost')
      .addSelect([
        'cost.id',
        'cost.code',
        'cost.name',
        'cost.description',
        'cost.status',
        'cost.groupCost',
      ]);

    /// COST CENTER
    queryBuilder
      .innerJoin('budgets.costcenterSubaccount', 'costcenterSubaccount')
      .addSelect([
        'costcenterSubaccount.id',
        'costcenterSubaccount.code',
        'costcenterSubaccount.name',
        'costcenterSubaccount.description',
        'costcenterSubaccount.status',
        'costcenterSubaccount.note1',
        'costcenterSubaccount.note2',
        'costcenterSubaccount.note3',
        'costcenterSubaccount.note4',
        'costcenterSubaccount.note5',
        'costcenterSubaccount.note6',
        'costcenterSubaccount.sectorId',
        'costcenterSubaccount.effectiveStartDate',
        'costcenterSubaccount.effectiveEndDate',
      ]);

    queryBuilder = this.queryReportBudgetConditions(
      queryBuilder,
      jwtPayload,
      conditions,
    );

    queryBuilder.orderBy('budgets.created_at', 'ASC');
    queryBuilder.addOrderBy('currencyExchanges.effective_start_date', 'DESC');

    return queryBuilder;
  }

  private queryReportBudgetConditions(
    queryBuilder: SelectQueryBuilder<BudgetEntity>,
    jwtPayload: any,
    conditions: ReportBudgetDto,
  ) {
    /// BUDGET
    if (conditions.from && conditions.to) {
      queryBuilder.andWhere(
        '(budgets.effectiveStartDate <= :endDate AND budgets.effectiveEndDate >= :startDate)',
        {
          startDate: conditions.from,
          endDate: conditions.to,
        },
      );
    }

    /// BUDGET CODE
    queryBuilder.andWhere('budgetCode.budgetType = :budgetType', {
      budgetType: conditions.budgetType,
    });

    if (conditions.budgetCodeIds && conditions.budgetCodeIds.length) {
      queryBuilder.andWhere('budgetCode.id IN (:...budgetCodeIds)', {
        budgetCodeIds: conditions.budgetCodeIds,
      });
    }

    /// BUSINESS OWNER
    if (conditions.businessOwnerIds && conditions.businessOwnerIds.length) {
      queryBuilder.andWhere('businessOwner.id IN (:...businessOwnerIds)', {
        businessOwnerIds: conditions.businessOwnerIds,
      });
    }

    /// COST CENTER
    if (conditions.sectorIds && conditions.sectorIds.length) {
      queryBuilder.andWhere(
        'costcenterSubaccount.sectorId IN (:...sectorIds)',
        {
          sectorIds: conditions.sectorIds,
        },
      );
    }

    if (conditions.costCenterIds && conditions.costCenterIds.length) {
      queryBuilder.andWhere('costcenterSubaccount.id IN (:...costCenterIds)', {
        costCenterIds: conditions.costCenterIds,
      });
    }

    /// QUERY ROLE BUSINESS OWNER
    this.queryReportBudgetWithRoleForBusinessOwner(
      queryBuilder,
      jwtPayload,
      conditions,
    );

    return queryBuilder;
  }

  private queryReportBudgetWithRoleForBusinessOwner(
    queryBuilder: SelectQueryBuilder<BudgetEntity>,
    jwtPayload: any,
    conditions: ReportBudgetDto,
  ) {
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;

    if (!(jwtPayload?.isSuperAdmin ?? false)) {
      if (
        !parseScopes(jwtPayload?.scopes, [
          EBusinessOwnerPermission.CREATE,
          EBusinessOwnerPermission.EDIT,
        ]) &&
        conditions.businessOwnerCodes?.length
      ) {
        queryBuilder.andWhere(
          `(businessOwner.id IS NULL OR businessOwner.code IN (:...businessOwnerCodes))`,
          {
            businessOwnerCodes: conditions.businessOwnerCodes,
          },
        );
      }
    }

    return queryBuilder;
  }

  async reportBudgetOverview(
    conditions: ReportBudgetDto,
    jwtPayload: any,
  ): Promise<ResponseDto<IGroupBudgetByBusinessOwnerAndCostCenter>> {
    const group = await this.queryBusinessOwnerAndCostCenterOfBudget(
      conditions,
      jwtPayload,
    );

    /// Lấy Ngân sách phù hợp điều kiện
    const queryReportBudgetDetails = await this.queryReportBudgets(
      conditions,
      jwtPayload,
    );
    const budgets = await queryReportBudgetDetails.getMany();

    const budgetsByBusinessOwnerAndCostcenter: IGroupBudgetByBusinessOwnerAndCostCenter[] =
      [];

    for (let i = 0; i < group?.results?.length; i++) {
      budgetsByBusinessOwnerAndCostcenter.push({
        businessOwnerId: group?.results[i]?.businessOwnerId,
        division: group?.results[i]?.note2,
        costId: group?.results[i]?.costId,
        budgets: budgets.filter((item) =>
          conditions.budgetType == EBudgetType.CAPEX
            ? item.budgetCode?.businessOwner?.id ==
                group?.results[i]?.businessOwnerId &&
              item.costcenterSubaccount?.note2 == group?.results[i]?.note2
            : item.budgetCode?.businessOwner?.id ==
                group?.results[i]?.businessOwnerId &&
              item.costcenterSubaccount?.note2 == group?.results[i]?.note2 &&
              item.budgetCode.costId == group?.results[i]?.costId,
        ),
      });
    }

    return new ResponseDto<IGroupBudgetByBusinessOwnerAndCostCenter>(
      budgetsByBusinessOwnerAndCostcenter,
      conditions.page,
      conditions.limit,
      group?.total || 0,
    );
  }

  private async queryBusinessOwnerAndCostCenterOfBudget(
    conditions: ReportBudgetDto,
    jwtPayload: any,
  ): Promise<ResponseDto<any>> {
    const repository = this.getRepository(BudgetEntity);

    const { queryFromWhere, limitOffset, paramsObject, paramsObjectCount } =
      await this.getQueryFromWhereForBudget(conditions, jwtPayload);

    let selectAndGroup = ``;
    let selectAndGroupForSub = ``;

    if (conditions.budgetType == EBudgetType.OPEX) {
      selectAndGroup = `
        "businessOwner"."id" AS "businessOwnerId",
        "costcenterSubaccount"."note2" AS "note2",
        "cost"."id" AS "costId"
        `;

      selectAndGroupForSub = `
        "sub"."businessOwnerId",
        "sub"."note2",
        "sub"."costId"
        `;
    } else {
      selectAndGroup = `
        "businessOwner"."id" AS "businessOwnerId",
        "costcenterSubaccount"."note2" AS "note2"
        `;

      selectAndGroupForSub = `
        "sub"."businessOwnerId",
        "sub"."note2"
        `;
    }

    /// SUB QUERY vì cần order by theo created_at
    const query = `
    SELECT 
        ${selectAndGroupForSub}
    FROM (
        SELECT
            ${selectAndGroup}
        ${queryFromWhere}
    ) sub
    GROUP BY
        ${selectAndGroupForSub}
    ${limitOffset}
    `;

    const queryCount = `
    SELECT
        ${selectAndGroupForSub}
    FROM (
        SELECT
            ${selectAndGroup}
        ${queryFromWhere}
    ) sub
    GROUP BY
        ${selectAndGroupForSub}
    `;

    const [data, total] = await Promise.all([
      repository.query(query, Object.values(paramsObject)),
      await repository.query(queryCount, Object.values(paramsObjectCount)),
    ]);

    return new ResponseDto<any>(
      data,
      conditions.page,
      conditions.limit,
      total?.length,
    );
  }

  async getQueryFromWhereForBudget(
    conditions: ReportBudgetDto,
    jwtPayload: any,
  ) {
    conditions.businessOwnerCodes = jwtPayload?.businessOwners;
    conditions.fucntionUnitCodes = jwtPayload?.functionUnits;

    const paramsObject = {
      budgetType: conditions.budgetType,
      businessOwnerIds: conditions.businessOwnerIds,
      businessOwnerCodes: conditions.businessOwnerCodes?.filter(Boolean),
      sectorIds: conditions.sectorIds,
      budgetCodeIds: conditions.budgetCodeIds,
      costCenterIds: conditions.costCenterIds,
      fucntionUnitCodes: conditions.fucntionUnitCodes,
      to: conditions.to,
      from: conditions.from,
      /// Limit Offset phải để cuối cùng
      limit: conditions.limit,
      offset: (conditions.page - 1) * conditions.limit,
    };

    const paramsObjectCount = {
      budgetType: conditions.budgetType,
      businessOwnerIds: conditions.businessOwnerIds,
      businessOwnerCodes: conditions.businessOwnerCodes?.filter(Boolean),
      sectorIds: conditions.sectorIds,
      budgetCodeIds: conditions.budgetCodeIds,
      costCenterIds: conditions.costCenterIds,
      fucntionUnitCodes: conditions.fucntionUnitCodes,
      to: conditions.to,
      from: conditions.from,
    };

    Object.keys(paramsObject).forEach((key) => {
      if (
        (!paramsObject[key] && paramsObject[key] != 0) ||
        paramsObject[key]?.length == 0
      ) {
        delete paramsObject[key];
      }
    });

    Object.keys(paramsObjectCount).forEach((key) => {
      if (
        (!paramsObjectCount[key] && paramsObjectCount[key] != 0) ||
        paramsObjectCount[key]?.length == 0
      ) {
        delete paramsObjectCount[key];
      }
    });

    if (
      jwtPayload?.isSuperAdmin ||
      parseScopes(jwtPayload?.scopes, [
        EBusinessOwnerPermission.CREATE,
        EBusinessOwnerPermission.EDIT,
      ])
    ) {
      delete paramsObject['businessOwnerCodes'];
      delete paramsObjectCount['businessOwnerCodes'];
    }

    /// [1] JOIN BUDGET CODE
    let queryFromForJoinBudgetCode = `
        INNER JOIN "budget_code" "budgetCode" ON "budgetCode"."id" = "budgets"."budget_code_id"
        AND "budgetCode"."deleted_at" IS NULL 
        AND "budgetCode"."budget_type" = $${getIndexOfKeyInObject(paramsObject, 'budgetType') + 1}
        
    `;

    if (
      conditions.budgetCodeIds &&
      conditions.budgetCodeIds.length &&
      getIndexOfKeyInObject(paramsObject, 'budgetCodeIds') >= 0
    ) {
      queryFromForJoinBudgetCode += `
        AND "budgetCode"."id" = ANY($${getIndexOfKeyInObject(paramsObject, 'budgetCodeIds') + 1})
      `;
    }

    queryFromForJoinBudgetCode += `
        LEFT JOIN "costs" "cost" ON "cost"."id" = "budgetCode"."cost_id"
        AND "cost"."deleted_at" IS NULL 
    `;

    /// [2] JOIN BUSINESS OWNER
    let queryFromForJoinBusinessOwner = `
        INNER JOIN "business_owners" "businessOwner" ON "businessOwner"."id" = "budgetCode"."business_owner_id"
        AND "businessOwner"."deleted_at" IS NULL 
    `;

    if (
      conditions.businessOwnerIds &&
      conditions.businessOwnerIds.length &&
      getIndexOfKeyInObject(paramsObject, 'businessOwnerIds') >= 0
    ) {
      queryFromForJoinBusinessOwner += `
        AND "businessOwner"."id" = ANY($${getIndexOfKeyInObject(paramsObject, 'businessOwnerIds') + 1})
      `;
    }

    if (
      !(jwtPayload?.isSuperAdmin ?? false) &&
      !parseScopes(jwtPayload?.scopes, [
        EBusinessOwnerPermission.CREATE,
        EBusinessOwnerPermission.EDIT,
      ]) &&
      getIndexOfKeyInObject(paramsObject, 'businessOwnerCodes') >= 0
    ) {
      queryFromForJoinBusinessOwner += `
        AND "businessOwner"."code" = ANY($${getIndexOfKeyInObject(paramsObject, 'businessOwnerCodes') + 1})
      `;
    }

    /// [3] JOIN COST CENTER
    let queryFromForJoinCostCenter = `
        INNER JOIN "costcenter_subaccount" "costcenterSubaccount" ON "costcenterSubaccount"."id" = "budgets"."costcenter_subaccount_id"
        AND "costcenterSubaccount"."deleted_at" IS NULL
        LEFT JOIN "costcenter_function_units" "cfu" ON "cfu"."costcenter_id" = "costcenterSubaccount"."id"
        LEFT JOIN "function_unit" "functionUnits"
        ON "functionUnits"."id" = "cfu"."function_unit_id"
        AND "functionUnits"."deleted_at" IS NULL
    `;

    if (
      conditions.sectorIds &&
      conditions.sectorIds.length &&
      getIndexOfKeyInObject(paramsObject, 'sectorIds') >= 0
    ) {
      queryFromForJoinCostCenter += `
        AND "costcenterSubaccount"."sector_id" = ANY($${getIndexOfKeyInObject(paramsObject, 'sectorIds') + 1})
      `;
    }

    if (
      conditions.costCenterIds &&
      conditions.costCenterIds.length &&
      getIndexOfKeyInObject(paramsObject, 'costCenterIds') >= 0
    ) {
      queryFromForJoinCostCenter += `
        AND "costcenterSubaccount"."id" = ANY($${getIndexOfKeyInObject(paramsObject, 'costCenterIds') + 1})
      `;
    }

    if (
      !jwtPayload?.isSuperAdmin &&
      conditions.fucntionUnitCodes?.length &&
      getIndexOfKeyInObject(paramsObject, 'fucntionUnitCodes') >= 0
    ) {
      queryFromForJoinCostCenter += `
        AND ("functionUnits"."id" IS NULL OR "functionUnits"."code" = ANY($${getIndexOfKeyInObject(paramsObject, 'fucntionUnitCodes') + 1}))
      `;
    }

    /// [4] WHERE
    let queryWhere = `
        "budgets"."deleted_at" IS NULL
        AND "budgets"."budget_code_id" IS NOT NULL
        AND "budgets"."costcenter_subaccount_id" IS NOT NULL
    `;

    if (
      conditions.from &&
      conditions.to &&
      getIndexOfKeyInObject(paramsObject, 'from') >= 0 &&
      getIndexOfKeyInObject(paramsObject, 'to') >= 0
    ) {
      queryWhere += `
        AND ("budgets"."effective_start_date" <= $${getIndexOfKeyInObject(paramsObject, 'to') + 1} AND "budgets"."effective_end_date" >= $${getIndexOfKeyInObject(paramsObject, 'from') + 1})
      `;
    }

    /// [5] LIMIT OFFSET
    let queryLimitOffset = ``;
    if (conditions.getAll !== 1) {
      if (
        getIndexOfKeyInObject(paramsObject, 'limit') >= 0 &&
        getIndexOfKeyInObject(paramsObject, 'offset') >= 0
      ) {
        queryLimitOffset += `
          LIMIT $${getIndexOfKeyInObject(paramsObject, 'limit') + 1} OFFSET $${getIndexOfKeyInObject(paramsObject, 'offset') + 1}
        `;
      }
    } else {
      delete paramsObject['limit'];
      delete paramsObject['offset'];
    }

    const queryFromWhere = `
        FROM
            "budget" "budgets"
            ${queryFromForJoinBudgetCode}
            ${queryFromForJoinBusinessOwner}
            ${queryFromForJoinCostCenter}
        WHERE
            ${queryWhere}
        ORDER BY "budgets"."created_at" DESC
    `;
    const limitOffset = queryLimitOffset;

    return { queryFromWhere, limitOffset, paramsObject, paramsObjectCount };
  }
}
