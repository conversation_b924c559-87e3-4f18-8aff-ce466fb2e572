import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetDetailPurchaseOrderTypeDto } from '../../controller/purchase-order-type/dtos/get-detail-purchase-order-type.dto';
import { GetPurchaseOrderTypeListDto } from '../../controller/purchase-order-type/dtos/get-purchase-order-type-list.dto';
import { UpdatePurchaseOrderTypeDto } from '../../controller/purchase-order-type/dtos/update-purchase-order-type.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PurchaseOrderTypeModel } from '../../domain/model/purchase-order-type.model';
import { IPurchaseOrderTypeRepository } from '../../domain/repositories/purchase-order-type.repository';
import { parseScopes } from '../../utils/common';
import { EPurchaseOrderTypePermission } from '../../utils/constants/permission.enum';
import { PurchaseOrderTypeEntity } from '../entities/purchase-order-type.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class PurchaseOrderTypeRepository
  extends BaseRepository
  implements IPurchaseOrderTypeRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createPurchaseOrderType(
    data: PurchaseOrderTypeModel,
  ): Promise<PurchaseOrderTypeModel> {
    const repository = this.getRepository(PurchaseOrderTypeEntity);

    const prType = repository.create(data);

    return await repository.save(prType);
  }
  async updatePurchaseOrderType(
    id: string,
    updatePurchaseOrderTypeDto: UpdatePurchaseOrderTypeDto,
  ): Promise<PurchaseOrderTypeModel> {
    const repository = this.getRepository(PurchaseOrderTypeEntity);
    const purchaseOrderType = repository.create({
      id,
      ...updatePurchaseOrderTypeDto,
    });
    return await repository.save(purchaseOrderType);
  }

  async deletePurchaseOrderType(id: string): Promise<void> {
    const repository = this.getRepository(PurchaseOrderTypeEntity);
    await repository.delete(id);
  }

  async getPurchaseOrderTypes(
    conditions: GetPurchaseOrderTypeListDto,
    jwtPayload,
  ): Promise<ResponseDto<PurchaseOrderTypeModel>> {
    const repository = this.getRepository(PurchaseOrderTypeEntity);
    let queryBuilder = repository.createQueryBuilder('purchaseOrders');

    if (conditions.statuses) {
      queryBuilder.andWhere('purchaseOrders.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.forms) {
      queryBuilder.andWhere('purchaseOrders.form IN (:...forms)', {
        forms: conditions.forms,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    queryBuilder.orderBy('purchaseOrders.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getPurchaseOrderTypeByCode(
    code: string,
    id?: string,
  ): Promise<PurchaseOrderTypeModel> {
    const repository = this.getRepository(PurchaseOrderTypeEntity);

    const query: FindOneOptions<PurchaseOrderTypeEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getPurchaseOrderTypeDetail(
    conditions: GetDetailPurchaseOrderTypeDto,
    jwtPayload,
  ): Promise<PurchaseOrderTypeModel> {
    const repository = this.getRepository(PurchaseOrderTypeEntity);
    let queryBuilder = repository
      .createQueryBuilder('purchaseOrders')
      .select([
        'purchaseOrders.id',
        'purchaseOrders.code',
        'purchaseOrders.name',
        'purchaseOrders.description',
        'purchaseOrders.form',
        'purchaseOrders.status',
        'purchaseOrders.createdAt',
      ]);

    queryBuilder.where('purchaseOrders.id = :id', {
      id: conditions.id,
    });

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<PurchaseOrderTypeEntity>,
    jwtPayload: any,
    conditions: GetPurchaseOrderTypeListDto | GetDetailPurchaseOrderTypeDto,
  ) {
    conditions.codes = jwtPayload?.poTypeCodes; // Data role
    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        EPurchaseOrderTypePermission.CREATE,
        EPurchaseOrderTypePermission.EDIT,
      ]) &&
      conditions?.codes?.length
    ) {
      queryBuilder.andWhere(
        '(purchaseOrders.id IS NULL OR purchaseOrders.code IN (:...codes))',
        {
          codes: conditions.codes,
        },
      );
    }

    return queryBuilder;
  }

  async getPoTypeByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<PurchaseOrderTypeModel[]> {
    const repository = this.getRepository(PurchaseOrderTypeEntity);
    let queryBuilder = repository.createQueryBuilder('purchaseOrders');

    if (ids.length) {
      queryBuilder.where('purchaseOrders.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetPurchaseOrderTypeListDto({}),
      );
    }

    return await queryBuilder.getMany();
  }
}
