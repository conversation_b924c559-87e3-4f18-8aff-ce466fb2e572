import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, In, Not } from 'typeorm';
import { BudgetInvestmentModel } from '../../domain/model/budget-investment.model';
import { IBudgetInvestmentRepository } from '../../domain/repositories/budget-investment.repository';
import { BudgetInvestmentEntity } from '../entities/budget-investment.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class BudgetInvestmentRepository
  extends BaseRepository
  implements IBudgetInvestmentRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createManyBudgetInvestment(
    data: BudgetInvestmentModel[],
  ): Promise<BudgetInvestmentModel[]> {
    const repository = this.getRepository(BudgetInvestmentEntity);
    const newBudgetInvestments = repository.create(data);

    return await repository.save(newBudgetInvestments);
  }

  async updateManyBudgetInvestment(
    data: BudgetInvestmentModel[],
  ): Promise<BudgetInvestmentModel[]> {
    const repository = this.getRepository(BudgetInvestmentEntity);
    const updateBudgetInvestments = repository.create(data);
    return await repository.save(updateBudgetInvestments);
  }

  async deleteBudgetInvestmentsNotIn(
    ids: string[],
    budgetCapexId: string,
  ): Promise<void> {
    const repository = this.getRepository(BudgetInvestmentEntity);
    await repository.delete({
      id: Not(In(ids)),
      budgetCapexId: budgetCapexId,
    });
  }
}
