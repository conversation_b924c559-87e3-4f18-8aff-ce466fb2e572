import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource } from 'typeorm';
import { CreateProcessConditionDto } from '../../controller/process/dtos/create-process-condition.dto';
import { ProcessConditionModel } from '../../domain/model/process-condition.model';
import { IProcessConditionRepository } from '../../domain/repositories/process-condition.repository';
import { ProcessConditionEntity } from '../entities/process-condition.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ProcessConditionRepository
  extends BaseRepository
  implements IProcessConditionRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createProcessCondition(
    createProcessConditionDto: CreateProcessConditionDto,
  ): Promise<ProcessConditionModel> {
    const repository = this.getRepository(ProcessConditionEntity);

    const processCondition = await repository.create(createProcessConditionDto);

    return repository.save(processCondition);
  }

  async deleteProcessConditionByProcessId(processId: string): Promise<void> {
    const repository = this.getRepository(ProcessConditionEntity);
    await repository.delete({ processId });
  }
}
