import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { GetListPurchaseRequestDto } from '../../controller/purchase-request/dtos/get-list-purchase-request.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PurchaseRequestModel } from '../../domain/model/purchase_request.model';
import { IPurchaseRequestRepository } from '../../domain/repositories/purchase-request.repository';
import { parseScopes } from '../../utils/common';
import {
  EApprovedMaterialPermission,
  EBusinessUnitPermission,
  EDepartmentPermission,
  EFunctionUnitPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { PurchaseRequestEntity } from '../entities/purchase_request.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class PurchaseRequestRepository
  extends BaseRepository
  implements IPurchaseRequestRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async getListPurchaseRequest(
    conditions: GetListPurchaseRequestDto,
    jwtPayload: any,
    isNeedDetails: boolean = false,
  ): Promise<ResponseDto<PurchaseRequestModel>> {
    const repository = this.getRepository(PurchaseRequestEntity);

    let select = `SELECT
      "pr".*,
      jsonb_build_object('id', "sector".id , 'name', "sector".name,'code', "sector".code) AS sector,
      jsonb_build_object('id', "requester".id , 'firstName', "requester".first_name, 'lastName', "requester".last_name,'code', "requester".code) AS requester,
      jsonb_build_object('id', "purchaser".id , 'firstName', "purchaser".first_name, 'lastName', "purchaser".last_name,'code', "purchaser".code) AS purchaser,
      jsonb_build_object('id', "business_unit".id , 'name', "business_unit".name,'code', "business_unit".code) AS business_unit,
      jsonb_build_object('id', "type_pr".id , 'name', "type_pr".name,'code', "type_pr".code) AS type_pr,
      jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code) AS budget_code,
      (SELECT jsonb_agg(jsonb_build_object('id', "history".id ,'level', "history".level, 'status', "history".status,'name', "history".name,'created_at', "history".created_at,'updated_at', "history".updated_at))
      FROM "history-approve" "history"
      WHERE "history"."purchaseRequestId" = "pr"."id") AS history
    `;

    let from = `
      FROM "purchase_requests" "pr"
      INNER JOIN "purchase_request_details" "details" ON "details"."purchaseRequestId" = "pr"."id" AND "details".deleted_at IS NULL
      LEFT JOIN "sectors" "sector" ON "sector"."id" = "pr"."sector"::UUID AND "sector".deleted_at IS NULL
      LEFT JOIN "staffs" "requester" ON "requester"."id" = "pr"."requester"::UUID AND "requester".deleted_at IS NULL
      LEFT JOIN "staffs" "purchaser" ON "purchaser"."id" = "pr"."purchaser"::UUID AND "purchaser".deleted_at IS NULL
      LEFT JOIN "business_unit" "business_unit" ON "business_unit"."id" = "pr"."business_unit"::UUID AND "business_unit".deleted_at IS NULL
      LEFT JOIN "purchase_request_type" "type_pr" ON "type_pr"."id" = "pr"."type_pr"::UUID AND "type_pr".deleted_at IS NULL
      LEFT JOIN "process_types" "process_type" ON "process_type"."id" = "pr"."process_type"::UUID AND "process_type".deleted_at IS NULL
      LEFT JOIN "budget_code" "budget_code" ON "budget_code"."id" = "pr"."budget_code"::UUID AND "budget_code".deleted_at IS NULL
      LEFT JOIN "function_unit" "function_unit" ON "function_unit"."id" = "pr"."function_unit"::UUID AND "function_unit".deleted_at IS NULL
      LEFT JOIN "department" "department" ON "department"."id" = "pr"."department"::UUID AND "department".deleted_at IS NULL`;

    let groupBy = ` GROUP BY "pr".id, "sector".id, "requester".id, "purchaser".id, "business_unit".id, "type_pr".id, "budget_code".id `;

    let orderBy = ` ORDER BY "pr"."created_at" DESC `;

    let where = ` WHERE "pr"."created_at" NOTNULL `;

    const parameters = [];

    let parameterIndex = 1; // Bắt đầu từ $1

    if (conditions.crudTemplateActiveTab == 'APPROVED') {
      if (jwtPayload?.email) {
        from += ` INNER JOIN "approval-level" "levels" 
                      ON "levels"."email" = $${parameters.length + 1} 
                      AND "levels"."status" IN ('Approved', 'Rejected') AND "pr"."id" = "levels"."purchase_request_id"`;
        parameters.push(jwtPayload?.email);
        parameterIndex++;
      } else {
        return new ResponseDto([], conditions.page, conditions.limit, 0);
      }
    }

    const addCondition = (condition: string, value: any) => {
      if (Array.isArray(value)) {
        // Tạo chuỗi các `$n` tương ứng
        const placeholders = value.map(() => `$${parameterIndex++}`).join(', ');
        where += condition.replace('?', placeholders);

        // Thêm tất cả giá trị vào parameters
        parameters.push(...value);
      } else {
        where += condition.replace('?', `$${parameterIndex}`);
        parameters.push(value);
        parameterIndex++;
      }
    };

    if (conditions.department?.length) {
      addCondition(
        ` AND "pr"."department" IN (?)`,
        `${conditions.department.join("', '")}`,
      );
    }

    if (conditions.function_unit?.length) {
      addCondition(
        ` AND "pr"."function_unit" IN (?)`,
        `${conditions.function_unit.join("', '")}`,
      );
    }

    if (conditions.type_pr?.length) {
      addCondition(
        ` AND "pr"."type_pr" IN (?)`,
        `${conditions.type_pr.join("', '")}`,
      );
    }

    if (conditions.budget_code?.length) {
      addCondition(
        ` AND "pr"."budget_code" IN (?)`,
        `${conditions.budget_code.join("', '")}`,
      );
    }

    if (conditions.status_pr?.length) {
      addCondition(` AND "pr"."status_pr" IN (?)`, conditions.status_pr);
    }

    if (conditions.state_pr?.length) {
      addCondition(` AND "pr"."state_pr" IN (?)`, conditions.state_pr);
    }

    if (conditions.business_unit?.length) {
      addCondition(
        ` AND "pr"."business_unit" IN (?)`,
        `${conditions.business_unit.join("', '")}`,
      );
    }

    if (conditions.sector?.length) {
      addCondition(
        ` AND "pr"."sector" IN (?)`,
        `${conditions.sector.join("', '")}`,
      );
    }

    if (isNeedDetails) {
      if (conditions.material_group?.length) {
        addCondition(
          ` AND "details"."material_group" IN (?)`,
          `${conditions.material_group.join("', '")}`,
        );
      }

      if (conditions.cost_center?.length) {
        addCondition(
          ` AND "details"."cost_center" IN (?)`,
          `${conditions.cost_center.join("', '")}`,
        );
      }
    }

    if (conditions.isMaterial) {
      where += ` AND "pr"."status_pr" = 'Approved' `;
    }

    if (conditions.process_type?.length) {
      addCondition(
        ` AND "pr"."process_type" IN (?)`,
        `${conditions.process_type.join("', '")}`,
      );
    }

    if (conditions.crudTemplateActiveTab == 'REQUEST') {
      if (jwtPayload?.staffId) {
        addCondition(` AND "pr"."requester" = ? `, jwtPayload.staffId);
      } else {
        return new ResponseDto([], conditions.page, conditions.limit, 0);
      }
    }

    if (conditions.crudTemplateActiveTab == 'WAITING') {
      if (jwtPayload?.email) {
        from += ` INNER JOIN "approval-level" "levels" ON  ( NOT EXISTS (
            SELECT 1
            FROM "approval-level" l_prev
            WHERE l_prev.level < levels.level
            AND l_prev.status != 'Approved'
            AND "pr"."id" = "l_prev"."purchase_request_id"
          ) OR levels."level" = 1) AND levels.status = 'Pending' AND "pr"."id" = "levels"."purchase_request_id"`;

        addCondition(
          ` AND "levels"."email" = ? AND "pr"."status_pr" IN ('Waiting to process', 'In-Progress')`,
          jwtPayload.email,
        );
      } else {
        return new ResponseDto([], conditions.page, conditions.limit, 0);
      }
    }

    if (conditions.searchString) {
      addCondition(
        ` AND CAST("pr"."id" AS TEXT) ILIKE ?`,
        `${conditions.searchString}`,
      );
    }

    if (
      conditions.crudTemplateActiveTab != 'REQUEST' &&
      conditions.crudTemplateActiveTab != 'WAITING' &&
      conditions.crudTemplateActiveTab != 'APPROVED'
    ) {
      if (!jwtPayload?.isSuperAdmin) {
        if (
          !parseScopes(jwtPayload?.scopes, [
            ESectorPermission.CREATE,
            ESectorPermission.EDIT,
          ]) &&
          jwtPayload?.sectors?.length
        ) {
          addCondition(` AND "sector".code IN (?)`, jwtPayload.sectors);
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EBusinessUnitPermission.CREATE,
            EBusinessUnitPermission.EDIT,
          ]) &&
          jwtPayload?.businessUnits?.length
        ) {
          addCondition(
            ` AND "business_unit".code IN (?)`,
            jwtPayload.businessUnits,
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EFunctionUnitPermission.CREATE,
            EFunctionUnitPermission.EDIT,
          ]) &&
          jwtPayload?.functionUnits?.length
        ) {
          addCondition(
            ` AND ("function_unit".id IS NULL OR "function_unit".code IN (?))`,
            jwtPayload.functionUnits,
          );
        }
        if (
          !parseScopes(jwtPayload?.scopes, [
            EDepartmentPermission.CREATE,
            EDepartmentPermission.EDIT,
          ]) &&
          jwtPayload?.departments?.length
        ) {
          addCondition(
            ` AND ("department".id IS NULL OR "department".code IN (?))`,
            jwtPayload.departments,
          );
        }
      }
    }

    const countSelect = ` SELECT COUNT(DISTINCT "pr"."id") `;

    if (isNeedDetails) {
      select += ` , (SELECT jsonb_agg(DISTINCT 
      jsonb_build_object(
      'id', "details".id , 
      'adjust_budget_id', "details".adjust_budget_id ,
      'budget', "details".budget , 
      'budget_id', "details".budget_id , 
      'created_at', "details".created_at ,
      'delivery_time', "details".delivery_time , 
      'estimated_price', "details".estimated_price , 
      'inventory_number', "details".inventory_number , 
      'material_code', "details".material_code ,
      'material_name', "details".material_name , 
      'material_group_name', "details".material_group_name , 
      'note', "details".note , 
      'quantity', "details".quantity ,
      'remaining_actual_budget', "details".remaining_actual_budget ,
      'remaining_budget', "details".remaining_budget ,
      'standard_quantity', "details".standard_quantity,
      'total_amount', "details".total_amount,
      'unit', "details".unit,
      'unit_price', "details".unit_price,
      'material', jsonb_build_object('id', "material".id, 'name', "material".name, 'code', "material".code),
      'budget_code', jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code,'budgetType', "budget_code".budget_type,'internalOrder', "budget_code".internal_order),
      'cost_center', jsonb_build_object('id', "cost_center".id , 'name', "cost_center".name,'code', "cost_center".code),
      'material_group', jsonb_build_object('id', "material_group".id , 'name', "material_group".name,'code', "material_group".code),
      'measure_code', jsonb_build_object('id', "measure_code".id , 'name', "measure_code".name,'code', "measure_code".code,'description', "measure_code".description),
      'number_po_created', (SELECT COALESCE(SUM("poDetails".quantity), 0) FROM "purchase_order_details" "poDetails"
      INNER JOIN "purchase_orders" "po" ON "po".id = "poDetails"."purchaseOrderId"
      WHERE "poDetails".pr_detail_id = "details"."id" AND "po".status_po NOT IN ('Rejected','Cancel') AND "poDetails".deleted_at IS NULL AND "po".deleted_at IS NULL)
      ))
      FROM "purchase_request_details" "details" 
      LEFT JOIN "costcenter_subaccount" "cost_center" ON "cost_center"."id" = "details"."cost_center"::UUID AND "cost_center".deleted_at IS NULL
      LEFT JOIN "materials" "material" ON "material"."id" = "details"."material_code"::UUID AND "material".deleted_at IS NULL
      LEFT JOIN "material_groups" "material_group" ON "material_group"."id" = "details"."material_group"::UUID AND "material_group".deleted_at IS NULL 
      LEFT JOIN "measures" "measure_code" ON "measure_code"."id" = "details"."measure_code"::UUID AND "measure_code".deleted_at IS NULL
      WHERE "details"."purchaseRequestId" = "pr"."id" AND "details".deleted_at IS NULL ) AS details ,
      jsonb_build_object('id', "department".id , 'name', "department".name,'code', "department".code) AS department,
      jsonb_build_object('id', "function_unit".id , 'name', "function_unit".name,'code', "function_unit".code) AS function_unit,
      jsonb_build_object('id', "process_type".id , 'name', "process_type".name,'code', "process_type".code) AS process_type,
      jsonb_build_object('id', "plant".id , 'name', "plant".name,'code', "plant".code) AS plant,
      jsonb_build_object('id', "purchase_group".id , 'name', "purchase_group".name,'code', "purchase_group".code) AS purchase_group,
      jsonb_build_object('id', "purchase_org".id , 'name', "purchase_org".name,'code', "purchase_org".code) AS purchase_org
`;

      from += `  
      LEFT JOIN "plants" "plant" ON "plant"."id" = "pr"."plant"::UUID AND "plant".deleted_at IS NULL 
      LEFT JOIN "purchasing_groups" "purchase_group" ON "purchase_group"."id" = "pr"."purchase_group"::UUID AND "purchase_group".deleted_at IS NULL 
      LEFT JOIN "purchasing_departments" "purchase_org" ON "purchase_org"."id" = "pr"."purchase_org"::UUID AND "purchase_org".deleted_at IS NULL `;

      groupBy += ` , "department".id, "function_unit".id, "process_type".id, "plant".id, "purchase_group".id, "purchase_org".id`;

      if (
        !jwtPayload?.isSuperAdmin &&
        parseScopes(jwtPayload?.scopes, [EApprovedMaterialPermission.CREATE])
      ) {
        addCondition(` AND "purchaser".id = ? `, jwtPayload.staffId);
      }
    } else {
      select += ` , (SELECT jsonb_agg(DISTINCT 
      jsonb_build_object(
      'id', "details".id , 
      'total_amount', "details".total_amount,'quantity', 
      "details".quantity , 'estimated_price', 
      "details".estimated_price , 
      'standard_quantity', "details".standard_quantity,
      'unit_price', "details".unit_price
      ))
      FROM "purchase_request_details" "details" WHERE "details"."purchaseRequestId" = "pr"."id" AND "details".deleted_at IS NULL ) AS details`;
    }

    const countRecords = await repository.query(
      countSelect + from + where,
      parameters,
    );

    if (conditions.getAll !== 1) {
      //paginate
      const offset = Number(conditions.limit) * (Number(conditions.page) - 1);
      const limit = Number(conditions.limit);

      orderBy += ` LIMIT ${limit} OFFSET ${offset}`;
    }

    const results = await repository.query(
      select + from + where + groupBy + orderBy,
      parameters,
    );

    return new ResponseDto(
      results.map((item) => {
        return { ...item, attachments: item?.files?.split(',') };
      }),
      conditions.page,
      conditions.limit,
      Number(countRecords[0].count),
    );
  }

  async findOne(id: number): Promise<PurchaseRequestModel> {
    const repository = this.getRepository(PurchaseRequestEntity);

    let select = `SELECT
      "pr".*,
      jsonb_build_object('id', "sector".id , 'name', "sector".name,'code', "sector".code) AS sector,
      jsonb_build_object('id', "requester".id , 'firstName', "requester".first_name, 'lastName', "requester".last_name,'code', "requester".code) AS requester,
      jsonb_build_object('id', "purchaser".id , 'firstName', "purchaser".first_name, 'lastName', "purchaser".last_name,'code', "purchaser".code) AS purchaser,
      jsonb_build_object('id', "business_unit".id , 'name', "business_unit".name,'code', "business_unit".code) AS business_unit,
      jsonb_build_object('id', "type_pr".id , 'name', "type_pr".name,'code', "type_pr".code) AS type_pr,
      jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code) AS budget_code
    `;

    let from = `
      FROM "purchase_requests" "pr"
      INNER JOIN "purchase_request_details" "details" ON "details"."purchaseRequestId" = "pr"."id" AND "details".deleted_at IS NULL
      LEFT JOIN "sectors" "sector" ON "sector"."id" = "pr"."sector"::UUID AND "sector".deleted_at IS NULL
      LEFT JOIN "staffs" "requester" ON "requester"."id" = "pr"."requester"::UUID AND "requester".deleted_at IS NULL
      LEFT JOIN "staffs" "purchaser" ON "purchaser"."id" = "pr"."purchaser"::UUID AND "purchaser".deleted_at IS NULL
      LEFT JOIN "business_unit" "business_unit" ON "business_unit"."id" = "pr"."business_unit"::UUID AND "business_unit".deleted_at IS NULL
      LEFT JOIN "purchase_request_type" "type_pr" ON "type_pr"."id" = "pr"."type_pr"::UUID AND "type_pr".deleted_at IS NULL
      LEFT JOIN "process_types" "process_type" ON "process_type"."id" = "pr"."process_type"::UUID AND "process_type".deleted_at IS NULL
      LEFT JOIN "budget_code" "budget_code" ON "budget_code"."id" = "pr"."budget_code"::UUID AND "budget_code".deleted_at IS NULL
      LEFT JOIN "function_unit" "function_unit" ON "function_unit"."id" = "pr"."function_unit"::UUID AND "function_unit".deleted_at IS NULL
      LEFT JOIN "department" "department" ON "department"."id" = "pr"."department"::UUID AND "department".deleted_at IS NULL`;

    let groupBy = ` GROUP BY "pr".id, "sector".id, "requester".id, "purchaser".id, "business_unit".id, "type_pr".id, "budget_code".id `;

    let orderBy = ` ORDER BY "pr"."created_at" DESC `;

    let where = ` WHERE "pr"."created_at" NOTNULL `;

    const parameters = [];

    let parameterIndex = 1; // Bắt đầu từ $1

    const addCondition = (condition: string, value: any) => {
      if (Array.isArray(value)) {
        // Tạo chuỗi các `$n` tương ứng
        const placeholders = value.map(() => `$${parameterIndex++}`).join(', ');
        where += condition.replace('?', placeholders);

        // Thêm tất cả giá trị vào parameters
        parameters.push(...value);
      } else {
        where += condition.replace('?', `$${parameterIndex}`);
        parameters.push(value);
        parameterIndex++;
      }
    };

    addCondition(` AND "pr"."id" = ?`, `${id}`);

    select += ` , (SELECT jsonb_agg(DISTINCT 
    jsonb_build_object(
    'id', "history".id , 
    'created_at', "history".created_at,
    'email', "history".email,
    'id', "history".id,
    'level', "history".level,
    'name', "history".name,
    'reason', "history".reason,
    'role', "history".role,
    'status', "history".status,
    'updated_at', "history".updated_at,
    'user_id', "history".user_id
    ))
    FROM "history-approve" "history" 
    WHERE "history"."purchaseRequestId" = "pr"."id" ) AS history, (SELECT jsonb_agg(DISTINCT 
    jsonb_build_object(
    'id', "details".id , 
    'adjust_budget_id', "details".adjust_budget_id ,
    'budget', "details".budget , 
    'created_at', "details".created_at,
    'budget_id', "details".budget_id , 
    'created_at', "details".created_at ,
    'delivery_time', "details".delivery_time , 
    'estimated_price', "details".estimated_price , 
    'inventory_number', "details".inventory_number , 
    'material_code', "details".material_code ,
    'material_name', "details".material_name , 
    'material_group_name', "details".material_group_name , 
    'note', "details".note , 
    'quantity', "details".quantity ,
    'remaining_actual_budget', "details".remaining_actual_budget ,
    'remaining_budget', "details".remaining_budget ,
    'standard_quantity', "details".standard_quantity,
    'total_amount', "details".total_amount,
    'unit', "details".unit,
    'unit_price', "details".unit_price,
    'material', jsonb_build_object('id', "material".id, 'name', "material".name, 'code', "material".code),
    'budget_code', jsonb_build_object('id', "budget_code".id , 'name', "budget_code".name,'code', "budget_code".code,'budgetType', "budget_code".budget_type,'internalOrder', "budget_code".internal_order),
    'cost_center', jsonb_build_object('id', "cost_center".id , 'name', "cost_center".name,'code', "cost_center".code),
    'material_group', jsonb_build_object('id', "material_group".id , 'name', "material_group".name,'code', "material_group".code),
    'measure_code', jsonb_build_object('id', "measure_code".id , 'name', "measure_code".name,'code', "measure_code".code,'description', "measure_code".description),
    'number_po_created', (SELECT COALESCE(SUM("poDetails".quantity), 0) FROM "purchase_order_details" "poDetails"
    INNER JOIN "purchase_orders" "po" ON "po".id = "poDetails"."purchaseOrderId"
    WHERE "poDetails".pr_detail_id = "details"."id" AND "po".status_po NOT IN ('Rejected','Cancel') AND "poDetails".deleted_at IS NULL AND "po".deleted_at IS NULL)
    ))
    FROM "purchase_request_details" "details" 
    LEFT JOIN "costcenter_subaccount" "cost_center" ON "cost_center"."id" = "details"."cost_center"::UUID AND "cost_center".deleted_at IS NULL
    LEFT JOIN "materials" "material" ON "material"."id" = "details"."material_code"::UUID AND "material".deleted_at IS NULL
    LEFT JOIN "material_groups" "material_group" ON "material_group"."id" = "details"."material_group"::UUID AND "material_group".deleted_at IS NULL 
    LEFT JOIN "measures" "measure_code" ON "measure_code"."id" = "details"."measure_code"::UUID AND "measure_code".deleted_at IS NULL
    WHERE "details"."purchaseRequestId" = "pr"."id" AND "details".deleted_at IS NULL ) AS details ,
    jsonb_build_object('id', "department".id , 'name', "department".name,'code', "department".code) AS department,
    jsonb_build_object('id', "function_unit".id , 'name', "function_unit".name,'code', "function_unit".code) AS function_unit,
    jsonb_build_object('id', "process_type".id , 'name', "process_type".name,'code', "process_type".code) AS process_type,
    jsonb_build_object('id', "plant".id , 'name', "plant".name,'code', "plant".code) AS plant,
    jsonb_build_object('id', "purchase_group".id , 'name', "purchase_group".name,'code', "purchase_group".code) AS purchase_group,
    jsonb_build_object('id', "purchase_org".id , 'name', "purchase_org".name,'code', "purchase_org".code) AS purchase_org
`;

    from += `  
    LEFT JOIN "plants" "plant" ON "plant"."id" = "pr"."plant"::UUID AND "plant".deleted_at IS NULL 
    LEFT JOIN "purchasing_groups" "purchase_group" ON "purchase_group"."id" = "pr"."purchase_group"::UUID AND "purchase_group".deleted_at IS NULL 
    LEFT JOIN "purchasing_departments" "purchase_org" ON "purchase_org"."id" = "pr"."purchase_org"::UUID AND "purchase_org".deleted_at IS NULL `;

    groupBy += ` , "department".id, "function_unit".id, "process_type".id, "plant".id, "purchase_group".id, "purchase_org".id`;

    const limit = Number(1);

    orderBy += ` LIMIT ${limit}`;

    const results = await repository.query(
      select + from + where + groupBy + orderBy,
      parameters,
    );

    if (results[0]) {
      results[0].attachments = results[0]?.files.split(',');
    }

    return results[0];
  }
}
