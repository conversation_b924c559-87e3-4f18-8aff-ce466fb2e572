import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetDetailMaterialGroupDto } from '../../controller/material-group/dtos/get-detail-material-group.dto';
import { GetMaterialGroupListDto } from '../../controller/material-group/dtos/get-material-group-list.dto';
import { UpdateMaterialGroupDto } from '../../controller/material-group/dtos/update-material-group.model';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { MaterialGroupModel } from '../../domain/model/material-group.model';
import { IMaterialGroupRepository } from '../../domain/repositories/material-group.repository';
import { parseScopes } from '../../utils/common';
import {
  EBusinessOwnerPermission,
  EMaterialGroupPermission,
} from '../../utils/constants/permission.enum';
import { MaterialGroupEntity } from '../entities/material-group.entity';
import { BaseRepository } from './base.repository';
import { GetMaterialGroupListByIdsDto } from '../../controller/material-group/dtos/get-material-group-list-by-ids.dto';

@Injectable({ scope: Scope.REQUEST })
export class MaterialGroupRepository
  extends BaseRepository
  implements IMaterialGroupRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createMaterialGroup(
    data: MaterialGroupModel,
  ): Promise<MaterialGroupModel> {
    const repository = this.getRepository(MaterialGroupEntity);

    const materialGroup = repository.create(data);

    return await repository.save(materialGroup);
  }
  async updateMaterialGroup(
    id: string,
    updateMaterialGroupDto: UpdateMaterialGroupDto,
  ): Promise<MaterialGroupModel> {
    const repository = this.getRepository(MaterialGroupEntity);
    const materialGroup = repository.create({ id, ...updateMaterialGroupDto });
    return await repository.save(materialGroup);
  }
  async deleteMaterialGroup(id: string): Promise<void> {
    const repository = this.getRepository(MaterialGroupEntity);
    await repository.delete(id);
  }

  // async getMaterialGroupById(id: string): Promise<MaterialGroupModel> {
  //   const repository = this.getRepository(MaterialGroupEntity);
  //   return await repository.findOneBy({ id: id });
  // }

  async getMaterialGroups(
    conditions: GetMaterialGroupListDto,
    jwtPayload,
  ): Promise<ResponseDto<MaterialGroupModel>> {
    const repository = this.getRepository(MaterialGroupEntity);
    let queryBuilder = repository
      .createQueryBuilder('materialGroups')
      .leftJoin('materialGroups.businessOwners', 'businessOwners')
      .leftJoin('materialGroups.processTypes', 'processTypes')
      .select([
        'materialGroups.id',
        'materialGroups.code',
        'materialGroups.name',
        'materialGroups.description',
        'materialGroups.status',
        'materialGroups.searchValue',
        'materialGroups.createdAt',
        'businessOwners.id',
        'businessOwners.code',
        'businessOwners.name',
        'businessOwners.description',
        'businessOwners.status',
        'businessOwners.searchValue',
        'processTypes.id',
        'processTypes.code',
        'processTypes.name',
        'processTypes.description',
        'processTypes.status',
        'processTypes.searchValue',
      ]);

    if (conditions.statuses) {
      queryBuilder.andWhere('materialGroups.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.searchString) {
      queryBuilder.andWhere(
        'materialGroups.searchValue ILIKE :searchString OR businessOwners.searchValue ILIKE :searchString OR processTypes.searchValue ILIKE :searchString',
        {
          searchString: conditions.searchString,
        },
      );
    }

    if (conditions.businessOwnerIds?.length) {
      queryBuilder.andWhere('businessOwners.id IN (:...businessOwnerIds)', {
        businessOwnerIds: conditions.businessOwnerIds,
      });
    }

    if (conditions.processTypeIds?.length) {
      queryBuilder.andWhere('processTypes.id IN (:...processTypeIds)', {
        processTypeIds: conditions.processTypeIds,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('materialGroups.createdAt', 'DESC');

    return await this.pagination(queryBuilder, {
      ...conditions,
      searchString: null,
    });
  }

  async getMaterialGroupByCode(
    code: string,
    id?: string,
  ): Promise<MaterialGroupModel> {
    const repository = this.getRepository(MaterialGroupEntity);

    const query: FindOneOptions<MaterialGroupEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getMaterialGroupDetail(
    conditions: GetDetailMaterialGroupDto,
    jwtPayload: any,
  ): Promise<MaterialGroupModel> {
    const repository = this.getRepository(MaterialGroupEntity);
    let queryBuilder = repository
      .createQueryBuilder('materialGroups')
      .leftJoin('materialGroups.businessOwners', 'businessOwners')
      .leftJoin('materialGroups.processTypes', 'processTypes')
      .select([
        'materialGroups.id',
        'materialGroups.code',
        'materialGroups.name',
        'materialGroups.description',
        'materialGroups.status',
        'materialGroups.createdAt',
        'businessOwners.id',
        'businessOwners.code',
        'businessOwners.name',
        'businessOwners.description',
        'businessOwners.status',
        'processTypes.id',
        'processTypes.code',
        'processTypes.name',
        'processTypes.description',
        'processTypes.status',
      ]);

    if (conditions.id) {
      queryBuilder.andWhere('materialGroups.id = :id', { id: conditions.id });
    }

    if (conditions.code) {
      queryBuilder.andWhere('materialGroups.code = :code', {
        code: conditions.code,
      });
    }

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<MaterialGroupEntity>,
    jwtPayload: any,
    conditions: GetMaterialGroupListDto | GetDetailMaterialGroupDto,
  ) {
    if (!jwtPayload?.isSuperAdmin) {
      conditions.codes = jwtPayload?.materialGroups;
      conditions.businessOwnerCodes = jwtPayload?.businessOwners;

      if (
        !parseScopes(jwtPayload?.scopes, [
          EMaterialGroupPermission.CREATE,
          EMaterialGroupPermission.EDIT,
        ]) &&
        conditions.codes?.length
      ) {
        queryBuilder.andWhere(
          '(materialGroups.id IS NULL ORmaterialGroups.code IN (:...codes))',
          {
            codes: conditions.codes,
          },
        );
      }

      if (
        !parseScopes(jwtPayload?.scopes, [
          EBusinessOwnerPermission.CREATE,
          EBusinessOwnerPermission.EDIT,
        ]) &&
        conditions.businessOwnerCodes?.length
      ) {
        queryBuilder.andWhere(
          `(businessOwners.id IS NULL OR businessOwners.code IN (:...businessOwnerCodes))`,
          {
            businessOwnerCodes: conditions.businessOwnerCodes,
          },
        );
      }

      return queryBuilder;
    }

    return queryBuilder;
  }

  async getMaterialGroupByCodesWithRole(
    codes: string[],
    jwtPayload,
    isNeedPermission: boolean = true,
  ): Promise<MaterialGroupModel[]> {
    const repository = this.getRepository(MaterialGroupEntity);
    let queryBuilder = repository
      .createQueryBuilder('materialGroups')
      .leftJoin('materialGroups.businessOwners', 'businessOwners');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetMaterialGroupListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('materialGroups.code IN (:...codes)', { codes });
      } else {
        return [];
      }
    }

    queryBuilder.orderBy('materialGroups.createdAt', 'DESC');

    return await queryBuilder.getMany();
  }

  async getListByIds(
    conditions: GetMaterialGroupListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<MaterialGroupModel>> {
    const repository = this.getRepository(MaterialGroupEntity);
    let queryBuilder = repository.createQueryBuilder('materialGroups');

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.where('materialGroups.id IN (:...ids)', {
        ids: conditions.ids,
      });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder.orderBy('materialGroups.createdAt', 'DESC');

    return await this.pagination<MaterialGroupEntity>(queryBuilder, conditions);
  }
}
