import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource } from 'typeorm';
import { CreateConditionDto } from '../../controller/process/dtos/create-condition.dto';
import { ConditionModel } from '../../domain/model/condition.model';
import { IConditionRepository } from '../../domain/repositories/condition.repository';
import { ConditionEntity } from '../entities/condition.entity';
import { BaseRepository } from './base.repository';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ConditionRepository
  extends BaseRepository
  implements IConditionRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createCondition(data: CreateConditionDto): Promise<ConditionModel> {
    const repository = this.getRepository(ConditionEntity);

    const condition = repository.create(data);

    return await repository.save(condition);
  }

  async deleteCondition(id: string): Promise<void> {
    const repository = this.getRepository(ConditionEntity);
    await repository.delete(id);
  }

  async getConditionByProcessId(processId: string) {
    const repository = this.getRepository(ConditionEntity);
    const result = await repository.query(
      `
          WITH w_process as (SELECT id AS "processId", nlevel(path) AS "nlevel", name, description
                            FROM processes p
                            WHERE path @> (SELECT ps."path"
                                            FROM processes ps
                                            WHERE ps."path" ~ $1) AND path != '' AND p.deleted_at IS NULL
          ORDER BY nlevel(path) DESC
              )
          SELECT w_process.*,
                 c.id,
                 cd.*
          FROM w_process
          LEFT JOIN process_conditions pc ON w_process."processId" = pc.process_id AND pc.deleted_at IS NULL
          LEFT JOIN conditions c ON c.id = pc.condition_id AND c.deleted_at IS NULL
          LEFT JOIN condition_details cd ON cd.condition_id = c.id AND cd.deleted_at IS NULL
          LEFT JOIN 
          ORDER BY w_process.nlevel DESC`,
      ['*.' + processId.replace(/-/g, '')],
    );

    return result;
  }
}
