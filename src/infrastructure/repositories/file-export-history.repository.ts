import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { DataSource, Repository } from 'typeorm';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { REQUEST } from '@nestjs/core';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { IntersectionType } from '@nestjs/swagger';
import { IFileExportHistoryRepository } from '../../domain/repositories/file-export-history.repository';
import { FileExportHistoryEntity } from '../entities/file-export-history.entity';
import { FileExportHistoryModel } from '../../domain/model/file-export-history.model';
import { GetListFileExportHistoryDto } from '../../controller/file-export-history/dtos/get-list-file-export-history.dto';

@Injectable({ scope: Scope.REQUEST })
export class FileExportHistoryRepository
  extends BaseRepository
  implements IFileExportHistoryRepository
{
  constructor(
    @InjectRepository(FileExportHistoryEntity)
    private readonly fileExportHistoryEntityRepository: Repository<FileExportHistoryEntity>,
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createFileExportHistory(
    data: FileExportHistoryModel,
  ): Promise<FileExportHistoryModel> {
    // const repository = this.getRepository(FileExportHistoryEntity); tắt transaction
    const newFileExportHistory = this.fileExportHistoryEntityRepository.create({
      ...data,
    });
    return await this.fileExportHistoryEntityRepository.save(
      newFileExportHistory,
    );
  }

  async getListFileExportHistory(
    conditions: GetListFileExportHistoryDto,
  ): Promise<ResponseDto<FileExportHistoryModel>> {
    const repository = this.getRepository(FileExportHistoryEntity);
    const queryBuilder = repository.createQueryBuilder('fileExportHistories');

    queryBuilder.where('fileExportHistories.importType IN (:...importTypes)', {
      importTypes: conditions.importTypes,
    });

    queryBuilder.orderBy('fileExportHistories.createdAt', 'DESC');
    return await this.pagination(queryBuilder, conditions);
  }

  async deleteFileExportHistory(id: string): Promise<void> {
    const repository = this.getRepository(FileExportHistoryEntity);
    await repository.softDelete(id);
  }

  async updateFileExportHistory(
    data: FileExportHistoryModel,
  ): Promise<FileExportHistoryModel> {
    // const repository = this.getRepository(FileExportHistoryEntity); tắt transaction
    const updateFileExportHistory =
      this.fileExportHistoryEntityRepository.create({ ...data });
    return await this.fileExportHistoryEntityRepository.save(
      updateFileExportHistory,
    );
  }

  async getFileExportHistoryById(id: string): Promise<FileExportHistoryModel> {
    const repository = this.getRepository(FileExportHistoryEntity);
    return await repository.findOne({ where: { id } });
  }
}
