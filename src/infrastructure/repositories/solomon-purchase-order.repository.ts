import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { ISolomonPurchaseOrderRepository } from '../../domain/repositories/solomon-purchase-order.repository';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { SolomonPurchaseOrderEntity } from '../entities/solomon-purchase-order.entity';
import { SolomonPurchaseOrderModel } from '../../domain/model/solomon-purchase-order.model';
import { CreateSolomonPurchaseOrderDto } from '../../controller/solomon-purchase-order/dtos/create-solomon-purchase-order.dto';

@Injectable({
  scope: Scope.REQUEST,
})
export class SolomonPurchaseOrderRepository
  extends BaseRepository
  implements ISolomonPurchaseOrderRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
    @InjectRepository(SolomonPurchaseOrderEntity)
    private readonly solomonPurchaseOrderEntityRepository: Repository<SolomonPurchaseOrderEntity>,
  ) {
    super(dataSource, req);
  }

  async getListSolomonPurchaseOrderByIds(
    ids: string[],
  ): Promise<SolomonPurchaseOrderModel[]> {
    const repository = this.getRepository(SolomonPurchaseOrderEntity);

    return ids && ids.length
      ? await repository.find({ where: { id: In(ids) } })
      : [];
  }

  async createSolomonPurchaseOrder(
    createSolomonPurchaseOrderDto: CreateSolomonPurchaseOrderDto,
  ): Promise<SolomonPurchaseOrderModel> {
    return await this.solomonPurchaseOrderEntityRepository.save(
      createSolomonPurchaseOrderDto,
    );
  }

  async updateSolomonPurchaseOrder(
    model: SolomonPurchaseOrderModel,
  ): Promise<SolomonPurchaseOrderModel> {
    return await this.solomonPurchaseOrderEntityRepository.save(model);
  }

  async deleteSolomonPurchaseOrdersById(ids: string[]): Promise<void> {
    await this.solomonPurchaseOrderEntityRepository.delete({ id: In(ids) });
  }
}
