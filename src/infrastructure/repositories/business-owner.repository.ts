import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import {
  DataSource,
  FindOneOptions,
  In,
  Not,
  SelectQueryBuilder,
} from 'typeorm';
import { GetBusinessOwnerListDto } from '../../controller/business-owner/dtos/get-business-owner-list.dto';
import { GetDetailBusinessOwnerDto } from '../../controller/business-owner/dtos/get-detail-business-owner.dto';
import { UpdateBusinessOwnerDto } from '../../controller/business-owner/dtos/update-business-owner.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { BusinessOwnerModel } from '../../domain/model/business-owner.model';
import { IBusinessOwnerRepository } from '../../domain/repositories/business-owner.repository';
import { parseScopes } from '../../utils/common';
import { EBusinessOwnerPermission } from '../../utils/constants/permission.enum';
import { BusinessOwnerEntity } from '../entities/business-owner.entity';
import { BaseRepository } from './base.repository';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class BusinessOwnerRepository
  extends BaseRepository
  implements IBusinessOwnerRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createBusinessOwner(
    data: BusinessOwnerModel,
  ): Promise<BusinessOwnerModel> {
    const repository = this.getRepository(BusinessOwnerEntity);

    const businessOwner = repository.create({
      code: data.code,
      description: data.description,
      name: data.name,
      status: data.status,
    });

    return await repository.save(businessOwner);
  }
  async updateBusinessOwner(
    id: string,
    updateBusinessOwnerDto: UpdateBusinessOwnerDto,
  ): Promise<BusinessOwnerModel> {
    const repository = this.getRepository(BusinessOwnerEntity);
    const businessOwner = repository.create({ id, ...updateBusinessOwnerDto });
    return await repository.save(businessOwner);
  }
  async deleteBusinessOwner(id: string): Promise<void> {
    const repository = this.getRepository(BusinessOwnerEntity);
    await repository.delete(id);
  }

  async getBusinessOwnerById(id: string): Promise<BusinessOwnerModel> {
    const repository = this.getRepository(BusinessOwnerEntity);
    return await repository.findOneBy({ id: id });
  }

  async getBusinessOwners(
    conditions: GetBusinessOwnerListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<BusinessOwnerModel>> {
    conditions.codes = jwtPayload?.businessOwners; // Data role

    const repository = this.getRepository(BusinessOwnerEntity);
    let queryBuilder = repository.createQueryBuilder('businessOwners');

    if (conditions.statuses) {
      queryBuilder.andWhere('businessOwners.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('businessOwners.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('businessOwners.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getBusinessOwnerByCode(
    code: string,
    id?: string,
  ): Promise<BusinessOwnerModel> {
    const repository = this.getRepository(BusinessOwnerEntity);

    const query: FindOneOptions<BusinessOwnerEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getBusinessOwnerByIds(
    ids: string[],
    jwtPayload,
  ): Promise<BusinessOwnerModel[]> {
    const repository = this.getRepository(BusinessOwnerEntity);

    let queryBuilder = repository
      .createQueryBuilder('businessOwners')
      .leftJoin('businessOwners.budgetCodes', 'budgetCodes')
      .addSelect(['budgetCodes.id', 'budgetCodes.code']);

    queryBuilder.where('businessOwners.id IN (:...ids)', { ids: ids });

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetBusinessOwnerListDto({}),
    );

    return await queryBuilder.getMany();
  }

  async getDetailBusinessOwner(
    conditions: GetDetailBusinessOwnerDto,
    jwtPayload: any,
  ): Promise<BusinessOwnerModel> {
    const repository = this.getRepository(BusinessOwnerEntity);
    let queryBuilder = repository.createQueryBuilder('businessOwners');

    queryBuilder.where('businessOwners.id = :id', {
      id: conditions.id,
    });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  async getBusinessOwnerByCodes(
    codes: string[],
  ): Promise<BusinessOwnerModel[]> {
    const repository = this.getRepository(BusinessOwnerEntity);

    const query: FindOneOptions<BusinessOwnerEntity> = {
      where: {
        code: In(codes),
      },
    };

    return await repository.find(query);
  }

  async getBusinessOwnersByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<BusinessOwnerModel[]> {
    const repository = this.getRepository(BusinessOwnerEntity);
    let queryBuilder = repository.createQueryBuilder('businessOwners');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetBusinessOwnerListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.where('businessOwners.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.where('businessOwners.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<BusinessOwnerEntity>,
    jwtPayload: any,
    conditions: GetBusinessOwnerListDto | GetDetailBusinessOwnerDto,
  ) {
    conditions.codes = jwtPayload?.businessOwners;
    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        EBusinessOwnerPermission.CREATE,
        EBusinessOwnerPermission.EDIT,
      ]) &&
      conditions.codes?.length
    ) {
      queryBuilder.andWhere(
        '(businessOwners.id IS NULL OR businessOwners.code IN (:...codes))',
        {
          codes: conditions.codes,
        },
      );
    }

    return queryBuilder;
  }
}
