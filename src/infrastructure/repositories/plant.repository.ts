import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetDetailPlantDto } from '../../controller/plant/dtos/get-detail-plant.dto';
import { GetPlantListDto } from '../../controller/plant/dtos/get-plant-list.dto';
import { UpdatePlantDto } from '../../controller/plant/dtos/update-plant.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PlantModel } from '../../domain/model/plant.model';
import { IPlantRepository } from '../../domain/repositories/plant.repository';
import { parseScopes } from '../../utils/common';
import {
  EPlantPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { PlantEntity } from '../entities/plant.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class PlantRepository
  extends BaseRepository
  implements IPlantRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createPlant(data: PlantModel): Promise<PlantModel> {
    const repository = this.getRepository(PlantEntity);

    const plant = repository.create(data);

    return await repository.save(plant);
  }
  async updatePlant(
    id: string,
    updatePlantDto: UpdatePlantDto,
  ): Promise<PlantModel> {
    const repository = this.getRepository(PlantEntity);
    const plant = repository.create({
      id,
      ...updatePlantDto,
    });
    return await repository.save(plant);
  }
  async deletePlant(id: string): Promise<void> {
    const repository = this.getRepository(PlantEntity);
    await repository.delete(id);
  }

  // async getPlantById(id: string): Promise<PlantModel> {
  //   const repository = this.getRepository(PlantEntity);
  //   const queryBuilder = repository.createQueryBuilder('plants');

  //   queryBuilder.where('plants.id = :id', { id });
  //   queryBuilder.leftJoinAndSelect('plants.sector', 'sector');

  //   return await queryBuilder.getOne();
  // }

  async getPlants(
    conditions: GetPlantListDto,
    jwtPayload,
  ): Promise<ResponseDto<PlantModel>> {
    conditions.codes = jwtPayload?.plants;
    conditions.sectorCodes = jwtPayload?.sectors;

    const repository = this.getRepository(PlantEntity);
    let queryBuilder = repository.createQueryBuilder('plants');

    queryBuilder.leftJoinAndSelect('plants.sector', 'sector');

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('plants.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.statuses) {
      queryBuilder.andWhere('plants.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.sectorIds) {
      queryBuilder.andWhere('plants.sectorId IN (:...sectorIds)', {
        sectorIds: conditions.sectorIds,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    queryBuilder.orderBy('plants.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getPlantByCode(code: string, id?: string): Promise<PlantModel> {
    const repository = this.getRepository(PlantEntity);

    const query: FindOneOptions<PlantEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getPlantDetail(
    conditions: GetDetailPlantDto,
    jwtPayload: any,
  ): Promise<PlantModel> {
    conditions.codes = jwtPayload?.plants;
    conditions.sectorCodes = jwtPayload?.sectors;

    const repository = this.getRepository(PlantEntity);
    let queryBuilder = repository.createQueryBuilder('plants');

    queryBuilder.where('plants.id = :id', { id: conditions.id });
    queryBuilder.leftJoinAndSelect('plants.sector', 'sector');

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<PlantEntity>,
    jwtPayload: any,
    conditions: GetPlantListDto | GetDetailPlantDto,
  ) {
    if (!jwtPayload?.isSuperAdmin) {
      if (
        !parseScopes(jwtPayload?.scopes, [
          EPlantPermission.CREATE,
          EPlantPermission.EDIT,
        ]) &&
        conditions?.codes?.length
      ) {
        queryBuilder.andWhere(
          '(plants.id IS NULL OR plants.code IN (:...codes))',
          {
            codes: conditions.codes,
          },
        );
      }

      if (
        !parseScopes(jwtPayload?.scopes, [
          ESectorPermission.CREATE,
          ESectorPermission.EDIT,
        ]) &&
        conditions?.sectorCodes?.length
      ) {
        queryBuilder.andWhere(
          '(sector.id IS NULL OR sector.code IN (:...sectorCodes))',
          {
            sectorCodes: conditions?.sectorCodes,
          },
        );
      }
    }

    return queryBuilder;
  }

  async getPlantByIds(ids: string[], jwtPayload: any): Promise<PlantModel[]> {
    const repository = this.getRepository(PlantEntity);
    let queryBuilder = repository.createQueryBuilder('processType');

    if (ids.length) {
      queryBuilder.where('processType.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetPlantListDto({}),
    );

    return await queryBuilder.getMany();
  }
}
