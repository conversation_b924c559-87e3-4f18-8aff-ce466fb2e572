import { Provider } from '@nestjs/common';
import { IActualSpendingRepository } from '../../domain/repositories/actual-spending.repository';
import { IApiLogRepository } from '../../domain/repositories/api-log.repository';
import { IApprovalLevelRepository } from '../../domain/repositories/approval-level.repository';
import { IApprovalProcessDetailRepository } from '../../domain/repositories/approval-process-detail.repository';
import { IApprovalWorkflowRepository } from '../../domain/repositories/approval-workflow.repository';
import { IBudgetCapexRepository } from '../../domain/repositories/budget-capex.repository';
import { IBudgetCodeRepository } from '../../domain/repositories/budget-code.repository';
import { IBudgetInvestmentRepository } from '../../domain/repositories/budget-investment.repository';
import { IBudgetOpexRepository } from '../../domain/repositories/budget-opex.repository';
import { IBudgetRepository } from '../../domain/repositories/budget.repository';
import { IBusinessOwnerRepository } from '../../domain/repositories/business-owner.repository';
import { IBusinessUnitRepository } from '../../domain/repositories/business-unit.repository';
import { ICompanyRepository } from '../../domain/repositories/company.repository';
import { IConditionDetailRepository } from '../../domain/repositories/condition-detail.repository';
import { IConditionRepository } from '../../domain/repositories/condition.repository';
import { ICostSubHistoryRepository } from '../../domain/repositories/cost-sub-history.repository';
import { ICostRepository } from '../../domain/repositories/cost.repository';
import { ICostcenterSubaccountRepository } from '../../domain/repositories/costcenter-subaccount.repository';
import { ICurrencyUnitExchangeRepository } from '../../domain/repositories/currency-unit-exchange.repository';
import { ICurrencyUnitRepository } from '../../domain/repositories/currency-unit.repository';
import { IDepartmentRepository } from '../../domain/repositories/department.repository';
import { IFileExportHistoryRepository } from '../../domain/repositories/file-export-history.repository';
import { IFileImportHistoryRepository } from '../../domain/repositories/file-import-history.repository';
import { IFunctionUnitRepository } from '../../domain/repositories/function-unit.repository';
import { IIncreasementCodeRepository } from '../../domain/repositories/increasement-code.repository';
import { IInventoryStandardRepository } from '../../domain/repositories/inventory-standard.repository';
import { IMaterialGroupRepository } from '../../domain/repositories/material-group.repository';
import { IMaterialSectorRepository } from '../../domain/repositories/material-sector.repository';
import { IMaterialTypeRepository } from '../../domain/repositories/material-type.repository';
import { IMaterialRepository } from '../../domain/repositories/material.repository';
import { IMeasureRepository } from '../../domain/repositories/measure.repository';
import { INotificationFormRepository } from '../../domain/repositories/notification-form.repository';
import { INotificationRepository } from '../../domain/repositories/notification.repository';
import { IOwnerDeviceTokenRepository } from '../../domain/repositories/owner-device-token.repository';
import { IPlantRepository } from '../../domain/repositories/plant.repository';
import { IPositionRepository } from '../../domain/repositories/position.repository';
import { IProcessConditionRepository } from '../../domain/repositories/process-condition.repository';
import { IProcessTypeRepository } from '../../domain/repositories/process-type.repository';
import { IProcessRepository } from '../../domain/repositories/process.repository';
import { IProfitCenterRepository } from '../../domain/repositories/profit-center.repository';
import { IPurchaseOrderTypeRepository } from '../../domain/repositories/purchase-order-type.repository';
import { IPurchaseRequestTypeRepository } from '../../domain/repositories/purchase-request-type.repository';
import { IPurchasingDepartmentRepository } from '../../domain/repositories/purchasing-department.repository';
import { IPurchasingGroupRepository } from '../../domain/repositories/purchasing-group.repository';
import { ISectorRepository } from '../../domain/repositories/sector.repository';
import { IStaffApprovalWorkflowRepository } from '../../domain/repositories/staff-approval-workflow.repository';
import { IStaffHierarchyRepository } from '../../domain/repositories/staff-hierarchy.repository';
import { IStaffRepository } from '../../domain/repositories/staff.repository';
import { ISupplierSectorRepository } from '../../domain/repositories/supplier-sector.repository';
import { ISupplierRepository } from '../../domain/repositories/supplier.repository';
import { ActualSpendingRepository } from './actual-spending.repository';
import { ApiLogRepository } from './api-log.repository';
import { prApprovalRepository } from './approval-flow.repository';
import { ApprovalLevelRepository } from './approval-level.repository';
import { ApprovalProcessDetailRepository } from './approval-process-detail.repository';
import { ApprovalWorkflowRepository } from './approval-workflow.repository';
import { BudgetCapexRepository } from './budget-capex.repository';
import { BudgetCodeRepository } from './budget-code.repository';
import { BudgetInvestmentRepository } from './budget-investment.repository';
import { BudgetOpexRepository } from './budget-opex.repository';
import { BudgetRepository } from './budget.repository';
import { BusinessOwnerRepository } from './business-owner.repository';
import { BusinessUnitRepository } from './business-unit.repository';
import { CompanyRepository } from './company.repository';
import { ConditionDetailRepository } from './condition-detail.repository';
import { ConditionRepository } from './condition.repository';
import { CostSubHistoryRepository } from './cost-sub-history.repository';
import { CostRepository } from './cost.repository';
import { CostcenterSubaccountRepository } from './costcenter-subaccount.repository';
import { CurrencyUnitExchangeRepository } from './currency-unit-exchange.repository';
import { CurrencyUnitRepository } from './currency-unit.repository';
import { DepartmentRepository } from './department.repository';
import { FileExportHistoryRepository } from './file-export-history.repository';
import { FileImportHistoryRepository } from './file-import-history.repository';
import { FileRepository } from './file.repository';
import { FunctionUnitRepository } from './function-unit.repository';
import { IncreasementCodeRepository } from './increasement-code.repository';
import { InventoryStandardRepository } from './inventory-standard.repository';
import { MaterialGroupRepository } from './material-group.repository';
import { MaterialSectorRepository } from './material-sector.repository';
import { MaterialTypeRepository } from './material-type.repository';
import { MaterialRepository } from './material.repository';
import { MeasureRepository } from './measure.repository';
import { NotificationFormRepository } from './notification-form.repository';
import { NotificationRepository } from './notification.repository';
import { OwnerDeviceTokenRepository } from './owner-device-token.repository';
import { PlantRepository } from './plant.repository';
import { PositionRepository } from './position.repository';
import { priceInformationRecordRepository } from './price_information_record.repository';
import { ProcessConditionRepository } from './process-condition.repository';
import { ProcessTypeRepository } from './process-type.repository';
import { ProcessRepository } from './process.repository';
import { ProfitCenterRepository } from './profit-center.repository';
import { PurchaseOrderTypeRepository } from './purchase-order-type.repository';
import { PurchaseRequestTypeRepository } from './purchase-request-type.repository';
import { purchaseOrderRepository } from './purchase_order.repository';
import { purchaseRequestRepository } from './purchase_request.repository';
import { PurchasingDepartmentRepository } from './purchasing-department.repository';
import { PurchasingGroupRepository } from './purchasing-group.repository';
import { SapPurchaseOrderItemRepository } from './sap-purchase-order-item.repository';
import { SapPurchaseOrderRepository } from './sap-purchase-order.repository';
import { SectorRepository } from './sector.repository';
import { StaffApprovalWorkflowRepository } from './staff-approval-workflow.repository';
import { StaffHierarchyRepository } from './staff-hierarchy.repository';
import { StaffRepository } from './staff.repository';
import { SupplierSectorRepository } from './supplier-sector.repository';
import { SupplierRepository } from './supplier.repository';
import { ITaxCodeRepository } from '../../domain/repositories/tax-code.repository';
import { TaxCodeRepository } from './tax-code.repository';
import { IReceiptSolomonRepository } from '../../domain/repositories/receipt-solomon.repository';
import { ReceiptSolomonRepository } from './receipt-solomon.repository';
import { IWarehouseRepository } from '../../domain/repositories/warehouse.repository';
import { WarehouseRepository } from './warehouse.repository';
import { SolomonPurchaseOrderRepository } from './solomon-purchase-order.repository';
import { ISolomonPurchaseOrderRepository } from '../../domain/repositories/solomon-purchase-order.repository';
import { ISolomonPurchaseOrderItemRepository } from '../../domain/repositories/solomon-purchase-order-item.repository';
import { SolomonPurchaseOrderItemRepository } from './solomon-purchase-order-item.repository';

export const BusinessUnitRepositoryProvider = {
  provide: IBusinessUnitRepository,
  useClass: BusinessUnitRepository,
};

export const SectorRepositoryProvider = {
  provide: ISectorRepository,
  useClass: SectorRepository,
};

export const CompanyRepositoryProvider = {
  provide: ICompanyRepository,
  useClass: CompanyRepository,
};

export const BusinessOwnerRepositoryProvider = {
  provide: IBusinessOwnerRepository,
  useClass: BusinessOwnerRepository,
};

export const DepartmentRepositoryProvider = {
  provide: IDepartmentRepository,
  useClass: DepartmentRepository,
};

export const FunctionUnitRepositoryProvider = {
  provide: IFunctionUnitRepository,
  useClass: FunctionUnitRepository,
};

export const BudgetCapexRepositoryProvider = {
  provide: IBudgetCapexRepository,
  useClass: BudgetCapexRepository,
};

export const BudgetOpexRepositoryProvider = {
  provide: IBudgetOpexRepository,
  useClass: BudgetOpexRepository,
};

export const BudgetCodeRepositoryProvider = {
  provide: IBudgetCodeRepository,
  useClass: BudgetCodeRepository,
};

export const BudgetInvestmentRepositoryProvider = {
  provide: IBudgetInvestmentRepository,
  useClass: BudgetInvestmentRepository,
};

export const BudgetRepositoryProvider = {
  provide: IBudgetRepository,
  useClass: BudgetRepository,
};

export const CostcenterSubaccountRepositoryProvider = {
  provide: ICostcenterSubaccountRepository,
  useClass: CostcenterSubaccountRepository,
};

export const CurrencyUnitRepositoryProvider = {
  provide: ICurrencyUnitRepository,
  useClass: CurrencyUnitRepository,
};

export const IncreasementCodeRepositoryProvider = {
  provide: IIncreasementCodeRepository,
  useClass: IncreasementCodeRepository,
};

export const CurrencyUnitExchangeRepositoryProvider = {
  provide: ICurrencyUnitExchangeRepository,
  useClass: CurrencyUnitExchangeRepository,
};

export const FileRepositoryProvider = FileRepository;

export const CostSubHistoryRepositoryProvider = {
  provide: ICostSubHistoryRepository,
  useClass: CostSubHistoryRepository,
};

export const FileImportHistoryRepositoryProvider = {
  provide: IFileImportHistoryRepository,
  useClass: FileImportHistoryRepository,
};

export const MaterialTypeRepositoryProvider = {
  provide: IMaterialTypeRepository,
  useClass: MaterialTypeRepository,
};

export const MaterialGroupRepositoryProvider = {
  provide: IMaterialGroupRepository,
  useClass: MaterialGroupRepository,
};

export const MaterialRepositoryProvider = {
  provide: IMaterialRepository,
  useClass: MaterialRepository,
};

export const PurchaseRequestTypeProvider = {
  provide: IPurchaseRequestTypeRepository,
  useClass: PurchaseRequestTypeRepository,
};

export const PurchaseOrderTypeProvider = {
  provide: IPurchaseOrderTypeRepository,
  useClass: PurchaseOrderTypeRepository,
};

export const PurchasingGroupRepositoryProvider = {
  provide: IPurchasingGroupRepository,
  useClass: PurchasingGroupRepository,
};

export const PurchasingDepartmentRepositoryProvider = {
  provide: IPurchasingDepartmentRepository,
  useClass: PurchasingDepartmentRepository,
};

export const PlantRepositoryProvider = {
  provide: IPlantRepository,
  useClass: PlantRepository,
};

export const SupplierRepositoryProvider = {
  provide: ISupplierRepository,
  useClass: SupplierRepository,
};

export const PositionRepositoryProvider = {
  provide: IPositionRepository,
  useClass: PositionRepository,
};

export const StaffRepositoryProvider = {
  provide: IStaffRepository,
  useClass: StaffRepository,
};

export const StaffHierarchyRepositoryProvider = {
  provide: IStaffHierarchyRepository,
  useClass: StaffHierarchyRepository,
};

export const InventoryStandardRepositoryProvider = {
  provide: IInventoryStandardRepository,
  useClass: InventoryStandardRepository,
};

export const FileExportHistoryRepositoryProvider = {
  provide: IFileExportHistoryRepository,
  useClass: FileExportHistoryRepository,
};

export const NotificationFormRepositoryProvider = {
  provide: INotificationFormRepository,
  useClass: NotificationFormRepository,
};

export const NotificationRepositoryProvider = {
  provide: INotificationRepository,
  useClass: NotificationRepository,
};

export const OwnerDeviceTokenRepositoryProvider = {
  provide: IOwnerDeviceTokenRepository,
  useClass: OwnerDeviceTokenRepository,
};

export const ProfitCenterRepositoryProvider = {
  provide: IProfitCenterRepository,
  useClass: ProfitCenterRepository,
};

export const SupplierSectorRepositoryProvider = {
  provide: ISupplierSectorRepository,
  useClass: SupplierSectorRepository,
};

export const MaterialSectorRepositoryProvider = {
  provide: IMaterialSectorRepository,
  useClass: MaterialSectorRepository,
};

export const ConditionDetailRepositoryProvider = {
  provide: IConditionDetailRepository,
  useClass: ConditionDetailRepository,
};

export const ConditionRepositoryProvider = {
  provide: IConditionRepository,
  useClass: ConditionRepository,
};

export const ProcessConditionRepositoryProvider = {
  provide: IProcessConditionRepository,
  useClass: ProcessConditionRepository,
};

export const ProcessRepositoryProvider = {
  provide: IProcessRepository,
  useClass: ProcessRepository,
};

export const ApprovalWorkflowRepositoryProvider = {
  provide: IApprovalWorkflowRepository,
  useClass: ApprovalWorkflowRepository,
};

export const StaffApprovalWorkflowRepositoryProvider = {
  provide: IStaffApprovalWorkflowRepository,
  useClass: StaffApprovalWorkflowRepository,
};

export const CostRepositoryProvider = {
  provide: ICostRepository,
  useClass: CostRepository,
};

export const ActualSpendingRepositoryProvider = {
  provide: IActualSpendingRepository,
  useClass: ActualSpendingRepository,
};

export const ProcessTypeRepositoryProvider = {
  provide: IProcessTypeRepository,
  useClass: ProcessTypeRepository,
};

export const ApiLogRepositoryProvider = {
  provide: IApiLogRepository,
  useClass: ApiLogRepository,
};

export const PurchaseRequestRepositoryProvider = {
  provide: 'IPurchaseRequestRepository',
  useClass: purchaseRequestRepository,
};

export const PurchaseOrderRepositoryProvider = {
  provide: 'IPurchaseOrderRepository',
  useClass: purchaseOrderRepository,
};

export const PirRepositoryProvider = {
  provide: 'IPriceInformationRecordRepository',
  useClass: priceInformationRecordRepository,
};

export const SapPurchaseOrderRepositoryProvider = {
  provide: 'ISapPurchaseOrderRepository',
  useClass: SapPurchaseOrderRepository,
};

export const SapPurchaseOrderItemRepositoryProvider = {
  provide: 'ISapPurchaseOrderItemRepository',
  useClass: SapPurchaseOrderItemRepository,
};

export const PrApprovalFlowRepositoryProvider = {
  provide: 'IPrApprovalFlowRepository',
  useClass: prApprovalRepository,
};

export const MeasureRepositoryProvider = {
  provide: IMeasureRepository,
  useClass: MeasureRepository,
};

export const ApprovalLevelRepositoryProvider = {
  provide: IApprovalLevelRepository,
  useClass: ApprovalLevelRepository,
};

export const ApprovalProcessDetailRepositoryProvider = {
  provide: IApprovalProcessDetailRepository,
  useClass: ApprovalProcessDetailRepository,
};

export const TaxCodeRepositoryProvider = {
  provide: ITaxCodeRepository,
  useClass: TaxCodeRepository,
};

export const ReceiptSolomonRepositoryProvider = {
  provide: IReceiptSolomonRepository,
  useClass: ReceiptSolomonRepository,
};

export const WarehouseRepositoryProvider = {
  provide: IWarehouseRepository,
  useClass: WarehouseRepository,
};

export const SolomonPurchaseOrderRepositoryProvider = {
  provide: ISolomonPurchaseOrderRepository,
  useClass: SolomonPurchaseOrderRepository,
};

export const SolomonPurchaseOrderItemRepositoryProvider = {
  provide: ISolomonPurchaseOrderItemRepository,
  useClass: SolomonPurchaseOrderItemRepository,
};

export const RepositoryProviders: Provider[] = [
  BusinessUnitRepositoryProvider,
  SectorRepositoryProvider,
  CompanyRepositoryProvider,
  BusinessOwnerRepositoryProvider,
  DepartmentRepositoryProvider,
  FunctionUnitRepositoryProvider,
  BudgetCapexRepositoryProvider,
  BudgetOpexRepositoryProvider,
  BudgetCodeRepositoryProvider,
  BudgetInvestmentRepositoryProvider,
  BudgetRepositoryProvider,
  CostcenterSubaccountRepositoryProvider,
  CurrencyUnitRepositoryProvider,
  IncreasementCodeRepositoryProvider,
  FileRepositoryProvider,
  CurrencyUnitExchangeRepositoryProvider,
  CostSubHistoryRepositoryProvider,
  FileImportHistoryRepositoryProvider,
  MaterialTypeRepositoryProvider,
  MaterialGroupRepositoryProvider,
  MaterialRepositoryProvider,
  PurchaseRequestTypeProvider,
  PurchaseOrderTypeProvider,
  PurchasingGroupRepositoryProvider,
  PurchasingDepartmentRepositoryProvider,
  PlantRepositoryProvider,
  SupplierRepositoryProvider,
  PositionRepositoryProvider,
  StaffRepositoryProvider,
  StaffHierarchyRepositoryProvider,
  InventoryStandardRepositoryProvider,
  FileExportHistoryRepositoryProvider,
  NotificationFormRepositoryProvider,
  NotificationRepositoryProvider,
  OwnerDeviceTokenRepositoryProvider,
  ProfitCenterRepositoryProvider,
  SupplierSectorRepositoryProvider,
  MaterialSectorRepositoryProvider,
  ConditionDetailRepositoryProvider,
  ConditionRepositoryProvider,
  ProcessConditionRepositoryProvider,
  ProcessRepositoryProvider,
  ApprovalWorkflowRepositoryProvider,
  StaffApprovalWorkflowRepositoryProvider,
  CostRepositoryProvider,
  ActualSpendingRepositoryProvider,
  ProcessTypeRepositoryProvider,
  ApiLogRepositoryProvider,
  PurchaseRequestRepositoryProvider,
  PurchaseOrderRepositoryProvider,
  PirRepositoryProvider,
  SapPurchaseOrderRepositoryProvider,
  SapPurchaseOrderItemRepositoryProvider,
  PrApprovalFlowRepositoryProvider,
  MeasureRepositoryProvider,
  ApprovalLevelRepositoryProvider,
  ApprovalProcessDetailRepositoryProvider,
  TaxCodeRepositoryProvider,
  ReceiptSolomonRepositoryProvider,
  WarehouseRepositoryProvider,
  SolomonPurchaseOrderRepositoryProvider,
  SolomonPurchaseOrderItemRepositoryProvider,
];
