import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { ICostSubHistoryRepository } from '../../domain/repositories/cost-sub-history.repository';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { CostSubHistoryModel } from '../../domain/model/cost-sub-history.model';
import { CostSubHistoryEntity } from '../entities/cost-sub-history.entity';

@Injectable({ scope: Scope.REQUEST })
export class CostSubHistoryRepository
  extends BaseRepository
  implements ICostSubHistoryRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createCostSubHistory(
    data: CostSubHistoryModel,
  ): Promise<CostSubHistoryModel> {
    const repository = this.getRepository(CostSubHistoryEntity);
    const newCostSubHistory = repository.create(data);
    return await repository.save(newCostSubHistory);
  }
}
