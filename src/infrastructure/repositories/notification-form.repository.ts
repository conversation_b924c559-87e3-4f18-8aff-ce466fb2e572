import { Injectable, Scope } from '@nestjs/common';

import { GetDetailNotificationFormDto } from '../../controller/notification-form/dtos/get-detail-notification-form.dto';
import { GetNotificationFormListDto } from '../../controller/notification-form/dtos/get-notification-form-list.dto';
import { ENotificationFormType } from '../../domain/config/enums/notification-form.enum';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { INotificationFormRepository } from '../../domain/repositories/notification-form.repository';

import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { NotificationForm } from '../entities/notification-form.entity';
import { BaseMongoRepository } from './base-mongo.repository';

@Injectable({ scope: Scope.REQUEST })
export class NotificationFormRepository
  extends BaseMongoRepository<NotificationForm>
  implements INotificationFormRepository
{
  constructor(
    @InjectModel(NotificationForm.name)
    protected model: Model<NotificationForm>,
  ) {
    super(model);
  }

  async createNotificationForm(
    data: NotificationForm,
  ): Promise<NotificationForm> {
    return await this.model.create(data);
  }

  async updateNotificationForm(data: NotificationForm): Promise<void> {
    await this.model.updateOne({ _id: data._id }, { ...data });
  }

  async getNotificationForms(
    conditions: GetNotificationFormListDto,
  ): Promise<ResponseDto<NotificationForm>> {
    const query = this.buildFindQuery(conditions);
    return await this.pagination(conditions, query);
  }

  async getNotificationFormDetail(
    conditions: GetDetailNotificationFormDto,
  ): Promise<NotificationForm | null> {
    return await this.model
      .findOne({
        _id: new mongoose.Types.ObjectId(conditions.id),
      })
      .lean();
  }

  async deleteNotificationForm(id: string): Promise<void> {
    await this.model.deleteOne({ _id: new mongoose.Types.ObjectId(id) });
  }

  async getNotificationFormByType(
    type: ENotificationFormType,
  ): Promise<NotificationForm | null> {
    return await this.model.findOne({ type }).lean();
  }

  private buildFindQuery(conditions: GetNotificationFormListDto): any {
    const query: any = {};

    query.platform = { $eq: conditions.platform };

    if (conditions.statuses) {
      query.status = { $in: conditions.statuses };
    }

    if (conditions.types) {
      query.type = { $in: conditions.types };
    }

    return query;
  }
}
