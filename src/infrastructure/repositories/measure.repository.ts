import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { IMeasureRepository } from '../../domain/repositories/measure.repository';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { MeasureModel } from '../../domain/model/measure.model';
import { MeasureEntity } from '../entities/measure.entity';
import { UpdateMeasureDto } from '../../controller/measure/dtos/update-measure.dto';
import { GetMeasureListDto } from '../../controller/measure/dtos/get-measure-list.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { parseScopes } from '../../utils/common';
import { GetDetailMeasureDto } from '../../controller/measure/dtos/get-detail-cost.dto';
import { GetMeasureListByIdsDto } from '../../controller/measure/dtos/get-measure-list-by-ids.dto';

@Injectable({ scope: Scope.REQUEST })
export class MeasureRepository
  extends BaseRepository
  implements IMeasureRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createMeasure(data: MeasureModel): Promise<MeasureModel> {
    const repository = this.getRepository(MeasureEntity);

    const measure = repository.create(data);

    return await repository.save(measure);
  }

  async updateMeasure(
    id,
    updateMeasureDto: UpdateMeasureDto,
  ): Promise<MeasureModel> {
    const repository = this.getRepository(MeasureEntity);
    const measure = repository.create({ id, ...updateMeasureDto });
    return await repository.save(measure);
  }

  async deleteMeasure(id: string): Promise<void> {
    const repository = this.getRepository(MeasureEntity);
    await repository.delete(id);
  }

  async getMeasures(
    conditions: GetMeasureListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<MeasureModel>> {
    const repository = this.getRepository(MeasureEntity);
    let queryBuilder = repository.createQueryBuilder('measures');

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('measures.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    queryBuilder.orderBy('measures.updatedAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getDetailMeasure(
    conditions: GetDetailMeasureDto,
    jwtPayload: any,
  ): Promise<MeasureModel> {
    const repository = this.getRepository(MeasureEntity);
    let queryBuilder = repository.createQueryBuilder('measures');

    queryBuilder.where('measures.id = :id', {
      id: conditions.id,
    });

    return await queryBuilder.getOne();
  }

  async getMeasureByCode(code: string, id?: string): Promise<MeasureModel> {
    const repository = this.getRepository(MeasureEntity);

    const query: FindOneOptions<MeasureEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getMeasuresByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<MeasureModel[]> {
    const repository = this.getRepository(MeasureEntity);
    let queryBuilder = repository.createQueryBuilder('measures');

    if (codes?.length) {
      queryBuilder.andWhere('measures.code IN (:...codes)', {
        codes: codes,
      });
    } else {
      return [];
    }

    return await queryBuilder.getMany();
  }

  async getListByIds(
    conditions: GetMeasureListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<MeasureModel>> {
    const repository = this.getRepository(MeasureEntity);
    let queryBuilder = repository.createQueryBuilder('measures');

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.where('measures.id IN (:...ids)', {
        ids: conditions.ids,
      });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder.orderBy('measures.createdAt', 'DESC');

    return await this.pagination<MeasureEntity>(queryBuilder, conditions);
  }
}
