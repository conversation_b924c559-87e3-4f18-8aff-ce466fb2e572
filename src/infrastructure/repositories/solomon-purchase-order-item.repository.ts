import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { ISolomonPurchaseOrderItemRepository } from '../../domain/repositories/solomon-purchase-order-item.repository';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { SolomonPurchaseOrderItemEntity } from '../entities/solomon-purchase-order-item.entity';
import { SolomonPurchaseOrderItemModel } from '../../domain/model/solomon-purchase-order-item.model';
import { CreateSolomonPurchaseOrderItemDto } from '../../controller/solomon-purchase-order/dtos/create-solomon-purchase-order-item.dto';

@Injectable({
  scope: Scope.REQUEST,
})
export class SolomonPurchaseOrderItemRepository
  extends BaseRepository
  implements ISolomonPurchaseOrderItemRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
    @InjectRepository(SolomonPurchaseOrderItemEntity)
    private readonly solomonPurchaseOrderItemEntityRepository: Repository<SolomonPurchaseOrderItemEntity>,
  ) {
    super(dataSource, req);
  }

  async createSolomonPurchaseOrderItem(
    data: CreateSolomonPurchaseOrderItemDto[],
  ): Promise<SolomonPurchaseOrderItemModel[]> {
    // const repository = this.getRepository(SolomonPurchaseOrderItemEntity);
    const dataCreate = this.solomonPurchaseOrderItemEntityRepository.create({
      ...data,
    });

    return await this.solomonPurchaseOrderItemEntityRepository.save(dataCreate);
  }
  async getSolomonPOItemsByPoDetailId(
    poDetailId: number,
  ): Promise<SolomonPurchaseOrderItemModel[]> {
    const repository = this.getRepository(SolomonPurchaseOrderItemEntity);

    return await repository.find({
      where: { purchaseOrderDetailId: poDetailId },
    });
  }

  async deleteSolomonPOItemsBySolomonPurchaseOrderId(
    solomonPurchaseOrderIds: string[],
  ): Promise<void> {
    await this.solomonPurchaseOrderItemEntityRepository.delete({
      solomonPurchaseOrderId: In(solomonPurchaseOrderIds),
    });
  }
}
