import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { StaffHierarchyModel } from '../../domain/model/staff-hierarchy.model';
import { IStaffHierarchyRepository } from '../../domain/repositories/staff-hierarchy.repository';
import { StaffHierarchyEntity } from '../entities/staff-hierarchy.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class StaffHierarchyRepository
  extends BaseRepository
  implements IStaffHierarchyRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createStaffHierarchy(
    data: StaffHierarchyModel,
  ): Promise<StaffHierarchyModel> {
    const repository = this.getRepository(StaffHierarchyEntity);
    const newStaff = repository.create(data);

    return await repository.save(newStaff);
  }

  async deleteStaffHierarchy(id: string): Promise<void> {
    const repository = this.getRepository(StaffHierarchyEntity);

    await repository.softDelete({ id });
  }

  async getExistManagementLevel(
    subordinateId: string,
  ): Promise<StaffHierarchyModel[]> {
    const repository = this.getRepository(StaffHierarchyEntity);

    const queryBuilder = repository.createQueryBuilder('staffHierarchy');
    queryBuilder
      .leftJoin('staffHierarchy.manager', 'manager')
      .leftJoinAndSelect('staffHierarchy.subordinate', 'subordinate')
      .where('subordinate.id = :subordinateId', { subordinateId });

    return await queryBuilder.getMany();
  }
}
