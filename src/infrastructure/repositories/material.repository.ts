import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetDetailMaterialDto } from '../../controller/material/dtos/get-detail-material.dto';
import { GetMaterialListDto } from '../../controller/material/dtos/get-material-list.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { MaterialModel } from '../../domain/model/material.model';
import { IMaterialRepository } from '../../domain/repositories/material.repository';
import { parseScopes } from '../../utils/common';
import {
  EBusinessOwnerPermission,
  EBusinessUnitPermission,
  ECompanyPermission,
  EDepartmentPermission,
  EMaterialGroupPermission,
  EMaterialPermission,
  EMaterialTypePermission,
  EPurchasingDepartmentPermission,
  EPurchasingGroupPermission,
} from '../../utils/constants/permission.enum';
import { MaterialEntity } from '../entities/material.entity';
import { BaseRepository } from './base.repository';
import { GetMaterialListByIdsDto } from '../../controller/material/dtos/get-material-list-by-ids.dto';

@Injectable({ scope: Scope.REQUEST })
export class MaterialRepository
  extends BaseRepository
  implements IMaterialRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createMaterial(data: MaterialModel): Promise<MaterialModel> {
    const repository = this.getRepository(MaterialEntity);

    const material = repository.create(data);

    return await repository.save(material);
  }
  async updateMaterial(
    id: string,
    updateMaterialDto: MaterialModel,
  ): Promise<MaterialModel> {
    const repository = this.getRepository(MaterialEntity);
    const material = repository.create({ id, ...updateMaterialDto });
    return await repository.save(material);
  }
  async deleteMaterial(id: string): Promise<void> {
    const repository = this.getRepository(MaterialEntity);
    await repository.softDelete(id);
  }

  // async getMaterialById(id: string): Promise<MaterialModel> {
  //   const repository = this.getRepository(MaterialEntity);
  //   const queryBuilder = repository.createQueryBuilder('materials');

  //   queryBuilder.innerJoinAndSelect('materials.sector', 'sector');
  //   queryBuilder.innerJoinAndSelect('materials.materialType', 'materialType');
  //   queryBuilder.innerJoinAndSelect('materials.materialGroup', 'materialGroup');

  //   queryBuilder.where('materials.id = :id', { id: id });
  //   return await queryBuilder.getOne();
  // }

  async getMaterials(
    conditions: GetMaterialListDto,
    jwtPayload,
  ): Promise<ResponseDto<MaterialModel>> {
    if (conditions.isEmptyResult) {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    const repository = this.getRepository(MaterialEntity);
    let queryBuilder = repository
      .createQueryBuilder('materials')
      .leftJoin('materials.materialType', 'materialType')
      .addSelect([
        'materialType.id',
        'materialType.code',
        'materialType.name',
        'materialType.description',
        'materialType.status',
      ])
      .leftJoin('materials.measure', 'measure')
      .addSelect([
        'measure.id',
        'measure.code',
        'measure.name',
        'measure.description',
      ])
      .leftJoin('materials.materialGroup', 'materialGroup')
      .leftJoin('materialGroup.businessOwners', 'businessOwners')
      .addSelect([
        'materialGroup.id',
        'materialGroup.code',
        'materialGroup.name',
        'materialGroup.description',
        'materialGroup.status',
        'businessOwners.id',
        'businessOwners.code',
        'businessOwners.name',
        'businessOwners.description',
        'businessOwners.status',
      ])
      .leftJoin('materials.functionUnits', 'functionUnits')
      .addSelect([
        'functionUnits.id',
        'functionUnits.name',
        'functionUnits.code',
        'functionUnits.description',
        'functionUnits.status',
      ])
      .leftJoin('materials.purchasingGroup', 'purchasingGroup')
      .addSelect([
        'purchasingGroup.id',
        'purchasingGroup.code',
        'purchasingGroup.name',
        'purchasingGroup.description',
        'purchasingGroup.status',
      ])
      .leftJoin('materials.purchasingDepartment', 'purchasingDepartment')
      .addSelect([
        'purchasingDepartment.id',
        'purchasingDepartment.code',
        'purchasingDepartment.name',
        'purchasingDepartment.description',
        'purchasingDepartment.status',
      ]);

    // queryBuilder
    //   .leftJoin('materials.company', 'company')
    //   .addSelect([
    //     'company.id',
    //     'company.name',
    //     'company.code',
    //     'company.description',
    //     'company.status',
    //   ]);
    queryBuilder
      .leftJoin('materials.businessUnits', 'businessUnits')
      .leftJoin('businessUnits.company', 'company')
      .addSelect([
        'businessUnits.id',
        'businessUnits.name',
        'businessUnits.code',
        'businessUnits.description',
        'businessUnits.status',
        'company.id',
        'company.name',
        'company.code',
        'company.description',
        'company.status',
      ]);
    queryBuilder
      .leftJoin('materials.department', 'department')
      .addSelect([
        'department.id',
        'department.name',
        'department.code',
        'department.description',
        'department.status',
      ]);

    queryBuilder
      .leftJoin('materials.profitCenter', 'profitCenter')
      .addSelect([
        'profitCenter.id',
        'profitCenter.code',
        'profitCenter.description',
        'profitCenter.status',
      ]);
    queryBuilder
      .leftJoin('materials.industries', 'industries')
      .addSelect([
        'industries.id',
        'industries.codeSAP',
        'industries.status',
        'industries.sector',
      ]);
    queryBuilder
      .leftJoin('industries.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.code',
        'sector.name',
        'sector.description',
        'sector.status',
      ]);

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('materials.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.statuses) {
      queryBuilder.andWhere('materials.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.materialTypeIds) {
      queryBuilder.andWhere(
        'materials.materialTypeId IN (:...materialTypeIds)',
        {
          materialTypeIds: conditions.materialTypeIds,
        },
      );
    }

    if (conditions.profitCenterIds) {
      queryBuilder.andWhere('profitCenter.id IN (:...profitCenterIds)', {
        profitCenterIds: conditions.profitCenterIds,
      });
    }

    if (conditions.materialGroupIds) {
      queryBuilder.andWhere(
        'materials.materialGroupId IN (:...materialGroupIds)',
        {
          materialGroupIds: conditions.materialGroupIds,
        },
      );
    }

    // if (conditions.throughPurchasings) {
    //   queryBuilder.andWhere(
    //     'materials.throughPurchasing IN (:...throughPurchasings)',
    //     {
    //       throughPurchasings: conditions.throughPurchasings,
    //     },
    //   );
    // }

    // if (conditions.checkBudgets) {
    //   queryBuilder.andWhere('materials.checkBudget IN (:...checkBudgets)', {
    //     checkBudgets: conditions.checkBudgets,
    //   });
    // }

    if (conditions.companyIds) {
      queryBuilder.andWhere('company.id IN (:...companyIds)', {
        companyIds: conditions.companyIds,
      });
    }

    if (conditions.businessUnitIds) {
      queryBuilder.andWhere('businessUnits.id IN (:...businessUnitIds)', {
        businessUnitIds: conditions.businessUnitIds,
      });
    }

    if (conditions.departmentIds) {
      queryBuilder.andWhere('department.id IN (:...departmentIds)', {
        departmentIds: conditions.departmentIds,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    queryBuilder.orderBy('materials.createdAt', 'DESC');

    const data = await this.pagination(queryBuilder, conditions);

    return data;
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<MaterialEntity>,
    jwtPayload: any,
    conditions: GetMaterialListDto | GetDetailMaterialDto,
  ) {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.functionUnitCodes = jwtPayload?.functionUnits;

    if (conditions.sectorCodes?.length) {
      queryBuilder.andWhere(
        '(sector.id IS NULL OR sector.code IN (:...sectorCodes))',
        {
          sectorCodes: conditions.sectorCodes,
        },
      );
    }

    if (conditions.functionUnitCodes?.length) {
      queryBuilder.andWhere(
        '(functionUnits.id IS NULL OR functionUnits.code IN (:...functionUnitCodes))',
        {
          functionUnitCodes: conditions.functionUnitCodes,
        },
      );
    }

    // conditions.companyCodes = jwtPayload?.companies;
    // conditions.businessUnitCodes = jwtPayload?.businessUnits;
    // conditions.departmentCodes = jwtPayload?.department;
    // conditions.materialCodes = jwtPayload?.materials;
    // conditions.materialGroupCodes = jwtPayload?.materialGroups;
    // conditions.materialTypeCodes = jwtPayload?.materialTypes;
    // conditions.purchasingDepartmentCodes = jwtPayload?.purchasingDepartments;
    // conditions.purchasingGroupCodes = jwtPayload?.purchasingGroups;
    // conditions.businessOwnerCodes = jwtPayload?.businessOwners;

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EMaterialGroupPermission.CREATE,
    //     EMaterialGroupPermission.EDIT,
    //   ]) &&
    //   conditions?.materialGroupCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(materialGroup.id IS NULL OR materialGroup.code IN (:...materialGroupCodes))',
    //     {
    //       materialGroupCodes: conditions?.materialGroupCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EMaterialTypePermission.CREATE,
    //     EMaterialTypePermission.EDIT,
    //   ]) &&
    //   conditions?.materialTypeCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(materialType.id IS NULL OR materialType.code IN (:...materialTypeCodes))',
    //     {
    //       materialTypeCodes: conditions?.materialTypeCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EMaterialPermission.CREATE,
    //     EMaterialPermission.EDIT,
    //   ]) &&
    //   conditions?.materialCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(materials.id IS NULL OR materials.code IN (:...materialCodes))',
    //     {
    //       materialCodes: conditions?.materialCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     ECompanyPermission.CREATE,
    //     ECompanyPermission.EDIT,
    //   ]) &&
    //   conditions?.companyCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(company.id IS NULL OR company.code IN (:...companyCodes))',
    //     {
    //       companyCodes: conditions?.companyCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     EBusinessUnitPermission.CREATE,
    //     EBusinessUnitPermission.EDIT,
    //   ]) &&
    //   conditions?.businessUnitCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(businessUnits.id IS NULL OR businessUnits.code IN (:...businessUnitCodes))',
    //     {
    //       businessUnitCodes: conditions?.businessUnitCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     EDepartmentPermission.CREATE,
    //     EDepartmentPermission.EDIT,
    //   ]) &&
    //   conditions?.departmentCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(department.id IS NULL OR department.code IN (:...departmentCodes))',
    //     {
    //       departmentCodes: conditions?.departmentCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EPurchasingGroupPermission.CREATE,
    //     EPurchasingGroupPermission.EDIT,
    //   ]) &&
    //   conditions?.purchasingGroupCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(purchasingGroup.id IS NULL OR purchasingGroup.code IN (:...purchasingGroupCodes))',
    //     {
    //       purchasingGroupCodes: conditions?.purchasingGroupCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EPurchasingDepartmentPermission.CREATE,
    //     EPurchasingDepartmentPermission.EDIT,
    //   ]) &&
    //   conditions?.purchasingDepartmentCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(purchasingDepartment.id IS NULL OR purchasingDepartment.code IN (:...purchasingDepartmentCodes))',
    //     {
    //       purchasingDepartmentCodes: conditions?.purchasingDepartmentCodes,
    //     },
    //   );
    // }

    // if (
    //   !parseScopes(jwtPayload.scopes, [
    //     EBusinessOwnerPermission.CREATE,
    //     EBusinessOwnerPermission.EDIT,
    //   ]) &&
    //   conditions?.businessOwnerCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(businessOwners.id IS NULL OR businessOwners.code IN (:...businessOwnerCodes))',
    //     {
    //       businessOwnerCodes: conditions?.businessOwnerCodes,
    //     },
    //   );
    // }

    return queryBuilder;
  }

  async getMaterialByCode(code: string, id?: string): Promise<MaterialModel> {
    const repository = this.getRepository(MaterialEntity);

    //@TODO: check role
    const query: FindOneOptions<MaterialEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getMaterialDetail(
    conditions: GetDetailMaterialDto,
    jwtPayload,
  ): Promise<MaterialModel> {
    const repository = this.getRepository(MaterialEntity);
    let queryBuilder = repository.createQueryBuilder('materials');
    queryBuilder
      .select([
        'materials.id',
        'materials.code',
        'materials.name',
        'materials.description',
        'materials.unit',
        'materials.createdAt',
      ])
      .innerJoin('materials.materialType', 'materialType')
      .addSelect([
        'materialType.id',
        'materialType.code',
        'materialType.name',
        'materialType.description',
        'materialType.status',
      ])
      .innerJoin('materials.materialGroup', 'materialGroup')
      .addSelect([
        'materialGroup.id',
        'materialGroup.code',
        'materialGroup.name',
        'materialGroup.description',
        'materialGroup.status',
      ])
      .leftJoin('materials.purchasingDepartment', 'purchasingDepartment')
      .addSelect([
        'purchasingDepartment.id',
        'purchasingDepartment.code',
        'purchasingDepartment.name',
        'purchasingDepartment.description',
        'purchasingDepartment.status',
      ])
      .leftJoin('materials.measure', 'measure')
      .addSelect([
        'measure.id',
        'measure.code',
        'measure.name',
        'measure.description',
      ])
      .leftJoin('materials.purchasingGroup', 'purchasingGroup')
      .addSelect([
        'purchasingGroup.id',
        'purchasingGroup.code',
        'purchasingGroup.name',
        'purchasingGroup.description',
        'purchasingGroup.status',
      ]);

    // queryBuilder
    //   .leftJoin('materials.company', 'company')
    //   .addSelect([
    //     'company.id',
    //     'company.name',
    //     'company.code',
    //     'company.description',
    //     'company.status',
    //   ]);
    queryBuilder
      .leftJoin('materials.businessUnits', 'businessUnits')
      .leftJoin('businessUnits.company', 'company')
      .addSelect([
        'businessUnits.id',
        'businessUnits.name',
        'businessUnits.code',
        'businessUnits.description',
        'businessUnits.status',
        'company.id',
        'company.name',
        'company.code',
        'company.description',
        'company.status',
      ]);
    queryBuilder
      .leftJoin('materials.functionUnits', 'functionUnits')
      .addSelect([
        'functionUnits.id',
        'functionUnits.name',
        'functionUnits.code',
        'functionUnits.description',
        'functionUnits.status',
      ]);
    queryBuilder
      .leftJoin('materials.department', 'department')
      .addSelect([
        'department.id',
        'department.name',
        'department.code',
        'department.description',
        'department.status',
      ]);

    queryBuilder
      .leftJoin('materials.profitCenter', 'profitCenter')
      .addSelect([
        'profitCenter.id',
        'profitCenter.code',
        'profitCenter.description',
        'profitCenter.status',
      ]);
    queryBuilder
      .leftJoin('materials.industries', 'industries')
      .addSelect([
        'industries.id',
        'industries.codeSAP',
        'industries.status',
        'industries.sector',
      ]);
    queryBuilder
      .leftJoin('industries.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.code',
        'sector.name',
        'sector.description',
        'sector.status',
      ]);

    if (conditions.id) {
      queryBuilder.where('materials.id = :id', {
        id: conditions.id,
      });
    }

    if (conditions.code) {
      queryBuilder.where('materials.code = :code', {
        code: conditions.code,
      });
    }

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await queryBuilder.getOne();
  }

  async getMaterialByCodesWithRole(
    codes: string[],
    jwtPayload,
    isNeedPermission: boolean = true,
  ): Promise<MaterialModel[]> {
    const repository = this.getRepository(MaterialEntity);
    let queryBuilder = repository.createQueryBuilder('materials');

    queryBuilder
      .leftJoinAndSelect('materials.materialType', 'materialType')
      .leftJoin('materials.measure', 'measure')
      .addSelect([
        'measure.id',
        'measure.code',
        'measure.name',
        'measure.description',
      ]);
    queryBuilder
      .leftJoinAndSelect('materials.materialGroup', 'materialGroup')
      .leftJoin('materialGroup.businessOwners', 'businessOwners');
    queryBuilder.leftJoinAndSelect(
      'materials.purchasingGroup',
      'purchasingGroup',
    );
    queryBuilder.leftJoinAndSelect(
      'materials.purchasingDepartment',
      'purchasingDepartment',
    );

    queryBuilder
      .leftJoin('materials.company', 'company')
      .addSelect([
        'company.id',
        'company.name',
        'company.code',
        'company.description',
        'company.status',
      ]);
    queryBuilder
      .leftJoin('materials.businessUnits', 'businessUnits')
      .addSelect([
        'businessUnits.id',
        'businessUnits.name',
        'businessUnits.code',
        'businessUnits.description',
        'businessUnits.status',
      ]);
    queryBuilder
      .leftJoin('materials.functionUnits', 'functionUnits')
      .addSelect([
        'functionUnits.id',
        'functionUnits.name',
        'functionUnits.code',
        'functionUnits.description',
        'functionUnits.status',
      ]);
    queryBuilder
      .leftJoin('materials.department', 'department')
      .addSelect([
        'department.id',
        'department.name',
        'department.code',
        'department.description',
        'department.status',
      ]);

    queryBuilder
      .leftJoin('materials.profitCenter', 'profitCenter')
      .addSelect([
        'profitCenter.id',
        'profitCenter.code',
        'profitCenter.description',
        'profitCenter.status',
      ]);
    queryBuilder
      .leftJoin('materials.industries', 'industries')
      .addSelect([
        'industries.id',
        'industries.codeSAP',
        'industries.status',
        'industries.sector',
      ]);
    queryBuilder
      .leftJoin('industries.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.code',
        'sector.name',
        'sector.description',
        'sector.status',
      ]);

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetMaterialListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('materials.code IN (:...codes)', {
          codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('materials.code IN (:...codes)', {
    //     codes,
    //   });
    // } else {
    //   return [];
    // }

    queryBuilder.orderBy('materials.createdAt', 'DESC');

    return await queryBuilder.getMany();
  }

  async getListByIds(
    conditions: GetMaterialListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<MaterialModel>> {
    const repository = this.getRepository(MaterialEntity);
    let queryBuilder = repository.createQueryBuilder('materials');

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.where('materials.id IN (:...ids)', {
        ids: conditions.ids,
      });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder.orderBy('materials.createdAt', 'DESC');

    return await this.pagination<MaterialEntity>(queryBuilder, conditions);
  }
}
