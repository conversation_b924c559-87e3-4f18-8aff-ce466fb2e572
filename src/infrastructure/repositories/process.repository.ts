import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, In } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { CreateProcessDto } from '../../controller/process/dtos/create-process.dto';
import { GetProcessListDto } from '../../controller/process/dtos/get-process-list.dto';
import { UpdateProcessDto } from '../../controller/process/dtos/update-process.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { ProcessModel } from '../../domain/model/process.model';
import { IProcessRepository } from '../../domain/repositories/process.repository';
import { codeToIdMap, uidToPath } from '../../utils/common';
import { ConditionDetailEntity } from '../entities/condition-detail.entity';
import { ProcessEntity } from '../entities/process.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ProcessRepository
  extends BaseRepository
  implements IProcessRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createProcess(data: CreateProcessDto): Promise<ProcessModel> {
    const repository = this.getRepository(ProcessEntity);

    const process = repository.create(data);

    await repository.save(process);

    const partialEntity: QueryDeepPartialEntity<ProcessEntity> = {
      path: `${uidToPath(process.id)}`,
    };

    if (data.parentId) {
      const parentProcess = await repository.findOne({
        where: { id: data.parentId },
      });

      if (parentProcess) {
        partialEntity.parentId = parentProcess.id;
        partialEntity.path = `${parentProcess.path || ''}.${uidToPath(process.id)}`;
      }
    }

    await repository.update({ id: process.id }, partialEntity);

    return process;
  }
  async updateProcess(
    id: string,
    data: UpdateProcessDto,
  ): Promise<ProcessModel> {
    const repository = this.getRepository(ProcessEntity);

    const process = repository.create({
      id,
      ...data,
    });

    return await repository.save(process);
  }
  async deleteProcess(ids: string[]): Promise<void> {
    const repository = this.getRepository(ProcessEntity);
    if (ids?.length) await repository.softDelete(ids);
  }
  async getProcessById(id: string): Promise<ProcessModel> {
    const repository = this.getRepository(ProcessEntity);
    const queryBuilder = repository.createQueryBuilder('process');

    queryBuilder
      .leftJoin(
        'process.processConditions',
        'processConditions',
        'processConditions.deleted_at IS NULL',
      )
      .leftJoin(
        'processConditions.condition',
        'condition',
        'condition.deleted_at IS NULL',
      )
      .leftJoin(
        'condition.conditionDetails',
        'conditionDetails',
        'conditionDetails.deleted_at IS NULL',
      )
      .select([
        'process',
        'processConditions.id',
        'condition.id',
        'conditionDetails',
      ]);

    const relatedEntities = [
      'sectors',
      'companies',
      'businessUnits',
      'departments',
      'costCenters',
      'budgetCodes',
      'prTypes',
      'poTypes',
      'processTypes',
      'plants',
      'functionUnits',
    ];

    // Kiểm tra dữ liệu liên quan
    // const relatedDataMap = await Promise.all(
    //   relatedEntities.map(async (entity) => ({
    //     entity,
    //     hasData: await this.hasRelatedData(entity, null, id),
    //   })),
    // );

    // Lọc các entity có dữ liệu liên quan
    // relatedDataMap
    //   .filter((data) => data.hasData)
    //   .forEach(({ entity }) => {
    //     queryBuilder
    //       .leftJoin(`conditionDetails.${entity}`, entity)
    //       .addSelect([`${entity}.id`, `${entity}.name`]);
    //   });

    relatedEntities.forEach((item) => {
      queryBuilder
        .leftJoin(`conditionDetails.${item}`, item)
        .addSelect([`${item}.id`, `${item}.name`]);
    });

    queryBuilder.where('process.id = :id', { id });

    return await queryBuilder.getOne();
  }
  async getProcessList(
    conditions: GetProcessListDto,
  ): Promise<ResponseDto<ProcessModel>> {
    const repository = this.getRepository(ProcessEntity);
    const queryBuilder = repository
      .createQueryBuilder('processes')
      .orderBy('processes.createdAt', 'DESC')
      .andWhere('processes.parent_id IS NULL')
      .select([
        'processes.id',
        'processes.name',
        'processes.status',
        'processes.type',
        'processes.createdAt',
        'processes.description',
      ]);

    if (conditions?.statuses?.length) {
      queryBuilder.andWhere('processes.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions?.types?.length) {
      queryBuilder.andWhere('processes.type IN (:...types)', {
        types: conditions.types,
      });
    }

    return await this.pagination(queryBuilder, conditions);
  }

  ///getDetailProcessGraph OLD
  // async getDetailProcessGraph(id: string): Promise<any> {
  //   const repository = this.getRepository(ProcessEntity);
  //   const queryBuilder = repository.createQueryBuilder('processes');

  //   // Join cơ bản và select dữ liệu từ conditionDetails
  //   queryBuilder
  //     .leftJoin('processes.processConditions', 'processConditions')
  //     .leftJoin('processConditions.condition', 'condition')
  //     .leftJoin('condition.conditionDetails', 'conditionDetails')
  //     .select([
  //       'processes',
  //       'processConditions.id',
  //       'condition.id',
  //       'conditionDetails.id',
  //       'conditionDetails.type',
  //       'conditionDetails.comparisonType',
  //       'conditionDetails.valuePR',
  //       'conditionDetails.valuePO',
  //       'conditionDetails.valueBudget',
  //       'conditionDetails.budgetOverrun',
  //       'conditionDetails.budgetOverrunRate',
  //       'conditionDetails.differenceAmount',
  //       'conditionDetails.differenceAmountAllItems',
  //     ]);

  //   const processPath = `*.${id.replace(/-/g, '')}`;
  //   const relatedEntities = [
  //     'sectors',
  //     'companies',
  //     'businessUnits',
  //     'departments',
  //     'costCenters',
  //     'budgetCodes',
  //     'prTypes',
  //     'poTypes',
  //     'processTypes',
  //     'plants',
  //     'functionUnits',
  //   ];

  //   // Kiểm tra dữ liệu liên quan
  //   // const relatedDataMap = await Promise.all(
  //   //   relatedEntities.map(async (entity) => ({
  //   //     entity,
  //   //     hasData: await this.hasRelatedData(entity, processPath),
  //   //   })),
  //   // );

  //   // Lọc các entity có dữ liệu liên quan
  //   // relatedDataMap
  //   //   .filter((data) => data.hasData)
  //   //   .forEach(({ entity }) => {
  //   //     queryBuilder
  //   //       .leftJoin(`conditionDetails.${entity}`, entity)
  //   //       .addSelect([`${entity}.id`, `${entity}.name`]);
  //   //   });

  //   relatedEntities.forEach((item) => {
  //     queryBuilder
  //       .leftJoin(`conditionDetails.${item}`, item)
  //       .addSelect([`${item}.id`, `${item}.name`]);
  //   });

  //   // Thêm các điều kiện WHERE
  //   queryBuilder.where(
  //     `processes.path <@ (SELECT ps."path" FROM processes ps WHERE ps."path" ~ :path)
  //       AND processes.path != ''
  //       AND processes.deleted_at IS NULL`,
  //     { path: `*.${id.replace(/-/g, '')}` },
  //   );

  //   const result = await queryBuilder.getMany();

  //   // Tính toán nlevel sau khi truy vấn
  //   result?.forEach((item) => {
  //     item.nlevel = item.path.split('.').length;
  //   });

  //   return result;
  // }

  async getDetailProcessGraph(id: string): Promise<any> {
    const repository = this.getRepository(ProcessEntity);
    const queryBuilder = repository.createQueryBuilder('processes');

    // Join cơ bản và select dữ liệu từ conditionDetails
    queryBuilder
      .leftJoin('processes.processConditions', 'processConditions')
      .leftJoin('processConditions.condition', 'condition')
      .leftJoin('condition.conditionDetails', 'conditionDetails')
      .select([
        'processes',
        'processConditions.id',
        'condition.id',
        'conditionDetails.id',
        'conditionDetails.type',
        'conditionDetails.comparisonType',
        'conditionDetails.valuePR',
        'conditionDetails.valuePO',
        'conditionDetails.valueBudget',
        'conditionDetails.budgetOverrun',
        'conditionDetails.budgetOverrunRate',
        'conditionDetails.differenceAmount',
        'conditionDetails.differenceAmountAllItems',
        'conditionDetails.firstBudget',
      ]);

    // Thêm các điều kiện WHERE
    queryBuilder.where(
      `processes.path <@ (SELECT ps."path" FROM processes ps WHERE ps."path" ~ :path)
        AND processes.path != ''
        AND processes.deleted_at IS NULL`,
      { path: `*.${id.replace(/-/g, '')}` },
    );

    const result = await queryBuilder.getMany();

    const conditionDetailIds = [
      ...new Set(
        result
          ?.flatMap((item) => item.processConditions)
          ?.flatMap((item) => item.condition?.conditionDetails)
          ?.map((item) => item.id)
          ?.filter(Boolean) || [],
      ),
    ];

    const conditionDetailsRepository = this.getRepository(
      ConditionDetailEntity,
    );

    const queryConditionDetailBuilder = conditionDetailsRepository
      .createQueryBuilder('conditionDetails')
      .select([`conditionDetails.id`])
      .andWhere('conditionDetails.id IN (:...ids)', {
        ids: conditionDetailIds,
      })
      .groupBy('conditionDetails.id');

    const relatedEntities = {
      sectors: {
        tableJoin: 'sector_conditions',
        column: 'sector_id',
        aliasName: 'sectors',
      },
      companies: {
        tableJoin: 'company_conditions',
        column: 'company_id',
        aliasName: 'companies',
      },
      business_unit: {
        tableJoin: 'business_unit_conditions',
        column: 'business_unit_id',
        aliasName: 'businessUnits',
      },
      department: {
        tableJoin: 'department_conditions',
        column: 'department_id',
        aliasName: 'departments',
      },
      costcenter_subaccount: {
        tableJoin: 'cost_center_conditions',
        column: 'cost_center_id',
        aliasName: 'costCenters',
      },
      budget_code: {
        tableJoin: 'budget_code_conditions',
        column: 'budget_code_id',
        aliasName: 'budgetCodes',
      },
      purchase_request_type: {
        tableJoin: 'purchase_request_type_conditions',
        column: 'purchase_request_type_id',
        aliasName: 'prTypes',
      },
      purchase_order_type: {
        tableJoin: 'purchase_order_type_conditions',
        column: 'purchase_order_type_id',
        aliasName: 'poTypes',
      },
      process_types: {
        tableJoin: 'process_type_conditions',
        column: 'process_type_id',
        aliasName: 'processTypes',
      },
      plants: {
        tableJoin: 'plant_conditions',
        column: 'plant_id',
        aliasName: 'plants',
      },
      function_unit: {
        tableJoin: 'function_unit_conditions',
        column: 'function_unit_id',
        aliasName: 'functionUnits',
      },
    };

    ///subQuery lấy data conditions
    Object.keys(relatedEntities).forEach((key) => {
      const aliasName = relatedEntities[key].aliasName;
      const tableJoin = relatedEntities[key].tableJoin;
      const columnJoin = relatedEntities[key].column;

      const subQuery = queryBuilder
        .subQuery()
        .select(
          `COALESCE(json_agg(json_build_object('id', ${aliasName}.id, 'name', ${aliasName}.name)), '[]') AS ${aliasName}`,
        )
        .from(key, aliasName)
        .where(
          `EXISTS (
          SELECT 1 FROM ${tableJoin}
          WHERE ${tableJoin}.condition_id = "conditionDetails".id
          AND ${aliasName}.id = ${tableJoin}.${columnJoin}
        )`,
        )
        .getQuery();

      queryConditionDetailBuilder.addSelect(`(${subQuery})`, aliasName);
    });

    const conditionDetails = await queryConditionDetailBuilder.getRawMany();

    const dataConditionDetails = codeToIdMap(
      conditionDetails,
      'conditionDetails_id',
      [
        ...Object.keys(relatedEntities)?.map(
          (item) => relatedEntities[item].aliasName,
        ),
      ],
    );

    // Tính toán nlevel sau khi truy vấn và lấy data conditions
    result?.forEach((item) => {
      item.nlevel = item.path.split('.').length;
      item.processConditions = item.processConditions.map((item) => {
        return {
          ...item,
          condition: {
            ...item.condition,
            conditionDetails: item.condition?.conditionDetails.map((data) => {
              return {
                ...data,
                ...dataConditionDetails[data.id],
              };
            }),
          },
        };
      }) as any;
    });

    return result;
  }

  // Hàm kiểm tra tồn tại dữ liệu liên quan
  private async hasRelatedData(
    relation: string,
    processPath?: string,
    processId?: string,
  ): Promise<boolean> {
    const conditionDetailsRepository = this.getRepository(
      ConditionDetailEntity,
    );

    const queryBuilder = conditionDetailsRepository
      .createQueryBuilder('conditionDetails')
      .select([`DISTINCT(${relation}.id)`, `${relation}.name`])
      .leftJoin(`conditionDetails.${relation}`, relation)
      .leftJoin('conditionDetails.condition', 'condition')
      .leftJoin('condition.processCondition', 'processCondition')
      .leftJoin('processCondition.process', 'process')
      .andWhere('process.deleted_at IS NULL');

    if (processId) {
      queryBuilder.andWhere('process.id = :processId', { processId });
    } else if (processPath) {
      queryBuilder.andWhere(
        `process.path <@ (SELECT ps."path" FROM processes ps WHERE ps."path" ~ :path)`,
        { path: processPath },
      );
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }

  async getProcessByIds(ids: string[]): Promise<ProcessModel[]> {
    const repository = this.getRepository(ProcessEntity);

    return await repository.find({ where: { id: In(ids) } });
  }

  async getDetailProcessById(id: string): Promise<ProcessModel> {
    const repository = this.getRepository(ProcessEntity);
    return await repository.findOne({ where: { id: id } });
  }

  async getDetailParentProcess(id: string): Promise<ProcessModel> {
    const repository = this.getRepository(ProcessEntity);
    const queryBuilder = repository
      .createQueryBuilder('process')
      .andWhere('process.id = :id', { id: id })
      .select([
        'process.id',
        'process.name',
        'process.status',
        'process.createdAt',
        'process.description',
        'processes.id',
        'processes.name',
        'parentApprovalWorkflows.id',
        'parentApprovalWorkflows.sendEmailToCreator',
        'parentApprovalWorkflows.name',
        'staffApprovalWorkflows.id',
        'staffApprovalWorkflows.name',
        'staffApprovalWorkflows.level',
        'staffApprovalWorkflows.receiveEmail',
        'staffApprovalWorkflows.approver',
        'staffApprovalWorkflows.returnRule',
        'staffApprovalWorkflows.accountantApproved',
        'staffApprovalWorkflows.allowSelect',
        'staffApprovalWorkflows.allowEdit',
        'staff.id',
        'staff.code',
        'staff.firstName',
        'staff.lastName',
        'position.id',
        'position.name',
        'position.code',
        'selectedStaffs.id',
        'selectedStaffs.code',
        'selectedStaffs.firstName',
        'selectedStaffs.lastName',
        'selectedStaffs.email',
      ]);

    queryBuilder
      .leftJoin('process.parentApprovalWorkflows', 'parentApprovalWorkflows')
      .leftJoin('parentApprovalWorkflows.processes', 'processes')
      .leftJoin(
        'parentApprovalWorkflows.staffApprovalWorkflows',
        'staffApprovalWorkflows',
      )
      .leftJoin('staffApprovalWorkflows.staff', 'staff')
      .leftJoin('staffApprovalWorkflows.position', 'position')
      .leftJoin('staffApprovalWorkflows.selectedStaffs', 'selectedStaffs');

    return await queryBuilder.getOne();
  }

  // async getAllNodeForCheck(parentId: string): Promise<ProcessModel[]> {
  //   const repository = this.getRepository(ProcessEntity);

  //   // const result = await repository.query(
  //   //   `WITH
  //   //     SELECT id, nlevel(path) AS "nlevel", name, description, path, created_at as "createdAt", parent_id as "parentId"
  //   //                         FROM processes p
  //   //                         WHERE path <@ (SELECT ps."path"
  //   //                                         FROM processes ps
  //   //                                         WHERE ps."path" ~ $1) AND path != '' AND p.deleted_at IS NULL
  //   //       ORDER BY nlevel(path) DESC
  //   //          `,
  //   //   ['*.' + id.replace(/-/g, '')],
  //   // );
  //   return;
  // }
}
