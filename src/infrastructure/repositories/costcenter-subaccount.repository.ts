import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetCostcenterSubaccountListDto } from '../../controller/costcenter-subaccount/dtos/get-costcenter-subaccount-list.dto';
import { GetDetailCostcenterSubaccountDto } from '../../controller/costcenter-subaccount/dtos/get-detail-costcenter-subaccount.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { CostcenterSubaccountModel } from '../../domain/model/costcenter-subaccount.model';
import { ICostcenterSubaccountRepository } from '../../domain/repositories/costcenter-subaccount.repository';
import { parseScopes } from '../../utils/common';
import { ESectorPermission } from '../../utils/constants/permission.enum';
import { CostcenterSubaccountEntity } from '../entities/costcenter-subaccount.entity';
import { BaseRepository } from './base.repository';
import { GetCostCenterListByIdsDto } from '../../controller/costcenter-subaccount/dtos/get-cost-center-list-by-ids.dto';

@Injectable({ scope: Scope.REQUEST })
export class CostcenterSubaccountRepository
  extends BaseRepository
  implements ICostcenterSubaccountRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createCostcenterSubaccount(
    data: CostcenterSubaccountModel,
  ): Promise<CostcenterSubaccountModel> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    const newCostcenterSubaccount = repository.create(data);
    return await repository.save(newCostcenterSubaccount);
  }

  async updateCostcenterSubaccount(
    data: CostcenterSubaccountModel,
  ): Promise<CostcenterSubaccountModel> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    const updateCostcenterSubaccount = repository.create(data);
    return await repository.save(updateCostcenterSubaccount);
  }

  async getCostcenterSubaccounts(
    conditions: GetCostcenterSubaccountListDto,
    jwtPayload,
  ): Promise<ResponseDto<CostcenterSubaccountModel>> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    let queryBuilder = repository.createQueryBuilder('costcenterSubaccounts');

    queryBuilder.leftJoinAndSelect('costcenterSubaccounts.sector', 'sector');
    // queryBuilder.leftJoinAndSelect('costcenterSubaccounts.company', 'company');
    // queryBuilder.leftJoinAndSelect(
    //   'costcenterSubaccounts.businessUnit',
    //   'businessUnit',
    // );
    // queryBuilder.leftJoinAndSelect(
    //   'costcenterSubaccounts.department',
    //   'department',
    // );

    queryBuilder
      .leftJoin('costcenterSubaccounts.functionUnits', 'functionUnits')
      .addSelect([
        'functionUnits.id',
        'functionUnits.name',
        'functionUnits.code',
        'functionUnits.description',
        'functionUnits.status',
      ]);

    if (conditions.statuses) {
      queryBuilder.where('costcenterSubaccounts.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.sectorIds) {
      queryBuilder.andWhere(
        'costcenterSubaccounts.sectorId IN (:...sectorIds)',
        {
          sectorIds: conditions.sectorIds,
        },
      );
    }

    // if (conditions.companyIds) {
    //   queryBuilder.andWhere(
    //     'costcenterSubaccounts.companyId IN (:...companyIds)',
    //     {
    //       companyIds: conditions.companyIds,
    //     },
    //   );
    // }

    // if (conditions.businessUnitIds) {
    //   queryBuilder.andWhere(
    //     'costcenterSubaccounts.businessUnitId IN (:...businessUnitIds)',
    //     {
    //       businessUnitIds: conditions.businessUnitIds,
    //     },
    //   );
    // }

    // if (conditions.departmentIds) {
    //   queryBuilder.andWhere(
    //     'costcenterSubaccounts.departmentId IN (:...departmentIds)',
    //     {
    //       departmentIds: conditions.departmentIds,
    //     },
    //   );
    // }

    if (conditions.from && conditions.to) {
      queryBuilder.andWhere(
        'costcenterSubaccounts.effective_start_date BETWEEN :from AND :to',
        {
          from: conditions.from,
          to: conditions.to,
        },
      );
    } else if (conditions.from) {
      queryBuilder.andWhere(
        'costcenterSubaccounts.effective_start_date >= :from',
        {
          from: conditions.from,
        },
      );
    } else if (conditions.to) {
      queryBuilder.andWhere(
        'costcenterSubaccounts.effective_start_date <= : to',
        {
          to: conditions.to,
        },
      );
    }

    if (conditions.createdAt) {
      queryBuilder.andWhere(
        '(costcenterSubaccounts.effective_end_date IS NOT NULL && costcenterSubaccounts.effective_start_date <= :createdAt && costcenterSubaccounts.effective_end_date >= :createdAt) OR (costcenterSubaccounts.effective_end_date IS NULL && costcenterSubaccounts.effective_start_date <= :createdAt)',
        {
          createdAt: conditions.createdAt,
        },
      );
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('costcenterSubaccounts.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async deleteCostcenterSubaccount(id: string): Promise<void> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    await repository.delete(id);
  }

  async getCostcenterSubaccountById(
    id: string,
  ): Promise<CostcenterSubaccountModel> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    return await repository.findOne({
      where: { id: id },
      relations: [
        'sector',
        // 'company', 'businessUnit', 'department'
      ],
    });
  }

  async getCostcenterSubaccountByCode(
    code: string,
    id?: string,
  ): Promise<CostcenterSubaccountModel> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    const query: FindOneOptions<CostcenterSubaccountEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getHistory(id: string): Promise<CostcenterSubaccountModel> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    return await repository.findOne({
      where: { id: id },
      relations: ['histories'],
    });
  }

  async getCostcenterSubaccountDetail(
    conditions: GetDetailCostcenterSubaccountDto,
    jwtPayload,
  ): Promise<CostcenterSubaccountModel> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    let queryBuilder = repository
      .createQueryBuilder('costcenterSubaccounts')
      .select([
        'costcenterSubaccounts.id',
        'costcenterSubaccounts.name',
        'costcenterSubaccounts.code',
        'costcenterSubaccounts.description',
        'costcenterSubaccounts.status',
        'costcenterSubaccounts.sectorId',
        'costcenterSubaccounts.note1',
        'costcenterSubaccounts.note2',
        'costcenterSubaccounts.note3',
        'costcenterSubaccounts.note4',
        'costcenterSubaccounts.note5',
        'costcenterSubaccounts.note6',
        'costcenterSubaccounts.createdAt',
        'costcenterSubaccounts.effectiveStartDate',
        'costcenterSubaccounts.effectiveEndDate',
        // 'costcenterSubaccounts.companyId',
        // 'costcenterSubaccounts.businessUnitId',
        // 'costcenterSubaccounts.departmentId',
      ]);
    queryBuilder
      .leftJoin('costcenterSubaccounts.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.name',
        'sector.code',
        'sector.description',
        'sector.status',
      ]);
    // .leftJoinAndSelect('costcenterSubaccounts.company', 'company')
    // .leftJoinAndSelect('costcenterSubaccounts.businessUnit', 'businessUnit')
    // .leftJoinAndSelect('costcenterSubaccounts.department', 'department');
    queryBuilder
      .leftJoin('costcenterSubaccounts.functionUnits', 'functionUnits')
      .addSelect([
        'functionUnits.id',
        'functionUnits.name',
        'functionUnits.code',
        'functionUnits.description',
        'functionUnits.status',
      ]);

    queryBuilder.where('costcenterSubaccounts.id = :id', {
      id: conditions.id,
    });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  async getCostcenterSubaccountsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<CostcenterSubaccountModel[]> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    let queryBuilder = repository.createQueryBuilder('costcenterSubaccounts');
    queryBuilder.leftJoinAndSelect('costcenterSubaccounts.sector', 'sector');
    // .leftJoinAndSelect('costcenterSubaccounts.company', 'company')
    // .leftJoinAndSelect('costcenterSubaccounts.businessUnit', 'businessUnit')
    // .leftJoinAndSelect('costcenterSubaccounts.department', 'department');
    queryBuilder
      .leftJoin('costcenterSubaccounts.functionUnits', 'functionUnits')
      .addSelect([
        'functionUnits.id',
        'functionUnits.name',
        'functionUnits.code',
        'functionUnits.description',
        'functionUnits.status',
      ]);

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetCostcenterSubaccountListDto(),
      );
    }

    if (codes?.length) {
      queryBuilder.where('costcenterSubaccounts.code IN (:...codes)', {
        codes: codes,
      });
    } else {
      return [];
    }
    // if (codes?.length) {
    //   queryBuilder.where('costcenterSubaccounts.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<CostcenterSubaccountEntity>,
    jwtPayload: any,
    conditions:
      | GetCostcenterSubaccountListDto
      | GetDetailCostcenterSubaccountDto,
  ) {
    conditions.functionUnitCodes = jwtPayload?.functionUnits;

    if (!jwtPayload.isSuperAdmin) {
      if (conditions.functionUnitCodes?.length) {
        queryBuilder.andWhere(
          '(functionUnits.id IS NULL OR functionUnits.code IN (:...functionUnitCodes))',
          {
            functionUnitCodes: conditions.functionUnitCodes,
          },
        );
      }
    }

    // conditions.sectorCodes = jwtPayload?.sectors;
    // conditions.companyCodes = jwtPayload?.companies;
    // conditions.businessUnitCodes = jwtPayload?.businessUnits;
    // conditions.departmentCodes = jwtPayload?.departments;

    // if (!jwtPayload?.isSuperAdmin) {
    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     ESectorPermission.CREATE,
    //     ESectorPermission.EDIT,
    //   ]) &&
    //   conditions?.sectorCodes?.length
    // ) {
    //   queryBuilder.andWhere(
    //     '(sector.id IS NULL OR sector.code IN (:...sectorCodes))',
    //     {
    //       sectorCodes: conditions?.sectorCodes,
    //     },
    //   );
    // }
    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     ECompanyPermission.CREATE,
    //     ECompanyPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('company.code IN (:...companyCodes)', {
    //     companyCodes: conditions?.companyCodes?.length
    //       ? conditions?.companyCodes
    //       : [null],
    //   });
    // }
    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     EBusinessUnitPermission.CREATE,
    //     EBusinessUnitPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('businessUnit.code IN (:...businessUnitCodes)', {
    //     businessUnitCodes: conditions?.businessUnitCodes?.length
    //       ? conditions?.businessUnitCodes
    //       : [null],
    //   });
    // }
    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     EDepartmentPermission.CREATE,
    //     EDepartmentPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('department.code IN (:...departmentCodes)', {
    //     departmentCodes: conditions?.departmentCodes?.length
    //       ? conditions?.departmentCodes
    //       : [null],
    //   });
    // }
    // }

    return queryBuilder;
  }

  async getCostCenterByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<CostcenterSubaccountModel[]> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    let queryBuilder = repository.createQueryBuilder('CostCenters');

    if (ids.length) {
      queryBuilder.where('CostCenters.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetCostcenterSubaccountListDto(),
    );

    return await queryBuilder.getMany();
  }

  async checkDuplicateCostCenter(
    code: string,
    effectiveStartDate: Date,
    effectiveEndDate?: Date,
  ): Promise<CostcenterSubaccountModel> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    let queryBuilder = repository.createQueryBuilder('costCenters');

    queryBuilder.andWhere('costCenters.code = :code', { code: code });
    if (effectiveEndDate) {
      queryBuilder.andWhere(
        '((costCenters.effectiveEndDate IS NOT NULL AND costCenters.effectiveStartDate <= :endDate AND costCenters.effectiveEndDate >= :startDate) OR (costCenters.effectiveEndDate IS NULL AND costCenters.effectiveStartDate BETWEEN :startDate AND :endDate))',
        {
          startDate: effectiveStartDate,
          endDate: effectiveEndDate,
        },
      );
    } else {
      queryBuilder.andWhere(
        '(((costCenters.effectiveStartDate <= :startDate OR costCenters.effectiveStartDate >= :startDate) AND costCenters.effectiveEndDate IS NULL) OR (costCenters.effectiveEndDate IS NOT NULL AND costCenters.effectiveStartDate <= :startDate AND costCenters.effectiveEndDate >= :startDate))',
        {
          startDate: effectiveStartDate,
        },
      );
    }

    return await queryBuilder.getOne();
  }

  async getListByIds(
    conditions: GetCostCenterListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<CostcenterSubaccountModel>> {
    const repository = this.getRepository(CostcenterSubaccountEntity);
    let queryBuilder = repository.createQueryBuilder('costcenterSubaccounts');

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.where('costcenterSubaccounts.id IN (:...ids)', {
        ids: conditions.ids,
      });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder.orderBy('costcenterSubaccounts.createdAt', 'DESC');

    return await this.pagination<CostcenterSubaccountEntity>(
      queryBuilder,
      conditions,
    );
  }
}
