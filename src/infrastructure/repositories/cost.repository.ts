import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { ICostRepository } from '../../domain/repositories/cost.repository';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { CostModel } from '../../domain/model/cost.model';
import { CostEntity } from '../entities/cost.entity';
import { UpdateCostDto } from '../../controller/cost/dtos/update-cost.dto';
import { GetCostListDto } from '../../controller/cost/dtos/get-cost-list.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { GetDetailCostDto } from '../../controller/cost/dtos/get-detail-cost.dto';
import { ECostPermission } from '../../utils/constants/permission.enum';
import { parseScopes } from '../../utils/common';

@Injectable({ scope: Scope.REQUEST })
export class CostRepository extends BaseRepository implements ICostRepository {
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createCost(data: CostModel): Promise<CostModel> {
    const repository = this.getRepository(CostEntity);

    const cost = repository.create(data);

    return await repository.save(cost);
  }

  async updateCost(id, updateCostDto: UpdateCostDto): Promise<CostModel> {
    const repository = this.getRepository(CostEntity);
    const cost = repository.create({ id, ...updateCostDto });
    return await repository.save(cost);
  }

  async deleteCost(id: string): Promise<void> {
    const repository = this.getRepository(CostEntity);
    await repository.delete(id);
  }

  async getCosts(
    conditions: GetCostListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<CostModel>> {
    const repository = this.getRepository(CostEntity);
    let queryBuilder = repository.createQueryBuilder('costs');

    if (conditions.statuses) {
      queryBuilder.andWhere('costs.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('costs.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('costs.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getDetailCost(
    conditions: GetDetailCostDto,
    jwtPayload: any,
  ): Promise<CostModel> {
    const repository = this.getRepository(CostEntity);
    let queryBuilder = repository.createQueryBuilder('costs');

    queryBuilder.where('costs.id = :id', {
      id: conditions.id,
    });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<CostEntity>,
    jwtPayload: any,
    conditions: GetCostListDto | GetDetailCostDto,
  ) {
    conditions.codes = jwtPayload?.costs; // Data role
    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        ECostPermission.CREATE,
        ECostPermission.EDIT,
      ]) &&
      conditions.codes?.length
    ) {
      queryBuilder.andWhere('(costs.id IS NULL OR costs.code IN (:...codes))', {
        codes: conditions.codes,
      });
    }

    return queryBuilder;
  }

  async getCostByCode(code: string, id?: string): Promise<CostModel> {
    const repository = this.getRepository(CostEntity);

    const query: FindOneOptions<CostEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getCostsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<CostModel[]> {
    const repository = this.getRepository(CostEntity);
    let queryBuilder = repository.createQueryBuilder('costs');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetCostListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('costs.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('costs.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }
}
