import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import {
  DataSource,
  FindOneOptions,
  In,
  Not,
  SelectQueryBuilder,
} from 'typeorm';
import { GetDetailPurchasingDepartmentDto } from '../../controller/purchasing-department/dtos/get-detail-purchasing-department.dto';
import { GetPurchasingDepartmentListDto } from '../../controller/purchasing-department/dtos/get-purchasing-department-list.dto';
import { UpdatePurchasingDepartmentDto } from '../../controller/purchasing-department/dtos/update-purchasing-department.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PurchasingDepartmentModel } from '../../domain/model/purchasing-department.model';
import { IPurchasingDepartmentRepository } from '../../domain/repositories/purchasing-department.repository';
import { parseScopes } from '../../utils/common';
import {
  EPurchasingDepartmentPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { PurchasingDepartmentEntity } from '../entities/purchasing-department.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class PurchasingDepartmentRepository
  extends BaseRepository
  implements IPurchasingDepartmentRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createPurchasingDepartment(
    data: PurchasingDepartmentModel,
  ): Promise<PurchasingDepartmentModel> {
    const repository = this.getRepository(PurchasingDepartmentEntity);

    const purchasingDepartment = repository.create(data);

    return await repository.save(purchasingDepartment);
  }
  async updatePurchasingDepartment(
    id: string,
    updatePurchasingDepartmentDto: UpdatePurchasingDepartmentDto,
  ): Promise<PurchasingDepartmentModel> {
    const repository = this.getRepository(PurchasingDepartmentEntity);
    const purchasingDepartment = repository.create({
      id,
      ...updatePurchasingDepartmentDto,
    });
    return await repository.save(purchasingDepartment);
  }
  async deletePurchasingDepartment(id: string): Promise<void> {
    const repository = this.getRepository(PurchasingDepartmentEntity);
    await repository.delete(id);
  }

  // async getPurchasingDepartmentById(
  //   id: string,
  // ): Promise<PurchasingDepartmentModel> {
  //   const repository = this.getRepository(PurchasingDepartmentEntity);
  //   const queryBuilder = repository.createQueryBuilder('purchasingDepartments');

  //   queryBuilder.where('purchasingDepartments.id = :id', { id });
  //   queryBuilder.leftJoinAndSelect('purchasingDepartments.sector', 'sector');

  //   return await queryBuilder.getOne();
  // }

  async getPurchasingDepartments(
    conditions: GetPurchasingDepartmentListDto,
    jwtPayload,
  ): Promise<ResponseDto<PurchasingDepartmentModel>> {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.codes = jwtPayload?.purchasingDepartments;

    const repository = this.getRepository(PurchasingDepartmentEntity);
    let queryBuilder = repository.createQueryBuilder('purchasingDepartments');

    queryBuilder.leftJoinAndSelect('purchasingDepartments.sector', 'sector');

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('purchasingDepartments.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.statuses) {
      queryBuilder.andWhere('purchasingDepartments.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.sectorIds) {
      queryBuilder.andWhere(
        'purchasingDepartments.sectorId IN (:...sectorIds)',
        {
          sectorIds: conditions.sectorIds,
        },
      );
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    queryBuilder.orderBy('purchasingDepartments.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getPurchasingDepartmentByCode(
    code: string,
    id?: string,
  ): Promise<PurchasingDepartmentModel> {
    const repository = this.getRepository(PurchasingDepartmentEntity);

    const query: FindOneOptions<PurchasingDepartmentEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getPurchasingDepartmentDetail(
    conditions: GetDetailPurchasingDepartmentDto,
    jwtPayload,
  ): Promise<PurchasingDepartmentModel> {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.codes = jwtPayload?.purchasingDepartments;

    const repository = this.getRepository(PurchasingDepartmentEntity);
    let queryBuilder = repository
      .createQueryBuilder('purchasingDepartments')
      .select([
        'purchasingDepartments.id',
        'purchasingDepartments.code',
        'purchasingDepartments.name',
        'purchasingDepartments.description',
        'purchasingDepartments.status',
        'purchasingDepartments.sectorId',
      ]);

    queryBuilder
      .leftJoin('purchasingDepartments.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.name',
        'sector.code',
        'sector.description',
        'sector.status',
      ]);

    if (conditions.id) {
      queryBuilder.where('purchasingDepartments.id = :id', {
        id: conditions.id,
      });
    }

    if (conditions.code) {
      queryBuilder.where('purchasingDepartments.code = :code', {
        code: conditions.code,
      });
    }

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<PurchasingDepartmentEntity>,
    jwtPayload: any,
    conditions:
      | GetPurchasingDepartmentListDto
      | GetDetailPurchasingDepartmentDto,
  ) {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.codes = jwtPayload?.purchasingDepartments;
    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     EPurchasingDepartmentPermission.CREATE,
    //     EPurchasingDepartmentPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('purchasingDepartments.code IN (:...codes)', {
    //     codes: conditions?.codes?.length ? conditions.codes : [null],
    //   });
    // }

    if (
      !parseScopes(jwtPayload?.scopes, [
        ESectorPermission.CREATE,
        ESectorPermission.EDIT,
      ]) &&
      conditions?.sectorCodes?.length
    ) {
      queryBuilder.andWhere(
        '(sector.id IS NULL OR sector.code IN (:...sectorCodes))',
        {
          sectorCodes: conditions?.sectorCodes,
        },
      );
    }

    return queryBuilder;
  }

  async getPurchasingDepartmentByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<PurchasingDepartmentModel[]> {
    const repository = this.getRepository(PurchasingDepartmentEntity);
    let queryBuilder = repository
      .createQueryBuilder('purchasingDepartments')
      .select([
        'purchasingDepartments.id',
        'purchasingDepartments.code',
        'purchasingDepartments.name',
        'purchasingDepartments.description',
        'purchasingDepartments.status',
        'purchasingDepartments.sectorId',
      ]);

    queryBuilder
      .leftJoin('purchasingDepartments.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.name',
        'sector.code',
        'sector.description',
        'sector.status',
      ]);

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetPurchasingDepartmentListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('purchasingDepartments.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }

    return await queryBuilder.getMany();
  }
}
