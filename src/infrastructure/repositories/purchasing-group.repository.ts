import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { GetDetailPurchasingGroupDto } from '../../controller/purchasing-group/dtos/get-detail-purchasing-group.dto';
import { GetPurchasingGroupListDto } from '../../controller/purchasing-group/dtos/get-purchasing-group-list.dto';
import { UpdatePurchasingGroupDto } from '../../controller/purchasing-group/dtos/update-purchasing-group.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PurchasingGroupModel } from '../../domain/model/purchasing-group.model';
import { IPurchasingGroupRepository } from '../../domain/repositories/purchasing-group.repository';
import { parseScopes } from '../../utils/common';
import {
  EPurchasingGroupPermission,
  ESectorPermission,
} from '../../utils/constants/permission.enum';
import { PurchasingGroupEntity } from '../entities/purchasing-group.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class PurchasingGroupRepository
  extends BaseRepository
  implements IPurchasingGroupRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createPurchasingGroup(
    data: PurchasingGroupModel,
  ): Promise<PurchasingGroupModel> {
    const repository = this.getRepository(PurchasingGroupEntity);

    const purchasingGroup = repository.create(data);

    return await repository.save(purchasingGroup);
  }
  async updatePurchasingGroup(
    id: string,
    updatePurchasingGroupDto: UpdatePurchasingGroupDto,
  ): Promise<PurchasingGroupModel> {
    const repository = this.getRepository(PurchasingGroupEntity);
    const purchasingGroup = repository.create({
      id,
      ...updatePurchasingGroupDto,
    });
    return await repository.save(purchasingGroup);
  }
  async deletePurchasingGroup(id: string): Promise<void> {
    const repository = this.getRepository(PurchasingGroupEntity);
    await repository.delete(id);
  }

  // async getPurchasingGroupById(id: string): Promise<PurchasingGroupModel> {
  //   const repository = this.getRepository(PurchasingGroupEntity);
  //   const queryBuilder = repository.createQueryBuilder('purchasingGroups');

  //   queryBuilder.where('purchasingGroups.id = :id', { id });
  //   queryBuilder.leftJoinAndSelect('purchasingGroups.sector', 'sector');

  //   return await queryBuilder.getOne();
  // }

  async getPurchasingGroups(
    conditions: GetPurchasingGroupListDto,
    jwtPayload,
  ): Promise<ResponseDto<PurchasingGroupModel>> {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.codes = jwtPayload?.purchasingGroups;

    const repository = this.getRepository(PurchasingGroupEntity);
    let queryBuilder = repository.createQueryBuilder('purchasingGroups');

    queryBuilder.leftJoinAndSelect('purchasingGroups.sector', 'sector');

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('purchasingGroups.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.statuses) {
      queryBuilder.andWhere('purchasingGroups.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.sectorIds) {
      queryBuilder.andWhere('purchasingGroups.sectorId IN (:...sectorIds)', {
        sectorIds: conditions.sectorIds,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    queryBuilder.orderBy('purchasingGroups.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getPurchasingGroupByCode(
    code: string,
    jwtPayload: any,
    id?: string,
  ): Promise<PurchasingGroupModel> {
    const repository = this.getRepository(PurchasingGroupEntity);

    let queryBuilder = repository
      .createQueryBuilder('purchasingGroups')
      .leftJoinAndSelect('purchasingGroups.sector', 'sector')
      .andWhere('purchasingGroups.code = :code', { code });

    if (id) {
      queryBuilder.andWhere('purchasingGroups.id != :id', { id });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, {
        codes: jwtPayload?.purchasingGroups,
        sectorCodes: jwtPayload?.sectors,
      } as GetPurchasingGroupListDto);
    }
    return await queryBuilder.getOne();
  }

  async getPurchasingGroupDetail(
    conditions: GetDetailPurchasingGroupDto,
    jwtPayload,
  ): Promise<PurchasingGroupModel> {
    conditions.sectorCodes = jwtPayload?.sectors;
    conditions.codes = jwtPayload?.purchasingGroups;

    const repository = this.getRepository(PurchasingGroupEntity);
    let queryBuilder = repository
      .createQueryBuilder('purchasingGroups')
      .select([
        'purchasingGroups.id',
        'purchasingGroups.code',
        'purchasingGroups.name',
        'purchasingGroups.description',
        'purchasingGroups.status',
        'purchasingGroups.sectorId',
        'purchasingGroups.createdAt',
      ]);

    queryBuilder
      .leftJoin('purchasingGroups.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.name',
        'sector.code',
        'sector.description',
        'sector.status',
      ]);

    if (conditions.id) {
      queryBuilder.where('purchasingGroups.id = :id', { id: conditions.id });
    }

    if (conditions.code) {
      queryBuilder.where('purchasingGroups.code = :code', {
        code: conditions.code,
      });
    }

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<PurchasingGroupEntity>,
    jwtPayload: any,
    conditions: GetPurchasingGroupListDto | GetDetailPurchasingGroupDto,
  ) {
    // if (
    //   !parseScopes(jwtPayload?.scopes, [
    //     EPurchasingGroupPermission.CREATE,
    //     EPurchasingGroupPermission.EDIT,
    //   ])
    // ) {
    //   queryBuilder.andWhere('purchasingGroups.code IN (:...codes)', {
    //     codes: conditions?.codes?.length ? conditions.codes : [null],
    //   });
    // }

    if (
      !parseScopes(jwtPayload?.scopes, [
        ESectorPermission.CREATE,
        ESectorPermission.EDIT,
      ]) &&
      conditions?.sectorCodes?.length
    ) {
      queryBuilder.andWhere(
        '(sector.id IS NULL OR sector.code IN (:...sectorCodes))',
        {
          sectorCodes: conditions?.sectorCodes,
        },
      );
    }

    return queryBuilder;
  }

  async getPurchasingGroupByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<PurchasingGroupModel[]> {
    const repository = this.getRepository(PurchasingGroupEntity);
    let queryBuilder = repository.createQueryBuilder('purchasingGroups');

    queryBuilder.leftJoinAndSelect('purchasingGroups.sector', 'sector');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetPurchasingGroupListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('purchasingGroups.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('purchasingGroups.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }
}
