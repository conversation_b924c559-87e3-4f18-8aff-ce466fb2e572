import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetDetailMaterialTypeDto } from '../../controller/material-type/dtos/get-detail-material-type.dto';
import { GetMaterialTypeListDto } from '../../controller/material-type/dtos/get-material-type-list.dto';
import { UpdateMaterialTypeDto } from '../../controller/material-type/dtos/update-material-type.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { MaterialTypeModel } from '../../domain/model/material-type.model';
import { IMaterialTypeRepository } from '../../domain/repositories/material-type.repository';
import { parseScopes } from '../../utils/common';
import { EMaterialTypePermission } from '../../utils/constants/permission.enum';
import { MaterialTypeEntity } from '../entities/material-type.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class MaterialTypeRepository
  extends BaseRepository
  implements IMaterialTypeRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createMaterialType(
    data: MaterialTypeModel,
  ): Promise<MaterialTypeModel> {
    const repository = this.getRepository(MaterialTypeEntity);

    const materialType = repository.create(data);

    return await repository.save(materialType);
  }
  async updateMaterialType(
    id: string,
    updateMaterialTypeDto: UpdateMaterialTypeDto,
  ): Promise<MaterialTypeModel> {
    const repository = this.getRepository(MaterialTypeEntity);
    const materialType = repository.create({ id, ...updateMaterialTypeDto });
    return await repository.save(materialType);
  }
  async deleteMaterialType(id: string): Promise<void> {
    const repository = this.getRepository(MaterialTypeEntity);
    await repository.delete(id);
  }

  // async getMaterialTypeById(id: string): Promise<MaterialTypeModel> {
  //   const repository = this.getRepository(MaterialTypeEntity);
  //   return await repository.findOneBy({ id: id });
  // }

  async getMaterialTypes(
    conditions: GetMaterialTypeListDto,
    jwtPayload,
  ): Promise<ResponseDto<MaterialTypeModel>> {
    const repository = this.getRepository(MaterialTypeEntity);
    let queryBuilder = repository.createQueryBuilder('materialTypes');

    if (conditions.statuses) {
      queryBuilder.andWhere('materialTypes.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    queryBuilder.orderBy('materialTypes.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getMaterialTypeByCode(
    code: string,
    id?: string,
  ): Promise<MaterialTypeModel> {
    const repository = this.getRepository(MaterialTypeEntity);

    const query: FindOneOptions<MaterialTypeEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getMaterialTypeDetail(
    conditions: GetDetailMaterialTypeDto,
    jwtPayload: any,
  ): Promise<MaterialTypeModel> {
    const repository = this.getRepository(MaterialTypeEntity);
    let queryBuilder = repository.createQueryBuilder('materialTypes');

    if (conditions.id) {
      queryBuilder.andWhere('materialTypes.id = :id', { id: conditions.id });
    }

    if (conditions.code) {
      queryBuilder.andWhere('materialTypes.code = :code', {
        code: conditions.code,
      });
    }

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<MaterialTypeEntity>,
    jwtPayload: any,
    conditions: GetMaterialTypeListDto | GetDetailMaterialTypeDto,
  ) {
    conditions.codes = jwtPayload?.materialTypes;
    if (
      !parseScopes(jwtPayload?.scopes, [
        EMaterialTypePermission.CREATE,
        EMaterialTypePermission.EDIT,
      ]) &&
      conditions?.codes?.length
    ) {
      queryBuilder.andWhere(
        '(materialTypes.id IS NULL OR materialTypes.code IN (:...codes))',
        {
          codes: conditions?.codes?.length ? conditions.codes : [null],
        },
      );
    }

    return queryBuilder;
  }

  async getMaterialTypeByCodesWithRole(
    codes: string[],
    jwtPayload,
    isNeedPermission: boolean = true,
  ): Promise<MaterialTypeModel[]> {
    const repository = this.getRepository(MaterialTypeEntity);
    let queryBuilder = repository.createQueryBuilder('materialTypes');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetMaterialTypeListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('materialTypes.code IN (:...codes)', {
          codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('materialTypes.code IN (:...codes)', {
    //     codes,
    //   });
    // } else {
    //   return [];
    // }

    queryBuilder.orderBy('materialTypes.createdAt', 'DESC');

    return await queryBuilder.getMany();
  }
}
