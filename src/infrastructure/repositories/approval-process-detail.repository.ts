import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { GetApprovalProcessDetailListDto } from '../../controller/approval-process-detail/dtos/get-approval-process-detail-list.dto';
import { GetDetailApprovalProcessDetailDto } from '../../controller/approval-process-detail/dtos/get-detail-approval-process-detail.dto';
import { UpdateApprovalProcessDetailDto } from '../../controller/approval-process-detail/dtos/update-approval-process-detail.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { ApprovalProcessDetailModel } from '../../domain/model/approval-process-detail.model';
import { IApprovalProcessDetailRepository } from '../../domain/repositories/approval-process-detail.repository';
import { ApprovalProcessDetailEntity } from '../entities/approval-process-detail.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class ApprovalProcessDetailRepository
  extends BaseRepository
  implements IApprovalProcessDetailRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createApprovalProcessDetail(
    data: ApprovalProcessDetailModel,
  ): Promise<ApprovalProcessDetailModel> {
    const repository = this.getRepository(ApprovalProcessDetailEntity);

    const approvalProcessDetail = repository.create(data);

    return await repository.save(approvalProcessDetail);
  }

  async updateApprovalProcessDetail(
    id: string,
    updateApprovalProcessDetailDto: UpdateApprovalProcessDetailDto,
  ): Promise<ApprovalProcessDetailModel> {
    const repository = this.getRepository(ApprovalProcessDetailEntity);
    const approvalProcessDetail = repository.create({
      id,
      ...updateApprovalProcessDetailDto,
    });
    return await repository.save(approvalProcessDetail);
  }

  async deleteApprovalProcessDetail(id: string): Promise<void> {
    const repository = this.getRepository(ApprovalProcessDetailEntity);
    await repository.delete(id);
  }

  async getApprovalProcessDetails(
    conditions: GetApprovalProcessDetailListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<ApprovalProcessDetailModel>> {
    const repository = this.getRepository(ApprovalProcessDetailEntity);
    let queryBuilder = repository.createQueryBuilder('approvalProcessDetails');

    queryBuilder
      .innerJoin('approvalProcessDetails.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.name',
        'sector.code',
        'sector.description',
        'sector.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.functionUnit', 'functionUnit')
      .addSelect([
        'functionUnit.id',
        'functionUnit.name',
        'functionUnit.code',
        'functionUnit.description',
        'functionUnit.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.department', 'department')
      .addSelect([
        'department.id',
        'department.name',
        'department.code',
        'department.description',
        'department.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.businessUnit', 'businessUnit')
      .addSelect([
        'businessUnit.id',
        'businessUnit.name',
        'businessUnit.code',
        'businessUnit.description',
        'businessUnit.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.prTypes', 'prTypes')
      .addSelect([
        'prTypes.id',
        'prTypes.name',
        'prTypes.code',
        'prTypes.description',
        'prTypes.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.prCreatedBy', 'prCreatedBy')
      .addSelect([
        'prCreatedBy.id',
        'prCreatedBy.firstName',
        'prCreatedBy.lastName',
        'prCreatedBy.code',
        'prCreatedBy.email',
        'prCreatedBy.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.poCreatedBy', 'poCreatedBy')
      .addSelect([
        'poCreatedBy.id',
        'poCreatedBy.firstName',
        'poCreatedBy.lastName',
        'poCreatedBy.code',
        'poCreatedBy.email',
        'poCreatedBy.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.prApprover1', 'prApprover1')
      .addSelect([
        'prApprover1.id',
        'prApprover1.firstName',
        'prApprover1.lastName',
        'prApprover1.code',
        'prApprover1.email',
        'prApprover1.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover2', 'prApprover2')
      .addSelect([
        'prApprover2.id',
        'prApprover2.firstName',
        'prApprover2.lastName',
        'prApprover2.code',
        'prApprover2.email',
        'prApprover2.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover3', 'prApprover3')
      .addSelect([
        'prApprover3.id',
        'prApprover3.firstName',
        'prApprover3.lastName',
        'prApprover3.code',
        'prApprover3.email',
        'prApprover3.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover4', 'prApprover4')
      .addSelect([
        'prApprover4.id',
        'prApprover4.firstName',
        'prApprover4.lastName',
        'prApprover4.code',
        'prApprover4.email',
        'prApprover4.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover5', 'prApprover5')
      .addSelect([
        'prApprover5.id',
        'prApprover5.firstName',
        'prApprover5.lastName',
        'prApprover5.code',
        'prApprover5.email',
        'prApprover5.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover6', 'prApprover6')
      .addSelect([
        'prApprover6.id',
        'prApprover6.firstName',
        'prApprover6.lastName',
        'prApprover6.code',
        'prApprover6.email',
        'prApprover6.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover7', 'prApprover7')
      .addSelect([
        'prApprover7.id',
        'prApprover7.firstName',
        'prApprover7.lastName',
        'prApprover7.code',
        'prApprover7.email',
        'prApprover7.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.poApprover1', 'poApprover1')
      .addSelect([
        'poApprover1.id',
        'poApprover1.firstName',
        'poApprover1.lastName',
        'poApprover1.code',
        'poApprover1.email',
        'poApprover1.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.poApprover2', 'poApprover2')
      .addSelect([
        'poApprover2.id',
        'poApprover2.firstName',
        'poApprover2.lastName',
        'poApprover2.code',
        'poApprover2.email',
        'poApprover2.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.poApprover3', 'poApprover3')
      .addSelect([
        'poApprover3.id',
        'poApprover3.firstName',
        'poApprover3.lastName',
        'poApprover3.code',
        'poApprover3.email',
        'poApprover3.status',
      ]);

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('approvalProcessDetails.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.sectorIds && conditions.sectorIds.length) {
      queryBuilder.andWhere(
        'approvalProcessDetails.sectorId IN (:...sectorIds)',
        {
          sectorIds: conditions.sectorIds,
        },
      );
    }

    if (conditions.departmentIds && conditions.departmentIds.length) {
      queryBuilder.andWhere(
        'approvalProcessDetails.departmentId IN (:...departmentIds)',
        {
          departmentIds: conditions.departmentIds,
        },
      );
    }

    if (conditions.functionUnitIds && conditions.functionUnitIds.length) {
      queryBuilder.andWhere(
        'approvalProcessDetails.functionUnitId IN (:...functionUnitIds)',
        {
          functionUnitIds: conditions.functionUnitIds,
        },
      );
    }

    if (conditions.businessUnitIds && conditions.businessUnitIds.length) {
      queryBuilder.andWhere(
        'approvalProcessDetails.businessUnitId IN (:...businessUnitIds)',
        {
          businessUnitIds: conditions.businessUnitIds,
        },
      );
    }

    if (conditions.prTypeIds && conditions.prTypeIds.length) {
      queryBuilder.andWhere('prTypes.id IN (:...prTypeIds)', {
        prTypeIds: conditions.prTypeIds,
      });
    }

    if (conditions.prCreatedByIds?.length) {
      queryBuilder.andWhere(
        'approvalProcessDetails.prCreatedById IN (:...prCreatedByIds)',
        {
          prCreatedByIds: conditions.prCreatedByIds,
        },
      );
    }

    if (conditions.poCreatedByIds?.length) {
      queryBuilder.andWhere(
        'approvalProcessDetails.poCreatedById IN (:...poCreatedByIds)',
        {
          poCreatedByIds: conditions.poCreatedByIds,
        },
      );
    }

    queryBuilder.orderBy('approvalProcessDetails.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getDetailApprovalProcessDetail(
    conditions: GetDetailApprovalProcessDetailDto,
    jwtPayload: any,
  ): Promise<ApprovalProcessDetailModel> {
    const repository = this.getRepository(ApprovalProcessDetailEntity);
    let queryBuilder = repository.createQueryBuilder('approvalProcessDetails');

    queryBuilder
      .innerJoin('approvalProcessDetails.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.name',
        'sector.code',
        'sector.description',
        'sector.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.functionUnit', 'functionUnit')
      .addSelect([
        'functionUnit.id',
        'functionUnit.name',
        'functionUnit.code',
        'functionUnit.description',
        'functionUnit.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.department', 'department')
      .addSelect([
        'department.id',
        'department.name',
        'department.code',
        'department.description',
        'department.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.businessUnit', 'businessUnit')
      .addSelect([
        'businessUnit.id',
        'businessUnit.name',
        'businessUnit.code',
        'businessUnit.description',
        'businessUnit.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.prTypes', 'prTypes')
      .addSelect([
        'prTypes.id',
        'prTypes.name',
        'prTypes.code',
        'prTypes.description',
        'prTypes.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.prCreatedBy', 'prCreatedBy')
      .addSelect([
        'prCreatedBy.id',
        'prCreatedBy.firstName',
        'prCreatedBy.lastName',
        'prCreatedBy.code',
        'prCreatedBy.email',
        'prCreatedBy.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.poCreatedBy', 'poCreatedBy')
      .addSelect([
        'poCreatedBy.id',
        'poCreatedBy.firstName',
        'poCreatedBy.lastName',
        'poCreatedBy.code',
        'poCreatedBy.email',
        'poCreatedBy.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.prApprover1', 'prApprover1')
      .addSelect([
        'prApprover1.id',
        'prApprover1.firstName',
        'prApprover1.lastName',
        'prApprover1.code',
        'prApprover1.email',
        'prApprover1.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover2', 'prApprover2')
      .addSelect([
        'prApprover2.id',
        'prApprover2.firstName',
        'prApprover2.lastName',
        'prApprover2.code',
        'prApprover2.email',
        'prApprover2.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover3', 'prApprover3')
      .addSelect([
        'prApprover3.id',
        'prApprover3.firstName',
        'prApprover3.lastName',
        'prApprover3.code',
        'prApprover3.email',
        'prApprover3.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover4', 'prApprover4')
      .addSelect([
        'prApprover4.id',
        'prApprover4.firstName',
        'prApprover4.lastName',
        'prApprover4.code',
        'prApprover4.email',
        'prApprover4.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover5', 'prApprover5')
      .addSelect([
        'prApprover5.id',
        'prApprover5.firstName',
        'prApprover5.lastName',
        'prApprover5.code',
        'prApprover5.email',
        'prApprover5.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover6', 'prApprover6')
      .addSelect([
        'prApprover6.id',
        'prApprover6.firstName',
        'prApprover6.lastName',
        'prApprover6.code',
        'prApprover6.email',
        'prApprover6.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.prApprover7', 'prApprover7')
      .addSelect([
        'prApprover7.id',
        'prApprover7.firstName',
        'prApprover7.lastName',
        'prApprover7.code',
        'prApprover7.email',
        'prApprover7.status',
      ]);

    queryBuilder
      .innerJoin('approvalProcessDetails.poApprover1', 'poApprover1')
      .addSelect([
        'poApprover1.id',
        'poApprover1.firstName',
        'poApprover1.lastName',
        'poApprover1.code',
        'poApprover1.email',
        'poApprover1.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.poApprover2', 'poApprover2')
      .addSelect([
        'poApprover2.id',
        'poApprover2.firstName',
        'poApprover2.lastName',
        'poApprover2.code',
        'poApprover2.email',
        'poApprover2.status',
      ]);

    queryBuilder
      .leftJoin('approvalProcessDetails.poApprover3', 'poApprover3')
      .addSelect([
        'poApprover3.id',
        'poApprover3.firstName',
        'poApprover3.lastName',
        'poApprover3.code',
        'poApprover3.email',
        'poApprover3.status',
      ]);

    queryBuilder.where('approvalProcessDetails.id = :id', {
      id: conditions.id,
    });

    return await queryBuilder.getOne();
  }
}
