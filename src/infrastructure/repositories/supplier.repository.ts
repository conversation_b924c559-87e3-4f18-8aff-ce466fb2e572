import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetDetailSupplierDto } from '../../controller/supplier/dtos/get-detail-supplier.dto';
import { GetSupplierListDto } from '../../controller/supplier/dtos/get-supplier-list.dto';
import { UpdateSupplierDto } from '../../controller/supplier/dtos/update-supplier.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { SupplierModel } from '../../domain/model/supplier.model';
import { ISupplierRepository } from '../../domain/repositories/supplier.repository';
import { parseScopes } from '../../utils/common';
import { ESupplierPermission } from '../../utils/constants/permission.enum';
import { SupplierEntity } from '../entities/supplier.entity';
import { BaseRepository } from './base.repository';
import { GetSupplierListByIdsDto } from '../../controller/supplier/dtos/get-supplier-list-by-ids.dto';

@Injectable({ scope: Scope.REQUEST })
export class SupplierRepository
  extends BaseRepository
  implements ISupplierRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createSupplier(data: SupplierModel): Promise<SupplierModel> {
    const repository = this.getRepository(SupplierEntity);

    const supplier = repository.create(data);

    return await repository.save(supplier);
  }
  async updateSupplier(
    id: string,
    updateSupplierDto: UpdateSupplierDto,
  ): Promise<SupplierModel> {
    const repository = this.getRepository(SupplierEntity);

    const supplier = repository.create({
      id,
      ...updateSupplierDto,
    });
    return await repository.save(supplier);
  }
  async deleteSupplier(id: string): Promise<void> {
    const repository = this.getRepository(SupplierEntity);
    await repository.softDelete(id);
  }

  // async getSupplierById(id: string): Promise<SupplierModel> {
  //   const repository = this.getRepository(SupplierEntity);
  //   const queryBuilder = repository.createQueryBuilder('suppliers');

  //   queryBuilder.where('suppliers.id = :id', { id });

  //   return await queryBuilder.getOne();
  // }

  async getSuppliers(
    conditions: GetSupplierListDto,
    jwtPayload,
  ): Promise<ResponseDto<SupplierModel>> {
    conditions.codes = jwtPayload?.suppliers;

    const repository = this.getRepository(SupplierEntity);
    let queryBuilder = repository.createQueryBuilder('suppliers');
    queryBuilder
      .leftJoin('suppliers.industries', 'industries')
      .addSelect([
        'industries.id',
        'industries.codeSAP',
        'industries.status',
        'industries.sector',
      ]);
    queryBuilder
      .leftJoin('industries.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.code',
        'sector.name',
        'sector.description',
        'sector.status',
      ]);

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('suppliers.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (conditions.statuses) {
      queryBuilder.andWhere('suppliers.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.types) {
      queryBuilder.andWhere('suppliers.type IN (:...types)', {
        types: conditions.types,
      });
    }

    if (!jwtPayload?.isSuperAdmin) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        conditions,
      );
    }

    queryBuilder.orderBy('suppliers.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getSupplierByCode(code: string, id?: string): Promise<SupplierModel> {
    const repository = this.getRepository(SupplierEntity);

    const query: FindOneOptions<SupplierEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getSupplierDetail(
    conditions: GetDetailSupplierDto,
    jwtPayload: any,
  ): Promise<SupplierModel> {
    conditions.codes = jwtPayload?.suppliers;

    const repository = this.getRepository(SupplierEntity);
    let queryBuilder = repository
      .createQueryBuilder('suppliers')
      .select([
        'suppliers.id',
        'suppliers.code',
        'suppliers.name',
        'suppliers.description',
        'suppliers.type',
        'suppliers.createdAt',
        'suppliers.updatedAt',
        'suppliers.address',
        'suppliers.phone',
        'suppliers.fax',
        'suppliers.businessLicenseNumber',
        'suppliers.taxCode',
        'suppliers.contactPerson',
        'suppliers.transactionCurrency',
        'suppliers.paymentMethod',
        'suppliers.note',
        'suppliers.createdAt',
      ]);
    queryBuilder
      .leftJoin('suppliers.industries', 'industries')
      .addSelect([
        'industries.id',
        'industries.codeSAP',
        'industries.status',
        'industries.sector',
      ]);
    queryBuilder
      .leftJoin('industries.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.code',
        'sector.name',
        'sector.description',
        'sector.status',
      ]);

    if (conditions.id) {
      queryBuilder.andWhere('suppliers.id = :id', { id: conditions.id });
    }

    if (conditions.code) {
      queryBuilder.andWhere('suppliers.code = :code', {
        code: conditions.code,
      });
    }

    // if (!jwtPayload?.isSuperAdmin) {
    //   queryBuilder = this.queryWithDataRole(
    //     queryBuilder,
    //     jwtPayload,
    //     conditions,
    //   );
    // }

    return await queryBuilder.getOne();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<SupplierEntity>,
    jwtPayload: any,
    conditions: GetSupplierListDto | GetDetailSupplierDto,
  ) {
    if (
      !parseScopes(jwtPayload?.scopes, [
        ESupplierPermission.CREATE,
        ESupplierPermission.EDIT,
      ]) &&
      conditions?.codes?.length
    ) {
      queryBuilder.andWhere(
        '(suppliers.id IS NULL OR suppliers.code IN (:...codes))',
        {
          codes: conditions.codes,
        },
      );
    }

    return queryBuilder;
  }

  async getSupplierByCodesWithRole(
    codes: string[],
    jwtPayload,
    isNeedPermission: boolean = true,
  ): Promise<SupplierModel[]> {
    const repository = this.getRepository(SupplierEntity);
    let queryBuilder = repository.createQueryBuilder('suppliers');

    queryBuilder
      .leftJoin('suppliers.industries', 'industries')
      .addSelect([
        'industries.id',
        'industries.codeSAP',
        'industries.status',
        'industries.sector',
      ]);
    queryBuilder
      .leftJoin('industries.sector', 'sector')
      .addSelect([
        'sector.id',
        'sector.code',
        'sector.name',
        'sector.description',
        'sector.status',
      ]);

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetSupplierListDto({
          codes: jwtPayload?.suppliers,
        }),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('suppliers.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('suppliers.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    queryBuilder.orderBy('suppliers.createdAt', 'DESC');

    return await queryBuilder.getMany();
  }

  async getListByIds(
    conditions: GetSupplierListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<SupplierModel>> {
    const repository = this.getRepository(SupplierEntity);
    let queryBuilder = repository.createQueryBuilder('suppliers');

    if (conditions.ids && conditions.ids?.length) {
      queryBuilder.where('suppliers.id IN (:...ids)', {
        ids: conditions.ids,
      });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    queryBuilder.orderBy('suppliers.createdAt', 'DESC');

    return await this.pagination<SupplierEntity>(queryBuilder, conditions);
  }
}
