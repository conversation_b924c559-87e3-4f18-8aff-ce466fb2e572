import { Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { IOwnerDeviceTokenRepository } from '../../domain/repositories/owner-device-token.repository';
import { OwnerDeviceTokenEntity } from '../entities/owner-device-token.entity';
import { BaseMongoRepository } from './base-mongo.repository';

@Injectable({ scope: Scope.REQUEST })
export class OwnerDeviceTokenRepository
  extends BaseMongoRepository<OwnerDeviceTokenEntity>
  implements IOwnerDeviceTokenRepository
{
  constructor(
    @InjectModel(OwnerDeviceTokenEntity.name)
    protected model: Model<OwnerDeviceTokenEntity>,
  ) {
    super(model);
  }

  async createOwnerDeviceToken(data: OwnerDeviceTokenEntity): Promise<void> {
    await this.model.create(data);
  }

  async deleteOnwerDeviceToken(
    deviceToken: string,
    platform: EPlatform,
    ownerId?: string,
  ): Promise<void> {
    let query: any = {
      deviceToken: deviceToken,
      platform: platform,
    };

    if (ownerId) {
      query = {
        ...query,
        ownerId: ownerId,
      };
    }

    await this.model.deleteMany(query);
  }

  async getOwnerDeviceTokens(
    ownerIds: string[],
    platform: EPlatform,
  ): Promise<OwnerDeviceTokenEntity[]> {
    return await this.model
      .find({
        ownerId: { $in: ownerIds },
        platform: platform,
      })
      .lean();
  }
}
