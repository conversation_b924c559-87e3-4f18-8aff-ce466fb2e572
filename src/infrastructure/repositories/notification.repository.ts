import { Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { NotificationEntity } from '../entities/notification.entity';
import { INotificationRepository } from '../../domain/repositories/notification.repository';
import mongoose, { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { GetNotificationListDto } from '../../controller/notification/dtos/get-notification-list.dto';
import { GetDetailNotificationDto } from '../../controller/notification/dtos/get-detail-notification.dto';
import { MarkAsReadDto } from '../../controller/notification/dtos/mark-as-read-notification.dto';
import { MarkAllAsReadDto } from '../../controller/notification/dtos/mark-all-as-read-notification.dto';
import { CountUnReadNotificationDto } from '../../controller/notification/dtos/count-un-read-notification.dto';
import { BaseMongoRepository } from './base-mongo.repository';

@Injectable({ scope: Scope.REQUEST })
export class NotificationRepository
  extends BaseMongoRepository<NotificationEntity>
  implements INotificationRepository
{
  constructor(
    @InjectModel(NotificationEntity.name)
    protected model: Model<NotificationEntity>,
  ) {
    super(model);
  }

  async createNotification(
    data: NotificationEntity,
  ): Promise<NotificationEntity> {
    return await this.model.create(data);
  }

  async getNotifications(
    conditions: GetNotificationListDto,
  ): Promise<ResponseDto<NotificationEntity>> {
    const query = this.buildFindQuery(conditions);
    return await this.pagination(conditions, query);
  }

  async getNotificationDetail(
    conditions: GetDetailNotificationDto,
  ): Promise<NotificationEntity> {
    const query = this.buildFindQuery(conditions);
    return await this.model
      .findOne({
        ...query,
        _id: new mongoose.Types.ObjectId(conditions.id),
      })
      .lean();
  }

  async markAsRead(conditions: MarkAsReadDto): Promise<void> {
    const query = this.buildFindQuery(conditions);
    await this.model.updateOne(
      {
        ...query,
        _id: new mongoose.Types.ObjectId(conditions.id),
      },
      {
        seenFlag: true,
      },
    );
  }

  async markAllAsRead(conditions: MarkAllAsReadDto): Promise<void> {
    const query = this.buildFindQuery(conditions);
    await this.model.updateMany(
      {
        ...query,
        seenFlag: false,
      },
      {
        seenFlag: true,
      },
    );
  }

  async countUnRead(conditions: CountUnReadNotificationDto): Promise<object> {
    const query = this.buildFindQuery(conditions);
    const total = await this.model.countDocuments({
      ...query,
      seenFlag: false,
    });
    return {
      total: total,
    };
  }

  private buildFindQuery(
    conditions:
      | GetNotificationListDto
      | GetDetailNotificationDto
      | MarkAsReadDto
      | MarkAllAsReadDto
      | CountUnReadNotificationDto,
  ): any {
    const query: any = {};

    query.ownerId = { $eq: conditions.onwerId };

    query.platform = { $eq: conditions.platform };

    return query;
  }
}
