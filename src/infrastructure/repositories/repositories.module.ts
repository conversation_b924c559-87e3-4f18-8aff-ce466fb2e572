import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RedisModule } from '../config/redis/redis.module';
import { TypeOrmConfigModule } from '../config/typeorm/typeorm.module';
import { ActualSpendingEntity } from '../entities/actual-spending.entity';
import { ApprovalLevelEntity } from '../entities/approval-level.entity';
import { ApprovalWorkflowEntity } from '../entities/approval-workflow.entity';
import { BudgetCapexEntity } from '../entities/budget-capex.entity';
import { BudgetCodeEntity } from '../entities/budget-code.entity';
import { BudgetInvestmentEntity } from '../entities/budget-investment.entity';
import { BudgetOpexEntity } from '../entities/budget-opex.entity';
import { BudgetEntity } from '../entities/budget.entity';
import { BusinessOwnerEntity } from '../entities/business-owner.entity';
import { BusinessUnitEntity } from '../entities/business-unit.entity';
import { CompanyEntity } from '../entities/company.entity';
import { ConditionDetailEntity } from '../entities/condition-detail.entity';
import { ConditionEntity } from '../entities/condition.entity';
import { CostSubHistoryEntity } from '../entities/cost-sub-history.entity';
import { CostEntity } from '../entities/cost.entity';
import { CostcenterSubaccountEntity } from '../entities/costcenter-subaccount.entity';
import { CurrencyUnitExchangeEntity } from '../entities/currency-unit-exchange.entity';
import { CurrencyUnitEntity } from '../entities/currency-unit.entity';
import { DepartmentEntity } from '../entities/department.entity';
import { FileExportHistoryEntity } from '../entities/file-export-history.entity';
import { FileImportHistoryEntity } from '../entities/file-import-history.entity';
import { FunctionUnitEntity } from '../entities/function-unit.entity';
import { HistoryApproveEntity } from '../entities/history-approve.entity';
import { IncreasementCodeEntity } from '../entities/increament-code.entity';
import { InventoryStandardEntity } from '../entities/inventory-standard.entity';
import { MaterialGroupEntity } from '../entities/material-group.entity';
import { MaterialSectorEntity } from '../entities/material-sector.entity';
import { MaterialTypeEntity } from '../entities/material-type.entity';
import { MaterialEntity } from '../entities/material.entity';
import {
  NotificationForm,
  NotificationFormSchema,
} from '../entities/notification-form.entity';
import {
  NotificationEntity,
  NotificationSchema,
} from '../entities/notification.entity';
import {
  OwnerDeviceTokenEntity,
  OwnerDeviceTokenSchema,
} from '../entities/owner-device-token.entity';
import { PlantEntity } from '../entities/plant.entity';
import { PositionEntity } from '../entities/position.entity';
import { PriceInformationRecordEntity } from '../entities/price_information_record.entity';
import { ProcessConditionEntity } from '../entities/process-condition.entity';
import { ProcessTypeEntity } from '../entities/process-type.entity';
import { ProcessEntity } from '../entities/process.entity';
import { ProfitCenterEntity } from '../entities/profit-center.entity';
import { PurchaseOrderTypeEntity } from '../entities/purchase-order-type.entity';
import { PurchaseRequestTypeEntity } from '../entities/purchase-request-type.entity';
import { PurchaseOrderEntity } from '../entities/purchase_order.entity';
import { PurchaseOrderDetailEntity } from '../entities/purchase_order_detail.entity';
import { PurchaseRequestEntity } from '../entities/purchase_request.entity';
import { PurchaseRequestDetailEntity } from '../entities/purchase_request_detail.entity';
import { PurchasingDepartmentEntity } from '../entities/purchasing-department.entity';
import { PurchasingGroupEntity } from '../entities/purchasing-group.entity';
import { SapPurchaseOrderEntity } from '../entities/sap_purchase_order.entity';
import { SapPurchaseOrderItemEntity } from '../entities/sap_purchase_order_item.entity';
import { SectorEntity } from '../entities/sector.entity';
import { StaffApprovalWorkflowEntity } from '../entities/staff-approval-workflow.entity';
import { StaffEntity } from '../entities/staff.entity';
import { SupplierSectorEntity } from '../entities/supplier-sector.entity';
import { SupplierEntity } from '../entities/supplier.entity';
import { ApiLogEntity, ApiLogSchema } from '../schemas/api-log.schema';
import { RepositoryProviders } from './repositories.provider';
import { MeasureEntity } from '../entities/measure.entity';
import { ApprovalProcessDetailEntity } from '../entities/approval-process-detail.entity';
import { TaxCodeEntity } from '../entities/tax-code.entity';
import { ReceiptSolomonEntity } from '../entities/receipt-solomon.entity';
import { WarehouseEntity } from '../entities/warehouse.entity';
import { SolomonPurchaseOrderEntity } from '../entities/solomon-purchase-order.entity';
import { SolomonPurchaseOrderItemEntity } from '../entities/solomon-purchase-order-item.entity';

@Module({
  imports: [
    TypeOrmConfigModule,
    TypeOrmModule.forFeature([
      BusinessUnitEntity,
      SectorEntity,
      CompanyEntity,
      BusinessOwnerEntity,
      DepartmentEntity,
      FunctionUnitEntity,
      BudgetEntity,
      BudgetCapexEntity,
      BudgetOpexEntity,
      BudgetInvestmentEntity,
      CurrencyUnitEntity,
      BudgetCodeEntity,
      CostcenterSubaccountEntity,
      IncreasementCodeEntity,
      CurrencyUnitExchangeEntity,
      CostSubHistoryEntity,
      FileImportHistoryEntity,
      MaterialTypeEntity,
      MaterialGroupEntity,
      MaterialEntity,
      PurchaseRequestTypeEntity,
      PurchaseOrderTypeEntity,
      PurchasingGroupEntity,
      PurchasingDepartmentEntity,
      PlantEntity,
      SupplierEntity,
      PositionEntity,
      StaffEntity,
      InventoryStandardEntity,
      FileExportHistoryEntity,
      ProfitCenterEntity,
      SupplierSectorEntity,
      MaterialSectorEntity,
      ConditionDetailEntity,
      ProcessConditionEntity,
      ProcessEntity,
      ConditionEntity,
      ApprovalWorkflowEntity,
      StaffApprovalWorkflowEntity,
      CostEntity,
      ActualSpendingEntity,
      ProcessTypeEntity,
      ApprovalLevelEntity,
      HistoryApproveEntity,
      PurchaseRequestEntity,
      PurchaseRequestDetailEntity,
      PurchaseOrderEntity,
      PurchaseOrderDetailEntity,
      PriceInformationRecordEntity,
      SapPurchaseOrderEntity,
      SapPurchaseOrderItemEntity,
      MeasureEntity,
      ApprovalProcessDetailEntity,
      TaxCodeEntity,
      ReceiptSolomonEntity,
      WarehouseEntity,
      SolomonPurchaseOrderEntity,
      SolomonPurchaseOrderItemEntity,
    ]),
    MongooseModule.forRoot(process.env.MONGO_DB_URI),
    MongooseModule.forFeature([
      { name: NotificationForm.name, schema: NotificationFormSchema },
      { name: NotificationEntity.name, schema: NotificationSchema },
      { name: OwnerDeviceTokenEntity.name, schema: OwnerDeviceTokenSchema },
      { name: ApiLogEntity.name, schema: ApiLogSchema },
    ]),
    RedisModule,
  ],
  providers: [...RepositoryProviders],
  exports: [...RepositoryProviders],
})
export class RepositoriesModule {}
