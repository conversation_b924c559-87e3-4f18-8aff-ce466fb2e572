import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetFunctionUnitListDto } from '../../controller/function-unit/dtos/get-function-unit-list.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { FunctionUnitModel } from '../../domain/model/function-unit.model';
import { IFunctionUnitRepository } from '../../domain/repositories/function-unit.repository';
import { FunctionUnitEntity } from '../entities/function-unit.entity';
import { BaseRepository } from './base.repository';
import { GetDetailFunctionUnitDto } from '../../controller/function-unit/dtos/get-detail-function-unit.dto';
import { parseScopes } from '../../utils/common';
import { EFunctionUnitPermission } from '../../utils/constants/permission.enum';

@Injectable({ scope: Scope.REQUEST })
export class FunctionUnitRepository
  extends BaseRepository
  implements IFunctionUnitRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createFunctionUnit(
    data: FunctionUnitModel,
  ): Promise<FunctionUnitModel> {
    const repository = this.getRepository(FunctionUnitEntity);
    const newFunctionUnit = repository.create(data);
    return await repository.save(newFunctionUnit);
  }

  async updateFunctionUnit(
    data: FunctionUnitModel,
  ): Promise<FunctionUnitModel> {
    const repository = this.getRepository(FunctionUnitEntity);
    const updateFunctionUnit = repository.create(data);
    return await repository.save(updateFunctionUnit);
  }

  async getFunctionUnits(
    conditions: GetFunctionUnitListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<FunctionUnitModel>> {
    conditions.codes = jwtPayload?.functionUnits; // Data role

    const repository = this.getRepository(FunctionUnitEntity);
    const queryBuilder = repository.createQueryBuilder('functionUnits');

    if (conditions.statuses) {
      queryBuilder.where('functionUnits.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('functionUnits.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        EFunctionUnitPermission.CREATE,
        EFunctionUnitPermission.EDIT,
      ]) &&
      conditions.codes?.length
    ) {
      if (conditions.codes && conditions.codes.length) {
        queryBuilder.andWhere(
          '(functionUnits.id IS NULL OR functionUnits.code IN (:...codes))',
          {
            codes: conditions.codes,
          },
        );
      } else {
        return new ResponseDto<FunctionUnitModel>(
          [],
          conditions.page,
          conditions.limit,
          0,
        );
      }
    }

    queryBuilder.orderBy('functionUnits.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async deleteFunctionUnit(id: string): Promise<void> {
    const repository = this.getRepository(FunctionUnitEntity);
    await repository.softDelete(id);
  }

  async getFunctionUnitById(id: string): Promise<FunctionUnitModel> {
    const repository = this.getRepository(FunctionUnitEntity);
    const detail = await repository.findOne({ where: { id: id } });
    return detail;
  }

  async getFunctionUnitByCode(
    code: string,
    id?: string,
  ): Promise<FunctionUnitModel> {
    const repository = this.getRepository(FunctionUnitEntity);
    const query: FindOneOptions<FunctionUnitEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getDetailFunctionUnit(
    conditions: GetDetailFunctionUnitDto,
    jwtPayload: any,
  ): Promise<FunctionUnitModel> {
    conditions.functionUnitCodes = jwtPayload?.functionUnits;
    const repository = this.getRepository(FunctionUnitEntity);

    const queryBuilder = repository.createQueryBuilder('functionUnits');
    queryBuilder.where('functionUnits.id = :id', { id: conditions.id });

    // if (
    //   !jwtPayload?.isSuperAdmin &&
    //   !parseScopes(jwtPayload?.scopes, [
    //     EFunctionUnitPermission.CREATE,
    //     EFunctionUnitPermission.EDIT,
    //   ])
    // ) {
    //   if (conditions.functionUnitCodes?.length) {
    //     queryBuilder.andWhere('functionUnits.code IN (:...codes)', {
    //       codes: conditions.functionUnitCodes,
    //     });
    //   } else {
    //     return null;
    //   }
    // }

    return await queryBuilder.getOne();
  }

  async getFunctionUnitsByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<FunctionUnitModel[]> {
    const repository = this.getRepository(FunctionUnitEntity);
    let queryBuilder = repository.createQueryBuilder('functionUnits');

    if (ids.length) {
      queryBuilder.where('functionUnits.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetFunctionUnitListDto({}),
    );

    return await queryBuilder.getMany();
  }

  private queryWithDataRole(
    queryBuilder: SelectQueryBuilder<FunctionUnitEntity>,
    jwtPayload: any,
    conditions: GetFunctionUnitListDto,
  ) {
    if (conditions.codes?.length) {
      if (jwtPayload?.functionUnits?.length) {
        conditions.codes = conditions.codes.filter((item) =>
          jwtPayload?.functionUnits.includes(item),
        );
      }
    } else {
      conditions.codes = jwtPayload?.functionUnits;
    }

    if (!jwtPayload?.isSuperAdmin) {
      if (
        !parseScopes(jwtPayload?.scopes, [
          EFunctionUnitPermission.CREATE,
          EFunctionUnitPermission.EDIT,
        ]) &&
        conditions?.codes?.length
      ) {
        queryBuilder.andWhere(
          '(functionUnits.id IS NULL OR functionUnits.code IN (:...codes))',
          {
            codes: conditions.codes,
          },
        );
      }
    }

    return queryBuilder;
  }

  async getFunctionUnitsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<FunctionUnitModel[]> {
    const repository = this.getRepository(FunctionUnitEntity);
    let queryBuilder = repository.createQueryBuilder('functionUnits');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetFunctionUnitListDto({ codes }),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('functionUnits.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }

    return await queryBuilder.getMany();
  }
}
