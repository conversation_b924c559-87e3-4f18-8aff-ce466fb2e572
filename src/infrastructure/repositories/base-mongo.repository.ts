import { Model } from 'mongoose';
import { PaginationDto } from '../../domain/dtos/pagination.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';

export const MAX_LIMIT = 100;
export const DEFAULT_LIMIT = 15;

export class BaseMongoRepository<T> {
  constructor(
    protected model: Model<T>, // Use EntityManager for MongoDB
  ) {}

  public async pagination<T>(
    options: Partial<PaginationDto> & { collectionName?: string },
    findParams?: any, // MongoDB find parameters
  ): Promise<ResponseDto<T>> {
    let results = [];
    let total = 0;

    options = await this.prepareOptions(options);

    // Build the query based on the parameters
    const query = this.buildQuery(findParams, options);

    total = await this.model.countDocuments(query).exec();

    if (options.getAll !== 1) {
      results = await this.model
        .find({
          ...query,
        })
        .sort({ created_at: 1 })
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .exec();
    } else {
      results = await this.model
        .find({
          ...query,
        })
        .sort({ created_at: 1 })
        .exec();
    }
    return new ResponseDto<T>(results, options.page, options.limit, total);
  }

  private buildQuery(findParams: any, options: Partial<PaginationDto>): any {
    const query: any = {};

    const { from, searchString, to } = options;

    if (from && to) {
      query.createdAt = { $gte: new Date(from), $lte: new Date(to) };
    } else if (from) {
      query.createdAt = { $gte: new Date(from) };
    } else if (to) {
      query.createdAt = { $lte: new Date(to) };
    }

    if (searchString) {
      query.searchValue = new RegExp(searchString.replace(/%/gi, ''), 'i'); // Case-insensitive search
    }

    return { ...query, ...findParams };
  }

  async prepareOptions(
    options: Partial<PaginationDto> & { collectionName?: string },
  ): Promise<Partial<PaginationDto> & { collectionName?: string }> {
    if (typeof options.limit === 'string') {
      options.limit = parseInt(options.limit);
    }
    let limit: number = options.limit || DEFAULT_LIMIT;
    if (options.limit > MAX_LIMIT) {
      limit = MAX_LIMIT;
    }

    return {
      ...options,
      limit,
      page: options.page < 1 ? 0 : options.page,
    };
  }
}
