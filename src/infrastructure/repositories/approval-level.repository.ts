import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { IApprovalLevelRepository } from '../../domain/repositories/approval-level.repository';
import { ApprovalLevelEntity } from '../entities/approval-level.entity';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { REQUEST } from '@nestjs/core';

@Injectable({ scope: Scope.REQUEST })
export class ApprovalLevelRepository
  extends BaseRepository
  implements IApprovalLevelRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async findApprovalLevels(
    prId?: number,
    poId?: number,
    greaterThanLevel?: number,
  ): Promise<ApprovalLevelEntity[]> {
    const repository = this.getRepository(ApprovalLevelEntity);
    const queryBuilder = repository.createQueryBuilder('levels');
    // .andWhere('levels.status = :status', { status: StatusLevel.Pending })
    // .orderBy('levels.created_at', 'DESC');

    if (prId) {
      queryBuilder.andWhere('levels.purchase_request_id = :prId', { prId });
    }

    if (poId) {
      queryBuilder.andWhere('levels.purchase_order_id = :poId', { poId });
    }

    if (greaterThanLevel && !isNaN(Number(greaterThanLevel))) {
      queryBuilder.andWhere('levels.level > :greaterThanLevel', {
        greaterThanLevel,
      });
    }

    return await queryBuilder.getMany();
  }
}
