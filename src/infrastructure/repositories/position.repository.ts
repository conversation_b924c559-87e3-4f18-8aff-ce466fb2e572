import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, FindOneOptions, Not } from 'typeorm';
import { GetPositionListDto } from '../../controller/position/dtos/get-position-list.dto';
import { UpdatePositionDto } from '../../controller/position/dtos/update-position.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PositionModel } from '../../domain/model/position.model';
import { IPositionRepository } from '../../domain/repositories/position.repository';
import { PositionEntity } from '../entities/position.entity';
import { BaseRepository } from './base.repository';
import { GetDetailPositionDto } from '../../controller/position/dtos/get-detail-position.dto';
import { parseScopes } from '../../utils/common';
import { EPositionPermission } from '../../utils/constants/permission.enum';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class PositionRepository
  extends BaseRepository
  implements IPositionRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createPosition(data: PositionModel): Promise<PositionModel> {
    const repository = this.getRepository(PositionEntity);

    const position = repository.create({
      code: data.code,
      description: data.description,
      name: data.name,
      status: data.status,
    });

    return await repository.save(position);
  }
  async updatePosition(
    id,
    updatePositionDto: UpdatePositionDto,
  ): Promise<PositionModel> {
    const repository = this.getRepository(PositionEntity);
    const position = repository.create({ id, ...updatePositionDto });
    return await repository.save(position);
  }
  async deletePosition(id: string): Promise<void> {
    const repository = this.getRepository(PositionEntity);
    await repository.delete(id);
  }

  async getPositionById(id: string): Promise<PositionModel> {
    const repository = this.getRepository(PositionEntity);
    return await repository.findOneBy({ id: id });
  }

  async getPositions(
    conditions: GetPositionListDto,
    jwtPayload,
  ): Promise<ResponseDto<PositionModel>> {
    conditions.codes = jwtPayload?.positions; // Data role

    const repository = this.getRepository(PositionEntity);
    const queryBuilder = repository.createQueryBuilder('positions');

    if (conditions.statuses) {
      queryBuilder.andWhere('positions.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        EPositionPermission.CREATE,
        EPositionPermission.EDIT,
      ])
    ) {
      if (conditions.codes && conditions.codes.length) {
        queryBuilder.andWhere(
          '(positions.id IS NULL OR positions.code IN (:...codes))',
          {
            codes: conditions.codes,
          },
        );
      } else {
        return new ResponseDto<PositionModel>(
          [],
          conditions.page,
          conditions.limit,
          0,
        );
      }
    }

    queryBuilder.orderBy('positions.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getPositionByCode(code: string, id?: string): Promise<PositionModel> {
    const repository = this.getRepository(PositionEntity);

    const query: FindOneOptions<PositionEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }
    return await repository.findOne(query);
  }

  async getDetailPosition(
    conditions: GetDetailPositionDto,
    jwtPayload: any,
  ): Promise<PositionModel> {
    conditions.positionCodes = jwtPayload?.positions;
    const repository = this.getRepository(PositionEntity);
    const queryBuilder = repository.createQueryBuilder('position');

    queryBuilder.where('position.id = :id', { id: conditions.id });

    // if (
    //   !jwtPayload?.isSuperAdmin &&
    //   !parseScopes(jwtPayload?.scopes, [
    //     EPositionPermission.CREATE,
    //     EPositionPermission.EDIT,
    //   ])
    // ) {
    //   if (conditions.positionCodes?.length) {
    //     queryBuilder.andWhere('position.code IN (:...codes)', {
    //       codes: conditions.positionCodes,
    //     });
    //   } else {
    //     return null;
    //   }
    // }

    return await queryBuilder.getOne();
  }
}
