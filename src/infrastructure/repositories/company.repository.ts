import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetCompanyListDto } from '../../controller/company/dtos/get-company-list.dto';
import { GetDetailCompanyDto } from '../../controller/company/dtos/get-detail-company.dto';
import { UpdateCompanyDto } from '../../controller/company/dtos/update-company.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { CompanyModel } from '../../domain/model/company.model';
import { ICompanyRepository } from '../../domain/repositories/company.repository';
import { parseScopes } from '../../utils/common';
import { ECompanyPermission } from '../../utils/constants/permission.enum';
import { CompanyEntity } from '../entities/company.entity';
import { BaseRepository } from './base.repository';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class CompanyRepository
  extends BaseRepository
  implements ICompanyRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createCompany(data: CompanyModel): Promise<CompanyModel> {
    const repository = this.getRepository(CompanyEntity);

    const company = repository.create({
      code: data.code,
      description: data.description,
      name: data.name,
      status: data.status,
    });

    return await repository.save(company);
  }
  async updateCompany(
    id,
    updateCompanyDto: UpdateCompanyDto,
  ): Promise<CompanyModel> {
    const repository = this.getRepository(CompanyEntity);
    const company = repository.create({ id, ...updateCompanyDto });
    return await repository.save(company);
  }
  async deleteCompany(id: string): Promise<void> {
    const repository = this.getRepository(CompanyEntity);
    await repository.delete(id);
  }

  async getCompanyById(id: string): Promise<CompanyModel> {
    const repository = this.getRepository(CompanyEntity);
    return await repository.findOneBy({ id: id });
  }

  async getCompanies(
    conditions: GetCompanyListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<CompanyModel>> {
    const repository = this.getRepository(CompanyEntity);
    let queryBuilder = repository.createQueryBuilder('companies');

    if (conditions.statuses) {
      queryBuilder.andWhere('companies.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('companies.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('companies.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async getCompanyByCode(code: string, id?: string): Promise<CompanyModel> {
    const repository = this.getRepository(CompanyEntity);

    const query: FindOneOptions<CompanyEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getDetailCompany(
    conditions: GetDetailCompanyDto,
    jwtPayload: any,
  ): Promise<CompanyModel> {
    const repository = this.getRepository(CompanyEntity);
    let queryBuilder = repository.createQueryBuilder('companies');

    queryBuilder.where('companies.id = :id', {
      id: conditions.id,
    });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  async getCompaniesByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<CompanyModel[]> {
    const repository = this.getRepository(CompanyEntity);
    let queryBuilder = repository.createQueryBuilder('companies');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetCompanyListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.where('companies.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.where('companies.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<CompanyEntity>,
    jwtPayload: any,
    conditions: GetCompanyListDto | GetDetailCompanyDto,
  ) {
    conditions.codes = jwtPayload?.companies; // Data role
    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        ECompanyPermission.CREATE,
        ECompanyPermission.EDIT,
      ]) &&
      conditions.codes?.length
    ) {
      queryBuilder.andWhere(
        '(companies.id IS NULL OR companies.code IN (:...codes))',
        {
          codes: conditions.codes,
        },
      );
    }

    return queryBuilder;
  }

  async getCompanyByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<CompanyModel[]> {
    const repository = this.getRepository(CompanyEntity);
    let queryBuilder = repository.createQueryBuilder('companies');

    if (ids.length) {
      queryBuilder.where('companies.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetCompanyListDto({}),
    );

    return await queryBuilder.getMany();
  }

  async getCompanyByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<CompanyModel[]> {
    const repository = this.getRepository(CompanyEntity);
    let queryBuilder = repository.createQueryBuilder('companies');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetCompanyListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('companies.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('companies.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }
}
