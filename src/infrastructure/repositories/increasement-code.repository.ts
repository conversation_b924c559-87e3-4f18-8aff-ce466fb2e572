import { Inject, Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { BudgetOpexModel } from '../../domain/model/budget-opex.model';
import { IBudgetOpexRepository } from '../../domain/repositories/budget-opex.repository';
import { IncreasementCodeModel } from '../../domain/model/increasement-code.model';
import { IIncreasementCodeRepository } from '../../domain/repositories/increasement-code.repository';
import { ECodeType } from '../../domain/config/enums/code-type.enum';
import { IncreasementCodeEntity } from '../entities/increament-code.entity';
import { BaseRepository } from './base.repository';
import { REQUEST } from '@nestjs/core';

@Injectable()
export class IncreasementCodeRepository
  extends BaseRepository
  implements IIncreasementCodeRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async getIncreasementCodeByType(
    codeType: ECodeType,
  ): Promise<IncreasementCodeModel | null> {
    const repository = this.getRepository(IncreasementCodeEntity);
    return await repository.findOne({
      where: {
        codeType,
      },
    });
  }

  async createIncreasementCode(
    increasementCode: IncreasementCodeModel,
  ): Promise<IncreasementCodeModel> {
    const repository = this.getRepository(IncreasementCodeEntity);
    const newCreateIncreasementCode = repository.create(increasementCode);
    return await repository.save(newCreateIncreasementCode);
  }
  async updateIncreasementCode(
    increasementCode: IncreasementCodeModel,
  ): Promise<IncreasementCodeModel> {
    const repository = this.getRepository(IncreasementCodeEntity);
    const updateIncreasementCode = repository.create(increasementCode);
    return await repository.save(updateIncreasementCode);
  }
}
