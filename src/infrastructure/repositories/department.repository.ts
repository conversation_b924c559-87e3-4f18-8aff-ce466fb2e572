import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, FindOneOptions, Not, SelectQueryBuilder } from 'typeorm';
import { GetDepartmentListDto } from '../../controller/department/dtos/get-department-list.dto';
import { GetDetailDepartmentDto } from '../../controller/department/dtos/get-detail-department.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { DepartmentModel } from '../../domain/model/department.model';
import { IDepartmentRepository } from '../../domain/repositories/department.repository';
import { parseScopes } from '../../utils/common';
import { EDepartmentPermission } from '../../utils/constants/permission.enum';
import { DepartmentEntity } from '../entities/department.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class DepartmentRepository
  extends BaseRepository
  implements IDepartmentRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createDepartment(data: DepartmentModel): Promise<DepartmentModel> {
    const repository = this.getRepository(DepartmentEntity);
    const newDepartment = repository.create(data);
    return await repository.save(newDepartment);
  }

  async updateDepartment(data: DepartmentModel): Promise<DepartmentModel> {
    const repository = this.getRepository(DepartmentEntity);
    const updateDepartment = repository.create(data);
    return await repository.save(updateDepartment);
  }

  async getDepartments(
    conditions: GetDepartmentListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<DepartmentModel>> {
    const repository = this.getRepository(DepartmentEntity);
    let queryBuilder = repository.createQueryBuilder('departments');

    if (conditions.statuses) {
      queryBuilder.where('departments.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere('departments.id IN (:...ids)', {
        ids: conditions.ids,
      });
    }

    queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    queryBuilder.orderBy('departments.createdAt', 'DESC');

    return await this.pagination(queryBuilder, conditions);
  }

  async deleteDepartment(id: string): Promise<void> {
    const repository = this.getRepository(DepartmentEntity);
    await repository.softDelete(id);
  }

  async getDepartmentById(id: string): Promise<DepartmentModel> {
    const repository = this.getRepository(DepartmentEntity);
    const detail = await repository.findOne({ where: { id: id } });
    return detail;
  }

  async getDepartmentByCode(
    code: string,
    id?: string,
  ): Promise<DepartmentModel> {
    const repository = this.getRepository(DepartmentEntity);
    const query: FindOneOptions<DepartmentEntity> = {
      where: {
        code,
      },
    };

    if (id) {
      query.where['id'] = Not(id);
    }

    return await repository.findOne(query);
  }

  async getDetailDepartment(
    conditions: GetDetailDepartmentDto,
    jwtPayload: any,
  ): Promise<DepartmentModel> {
    const repository = this.getRepository(DepartmentEntity);

    let queryBuilder = repository.createQueryBuilder('departments');
    queryBuilder.where('departments.id = :id', { id: conditions.id });

    // queryBuilder = this.queryWithDataRole(queryBuilder, jwtPayload, conditions);

    return await queryBuilder.getOne();
  }

  async getDepartmentsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<DepartmentModel[]> {
    const repository = this.getRepository(DepartmentEntity);

    let queryBuilder = repository.createQueryBuilder('departments');

    if (!jwtPayload?.isSuperAdmin && isNeedPermission) {
      queryBuilder = this.queryWithDataRole(
        queryBuilder,
        jwtPayload,
        new GetDepartmentListDto({}),
      );
    } else {
      if (codes?.length) {
        queryBuilder.andWhere('departments.code IN (:...codes)', {
          codes: codes,
        });
      } else {
        return [];
      }
    }
    // if (codes?.length) {
    //   queryBuilder.andWhere('departments.code IN (:...codes)', {
    //     codes: codes,
    //   });
    // } else {
    //   return [];
    // }

    return await queryBuilder.getMany();
  }

  queryWithDataRole(
    queryBuilder: SelectQueryBuilder<DepartmentEntity>,
    jwtPayload: any,
    conditions: GetDepartmentListDto | GetDetailDepartmentDto,
  ) {
    conditions.codes = jwtPayload?.departments; // Data role
    if (
      !jwtPayload?.isSuperAdmin &&
      !parseScopes(jwtPayload?.scopes, [
        EDepartmentPermission.CREATE,
        EDepartmentPermission.EDIT,
      ]) &&
      conditions.codes?.length
    ) {
      queryBuilder.andWhere(
        '(departments.id IS NULL OR departments.code IN (:...codes))',
        {
          codes: conditions.codes,
        },
      );
    }

    return queryBuilder;
  }

  async getDepartmentByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<DepartmentModel[]> {
    const repository = this.getRepository(DepartmentEntity);
    let queryBuilder = repository.createQueryBuilder('departments');

    if (ids.length) {
      queryBuilder.where('departments.id IN (:...ids)', { ids: ids });
    } else {
      return [];
    }

    queryBuilder = this.queryWithDataRole(
      queryBuilder,
      jwtPayload,
      new GetDepartmentListDto({}),
    );

    return await queryBuilder.getMany();
  }
}
