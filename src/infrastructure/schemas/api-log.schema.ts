import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { isJSON } from 'class-validator';
import { Document } from 'mongoose';

@Schema({
  collection: 'ep_purchase_service_api_logs',
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class ApiLogEntity extends Document {
  @Prop({ required: true, index: true }) controller: string; // Tên controller
  @Prop({ required: true, index: true }) method: string; // Method: GET, POST...
  @Prop({ required: true, index: true }) route: string; // Endpoint API
  @Prop({
    type: String,
    set: (value: any) => {
      // Chỉ stringify nếu value tồn tại và không phải JSON string
      if (value && typeof value !== 'string') {
        return JSON.stringify(value);
      }
      return value;
    },
    get: (value: any) => {
      // Chỉ parse nếu value tồn tại và là JSON hợp lệ
      if (value && typeof value === 'string') {
        try {
          return isJSON(value) ? JSON.parse(value) : value;
        } catch (e) {
          return value; // Nếu parse lỗi, tr<PERSON> về giá trị gốc
        }
      }
      return value;
    },
  })
  params: any; // Dữ liệu request
  @Prop({
    type: String,
    set: (value: any) => {
      if (value && typeof value !== 'string') {
        return JSON.stringify(value);
      }
      return value;
    },
    get: (value: any) => {
      if (value && typeof value === 'string') {
        try {
          return isJSON(value) ? JSON.parse(value) : value;
        } catch (e) {
          return value;
        }
      }
      return value;
    },
  })
  query: any; // Dữ liệu request
  @Prop({
    type: String,
    set: (value: any) => {
      if (value && typeof value !== 'string') {
        return JSON.stringify(value);
      }
      return value;
    },
    get: (value: any) => {
      if (value && typeof value === 'string') {
        try {
          return isJSON(value) ? JSON.parse(value) : value;
        } catch (e) {
          return value;
        }
      }
      return value;
    },
  })
  body: any; // Dữ liệu request
  @Prop({
    type: String,
    set: (value: any) => {
      if (value && typeof value !== 'string') {
        return JSON.stringify(value);
      }
      return value;
    },
    get: (value: any) => {
      if (value && typeof value === 'string') {
        try {
          return isJSON(value) ? JSON.parse(value) : value;
        } catch (e) {
          return value;
        }
      }
      return value;
    },
  })
  user: any; // Dữ liệu request
  @Prop({ required: true }) statusCode: number; // HTTP Status Code
  @Prop({ required: true }) isSuccess: boolean; // Thành công hay thất bại
  @Prop({
    type: String,
    set: (value: any) => {
      if (value && typeof value !== 'string') {
        return JSON.stringify(value);
      }
      return value;
    },
    get: (value: any) => {
      if (value && typeof value === 'string') {
        try {
          return isJSON(value) ? JSON.parse(value) : value;
        } catch (e) {
          return value;
        }
      }
      return value;
    },
  })
  errorMessage?: any; // Mô tả lỗi (nếu có)
  @Prop() duration?: number; // Thời gian thực thi API (ms)
  @Prop({
    type: Date,
    required: true,
    default: Date.now,
    index: true,
    expires: '30d',
  })
  timestamp: Date;
}

export const ApiLogSchema = SchemaFactory.createForClass(ApiLogEntity);
