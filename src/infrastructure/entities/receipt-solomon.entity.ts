import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { removeUnicode } from '../../utils/common';
import { isJSON } from 'class-validator';

@Entity('receipt_solomons')
export class ReceiptSolomonEntity extends BaseEntity {
  @Column({ name: 'po_id', type: 'int4', nullable: true })
  poId?: number; // Mã PO

  @ManyToOne(() => PurchaseOrderEntity, (po) => po.receiptSolomons)
  @JoinColumn({ name: 'po_id' })
  purchaseOrder?: PurchaseOrderEntity;

  @Column({ name: 'receipt_created_at', type: 'date' })
  receiptCreatedAt?: Date; //Thời gian phát sinh receipt

  @Column({ name: 'receipt_code', type: 'varchar', nullable: true })
  receiptCode?: string; // Mã số receipt

  @Column({ name: 'total_amount', type: 'decimal', nullable: true })
  totalAmount?: number;

  @Column({ name: 'budget_code_code', type: 'varchar', nullable: true })
  budgetCodeCode?: string;

  // Mã ngân sách
  @Column({ name: 'budget_code_id', type: 'uuid', nullable: true })
  budgetCodeId?: string;

  @ManyToOne(() => BudgetCodeEntity, (budgetCode) => budgetCode.receiptSolomons)
  @JoinColumn({ name: 'budget_code_id' })
  budgetCode?: BudgetCodeEntity;

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue = removeUnicode(this.receiptCode);
  }
}
