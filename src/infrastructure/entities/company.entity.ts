import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { ECompanyStatus } from '../../domain/config/enums/company.enum';
import { StaffEntity } from './staff.entity';
import { InventoryStandardEntity } from './inventory-standard.entity';
import { MaterialEntity } from './material.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { SapPurchaseOrderEntity } from './sap_purchase_order.entity';

@Entity('companies')
export class CompanyEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: ECompanyStatus,
    default: ECompanyStatus.ACTIVE,
  })
  status: ECompanyStatus; //Trạng thái

  @OneToMany(
    () => CostcenterSubaccountEntity,
    (costcenterSubaccount) => costcenterSubaccount.company,
  )
  costcenterSubaccounts?: CostcenterSubaccountEntity[];

  @ManyToMany(() => StaffEntity, (staff) => staff.companies)
  staffs: StaffEntity[];

  @OneToMany(
    () => InventoryStandardEntity,
    (inventoryStandard) => inventoryStandard.company,
  )
  inventoryStandards?: InventoryStandardEntity[];

  @OneToMany(() => MaterialEntity, (material) => material.company)
  materials?: MaterialEntity[];

  @ManyToMany(() => ConditionDetailEntity, (condition) => condition.companies)
  conditions?: ConditionDetailEntity[];

  @OneToMany(() => BusinessUnitEntity, (businessUnit) => businessUnit.company)
  businessUnits?: BusinessUnitEntity[];

  @OneToMany(() => SapPurchaseOrderEntity, (sapPO) => sapPO.company)
  sapPurchaseOrders?: SapPurchaseOrderEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
