import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import {
  EPurchaseOrderStatus,
  EPurchaseOrderTypeForm,
} from '../../domain/config/enums/purchasing.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { SapPurchaseOrderEntity } from './sap_purchase_order.entity';

@Entity('purchase_order_type')
export class PurchaseOrderTypeEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({ type: 'varchar' })
  description: string;

  @Column({ type: 'varchar' })
  searchValue: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EPurchaseOrderStatus,
    default: EPurchaseOrderStatus.ACTIVE,
  })
  status: EPurchaseOrderStatus; //Trạng thái

  @Column({
    name: 'form',
    type: 'enum',
    nullable: true,
    enum: EPurchaseOrderTypeForm,
    default: EPurchaseOrderTypeForm.NORMAL,
  })
  form: EPurchaseOrderTypeForm; //Hình thức

  @ManyToMany(() => ConditionDetailEntity, (condition) => condition.poTypes)
  conditions?: ConditionDetailEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.typePo)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(() => SapPurchaseOrderEntity, (sapPO) => sapPO.poType)
  sapPurchaseOrders?: SapPurchaseOrderEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
