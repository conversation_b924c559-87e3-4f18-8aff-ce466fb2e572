import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
  JoinColumn,
  DeleteDateColumn,
} from 'typeorm';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { MaterialEntity } from './material.entity';
import { MaterialGroupEntity } from './material-group.entity';
import { BudgetEntity } from './budget.entity';
import { MeasureEntity } from './measure.entity';
import { WarehouseEntity } from './warehouse.entity';

@Entity('purchase_request_details')
export class PurchaseRequestDetailEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'budget_code_id', type: 'uuid', nullable: true })
  @Index()
  budgetCodeId?: string;

  @ManyToOne(
    () => BudgetCodeEntity,
    (budgetCode) => budgetCode.purchaseRequestDetails,
  )
  @JoinColumn({ name: 'budget_code_id' })
  budgetCode?: BudgetCodeEntity;

  @Column({ name: 'cost_center_id', type: 'uuid', nullable: true })
  @Index()
  costCenterId?: string;

  @ManyToOne(
    () => CostcenterSubaccountEntity,
    (costCenter) => costCenter.purchaseRequestDetails,
  )
  @JoinColumn({ name: 'cost_center_id' })
  costCenter?: CostcenterSubaccountEntity;

  @Column({ name: 'material_code_id', type: 'uuid', nullable: true })
  @Index()
  materialCodeId: string;

  @ManyToOne(
    () => MaterialEntity,
    (material) => material.purchaseRequestDetails,
  )
  @JoinColumn({ name: 'material_code_id' })
  material?: MaterialEntity;

  @Column({ name: 'material_name', type: 'varchar', nullable: true })
  materialName: string;

  @Column({ name: 'material_group_id', type: 'uuid', nullable: true })
  @Index()
  materialGroupId?: string; //uuid

  @ManyToOne(
    () => MaterialGroupEntity,
    (materialGroup) => materialGroup.purchaseRequestDetails,
  )
  @JoinColumn({ name: 'material_group_id' })
  materialGroup?: MaterialGroupEntity;

  @Column({ type: 'varchar', nullable: true })
  materialGroupName?: string; //text

  @Column({ name: 'quantity', type: 'decimal', nullable: true })
  quantity: number;

  @Column({ name: 'unit', type: 'varchar', nullable: true })
  unit?: string;

  @Column({ name: 'unit_price', type: 'decimal', nullable: true })
  unitPrice?: number;

  @Column({ name: 'note', type: 'character varying', nullable: true })
  note?: string;

  @Column({ type: 'date' })
  deliveryTime: Date;

  @Column({ type: 'decimal' })
  totalAmount: number;

  @Column({ name: 'budget', type: 'decimal', nullable: true })
  budget?: number;

  @Column({ name: 'remaining_budget', type: 'decimal', nullable: true })
  remainingBudget?: number; //Ngân sách dự chi còn lại => Ngân sách còn lại

  @Column({ name: 'remaining_actual_budget', type: 'decimal', nullable: true })
  remainingActualBudget?: number; //Ngân sách thực chi còn lại

  @Column({ name: 'estimated_price', type: 'decimal', nullable: false })
  estimatedPrice: number;

  @Column({ name: 'standard_quantity', type: 'decimal', nullable: true })
  standardQuantity?: number;

  @Column({ name: 'inventory_number', type: 'decimal', nullable: true })
  inventoryNumber?: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({
    nullable: true,
  })
  deletedAt?: Date;

  @ManyToOne(
    () => PurchaseRequestEntity,
    (purchaseRequest) => purchaseRequest.details,
    { onDelete: 'CASCADE' },
  )
  @Index()
  purchaseRequest?: PurchaseRequestEntity;

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.prDetail, {
    cascade: true,
  })
  poDetails?: PurchaseOrderDetailEntity[];

  // Lưu vết khi tính toán remaining budget
  @Column({ name: 'budget_id', type: 'uuid', nullable: true })
  @Index()
  budgetId?: string;

  @ManyToOne(
    () => BudgetEntity,
    (budgetData) => budgetData.purchaseRequestDetailBudgets,
  )
  @JoinColumn({ name: 'budget_id' })
  budgetData?: BudgetEntity;

  @Column({ name: 'adjust_budget_id', type: 'uuid', nullable: true })
  @Index()
  adjustBudgetId?: string;

  @ManyToOne(
    () => BudgetEntity,
    (budgetData) => budgetData.purchaseRequestDetailAdjustBudgets,
  )
  @JoinColumn({ name: 'adjust_budget_id' })
  adjustBudget?: BudgetEntity;

  @Column({ name: 'measure_id', type: 'uuid', nullable: true })
  @Index()
  measureId?: string;

  @ManyToOne(() => MeasureEntity, (measure) => measure.purchaseRequestDetails)
  @JoinColumn({ name: 'measure_id' })
  measure?: MeasureEntity;

  @Column({ name: 'warehouse_id', type: 'uuid', nullable: true })
  warehouseId?: string;

  @ManyToOne(
    () => WarehouseEntity,
    (warehouse) => warehouse.purchaseRequestDetails,
  )
  @JoinColumn({ name: 'warehouse_id' })
  warehouse?: WarehouseEntity;

  numberPoCreated?: number;
}
