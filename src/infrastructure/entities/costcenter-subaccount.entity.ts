import {
  Column,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { BudgetEntity } from './budget.entity';
import { ECostcenterSubaccountStatus } from '../../domain/config/enums/costcenter-subaccount.enum';
import { SectorEntity } from './sector.entity';
import { CompanyEntity } from './company.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { DepartmentEntity } from './department.entity';
import { CostSubHistoryEntity } from './cost-sub-history.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { SapPurchaseOrderItemEntity } from './sap_purchase_order_item.entity';
import { FunctionUnitEntity } from './function-unit.entity';

@Entity({ name: 'costcenter_subaccount' })
export class CostcenterSubaccountEntity extends BaseEntity {
  @Column({ name: 'name', type: 'varchar', nullable: false, default: '' })
  name: string; //Tên

  @Column({ name: 'code', type: 'varchar', nullable: false })
  code: string; //ID

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description: string; //Mô tả

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: ECostcenterSubaccountStatus,
    default: ECostcenterSubaccountStatus.ACTIVE,
  })
  status: ECostcenterSubaccountStatus; //Trạng thái

  @Column({ name: 'sector_id', type: 'uuid', nullable: false })
  sectorId: string; //Mã mảng

  @Column({ name: 'company_id', type: 'uuid', nullable: true })
  companyId: string; //Mã công ty

  @Column({ name: 'business_unit_id', type: 'uuid', nullable: true })
  businessUnitId: string; //Mã đơn vị kinh doanh

  @Column({ name: 'department_id', type: 'uuid', nullable: true })
  departmentId: string; //Mã phòng ban

  @OneToMany(() => BudgetEntity, (budget) => budget.costcenterSubaccount)
  budgets?: BudgetEntity[];

  @ManyToOne(() => SectorEntity, (sector) => sector.costcenterSubaccounts)
  sector?: SectorEntity;

  @ManyToOne(() => CompanyEntity, (company) => company.costcenterSubaccounts)
  company?: CompanyEntity;

  @ManyToOne(
    () => BusinessUnitEntity,
    (businessUnit) => businessUnit.costcenterSubaccounts,
  )
  businessUnit?: BusinessUnitEntity;

  @ManyToOne(
    () => DepartmentEntity,
    (department) => department.costcenterSubaccounts,
  )
  department?: DepartmentEntity;

  @OneToMany(
    () => CostSubHistoryEntity,
    (history) => history.costcenterSubaccount,
  )
  histories?: CostSubHistoryEntity[];

  // @OneToMany(
  //   () => BudgetCodeEntity,
  //   (budgetCode) => budgetCode.costcenterSubaccount,
  // )
  // budgetCodes: BudgetCodeEntity[];

  @ManyToMany(() => ConditionDetailEntity, (condition) => condition.costCenters)
  conditions?: ConditionDetailEntity[];

  @Column({ name: 'note1', type: 'varchar', nullable: true })
  note1: string; //Ghi chú 1

  @Column({ name: 'note2', type: 'varchar', nullable: true })
  note2: string; //Ghi chú 2

  @Column({ name: 'note3', type: 'varchar', nullable: true })
  note3: string; //Ghi chú 3

  @Column({ name: 'note4', type: 'varchar', nullable: true })
  note4: string; //Ghi chú 4

  @Column({ name: 'note5', type: 'varchar', nullable: true })
  note5: string; //Ghi chú 5

  @Column({ name: 'note6', type: 'varchar', nullable: true })
  note6: string; //Ghi chú 6

  @Column({ name: 'effective_start_date', type: 'date', nullable: true })
  effectiveStartDate: Date; //Thời gian có hiệu lực

  @Column({ name: 'effective_end_date', type: 'date', nullable: true })
  effectiveEndDate?: Date; //Thời gian hết hiệu lực

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.costCenter)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(
    () => PurchaseRequestDetailEntity,
    (prDetail) => prDetail.costCenter,
  )
  purchaseRequestDetails?: PurchaseRequestDetailEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.costCenter)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.costCenter)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(
    () => SapPurchaseOrderItemEntity,
    (sapPOItem) => sapPOItem.costCenter,
  )
  sapPurchaseOrderItems?: SapPurchaseOrderItemEntity[];

  @ManyToMany(() => FunctionUnitEntity, (fu) => fu.costCenters)
  @JoinTable({
    name: 'costcenter_function_units',
    joinColumns: [{ name: 'costcenter_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'function_unit_id', referencedColumnName: 'id' },
    ],
  })
  functionUnits?: FunctionUnitEntity[];
}
