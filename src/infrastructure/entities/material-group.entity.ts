import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { EMaterialGroupStatus } from '../../domain/config/enums/material.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { BusinessOwnerEntity } from './business-owner.entity';
import { MaterialEntity } from './material.entity';
import { ProcessTypeEntity } from './process-type.entity';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { SapPurchaseOrderItemEntity } from './sap_purchase_order_item.entity';

@Entity('material_groups')
export class MaterialGroupEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EMaterialGroupStatus,
    default: EMaterialGroupStatus.ACTIVE,
  })
  status: EMaterialGroupStatus; //Trạng thái

  @OneToMany(() => MaterialEntity, (material) => material.materialGroup)
  materials?: MaterialEntity[];

  @ManyToMany(
    () => ProcessTypeEntity,
    (processType) => processType.materialGroups,
  )
  @JoinTable({
    name: 'process_type_material_groups',
    joinColumns: [{ name: 'material_group_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'process_type_id', referencedColumnName: 'id' },
    ],
  })
  processTypes?: ProcessTypeEntity[];

  @ManyToMany(() => BusinessOwnerEntity, (bo) => bo.materialGroups)
  @JoinTable({
    name: 'business_owner_material_groups',
    joinColumns: [{ name: 'material_group_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'business_owner_id', referencedColumnName: 'id' },
    ],
  })
  businessOwners?: BusinessOwnerEntity[];

  @OneToMany(
    () => PurchaseRequestDetailEntity,
    (prDetail) => prDetail.materialGroup,
  )
  purchaseRequestDetails?: PurchaseRequestDetailEntity[];

  @OneToMany(
    () => PurchaseOrderDetailEntity,
    (poDetail) => poDetail.materialGroup,
  )
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(
    () => SapPurchaseOrderItemEntity,
    (sapPOItem) => sapPOItem.materialGroup,
  )
  sapPurchaseOrderItems?: SapPurchaseOrderItemEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
