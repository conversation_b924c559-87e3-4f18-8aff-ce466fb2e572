import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { EStatus } from '../../domain/config/enums/status.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { BusinessOwnerEntity } from './business-owner.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { CompanyEntity } from './company.entity';
import { DepartmentEntity } from './department.entity';
import { FunctionUnitEntity } from './function-unit.entity';
import { PositionEntity } from './position.entity';
import { SectorEntity } from './sector.entity';
import { StaffApprovalWorkflowEntity } from './staff-approval-workflow.entity';
import { StaffHierarchyEntity } from './staff-hierarchy.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { ApprovalProcessDetailEntity } from './approval-process-detail.entity';

@Entity('staffs')
export class StaffEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar', nullable: true })
  firstName: string;

  @Column({ type: 'varchar', nullable: true })
  lastName: string;

  @Column({ name: 'email', type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  email: string;

  @Column({ name: 'phone', type: 'varchar' })
  phone: string;

  @Column({ type: 'varchar', default: EStatus.ACTIVE, nullable: true })
  status: EStatus;

  @Column({ name: 'po_creater_id', type: 'uuid', nullable: true })
  poCreatorId: string; //Người tạo PO

  @Column({ name: 'purchaser_id', type: 'uuid', nullable: true })
  purchaserId: string; //Người mua hàng

  @ManyToMany(() => SectorEntity, (sector) => sector.staffs)
  @JoinTable({
    name: 'staff_sectors',
    joinColumns: [{ name: 'staff_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'sector_id', referencedColumnName: 'id' }],
  })
  sectors?: SectorEntity[];

  @ManyToMany(() => CompanyEntity, (company) => company.staffs)
  @JoinTable({
    name: 'staff_companies',
    joinColumns: [{ name: 'staff_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'company_id', referencedColumnName: 'id' }],
  })
  companies?: CompanyEntity[];

  @ManyToMany(() => BusinessUnitEntity, (businessUnit) => businessUnit.staffs)
  @JoinTable({
    name: 'staff_business_units',
    joinColumns: [{ name: 'staff_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'business_unit_id', referencedColumnName: 'id' },
    ],
  })
  businessUnits?: BusinessUnitEntity[];

  @ManyToMany(() => DepartmentEntity, (department) => department.staffs)
  @JoinTable({
    name: 'staff_departments',
    joinColumns: [{ name: 'staff_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'department_id', referencedColumnName: 'id' }],
  })
  departments?: DepartmentEntity[];

  @ManyToMany(() => FunctionUnitEntity, (functionUnit) => functionUnit.staffs)
  @JoinTable({
    name: 'staff_function_units',
    joinColumns: [{ name: 'staff_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'function_unit_id', referencedColumnName: 'id' },
    ],
  })
  functionUnits?: FunctionUnitEntity[];

  @ManyToMany(
    () => BusinessOwnerEntity,
    (businessOwner) => businessOwner.staffs,
  )
  @JoinTable({
    name: 'staff_business_owners',
    joinColumns: [{ name: 'staff_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'business_owner_id', referencedColumnName: 'id' },
    ],
  })
  businessOwners?: BusinessOwnerEntity[];

  @ManyToOne(() => PositionEntity, (position) => position.staffs)
  position?: PositionEntity | null;

  @OneToMany(
    () => StaffHierarchyEntity,
    (staffHierarchy) => staffHierarchy.manager,
  )
  subordinateHierarchies?: StaffHierarchyEntity[];

  @OneToMany(
    () => StaffHierarchyEntity,
    (staffHierarchy) => staffHierarchy.subordinate,
  )
  managerHierarchies?: StaffHierarchyEntity[];

  @OneToMany(() => StaffEntity, (staff) => staff.poCreator)
  poStaffs: StaffEntity[];

  @ManyToOne(() => StaffEntity, (staff) => staff.poStaffs)
  @JoinColumn({ name: 'po_creater_id' })
  poCreator?: StaffEntity;

  @OneToMany(() => StaffEntity, (staff) => staff.purchaser)
  purchaserStaffs?: StaffEntity[];

  @ManyToOne(() => StaffEntity, (staff) => staff.purchaserStaffs)
  @JoinColumn({ name: 'purchaser_id' })
  purchaser?: StaffEntity;

  //Lưu nhiều người mua hàng
  @ManyToMany(() => StaffEntity, (staff) => staff.staffPurchasers)
  purchasers?: StaffEntity[];

  @ManyToMany(() => StaffEntity, (staff) => staff.purchasers)
  @JoinTable({
    name: 'staff_purchasers',
    joinColumns: [{ name: 'staff_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'purchaser_id', referencedColumnName: 'id' }],
  })
  staffPurchasers?: StaffEntity[];

  @OneToMany(
    () => StaffApprovalWorkflowEntity,
    (staffApprovalWorkflow) => staffApprovalWorkflow.staff,
  )
  staffApprovalWorkflows?: StaffApprovalWorkflowEntity[];

  @ManyToMany(
    () => StaffApprovalWorkflowEntity,
    (staffApprovalWorkflow) => staffApprovalWorkflow.selectedStaffs,
  )
  staffSelectedStaffApprovalWorkflows?: StaffApprovalWorkflowEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.requester)
  purchaseRequestRequesters?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.purchaser)
  purchaseRequestPurchasers?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.requester)
  purchaseOrderRequesters?: PurchaseOrderEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.purchaser)
  purchaseOrderPurchasers?: PurchaseOrderEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prCreatedBy,
  )
  prCreatedByApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.poCreatedBy,
  )
  poCreatedByApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prApprover1,
  )
  prApprover1ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prApprover2,
  )
  prApprover2ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prApprover3,
  )
  prApprover3ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prApprover4,
  )
  prApprover4ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prApprover5,
  )
  prApprover5ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prApprover6,
  )
  prApprover6ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prApprover7,
  )
  prApprover7ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.poApprover1,
  )
  poApprover1ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.poApprover2,
  )
  poApprover2ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.poApprover3,
  )
  poApprover3ApprovalProcessDetails: ApprovalProcessDetailEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    if (this.firstName && this.lastName && this.email) {
      this.email = this.email.trim().toLowerCase();
      this.searchValue =
        removeUnicode(this.firstName) +
        ' ' +
        removeUnicode(this.lastName) +
        ' ' +
        removeUnicode(this.firstName) +
        ' ' +
        removeUnicode(this.lastName) +
        ' ' +
        removeUnicode(this.email);
    }
  }
}
