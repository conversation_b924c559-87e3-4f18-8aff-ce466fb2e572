import { Column, Entity, ManyToOne } from 'typeorm';
import { EMaterialStatus } from '../../domain/config/enums/material.enum';
import { BaseEntity } from './base.entity';
import { MaterialEntity } from './material.entity';
import { SectorEntity } from './sector.entity';

@Entity('material_sectors')
export class MaterialSectorEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: false })
  codeSAP: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EMaterialStatus,
    default: EMaterialStatus.ACTIVE,
  })
  status: EMaterialStatus; //Trạng thái

  @ManyToOne(() => MaterialEntity, (material) => material.industries)
  material?: MaterialEntity;

  @ManyToOne(() => SectorEntity, (sector) => sector.industriesMaterial)
  sector?: SectorEntity;
}
