import { AfterLoad, AfterUpdate, Column, Entity } from 'typeorm';
import { BaseEntity } from './base.entity';
import { ECodeType } from '../../domain/config/enums/code-type.enum';

@Entity({ name: 'increasement_code' })
export class IncreasementCodeEntity extends BaseEntity {
  @Column({ name: 'sequencing', type: 'numeric', nullable: false })
  sequencing: number; //Thứ tự

  @Column({
    name: 'code_type',
    type: 'varchar',
    nullable: false,
    enum: ECodeType,
  })
  codeType: string; //Mã code cho Module nào

  @AfterLoad()
  afterLoad() {
    if (this.sequencing) {
      this.sequencing = Number(this.sequencing);
    }
  }

  @AfterUpdate()
  afterUpdate() {
    if (this.sequencing) {
      this.sequencing = Number(this.sequencing);
    }
  }
}
