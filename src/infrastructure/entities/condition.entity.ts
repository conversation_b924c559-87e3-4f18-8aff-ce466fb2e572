import {
  Column,
  Entity,
  Index,
  JoinColumn,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { ProcessConditionEntity } from './process-condition.entity';

@Entity('conditions')
export class ConditionEntity extends BaseEntity {
  @OneToOne(() => ProcessConditionEntity, { cascade: true })
  @JoinColumn({ name: 'process_condition_id' })
  processCondition?: ProcessConditionEntity;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  @Index({ where: 'deleted_at IS NULL' })
  processConditionId?: string;

  @OneToMany(
    () => ConditionDetailEntity,
    (conditionDetails) => conditionDetails.condition,
  )
  conditionDetails?: ConditionDetailEntity[];
}
