import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { EBusinessUnitStatus } from '../../domain/config/enums/business-unit.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { InventoryStandardEntity } from './inventory-standard.entity';
import { MaterialEntity } from './material.entity';
import { StaffEntity } from './staff.entity';
import { CompanyEntity } from './company.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { SapPurchaseOrderItemEntity } from './sap_purchase_order_item.entity';
import { PriceInformationRecordEntity } from './price_information_record.entity';
import { ApprovalProcessDetailEntity } from './approval-process-detail.entity';

@Entity('business_unit')
export class BusinessUnitEntity extends BaseEntity {
  @Column({ name: 'code', type: 'varchar', nullable: false })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string; //ID

  @Column({ name: 'name', type: 'varchar', nullable: false })
  name: string; //Tên Đơn vị kinh doanh

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description: string; //Mô tả

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EBusinessUnitStatus,
    default: EBusinessUnitStatus.ACTIVE,
  })
  status: EBusinessUnitStatus; //Trạng thái

  @OneToMany(
    () => CostcenterSubaccountEntity,
    (costcenterSubaccount) => costcenterSubaccount.businessUnit,
  )
  costcenterSubaccounts?: CostcenterSubaccountEntity[];

  @ManyToMany(() => StaffEntity, (staff) => staff.businessUnits)
  staffs: StaffEntity[];

  @OneToMany(
    () => InventoryStandardEntity,
    (inventoryStandard) => inventoryStandard.businessUnit,
  )
  inventoryStandards?: InventoryStandardEntity[];

  @ManyToMany(() => MaterialEntity, (material) => material.businessUnits)
  materials?: MaterialEntity[];

  @ManyToMany(
    () => ConditionDetailEntity,
    (condition) => condition.businessUnits,
  )
  conditions?: ConditionDetailEntity[];

  @ManyToOne(() => CompanyEntity, (company) => company.businessUnits)
  company?: CompanyEntity;

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.businessUnit)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.businessUnit)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(
    () => SapPurchaseOrderItemEntity,
    (sapPOItem) => sapPOItem.businessUnit,
  )
  sapPurchaseOrderItems?: SapPurchaseOrderItemEntity[];

  @ManyToMany(() => PriceInformationRecordEntity, (pir) => pir.businessUnits)
  pirs?: PriceInformationRecordEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.businessUnit,
  )
  approvalProcessDetails: ApprovalProcessDetailEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  updateSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
