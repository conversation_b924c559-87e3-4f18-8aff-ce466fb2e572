import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { SectorEntity } from './sector.entity';
import { EPlantStatus } from '../../domain/config/enums/plant.enum';
import { ConditionDetailEntity } from './condition-detail.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { PriceInformationRecordEntity } from './price_information_record.entity';
import { ApprovalProcessDetailEntity } from './approval-process-detail.entity';

@Entity('plants')
export class PlantEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ name: 'sector_id', type: 'uuid', nullable: false })
  sectorId: string; //Nhóm vật tư

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EPlantStatus,
    default: EPlantStatus.ACTIVE,
  })
  status: EPlantStatus; //Trạng thái

  @ManyToOne(() => SectorEntity, (sector) => sector.plants)
  sector?: SectorEntity;

  @ManyToMany(
    () => ConditionDetailEntity,
    (conditionDetail) => conditionDetail.plants,
  )
  conditionDetails?: ConditionDetailEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.plant)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.plant)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(() => PriceInformationRecordEntity, (pir) => pir.plant)
  pirs?: PriceInformationRecordEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
