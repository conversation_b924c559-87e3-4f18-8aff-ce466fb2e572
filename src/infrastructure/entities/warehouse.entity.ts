import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { EWarehouseStatus } from '../../domain/config/enums/warehouse.enum';
import { SectorEntity } from './sector.entity';
import { removeUnicode } from '../../utils/common';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { SolomonPurchaseOrderItemEntity } from './solomon-purchase-order-item.entity';

@Entity('warehouses')
export class WarehouseEntity extends BaseEntity {
  @Column({ name: 'code', type: 'varchar', nullable: false })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ name: 'name', type: 'varchar', nullable: false })
  name: string;

  @ManyToMany(() => SectorEntity, (sector) => sector.warehouses, {
    cascade: true,
  })
  @JoinTable({
    name: 'warehouse_sectors',
    joinColumns: [{ name: 'warehouse_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'sector_id', referencedColumnName: 'id' }],
  })
  sectors?: SectorEntity[];

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description?: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EWarehouseStatus,
    default: EWarehouseStatus.ACTIVE,
  })
  status?: EWarehouseStatus; //Trạng thái

  @OneToMany(
    () => PurchaseRequestDetailEntity,
    (prDetail) => prDetail.warehouse,
  )
  purchaseRequestDetails?: PurchaseRequestDetailEntity[];

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.warehouse)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(
    () => SolomonPurchaseOrderItemEntity,
    (solomonPOItem) => solomonPOItem.warehouse,
  )
  solomonPurchaseOrderItems?: SolomonPurchaseOrderItemEntity[];

  totalPrDetails?: number;
  totalPoDetails?: number;

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
