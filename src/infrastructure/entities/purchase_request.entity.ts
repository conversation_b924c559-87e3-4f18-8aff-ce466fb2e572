import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';

import { ApprovalLevelEntity } from './approval-level.entity';
import { HistoryApproveEntity } from './history-approve.entity';
import {
  Priority,
  State,
  Status,
} from '../../domain/config/enums/purchase-request.enum';
import { SectorEntity } from './sector.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { StaffEntity } from './staff.entity';
import { PurchaseRequestTypeEntity } from './purchase-request-type.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { PurchasingDepartmentEntity } from './purchasing-department.entity';
import { PurchasingGroupEntity } from './purchasing-group.entity';
import { ProcessTypeEntity } from './process-type.entity';
import { PlantEntity } from './plant.entity';
import { FunctionUnitEntity } from './function-unit.entity';
import { DepartmentEntity } from './department.entity';

@Entity('purchase_requests')
export class PurchaseRequestEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'sector_id', type: 'uuid', nullable: true })
  @Index()
  sectorId: string;

  @ManyToOne(() => SectorEntity, (sector) => sector.purchaseRequests)
  @JoinColumn({ name: 'sector_id' })
  sector?: SectorEntity;

  @Column({ name: 'business_unit_id', type: 'uuid', nullable: true })
  @Index()
  businessUnitId: string;

  @ManyToOne(
    () => BusinessUnitEntity,
    (businessUnit) => businessUnit.purchaseRequests,
  )
  @JoinColumn({ name: 'business_unit_id' })
  businessUnit?: BusinessUnitEntity;

  @Column({ name: 'is_check_budget', type: 'boolean', nullable: true })
  isCheckBudget: boolean;

  @Column({ name: 'requester_id', type: 'uuid', nullable: true })
  @Index()
  requesterId?: string;

  @ManyToOne(() => StaffEntity, (staff) => staff.purchaseRequestRequesters)
  @JoinColumn({ name: 'requester_id' })
  requester?: StaffEntity;

  @Column({ name: 'type_pr_id', type: 'uuid', nullable: false })
  @Index()
  typePrId: string;

  @ManyToOne(
    () => PurchaseRequestTypeEntity,
    (prType) => prType.purchaseRequests,
  )
  @JoinColumn({ name: 'type_pr_id' })
  typePr?: PurchaseRequestTypeEntity;

  @Column({ type: 'enum', enum: Status, default: Status.Pending })
  statusPr: Status;

  @Column({ type: 'enum', enum: State, default: State.Pending, nullable: true })
  statePr: State;

  @Column({ name: 'budget_code_id', type: 'uuid', nullable: true })
  @Index()
  budgetCodeId?: string;

  @ManyToOne(
    () => BudgetCodeEntity,
    (budgetCode) => budgetCode.purchaseRequests,
  )
  @JoinColumn({ name: 'budget_code_id' })
  budgetCode?: BudgetCodeEntity;

  @Column({ name: 'cost_center_id', type: 'uuid', nullable: true })
  @Index()
  costCenterId?: string;

  @ManyToOne(
    () => CostcenterSubaccountEntity,
    (costCenter) => costCenter.purchaseRequests,
  )
  @JoinColumn({ name: 'cost_center_id' })
  costCenter?: CostcenterSubaccountEntity;

  @Column({ name: 'purchase_org_id', type: 'uuid', nullable: true })
  @Index()
  purchaseOrgId?: string;

  @ManyToOne(
    () => PurchasingDepartmentEntity,
    (purchaseOrg) => purchaseOrg.purchaseRequests,
  )
  @JoinColumn({ name: 'purchase_org_id' })
  purchaseOrg?: PurchasingDepartmentEntity;

  @Column({ name: 'purchase_group_id', type: 'uuid', nullable: true })
  @Index()
  purchaseGroupId?: string;

  @ManyToOne(
    () => PurchasingGroupEntity,
    (purchaseGroup) => purchaseGroup.purchaseRequests,
  )
  @JoinColumn({ name: 'purchase_group_id' })
  purchaseGroup?: PurchasingGroupEntity;

  @Column({ type: 'enum', enum: Priority, nullable: true })
  priority: Priority;

  @Column({ name: 'reason', type: 'varchar', nullable: true })
  reason?: string;

  @Column({ name: 'is_po', type: 'boolean', nullable: true })
  isPo?: boolean;

  @Column({
    name: 'files',
    type: 'simple-array',
    nullable: true,
    default: [],
  })
  attachments: string[];

  @Column({ nullable: true })
  refId?: string;

  @Column({ name: 'account_gl', type: 'varchar', nullable: true })
  accountGl?: string;

  @OneToMany(
    () => PurchaseRequestDetailEntity,
    (detail) => detail.purchaseRequest,
    { cascade: true, orphanedRowAction: 'soft-delete' },
  )
  details?: PurchaseRequestDetailEntity[];

  @OneToMany(
    () => ApprovalLevelEntity,
    (approvalLevel) => approvalLevel.purchaseRequest,
  )
  levels?: ApprovalLevelEntity[];

  @OneToMany(() => HistoryApproveEntity, (history) => history.purchaseRequest, {
    cascade: true,
  })
  history?: HistoryApproveEntity[];

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @Column({ name: 'purchaser_id', type: 'uuid', nullable: true })
  @Index()
  purchaserId?: string;

  @ManyToOne(() => StaffEntity, (staff) => staff.purchaseRequestPurchasers)
  @JoinColumn({ name: 'purchaser_id' })
  purchaser?: StaffEntity;

  @Column({ name: 'is_accountant_approved', type: 'boolean', default: false })
  isAccountantApproved?: boolean;

  @Column({ name: 'process_type_id', type: 'uuid', nullable: true })
  @Index()
  processTypeId?: string;

  @ManyToOne(
    () => ProcessTypeEntity,
    (processType) => processType.purchaseRequests,
  )
  @JoinColumn({ name: 'process_type_id' })
  processType?: ProcessTypeEntity;

  @Column({ name: 'plant_id', type: 'uuid', nullable: true })
  @Index()
  plantId?: string;

  @ManyToOne(() => PlantEntity, (plant) => plant.purchaseRequests)
  @JoinColumn({ name: 'plant_id' })
  plant?: PlantEntity;

  @Column({ name: 'function_unit_id', type: 'uuid', nullable: true })
  @Index()
  functionUnitId?: string;

  @ManyToOne(
    () => FunctionUnitEntity,
    (functionUnit) => functionUnit.purchaseRequests,
  )
  @JoinColumn({ name: 'function_unit_id' })
  functionUnit?: FunctionUnitEntity;

  @Column({ name: 'department_id', type: 'uuid', nullable: true })
  @Index()
  departmentId?: string;

  @ManyToOne(
    () => DepartmentEntity,
    (department) => department.purchaseRequests,
  )
  @JoinColumn({ name: 'department_id' })
  department?: DepartmentEntity;
}
