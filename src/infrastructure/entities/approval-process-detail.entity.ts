import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
} from 'typeorm';
import { BaseEntity } from '../../infrastructure/entities/base.entity';
import { removeUnicode } from '../../utils/common';
import { BusinessUnitEntity } from './business-unit.entity';
import { FunctionUnitEntity } from './function-unit.entity';
import { PlantEntity } from './plant.entity';
import { PurchaseRequestTypeEntity } from './purchase-request-type.entity';
import { SectorEntity } from './sector.entity';
import { StaffEntity } from './staff.entity';
import { DepartmentEntity } from './department.entity';

@Entity('approval_process_details')
export class ApprovalProcessDetailEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({
    name: 'sector_id',
    type: 'uuid',
    nullable: false,
  })
  sectorId: string;

  @ManyToOne(() => SectorEntity, (sector) => sector.approvalProcessDetails)
  @JoinColumn({ name: 'sector_id' })
  sector?: SectorEntity;

  @Column({
    name: 'function_unit_id',
    type: 'uuid',
    nullable: false,
  })
  functionUnitId: string;

  @ManyToOne(
    () => FunctionUnitEntity,
    (functionUnit) => functionUnit.approvalProcessDetails,
  )
  @JoinColumn({ name: 'function_unit_id' })
  functionUnit?: FunctionUnitEntity;

  @Column({
    name: 'department_id',
    type: 'uuid',
    nullable: false,
  })
  departmentId: string;

  @ManyToOne(
    () => DepartmentEntity,
    (department) => department.approvalProcessDetails,
  )
  @JoinColumn({ name: 'department_id' })
  department?: DepartmentEntity;

  @Column({
    name: 'business_unit_id',
    type: 'uuid',
    nullable: false,
  })
  businessUnitId: string;

  @ManyToOne(
    () => BusinessUnitEntity,
    (businessUnit) => businessUnit.approvalProcessDetails,
  )
  @JoinColumn({ name: 'business_unit_id' })
  businessUnit?: BusinessUnitEntity;

  @Column({
    name: 'pr_created_by_id',
    type: 'uuid',
    nullable: false,
  })
  prCreatedById: string;

  @ManyToOne(
    () => StaffEntity,
    (prCreatedBy) => prCreatedBy.prCreatedByApprovalProcessDetails,
  )
  @JoinColumn({ name: 'pr_created_by_id' })
  prCreatedBy?: StaffEntity;

  @Column({
    name: 'po_created_by_id',
    type: 'uuid',
    nullable: false,
  })
  poCreatedById: string;

  @ManyToOne(
    () => StaffEntity,
    (poCreatedBy) => poCreatedBy.poCreatedByApprovalProcessDetails,
  )
  @JoinColumn({ name: 'po_created_by_id' })
  poCreatedBy?: StaffEntity;

  @Column({
    name: 'pr_approver_1_id',
    type: 'uuid',
    nullable: false,
  })
  prApprover1Id: string;

  @ManyToOne(
    () => StaffEntity,
    (prApprover1) => prApprover1.prApprover1ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'pr_approver_1_id' })
  prApprover1?: StaffEntity;

  @Column({
    name: 'pr_approver_2_id',
    type: 'uuid',
    nullable: true,
  })
  prApprover2Id?: string;

  @ManyToOne(
    () => StaffEntity,
    (prApprover2) => prApprover2.prApprover2ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'pr_approver_2_id' })
  prApprover2?: StaffEntity;

  @Column({
    name: 'pr_approver_3_id',
    type: 'uuid',
    nullable: true,
  })
  prApprover3Id?: string;

  @ManyToOne(
    () => StaffEntity,
    (prApprover3) => prApprover3.prApprover3ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'pr_approver_3_id' })
  prApprover3?: StaffEntity;

  @Column({
    name: 'pr_approver_4_id',
    type: 'uuid',
    nullable: true,
  })
  prApprover4Id?: string;

  @ManyToOne(
    () => StaffEntity,
    (prApprover4) => prApprover4.prApprover4ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'pr_approver_4_id' })
  prApprover4?: StaffEntity;

  @Column({
    name: 'pr_approver_5_id',
    type: 'uuid',
    nullable: true,
  })
  prApprover5Id?: string;

  @ManyToOne(
    () => StaffEntity,
    (prApprover5) => prApprover5.prApprover5ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'pr_approver_5_id' })
  prApprover5?: StaffEntity;

  @Column({
    name: 'pr_approver_6_id',
    type: 'uuid',
    nullable: true,
  })
  prApprover6Id?: string;

  @ManyToOne(
    () => StaffEntity,
    (prApprover6) => prApprover6.prApprover6ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'pr_approver_6_id' })
  prApprover6?: StaffEntity;

  @Column({
    name: 'pr_approver_7_id',
    type: 'uuid',
    nullable: true,
  })
  prApprover7Id?: string;

  @ManyToOne(
    () => StaffEntity,
    (prApprover7) => prApprover7.prApprover7ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'pr_approver_7_id' })
  prApprover7?: StaffEntity;

  @Column({
    name: 'po_approver_1_id',
    type: 'uuid',
    nullable: false,
  })
  poApprover1Id: string;

  @ManyToOne(
    () => StaffEntity,
    (poApprover1) => poApprover1.poApprover1ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'po_approver_1_id' })
  poApprover1?: StaffEntity;

  @Column({
    name: 'po_approver_2_id',
    type: 'uuid',
    nullable: true,
  })
  poApprover2Id?: string;

  @ManyToOne(
    () => StaffEntity,
    (poApprover2) => poApprover2.poApprover2ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'po_approver_2_id' })
  poApprover2?: StaffEntity;

  @Column({
    name: 'po_approver_3_id',
    type: 'uuid',
    nullable: true,
  })
  poApprover3Id?: string;

  @ManyToOne(
    () => StaffEntity,
    (poApprover3) => poApprover3.poApprover3ApprovalProcessDetails,
  )
  @JoinColumn({ name: 'po_approver_3_id' })
  poApprover3?: StaffEntity;

  @ManyToMany(
    () => PurchaseRequestTypeEntity,
    (prType) => prType.approvalProcessDetails,
  )
  @JoinTable({
    name: 'approval_process_detail_pr_type',
    joinColumns: [
      { name: 'approval_process_detail_id', referencedColumnName: 'id' },
    ],
    inverseJoinColumns: [
      { name: 'purchase_request_type_id', referencedColumnName: 'id' },
    ],
  })
  prTypes?: PurchaseRequestTypeEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue = removeUnicode(this.name);
  }
}
