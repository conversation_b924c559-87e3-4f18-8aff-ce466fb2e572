import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import {
  EDisplayStatus,
  EPaymentMethod,
  Priority,
  State,
  Status,
} from '../../domain/config/enums/purchase-order.enum';
import { ApprovalLevelEntity } from './approval-level.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { CurrencyUnitEntity } from './currency-unit.entity';
import { DepartmentEntity } from './department.entity';
import { FunctionUnitEntity } from './function-unit.entity';
import { HistoryApproveEntity } from './history-approve.entity';
import { PlantEntity } from './plant.entity';
import { ProcessTypeEntity } from './process-type.entity';
import { PurchaseOrderTypeEntity } from './purchase-order-type.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { PurchasingDepartmentEntity } from './purchasing-department.entity';
import { PurchasingGroupEntity } from './purchasing-group.entity';
import { SapPurchaseOrderEntity } from './sap_purchase_order.entity';
import { SectorEntity } from './sector.entity';
import { StaffEntity } from './staff.entity';
import { ReceiptSolomonEntity } from './receipt-solomon.entity';
import { SolomonPurchaseOrderEntity } from './solomon-purchase-order.entity';

@Entity('purchase_orders')
export class PurchaseOrderEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'sector_id', type: 'uuid', nullable: true })
  @Index()
  sectorId: string;

  @ManyToOne(() => SectorEntity, (sector) => sector.purchaseOrders)
  @JoinColumn({ name: 'sector_id' })
  sector?: SectorEntity;

  @Column({ name: 'business_unit_id', type: 'uuid', nullable: true })
  businessUnitId: string;

  @ManyToOne(
    () => BusinessUnitEntity,
    (businessUnit) => businessUnit.purchaseOrders,
  )
  @JoinColumn({ name: 'business_unit_id' })
  businessUnit?: BusinessUnitEntity;

  @Column({ name: 'is_check_budget', type: 'boolean', nullable: true })
  isCheckBudget: boolean;

  @Column({ name: 'requester_id', type: 'uuid', nullable: true })
  @Index()
  requesterId?: string;

  @ManyToOne(
    () => StaffEntity,
    (requester) => requester.purchaseOrderRequesters,
  )
  @JoinColumn({ name: 'requester_id' })
  requester?: StaffEntity;

  @Column({ name: 'type_po_id', type: 'uuid', nullable: false })
  @Index()
  typePoId: string;

  @ManyToOne(() => PurchaseOrderTypeEntity, (typePo) => typePo.purchaseOrders)
  @JoinColumn({ name: 'type_po_id' })
  typePo?: PurchaseOrderTypeEntity;

  @Column({ type: 'enum', enum: Status, default: Status.Pending })
  statusPo: Status;

  @Column({ type: 'enum', enum: State, default: State.Pending })
  statePo: State;

  @Column({ name: 'cost_center_id', type: 'uuid', nullable: true })
  @Index()
  costCenterId?: string;

  @ManyToOne(
    () => CostcenterSubaccountEntity,
    (costCenter) => costCenter.purchaseOrders,
  )
  @JoinColumn({ name: 'cost_center_id' })
  costCenter?: CostcenterSubaccountEntity;

  @Column({ name: 'budget_code_id', type: 'uuid', nullable: true })
  @Index()
  budgetCodeId?: string;

  @ManyToOne(() => BudgetCodeEntity, (budgetCode) => budgetCode.purchaseOrders)
  @JoinColumn({ name: 'budget_code_id' })
  budgetCode?: BudgetCodeEntity;

  @Column({ name: 'purchase_org_id', type: 'uuid', nullable: true })
  @Index()
  purchaseOrgId?: string;

  @ManyToOne(
    () => PurchasingDepartmentEntity,
    (purchaseOrg) => purchaseOrg.purchaseOrders,
  )
  @JoinColumn({ name: 'purchase_org_id' })
  purchaseOrg?: PurchasingDepartmentEntity;

  @Column({ name: 'purchase_group_id', type: 'uuid', nullable: true })
  @Index()
  purchaseGroupId?: string;

  @ManyToOne(
    () => PurchasingGroupEntity,
    (purchaseGroup) => purchaseGroup.purchaseOrders,
  )
  @JoinColumn({ name: 'purchase_group_id' })
  purchaseGroup?: PurchasingGroupEntity;

  @Column({ type: 'enum', enum: Priority, nullable: true })
  priority: Priority;

  @Column({ name: 'reason', type: 'varchar', nullable: true })
  reason?: string;

  @Column({ name: 'total', type: 'varchar', nullable: true })
  total?: number;

  @Column({ name: 'total_price_vat', type: 'varchar', nullable: true })
  totalPriceVat?: number;

  @Column({ name: 'purchaser_id', type: 'uuid', nullable: true })
  @Index()
  purchaserId?: string;

  @ManyToOne(
    () => StaffEntity,
    (purchaser) => purchaser.purchaseOrderPurchasers,
  )
  @JoinColumn({ name: 'purchaser_id' })
  purchaser?: StaffEntity;

  @Column({
    name: 'files',
    type: 'simple-array',
    nullable: true,
    default: [],
  })
  attachments: string[];

  @Column({ nullable: true })
  refId?: string;

  @Column({ name: 'account_gl', type: 'varchar', nullable: true })
  accountGl?: string;

  @Column({
    name: 'is_created_sap',
    type: 'boolean',
    default: false,
    nullable: true,
  })
  isCreatedSap?: boolean;

  @OneToMany(
    () => PurchaseOrderDetailEntity,
    (detail) => detail.purchaseOrder,
    { cascade: true, orphanedRowAction: 'soft-delete' },
  )
  details?: PurchaseOrderDetailEntity[];

  @OneToMany(
    () => ApprovalLevelEntity,
    (approvalLevel) => approvalLevel.purchaseOrder,
  )
  levels?: ApprovalLevelEntity[];

  @OneToMany(() => HistoryApproveEntity, (history) => history.purchaseOrder, {
    cascade: true,
  })
  history?: HistoryApproveEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'enum', enum: EPaymentMethod, nullable: true })
  paymentMethod: EPaymentMethod;

  @Column({ name: 'is_accountant_approved', type: 'boolean', default: false })
  isAccountantApproved?: boolean;

  @Column({ name: 'currency_id', type: 'uuid', nullable: true })
  @Index()
  currencyId?: string;

  @ManyToOne(() => CurrencyUnitEntity, (currency) => currency.purchaseOrders)
  @JoinColumn({ name: 'currency_id' })
  currency?: CurrencyUnitEntity;

  @Column({ name: 'exchange_rate', type: 'float', default: 1, nullable: true })
  exchangeRate?: number;

  @OneToMany(() => SapPurchaseOrderEntity, (sapPo) => sapPo.purchaseOrder)
  sapPos?: SapPurchaseOrderEntity[];

  @Column({
    type: 'enum',
    enum: EDisplayStatus,
    default: EDisplayStatus.Pending,
  })
  displayStatusPo: EDisplayStatus;

  @Column({ name: 'process_type_id', type: 'uuid', nullable: true })
  @Index()
  processTypeId?: string;

  @ManyToOne(
    () => ProcessTypeEntity,
    (processType) => processType.purchaseOrders,
  )
  @JoinColumn({ name: 'process_type_id' })
  processType?: ProcessTypeEntity;

  @Column({ name: 'plant_id', type: 'uuid', nullable: true })
  @Index()
  plantId?: string;

  @ManyToOne(() => PlantEntity, (plant) => plant.purchaseOrders)
  @JoinColumn({ name: 'plant_id' })
  plant?: PlantEntity;

  @Column({ name: 'function_unit_id', type: 'uuid', nullable: true })
  @Index()
  functionUnitId?: string;

  @ManyToOne(
    () => FunctionUnitEntity,
    (functionUnit) => functionUnit.purchaseOrders,
  )
  @JoinColumn({ name: 'function_unit_id' })
  functionUnit?: FunctionUnitEntity;

  @Column({ name: 'department_id', type: 'uuid', nullable: true })
  @Index()
  departmentId?: string;

  @ManyToOne(() => DepartmentEntity, (department) => department.purchaseOrders)
  @JoinColumn({ name: 'department_id' })
  department?: DepartmentEntity;

  @OneToOne(
    () => SolomonPurchaseOrderEntity,
    (solomonPo) => solomonPo.purchaseOrder,
    { onDelete: 'CASCADE' },
  )
  solomonPo?: SolomonPurchaseOrderEntity;

  @Column({
    name: 'is_created_solomon',
    type: 'boolean',
    default: false,
    nullable: true,
  })
  isCreatedSolomon?: boolean;

  historyPr?: HistoryApproveEntity[];

  @OneToMany(
    () => ReceiptSolomonEntity,
    (receiptSolomon) => receiptSolomon.purchaseOrder,
  )
  receiptSolomons?: ReceiptSolomonEntity[];
}
