import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import {
  ENotificationFormStatus,
  ENotificationFormType,
} from '../../domain/config/enums/notification-form.enum';
import { EPlatform } from '../../domain/config/enums/platform.enum';

export type NotificationFormDocument = HydratedDocument<NotificationForm>;
@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class NotificationForm {
  _id?: mongoose.Types.ObjectId;

  @Prop({ type: String, required: true })
  name: string;

  @Prop({
    type: String,
    enum: Object.values(ENotificationFormType),
    required: true,
  })
  type: string;

  @Prop({ type: String, default: null, required: false })
  description?: string;

  @Prop({
    type: String,
    enum: Object.values(ENotificationFormStatus),
    default: ENotificationFormStatus.ACTIVE,
    required: false,
  })
  status?: string;

  @Prop({ type: String, default: null, required: false })
  content?: string;

  @Prop({ type: String, default: null, required: false })
  searchValue?: string;

  @Prop({
    type: String,
    enum: Object.values(EPlatform),
    required: true,
  })
  platform: string;
}

export const NotificationFormSchema =
  SchemaFactory.createForClass(NotificationForm);

NotificationFormSchema.index({ searchValue: 'text' });
