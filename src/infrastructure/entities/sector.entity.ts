import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { ESectorStatus } from '../../domain/config/enums/sector.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { InventoryStandardEntity } from './inventory-standard.entity';
import { MaterialSectorEntity } from './material-sector.entity';
import { PlantEntity } from './plant.entity';
import { PurchasingDepartmentEntity } from './purchasing-department.entity';
import { PurchasingGroupEntity } from './purchasing-group.entity';
import { StaffEntity } from './staff.entity';
import { SupplierSectorEntity } from './supplier-sector.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { ApprovalProcessDetailEntity } from './approval-process-detail.entity';
import { WarehouseEntity } from './warehouse.entity';

@Entity('sectors')
export class SectorEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: ESectorStatus,
    default: ESectorStatus.ACTIVE,
  })
  status: ESectorStatus; //Trạng thái

  @OneToMany(
    () => CostcenterSubaccountEntity,
    (costcenterSubaccount) => costcenterSubaccount.sector,
  )
  costcenterSubaccounts?: CostcenterSubaccountEntity[];

  @OneToMany(
    () => PurchasingGroupEntity,
    (purchasingGroup) => purchasingGroup.sector,
  )
  purchasingGroups?: PurchasingGroupEntity[];

  @OneToMany(
    () => PurchasingDepartmentEntity,
    (purchasingDepartment) => purchasingDepartment.sector,
  )
  purchasingDepartments?: PurchasingDepartmentEntity[];

  @OneToMany(() => PlantEntity, (plant) => plant.sector)
  plants?: PlantEntity[];

  @ManyToMany(() => StaffEntity, (staff) => staff.sectors)
  staffs?: StaffEntity[];

  @OneToMany(
    () => InventoryStandardEntity,
    (inventoryStandard) => inventoryStandard.sector,
  )
  inventoryStandards?: InventoryStandardEntity[];

  @OneToMany(
    () => SupplierSectorEntity,
    (supplierSector) => supplierSector.sector,
  )
  industriesSupplier?: SupplierSectorEntity[];

  @OneToMany(
    () => MaterialSectorEntity,
    (materialSector) => materialSector.sector,
  )
  industriesMaterial?: MaterialSectorEntity[];

  @ManyToMany(() => ConditionDetailEntity, (condition) => condition.sectors)
  conditions?: ConditionDetailEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.sector)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.sector)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.sector,
  )
  approvalProcessDetails: ApprovalProcessDetailEntity[];

  @ManyToMany(() => WarehouseEntity, (warehouse) => warehouse.sectors)
  warehouses?: WarehouseEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
