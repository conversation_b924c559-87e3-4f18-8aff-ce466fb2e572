import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';

@Entity('history-approve')
export class HistoryApproveEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  level: number;

  @Column({ nullable: true })
  role: string;

  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true, default: 'Pending' })
  status: string;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  reason: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(
    () => PurchaseRequestEntity,
    (purchaseRequest) => purchaseRequest.history,
    { onDelete: 'CASCADE' },
  )
  @Index()
  purchaseRequest?: PurchaseRequestEntity;

  @ManyToOne(
    () => PurchaseOrderEntity,
    (purchaseOrder) => purchaseOrder.history,
    { onDelete: 'CASCADE' },
  )
  @Index()
  purchaseOrder?: PurchaseOrderEntity;
}
