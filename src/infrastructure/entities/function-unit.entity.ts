import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { EFunctionUnitStatus } from '../../domain/config/enums/function-unit.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { StaffEntity } from './staff.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { ApprovalProcessDetailEntity } from './approval-process-detail.entity';
import { MaterialEntity } from './material.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';

@Entity('function_unit')
export class FunctionUnitEntity extends BaseEntity {
  @Column({ name: 'code', type: 'varchar', nullable: false })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string; //ID

  @Column({ name: 'name', type: 'varchar', nullable: false })
  name: string; //Tên Khối chủ sở hữu

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description: string; //Mô tả

  @ManyToMany(
    () => ConditionDetailEntity,
    (conditionDetail) => conditionDetail.plants,
  )
  conditionDetails?: ConditionDetailEntity[];

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EFunctionUnitStatus,
    default: EFunctionUnitStatus.ACTIVE,
  })
  status: EFunctionUnitStatus; //Trạng thái

  @ManyToMany(() => StaffEntity, (staff) => staff.functionUnits)
  staffs?: StaffEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.functionUnit)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.functionUnit)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.functionUnit,
  )
  approvalProcessDetails: ApprovalProcessDetailEntity[];

  @ManyToMany(() => MaterialEntity, (material) => material.functionUnits)
  materials?: MaterialEntity[];

  @ManyToMany(
    () => CostcenterSubaccountEntity,
    (costCenter) => costCenter.functionUnits,
  )
  costCenters?: CostcenterSubaccountEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  updateSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
