import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Column,
  Entity,
  ManyToOne,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CurrencyUnitEntity } from './currency-unit.entity';

@Entity({ name: 'currency_unit_exchange' })
export class CurrencyUnitExchangeEntity extends BaseEntity {
  @Column({ name: 'currency_unit_id', type: 'uuid', nullable: true })
  currencyUnitId: string;

  @Column({
    name: 'exchange_rate',
    type: 'numeric',
    nullable: false,
    default: 1,
  })
  exchangeRate: number; //Quy đổi VND

  @Column({ name: 'effective_start_date', type: 'date', nullable: false })
  effectiveStartDate: Date; //Thời gian có hiệu lực

  @Column({ name: 'effective_end_date', type: 'date', nullable: true })
  effectiveEndDate: Date; //Thời gian hết hiệu lự<PERSON>

  @ManyToOne(() => CurrencyUnitEntity, (currency) => currency.currencyExchanges)
  currencyUnit?: CurrencyUnitEntity;

  @Column({ name: 'exchange_budget', type: 'bool', default: false })
  exchangeBudget: boolean; //Tỷ giá ngân sách

  @AfterInsert()
  afterInsert() {
    if (this.exchangeRate) {
      this.exchangeRate = Number(this.exchangeRate);
    }
  }

  @AfterLoad()
  afterLoad() {
    if (this.exchangeRate) {
      this.exchangeRate = Number(this.exchangeRate);
    }
  }

  @AfterUpdate()
  afterUpdate() {
    if (this.exchangeRate) {
      this.exchangeRate = Number(this.exchangeRate);
    }
  }
}
