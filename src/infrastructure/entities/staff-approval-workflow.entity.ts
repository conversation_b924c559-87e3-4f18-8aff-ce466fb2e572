import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
  JoinTable,
  ManyToMany,
  ManyToOne,
} from 'typeorm';
import { EApprover } from '../../domain/config/enums/approver.enum';
import { EReturnRuleApprove } from '../../domain/config/enums/return-rule-approve.enum';
import { ApprovalWorkflowEntity } from './approval-workflow.entity';
import { BaseEntity } from './base.entity';
import { PositionEntity } from './position.entity';
import { StaffEntity } from './staff.entity';

@Entity('staff_approval_workflows')
export class StaffApprovalWorkflowEntity extends BaseEntity {
  @Column({ type: 'boolean', default: false })
  receiveEmail: boolean;

  @Column({ type: 'int', default: 0, nullable: true })
  level: number;

  @Column({ type: 'varchar', default: '', nullable: true })
  name: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: EApprover,
    default: EApprover.LEVEL_1,
  })
  approver: EApprover;

  @ManyToOne(() => StaffEntity, (staff) => staff.staffApprovalWorkflows)
  @JoinColumn({ name: 'staff_id' })
  staff?: StaffEntity;

  @Column({ type: 'uuid', nullable: true })
  staffId?: string;

  @ManyToOne(
    () => PositionEntity,
    (position) => position.staffApprovalWorkflows,
  )
  @JoinColumn({ name: 'position_id' })
  position?: PositionEntity;

  @Column({ type: 'uuid', nullable: true })
  positionId?: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: EReturnRuleApprove,
    default: EReturnRuleApprove.NOT_ALLOW,
  })
  returnRule: EReturnRuleApprove;

  @Column({ type: 'boolean', default: false })
  accountantApproved: boolean;

  @Column({ type: 'boolean', default: false })
  allowSelect: boolean;

  @Column({ type: 'boolean', default: false })
  allowEdit: boolean;

  @ManyToMany(
    () => StaffEntity,
    (staff) => staff.staffSelectedStaffApprovalWorkflows,
  )
  @JoinTable({
    name: 'staff_selected_staff_approval_workflows',
    joinColumns: [
      { name: 'staff_approval_workflow_id', referencedColumnName: 'id' },
    ],
    inverseJoinColumns: [{ name: 'staff_id', referencedColumnName: 'id' }],
  })
  selectedStaffs?: StaffEntity[];

  @ManyToOne(
    () => ApprovalWorkflowEntity,
    (approvalWorkflow) => approvalWorkflow.staffApprovalWorkflows,
  )
  approvalWorkflow?: ApprovalWorkflowEntity;

  @Column({ type: 'uuid', nullable: true })
  approvalWorkflowId?: string;
}
