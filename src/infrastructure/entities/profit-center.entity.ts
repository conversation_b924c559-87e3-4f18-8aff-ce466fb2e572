import { Column, Entity, Index, OneToMany } from 'typeorm';
import { EProfitCenterStatus } from '../../domain/config/enums/profit-center.enum';
import { BaseEntity } from './base.entity';
import { MaterialEntity } from './material.entity';

@Entity('profit_centers')
export class ProfitCenterEntity extends BaseEntity {
  @Column({ name: 'code', type: 'varchar', nullable: false })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string; //ID

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description: string; //Mô tả

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EProfitCenterStatus,
    default: EProfitCenterStatus.ACTIVE,
  })
  status: EProfitCenterStatus; //Trạng thái

  @OneToMany(() => MaterialEntity, (material) => material.profitCenter)
  materials?: MaterialEntity[];
}
