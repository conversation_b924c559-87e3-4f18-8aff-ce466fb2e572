import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';

import { EPlatform } from '../../domain/config/enums/platform.enum';

export type NotificationEntityDocument = HydratedDocument<NotificationEntity>;
@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class NotificationEntity {
  _id?: mongoose.Types.ObjectId;

  @Prop({ type: Object, required: false })
  notificationForm?: object;

  @Prop({ type: String, required: true, index: true })
  ownerId: string;

  @Prop({ type: Boolean, default: false })
  seenFlag?: boolean;

  @Prop({ type: Object, required: false })
  metaData?: object;

  @Prop({ type: String, required: false })
  url?: string;

  @Prop({ type: String, required: false })
  title?: string;

  @Prop({ type: String, required: false })
  body?: string;

  @Prop({
    type: String,
    enum: Object.values(EPlatform),
    required: true,
    index: true,
  })
  platform: string;
}

export const NotificationSchema =
  SchemaFactory.createForClass(NotificationEntity);
