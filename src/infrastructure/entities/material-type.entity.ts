import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  OneToMany,
} from 'typeorm';
import { EMaterialTypeStatus } from '../../domain/config/enums/material.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { MaterialEntity } from './material.entity';

@Entity('material_types')
export class MaterialTypeEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ type: 'varchar' })
  searchValue: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EMaterialTypeStatus,
    default: EMaterialTypeStatus.ACTIVE,
  })
  status: EMaterialTypeStatus; //Trạng thái

  @OneToMany(() => MaterialEntity, (material) => material.materialType)
  materials?: MaterialEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
