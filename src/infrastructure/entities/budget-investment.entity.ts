import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Column,
  Entity,
  ManyToOne,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { BudgetCapexEntity } from './budget-capex.entity';

@Entity({ name: 'budget_investment' })
export class BudgetInvestmentEntity extends BaseEntity {
  @Column({ name: 'investment', type: 'varchar', nullable: false })
  investment: string; //Hạng mục đầu tư

  @Column({ name: 'quantity', type: 'int', nullable: false })
  quantity: number; //S<PERSON> lượng

  @Column({ name: 'price', type: 'numeric', nullable: true })
  price?: number; //Đơn giá

  @Column({ name: 'transportation_costs', type: 'numeric', nullable: true })
  transportationCosts?: number; //Tổng chi phí vận chuyển

  @Column({ name: 'budget_capex_id', type: 'uuid', nullable: false })
  budgetCapexId: string; //<PERSON><PERSON> sách Capex

  @ManyToOne(() => BudgetCapexEntity, (capex) => capex.budgetInvestments)
  budgetCapex?: BudgetCapexEntity;

  @AfterInsert()
  afterInsert() {
    if (this.price) {
      this.price = Number(this.price);
    }

    if (this.transportationCosts) {
      this.transportationCosts = Number(this.transportationCosts);
    }
  }

  @AfterLoad()
  afterLoad() {
    if (this.price) {
      this.price = Number(this.price);
    }

    if (this.transportationCosts) {
      this.transportationCosts = Number(this.transportationCosts);
    }
  }

  @AfterUpdate()
  afterUpdate() {
    if (this.price) {
      this.price = Number(this.price);
    }

    if (this.transportationCosts) {
      this.transportationCosts = Number(this.transportationCosts);
    }
  }
}
