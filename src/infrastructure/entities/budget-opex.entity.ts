import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import { BaseEntity } from './base.entity';
import { BudgetEntity } from './budget.entity';

@Entity({ name: 'budget_opex' })
export class BudgetOpexEntity extends BaseEntity {
  @Column({ name: 'form', type: 'varchar', nullable: true })
  form: string; //Biểu mẫu

  @Column({ name: 'operations', type: 'varchar', nullable: true })
  operations: string; //Diễn giải chi phí/<PERSON>hiệ<PERSON> vụ

  @OneToOne(() => BudgetEntity)
  budget?: BudgetEntity;
}
