import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { EProcessType } from '../../domain/config/enums/process-type.enum';
import { EStatus } from '../../domain/config/enums/status.enum';
import { removeUnicode } from '../../utils/common';
import { ApprovalWorkflowEntity } from './approval-workflow.entity';
import { BaseEntity } from './base.entity';
import { ProcessConditionEntity } from './process-condition.entity';

@Entity('processes')
export class ProcessEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: true, default: 'Start' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ type: 'uuid', nullable: true })
  parentId: string;

  @Column({ type: 'boolean', default: false })
  allowInherit: boolean;

  @Column({ type: 'ltree', nullable: true })
  path: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: EStatus,
    default: EStatus.ACTIVE,
  })
  status: EStatus; //Trạng thái

  @Column({
    type: 'enum',
    nullable: true,
    enum: EProcessType,
    default: EProcessType.PR,
  })
  type: EProcessType;

  @ManyToOne(() => ProcessEntity, (process) => process.children) // Inverted relationship for clarity
  parent?: ProcessEntity | null;

  @OneToMany(() => ProcessEntity, (process) => process.parent) // Define children explicitly
  children?: ProcessEntity[];

  @OneToMany(
    () => ProcessConditionEntity,
    (processCondition) => processCondition.process,
  )
  processConditions?: ProcessConditionEntity[];

  @ManyToMany(
    () => ApprovalWorkflowEntity,
    (approvalWorkflow) => approvalWorkflow.processes,
  )
  approvalWorkflows?: ApprovalWorkflowEntity[];

  @OneToMany(
    () => ApprovalWorkflowEntity,
    (approvalWorkflow) => approvalWorkflow.parentProcess,
  )
  parentApprovalWorkflows?: ApprovalWorkflowEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue = removeUnicode(this.name);
  }

  nlevel?: number;
}
