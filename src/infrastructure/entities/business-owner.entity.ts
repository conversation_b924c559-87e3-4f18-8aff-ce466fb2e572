import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { EBusinessOwnerStatus } from '../../domain/config/enums/business-owner.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { MaterialGroupEntity } from './material-group.entity';
import { StaffEntity } from './staff.entity';

@Entity('business_owners')
export class BusinessOwnerEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EBusinessOwnerStatus,
    default: EBusinessOwnerStatus.ACTIVE,
  })
  status: EBusinessOwnerStatus; //Trạng thái

  @ManyToMany(() => StaffEntity, (staff) => staff.businessOwners)
  staffs?: StaffEntity[];

  @OneToMany(() => BudgetCodeEntity, (budgetCode) => budgetCode.businessOwner)
  budgetCodes?: BudgetCodeEntity[];

  @ManyToMany(
    () => MaterialGroupEntity,
    (materialGroup) => materialGroup.businessOwners,
  )
  materialGroups?: MaterialGroupEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
