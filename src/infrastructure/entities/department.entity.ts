import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { EDepartmentStatus } from '../../domain/config/enums/department.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { InventoryStandardEntity } from './inventory-standard.entity';
import { MaterialEntity } from './material.entity';
import { StaffEntity } from './staff.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { ApprovalProcessDetailEntity } from './approval-process-detail.entity';

@Entity('department')
export class DepartmentEntity extends BaseEntity {
  @Column({ name: 'code', type: 'varchar', nullable: false })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string; //ID

  @Column({ name: 'name', type: 'varchar', nullable: false })
  name: string; //Tên phòng ban

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description: string; //Mô tả

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EDepartmentStatus,
    default: EDepartmentStatus.ACTIVE,
  })
  status: EDepartmentStatus; //Trạng thái

  @OneToMany(
    () => CostcenterSubaccountEntity,
    (costcenterSubaccount) => costcenterSubaccount.department,
  )
  costcenterSubaccounts: CostcenterSubaccountEntity[];

  @ManyToMany(() => StaffEntity, (staff) => staff.departments)
  staffs: StaffEntity[];

  @OneToMany(
    () => InventoryStandardEntity,
    (inventoryStandard) => inventoryStandard.department,
  )
  inventoryStandards?: InventoryStandardEntity[];

  @OneToMany(() => MaterialEntity, (material) => material.department)
  materials?: MaterialEntity[];

  @ManyToMany(() => ConditionDetailEntity, (condition) => condition.departments)
  conditions?: ConditionDetailEntity;

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.department)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.department)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.department,
  )
  approvalProcessDetails?: ApprovalProcessDetailEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  updateSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
