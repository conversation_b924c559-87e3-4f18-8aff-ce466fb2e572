import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { EStatus } from '../../domain/config/enums/status.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { MaterialGroupEntity } from './material-group.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';

@Entity('process_types')
export class ProcessTypeEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  hasInventoryStandard: boolean;

  @Column({
    type: 'enum',
    nullable: true,
    enum: EStatus,
    default: EStatus.ACTIVE,
  })
  status: EStatus; //Trạng thái

  @ManyToMany(
    () => MaterialGroupEntity,
    (materialGroup) => materialGroup.processTypes,
  )
  materialGroups?: MaterialGroupEntity[];

  @ManyToMany(
    () => ConditionDetailEntity,
    (conditionDetail) => conditionDetail.processTypes,
  )
  conditionDetails?: ConditionDetailEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.processType)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.processType)
  purchaseOrders?: PurchaseOrderEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
