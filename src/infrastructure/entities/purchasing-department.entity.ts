import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { EPurchasingDepartmentStatus } from '../../domain/config/enums/purchasing.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { MaterialEntity } from './material.entity';
import { SectorEntity } from './sector.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { SapPurchaseOrderEntity } from './sap_purchase_order.entity';
import { PriceInformationRecordEntity } from './price_information_record.entity';

@Entity('purchasing_departments')
export class PurchasingDepartmentEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ name: 'sector_id', type: 'uuid', nullable: false })
  sectorId: string; //Nhóm vật tư

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EPurchasingDepartmentStatus,
    default: EPurchasingDepartmentStatus.ACTIVE,
  })
  status: EPurchasingDepartmentStatus; //Trạng thái

  @ManyToOne(() => SectorEntity, (sector) => sector.purchasingDepartments)
  sector?: SectorEntity;

  @OneToMany(() => MaterialEntity, (material) => material.purchasingDepartment)
  materials?: MaterialEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.purchaseOrg)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.purchaseOrg)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(
    () => SapPurchaseOrderEntity,
    (sapPO) => sapPO.purchasingDepartment,
  )
  sapPurchaseOrders?: SapPurchaseOrderEntity[];

  @OneToMany(
    () => PriceInformationRecordEntity,
    (pir) => pir.purchaseOrganization,
  )
  pirs?: PriceInformationRecordEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
