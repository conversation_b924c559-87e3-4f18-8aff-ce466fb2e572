import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToOne,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { ConditionEntity } from './condition.entity';
import { ProcessEntity } from './process.entity';

@Entity('process_conditions')
export class ProcessConditionEntity extends BaseEntity {
  @Column({ type: 'boolean', default: true, nullable: true })
  isEffected: boolean;

  @ManyToOne(() => ProcessEntity, (process) => process.processConditions, {
    cascade: true,
  })
  process?: ProcessEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index({ where: 'deleted_at IS NULL' })
  processId?: string;

  @OneToOne(() => ConditionEntity, { cascade: true })
  @JoinColumn({ name: 'condition_id' })
  condition?: ConditionEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index({ where: 'deleted_at IS NULL' })
  conditionId?: string;
}
