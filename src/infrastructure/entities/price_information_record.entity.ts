import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Status } from '../../domain/config/enums/status.enum';
import { CurrencyUnitEntity } from './currency-unit.entity';
import { MaterialEntity } from './material.entity';
import { PlantEntity } from './plant.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { PurchasingDepartmentEntity } from './purchasing-department.entity';
import { SupplierEntity } from './supplier.entity';
import { PurchasingGroupEntity } from './purchasing-group.entity';
import { BusinessUnitEntity } from './business-unit.entity';

@Entity('price_information_records')
export class PriceInformationRecordEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'vendor_code_id', type: 'uuid' })
  vendorCodeId: string;

  @ManyToOne(() => SupplierEntity, (supplier) => supplier.pirs)
  @JoinColumn({ name: 'vendor_code_id' })
  vendor?: SupplierEntity;

  @Column({ name: 'material_code_id', type: 'uuid' })
  @Index()
  materialCodeId: string;

  @ManyToOne(() => MaterialEntity, (material) => material.pirs)
  @JoinColumn({ name: 'material_code_id' })
  material?: MaterialEntity;

  @Column({ name: 'purchase_organization_id', type: 'uuid' })
  @Index()
  purchaseOrganizationId: string;

  @ManyToOne(
    () => PurchasingDepartmentEntity,
    (purchaseOrganization) => purchaseOrganization.pirs,
  )
  @JoinColumn({ name: 'purchase_organization_id' })
  purchaseOrganization?: PurchasingDepartmentEntity;

  @Column({ name: 'plant_id', type: 'uuid', nullable: true })
  @Index()
  plantId?: string;

  @ManyToOne(() => PlantEntity, (plant) => plant.pirs)
  @JoinColumn({ name: 'plant_id' })
  plant?: PlantEntity;

  @Column({ type: 'simple-array', nullable: true })
  infoType: string[];

  @Column({ name: 'business_unit_ids', type: 'simple-array', nullable: true })
  businessUnitIds: string[];

  @Column({ nullable: true })
  purchaseUnit?: string;

  @Column({ nullable: true })
  vendorLeadtime?: string;

  @Column({ name: 'purchase_group_id', type: 'uuid' })
  purchaseGroupId: string;

  @ManyToOne(() => PurchasingGroupEntity, (purchaseGroup) => purchaseGroup.pirs)
  @JoinColumn({ name: 'purchase_group_id' })
  purchaseGroup?: PurchasingGroupEntity;

  @Column('decimal')
  regularPurchaseQuantity: number;

  @Column('decimal')
  minimumOrderQuantity: number;

  @Column('decimal')
  upperTolerance: number;

  @Column('decimal')
  lowerTolerance: number;

  @Column('decimal')
  purchasePrice: number;

  @Column({ name: 'currency_id', type: 'uuid' })
  @Index()
  currencyId: string;

  @ManyToOne(() => CurrencyUnitEntity, (currency) => currency.pirs)
  @JoinColumn({ name: 'currency_id' })
  currency?: CurrencyUnitEntity;

  @Column()
  overPurchaseUnit: string;

  @Column({ nullable: true })
  unitOfMeasurement?: string;

  @Column()
  effectiveDate: Date;

  @Column()
  expirationDate: Date;

  @Column()
  status: Status;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.pir)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @ManyToMany(() => BusinessUnitEntity, (businessUnit) => businessUnit.pirs)
  @JoinTable({
    name: 'pir_business_units',
    joinColumns: [{ name: 'pir_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'business_unit_id', referencedColumnName: 'id' },
    ],
  })
  businessUnits?: BusinessUnitEntity[];
}
