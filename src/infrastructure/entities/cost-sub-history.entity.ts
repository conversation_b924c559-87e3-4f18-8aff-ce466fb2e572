import {
  AfterInsert,
  AfterLoad,
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  ManyToOne,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';

@Entity('cost_sub_history')
export class CostSubHistoryEntity extends BaseEntity {
  @Column({ name: 'old_data', type: 'jsonb', nullable: true })
  oldData: string | object;

  @Column({ name: 'new_data', type: 'jsonb', nullable: true })
  newData: string | object;

  @Column({ name: 'costcenter_subaccount_id', type: 'uuid', nullable: false })
  costcenterSubaccountId: string; //Id cost center / sub account

  @Column({ name: 'user_info', type: 'jsonb', nullable: true })
  userInfo: string | object;

  @ManyToOne(
    () => CostcenterSubaccountEntity,
    (costcenter) => costcenter.histories,
  )
  costcenterSubaccount?: CostcenterSubaccountEntity;

  @BeforeInsert()
  @BeforeUpdate()
  jsonStringify() {
    if (this.oldData && typeof this.oldData !== 'string') {
      this.oldData = JSON.stringify(this.oldData);
    }

    if (this.newData && typeof this.newData !== 'string') {
      this.newData = JSON.stringify(this.newData);
    }

    if (this.userInfo && typeof this.userInfo !== 'string') {
      this.userInfo = JSON.stringify(this.userInfo);
    }
  }

  @AfterLoad()
  @AfterInsert()
  parseJsonString() {
    if (this.oldData && typeof this.oldData === 'string') {
      this.oldData = JSON.parse(this.oldData);
    }

    if (this.newData && typeof this.newData === 'string') {
      this.newData = JSON.parse(this.newData);
    }

    if (this.userInfo && typeof this.userInfo === 'string') {
      this.userInfo = JSON.parse(this.userInfo);
    }
  }
}
