import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import {
  EPurchaseRequestStatus,
  EPurchaseRequestTypeForm,
} from '../../domain/config/enums/purchasing.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { ApprovalProcessDetailEntity } from './approval-process-detail.entity';

@Entity('purchase_request_type')
export class PurchaseRequestTypeEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({ type: 'varchar' })
  description: string;

  @Column({ type: 'varchar' })
  searchValue: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EPurchaseRequestStatus,
    default: EPurchaseRequestStatus.ACTIVE,
  })
  status: EPurchaseRequestStatus; //Trạng thái

  @Column({
    name: 'form',
    type: 'enum',
    nullable: true,
    enum: EPurchaseRequestTypeForm,
    default: EPurchaseRequestTypeForm.NORMAL,
  })
  form: EPurchaseRequestTypeForm; //Hình thức

  @ManyToMany(() => ConditionDetailEntity, (condition) => condition.prTypes)
  conditions?: ConditionDetailEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.typePr)
  purchaseRequests?: PurchaseRequestEntity[];

  @ManyToMany(
    () => ApprovalProcessDetailEntity,
    (approvalProcessDetail) => approvalProcessDetail.prTypes,
  )
  approvalProcessDetails: ApprovalProcessDetailEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
