import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  OneToMany,
} from 'typeorm';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { SapPurchaseOrderItemEntity } from './sap_purchase_order_item.entity';
import { SolomonPurchaseOrderItemEntity } from './solomon-purchase-order-item.entity';

@Entity({ name: 'tax_codes' })
export class TaxCodeEntity extends BaseEntity {
  @Column({ name: 'code', type: 'varchar', nullable: false })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string; //Mã thuế (2 kí tự)

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description?: string; //Mô tả

  @Column({
    name: 'tax_rate',
    type: 'numeric',
    nullable: false,
  })
  taxRate: number; //Tỷ lệ thuế

  @OneToMany(() => PurchaseOrderDetailEntity, (po) => po.taxCode)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(() => SapPurchaseOrderItemEntity, (sapPOItem) => sapPOItem.taxCode)
  sapPurchaseOrderItems?: SapPurchaseOrderItemEntity[];

  @OneToMany(
    () => SolomonPurchaseOrderItemEntity,
    (solomonPOItem) => solomonPOItem.taxCode,
  )
  solomonPurchaseOrderItems?: SolomonPurchaseOrderItemEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  updateSearchValue() {
    this.searchValue = removeUnicode(this.code);
  }
}
