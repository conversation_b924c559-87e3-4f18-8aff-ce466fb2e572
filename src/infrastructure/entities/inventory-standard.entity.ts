import { BeforeInsert, BeforeUpdate, Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../infrastructure/entities/base.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { CompanyEntity } from './company.entity';
import { DepartmentEntity } from './department.entity';
import { MaterialEntity } from './material.entity';
import { SectorEntity } from './sector.entity';
import { EInventoryStandardStatus } from '../../domain/config/enums/inventory-standard.enum';
import { getPathInventoryStandard, uidToPath } from '../../utils/common';

@Entity('inventory_standards')
export class InventoryStandardEntity extends BaseEntity {
  @Column({ name: 'material_id', type: 'uuid', nullable: false })
  materialId: string; //Vật tư

  @Column({ type: 'varchar', nullable: true })
  unit: string; //Đơn vị tính

  @Column({ name: 'sector_id', type: 'uuid', nullable: false })
  sectorId: string; //Mảng

  @Column({ name: 'company_id', type: 'uuid', nullable: true })
  companyId: string; //Công ty

  @Column({ name: 'business_unit_id', type: 'uuid', nullable: true })
  businessUnitId: string; //Đơn vị kinh doanh

  @Column({ name: 'department_id', type: 'uuid', nullable: true })
  departmentId: string; //Phòng ban

  @Column({ name: 'standard_quantity', type: 'int', nullable: false })
  standardQuantity: number; //Số lượng định mức

  @Column({
    name: 'status',
    type: 'enum',
    nullable: false,
    enum: EInventoryStandardStatus,
  })
  status: EInventoryStandardStatus; //Trạng thái

  @Column({ type: 'ltree', nullable: true })
  path: string;

  @Column({ name: 'inventory_quantity', type: 'int', nullable: false })
  inventoryQuantity: number; //Số lượng tồn kho

  @ManyToOne(() => MaterialEntity, (material) => material.inventoryStandards)
  material?: MaterialEntity;

  @ManyToOne(() => SectorEntity, (sector) => sector.inventoryStandards)
  sector?: SectorEntity;

  @ManyToOne(() => CompanyEntity, (company) => company.inventoryStandards)
  company?: CompanyEntity;

  @ManyToOne(
    () => BusinessUnitEntity,
    (businessUnit) => businessUnit.inventoryStandards,
  )
  businessUnit?: BusinessUnitEntity;

  @ManyToOne(
    () => DepartmentEntity,
    (department) => department.inventoryStandards,
  )
  department?: DepartmentEntity;

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.path = getPathInventoryStandard(
      this.materialId,
      this.sectorId,
      this.companyId,
      this.businessUnitId,
      this.departmentId,
    );
  }
}
