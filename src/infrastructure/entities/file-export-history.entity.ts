import {
  AfterInsert,
  AfterLoad,
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
} from 'typeorm';
import {
  EFileExportStatus,
  EFileExportType,
} from '../../domain/config/enums/file-export-history.enum';
import { BaseEntity } from './base.entity';

@Entity('file-export-histories')
export class FileExportHistoryEntity extends BaseEntity {
  @Column({ name: 'file_name', type: 'varchar', nullable: false })
  fileName: string; //<PERSON>ô tả

  @Column({ name: 'file_path', type: 'varchar', nullable: false })
  filePath: string; //<PERSON>ô tả

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EFileExportStatus,
    default: EFileExportStatus.WAITING,
  })
  status: EFileExportStatus; //Trạng thái

  @Column({
    name: 'export_type',
    type: 'enum',
    nullable: false,
    enum: EFileExportType,
  })
  exportType: EFileExportType; //Loại import

  @Column({ name: 'errors', type: 'varchar', nullable: true })
  errors: string | object[]; //<PERSON><PERSON>ời thực hiện
}
