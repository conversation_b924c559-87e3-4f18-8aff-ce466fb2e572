import { isJSON } from 'class-validator';
import {
  AfterInsert,
  AfterLoad,
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  BaseEntity as TBaseEntity,
  UpdateDateColumn,
} from 'typeorm';

export class BaseApproveEntity extends TBaseEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @DeleteDateColumn({
    nullable: true,
  })
  deletedAt: string;

  @Column({ type: 'varchar', nullable: true })
  searchValue: string;

  @Column({ type: 'jsonb', nullable: true })
  createdBy: string | object;

  @Column({ type: 'jsonb', nullable: true })
  updatedBy: string | object;

  @Column({ type: 'jsonb', nullable: true })
  deletedBy: string | object;

  @BeforeInsert()
  @BeforeUpdate()
  jsonStringify() {
    if (this.createdBy && typeof this.createdBy !== 'string') {
      this.createdBy = JSON.stringify(this.createdBy);
    }

    if (this.updatedBy && typeof this.updatedBy !== 'string') {
      this.updatedBy = JSON.stringify(this.updatedBy);
    }

    if (this.deletedBy && typeof this.deletedBy !== 'string') {
      this.deletedBy = JSON.stringify(this.deletedBy);
    }
  }

  @AfterLoad()
  @AfterInsert()
  parseJsonString() {
    if (this.createdBy && typeof this.createdBy === 'string') {
      this.createdBy = isJSON(this.createdBy)
        ? JSON.parse(this.createdBy)
        : this.createdBy;
    }

    if (this.updatedBy && typeof this.updatedBy === 'string') {
      this.updatedBy = isJSON(this.updatedBy)
        ? JSON.parse(this.updatedBy)
        : this.updatedBy;
    }

    if (this.deletedBy && typeof this.deletedBy === 'string') {
      this.deletedBy = isJSON(this.deletedBy)
        ? JSON.parse(this.deletedBy)
        : this.deletedBy;
    }
  }
}
