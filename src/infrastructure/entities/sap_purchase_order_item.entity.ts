import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from './base.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { SapPurchaseOrderEntity } from './sap_purchase_order.entity';
import { EAccountAssignment } from '../../domain/config/enums/account-assignment.enum';
import { BaseApproveEntity } from './base-approve.entity';
import { MaterialEntity } from './material.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { MaterialGroupEntity } from './material-group.entity';
import { CurrencyUnitEntity } from './currency-unit.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { TaxCodeEntity } from './tax-code.entity';

@Entity('sap_purchase_order_items')
export class SapPurchaseOrderItemEntity extends BaseApproveEntity {
  @Column({ name: 'eprno', type: 'int', nullable: true })
  eprId: number; // gen rule SAP

  @Column({ name: 'material_id', type: 'uuid', nullable: true })
  materialId: string; // Material id

  @ManyToOne(() => MaterialEntity, (material) => material.sapPurchaseOrderItems)
  @JoinColumn({ name: 'material_id' })
  material?: MaterialEntity;

  @Column({ name: 'material_code', type: 'varchar', nullable: true })
  materialCode: string;

  @Column({ name: 'material_name', type: 'varchar', nullable: true })
  materialName: string; //Mô tả sản phẩm cần mua đối với mua PO dich vụ không theo mã

  @Column({ name: 'quantity', type: 'float', nullable: true })
  quantity: number;

  @Column({ name: 'unit', type: 'varchar', nullable: true })
  unit: string; //ĐVT

  @Column({ name: 'delivery_date', type: 'date', nullable: false })
  deliveryDate: Date;

  @Column({ name: 'price', type: 'float', nullable: true })
  price: number;

  @Column({
    name: 'account_assignment',
    type: 'enum',
    enum: EAccountAssignment,
    nullable: true,
  })
  accountAssignment: EAccountAssignment; //AAG - Loại đối tượng nhận hàng

  @Column({ name: 'gl_account', type: 'varchar', nullable: true })
  glAccount: string; //G/L Account Number

  @Column({ name: 'cost_center_id', type: 'uuid', nullable: true })
  costCenterId: string;

  @ManyToOne(
    () => CostcenterSubaccountEntity,
    (costCenter) => costCenter.sapPurchaseOrderItems,
  )
  @JoinColumn({ name: 'cost_center_id' })
  costCenter?: CostcenterSubaccountEntity;

  @Column({ name: 'cost_center_code', type: 'varchar', nullable: true })
  costCenterCode: string;

  @Column({ name: 'asset', type: 'varchar', nullable: true })
  asset: string;

  @Column({ name: 'internal_order', type: 'varchar', nullable: true })
  internalOrder: string;

  @Column({ name: 'wbs', type: 'varchar', nullable: true })
  wbs: string;

  @Column({ name: 'functional_area', type: 'varchar', nullable: true })
  functionalArea: string;

  @Column({ name: 'material_group_id', type: 'uuid', nullable: true })
  materialGroupId: string;

  @ManyToOne(
    () => MaterialGroupEntity,
    (materialGroup) => materialGroup.sapPurchaseOrderItems,
  )
  @JoinColumn({ name: 'material_group_id' })
  materialGroup?: MaterialGroupEntity;

  @Column({ name: 'material_group_code', type: 'varchar', nullable: true })
  materialGroupCode: string;

  @Column({ name: 'material_group_name', type: 'varchar', nullable: true })
  materialGroupName: string;

  @Column({ name: 'currency_id', type: 'uuid', nullable: true })
  currencyId: string;

  @ManyToOne(
    () => CurrencyUnitEntity,
    (currency) => currency.sapPurchaseOrderItems,
  )
  @JoinColumn({ name: 'currency_id' })
  currency?: CurrencyUnitEntity;

  @Column({ name: 'currency_code', type: 'varchar', nullable: true })
  currencyCode: string;

  @Column({ name: 'bu_id', type: 'uuid', nullable: true })
  buId: string; //BU Id-Plant SAP

  @ManyToOne(
    () => BusinessUnitEntity,
    (businessUnit) => businessUnit.sapPurchaseOrderItems,
  )
  @JoinColumn({ name: 'bu_id' })
  businessUnit?: BusinessUnitEntity;

  @Column({ name: 'bu_code', type: 'varchar', nullable: true })
  buCode: string; //Plant SAP

  @ManyToOne(
    () => SapPurchaseOrderEntity,
    (sapPurchaseOrder) => sapPurchaseOrder.items,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'sap_purchase_order_id' })
  sapPurchaseOrder?: SapPurchaseOrderEntity;

  @Column({ name: 'sap_purchase_order_id', type: 'int4', nullable: true })
  sapPurchaseOrderId?: number;

  @Column({ name: 'tax_code_id', type: 'uuid', nullable: true })
  taxCodeId: string;

  @ManyToOne(() => TaxCodeEntity, (taxCode) => taxCode.sapPurchaseOrderItems)
  @JoinColumn({ name: 'tax_code_id' })
  taxCode?: TaxCodeEntity;

  @Column({ name: 'tax_code_code', type: 'varchar', nullable: true })
  taxCodeCode: string;

  @ManyToOne(
    () => PurchaseOrderDetailEntity,
    (detail) => detail.sapPurchaseOrderItems,
  )
  @JoinColumn({ name: 'purchase_order_detail_id' })
  purchaseOrderDetail?: PurchaseOrderDetailEntity;

  @Column({ name: 'purchase_order_detail_id', type: 'int4', nullable: true })
  purchaseOrderDetailId?: number;

  @Column({ name: 'message_type', type: 'varchar', nullable: true })
  messageType: string; // Response from SAP

  @Column({ name: 'message', type: 'varchar', nullable: true })
  message: string;
}
