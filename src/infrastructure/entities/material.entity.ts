import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  Join<PERSON>olumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import {
  EMaterialCheckBudget,
  EMaterialThroughPurchasing,
} from '../../domain/config/enums/material.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { CompanyEntity } from './company.entity';
import { DepartmentEntity } from './department.entity';
import { InventoryStandardEntity } from './inventory-standard.entity';
import { MaterialGroupEntity } from './material-group.entity';
import { MaterialSectorEntity } from './material-sector.entity';
import { MaterialTypeEntity } from './material-type.entity';
import { ProfitCenterEntity } from './profit-center.entity';
import { PurchasingDepartmentEntity } from './purchasing-department.entity';
import { PurchasingGroupEntity } from './purchasing-group.entity';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { SapPurchaseOrderItemEntity } from './sap_purchase_order_item.entity';
import { PriceInformationRecordEntity } from './price_information_record.entity';
import { MeasureEntity } from './measure.entity';
import { SolomonPurchaseOrderItemEntity } from './solomon-purchase-order-item.entity';
import { FunctionUnitEntity } from './function-unit.entity';

@Entity('materials')
export class MaterialEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string; //Mã Vật tư

  @Column({ type: 'varchar', nullable: true })
  name: string; //Tên Vật tư

  @Column({ name: 'material_type_id', type: 'uuid', nullable: false })
  materialTypeId: string; //Loại vật tư

  @Column({ type: 'varchar', nullable: true })
  unit: string; //Đơn vị tính

  @Column({ name: 'material_group_id', type: 'uuid', nullable: false })
  materialGroupId: string; //Nhóm vật tư

  @Column({ type: 'varchar', nullable: true })
  description: string; //Mô tả

  @Column({
    name: 'through_purchasing',
    type: 'enum',
    nullable: true,
    enum: EMaterialThroughPurchasing,
  })
  throughPurchasing: EMaterialThroughPurchasing; //Qua thu mua

  @Column({
    name: 'check_budget',
    type: 'enum',
    nullable: true,
    enum: EMaterialCheckBudget,
  })
  checkBudget: EMaterialCheckBudget; //Kiểm tra ngân sách

  @ManyToOne(() => MaterialTypeEntity, (materialType) => materialType.materials)
  materialType?: MaterialTypeEntity;

  @ManyToOne(
    () => MaterialGroupEntity,
    (materialGroup) => materialGroup.materials,
  )
  materialGroup?: MaterialGroupEntity;

  @OneToMany(
    () => InventoryStandardEntity,
    (inventoryStandard) => inventoryStandard.material,
  )
  inventoryStandards?: InventoryStandardEntity[];

  @ManyToOne(() => ProfitCenterEntity, (profitCenter) => profitCenter.materials)
  profitCenter?: ProfitCenterEntity;

  @OneToMany(
    () => MaterialSectorEntity,
    (materialSector) => materialSector.material,
  )
  industries?: MaterialSectorEntity[];

  @ManyToOne(() => CompanyEntity, (company) => company.materials)
  company?: CompanyEntity;

  @ManyToMany(() => BusinessUnitEntity, (bu) => bu.materials)
  @JoinTable({
    name: 'material_business_units',
    joinColumns: [{ name: 'material_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'business_unit_id', referencedColumnName: 'id' },
    ],
  })
  businessUnits?: BusinessUnitEntity[];

  @ManyToMany(() => FunctionUnitEntity, (fu) => fu.materials)
  @JoinTable({
    name: 'material_function_units',
    joinColumns: [{ name: 'material_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'function_unit_id', referencedColumnName: 'id' },
    ],
  })
  functionUnits?: FunctionUnitEntity[];

  @ManyToOne(() => DepartmentEntity, (department) => department.materials)
  department: DepartmentEntity;

  @ManyToOne(
    () => PurchasingGroupEntity,
    (purchasingGroup) => purchasingGroup.materials,
  )
  @JoinColumn({ name: 'purchasing_group_id' })
  purchasingGroup?: PurchasingGroupEntity;

  @Column({ type: 'uuid', nullable: true })
  purchasingGroupId: string;

  @ManyToOne(
    () => PurchasingDepartmentEntity,
    (purchasingDepartment) => purchasingDepartment.materials,
  )
  @JoinColumn({ name: 'purchasing_department_id' })
  purchasingDepartment?: PurchasingDepartmentEntity;

  @Column({ type: 'uuid', nullable: true })
  purchasingDepartmentId: string;

  @OneToMany(() => PurchaseRequestDetailEntity, (prDetail) => prDetail.material)
  purchaseRequestDetails?: PurchaseRequestDetailEntity[];

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.material)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(
    () => SapPurchaseOrderItemEntity,
    (sapPOItem) => sapPOItem.material,
  )
  sapPurchaseOrderItems?: SapPurchaseOrderItemEntity[];

  @OneToMany(
    () => SolomonPurchaseOrderItemEntity,
    (solomonPOItem) => solomonPOItem.material,
  )
  solomonPurchaseOrderItems?: SolomonPurchaseOrderItemEntity[];

  @OneToMany(() => PriceInformationRecordEntity, (pir) => pir.material)
  pirs?: PriceInformationRecordEntity[];
  @Column({ name: 'measure_id', type: 'uuid', nullable: true })
  measureId?: string;

  @ManyToOne(() => MeasureEntity, (measure) => measure.materials)
  @JoinColumn({ name: 'measure_id' })
  measure: MeasureEntity;

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
