import { Column, Entity, ManyToOne } from 'typeorm';
import { EManagementLevel } from '../../domain/config/enums/management-level.enum';
import { BaseEntity } from './base.entity';
import { StaffEntity } from './staff.entity';

@Entity('staff_hierarchies')
export class StaffHierarchyEntity extends BaseEntity {
  @ManyToOne(() => StaffEntity, (staff) => staff.subordinateHierarchies)
  manager?: StaffEntity;

  @ManyToOne(() => StaffEntity, (staff) => staff.managerHierarchies)
  subordinate?: StaffEntity;

  @Column({
    type: 'enum',
    enum: EManagementLevel,
  })
  level: EManagementLevel;
}
