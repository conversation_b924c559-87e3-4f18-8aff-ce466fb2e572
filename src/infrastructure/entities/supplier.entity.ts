import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  OneToMany,
} from 'typeorm';
import { ESupplierType } from '../../domain/config/enums/supplier.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { SupplierSectorEntity } from './supplier-sector.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { SapPurchaseOrderEntity } from './sap_purchase_order.entity';
import { PriceInformationRecordEntity } from './price_information_record.entity';
import { SolomonPurchaseOrderEntity } from './solomon-purchase-order.entity';

@Entity('suppliers')
export class SupplierEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({
    name: 'type',
    type: 'enum',
    nullable: true,
    enum: ESupplierType,
    default: ESupplierType.OFFICIAL,
  })
  type: ESupplierType; //Trạng thái

  @Column({ type: 'varchar', nullable: true })
  address: string;

  @Column({ type: 'varchar', nullable: true })
  phone: string;

  @Column({ type: 'varchar', nullable: true })
  fax: string;

  @Column({ type: 'varchar', nullable: true })
  businessLicenseNumber: string;

  @Column({ type: 'varchar', nullable: true })
  taxCode: string;

  @Column({ type: 'varchar', nullable: true })
  contactPerson: string;

  @Column({ type: 'varchar', nullable: true })
  transactionCurrency: string;

  @Column({ type: 'varchar', nullable: true })
  paymentMethod: string;

  @Column({ type: 'varchar', nullable: true })
  note: string;

  @Column({ type: 'varchar' })
  searchValue: string;

  @OneToMany(
    () => SupplierSectorEntity,
    (supplierSector) => supplierSector.supplier,
  )
  industries?: SupplierSectorEntity[];

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.supplier)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(() => SapPurchaseOrderEntity, (sapPO) => sapPO.supplier)
  sapPurchaseOrders?: SapPurchaseOrderEntity[];

  @OneToMany(
    () => SolomonPurchaseOrderEntity,
    (solomonPO) => solomonPO.supplier,
  )
  solomonPurchaseOrders?: SolomonPurchaseOrderEntity[];

  @OneToMany(() => PriceInformationRecordEntity, (pir) => pir.vendor)
  pirs?: PriceInformationRecordEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue = (
      removeUnicode(this.code) +
      ' ' +
      removeUnicode(this.name) +
      ' ' +
      removeUnicode(this.phone) +
      ' ' +
      removeUnicode(this.businessLicenseNumber)
    ).trim();
  }
}
