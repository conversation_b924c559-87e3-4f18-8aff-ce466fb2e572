import { Column, Entity, ManyToOne } from 'typeorm';
import { ESupplierSectorStatus } from '../../domain/config/enums/supplier-sector.enum';
import { SupplierEntity } from './supplier.entity';
import { SectorEntity } from './sector.entity';
import { BaseEntity } from './base.entity';

@Entity('supplier_sectors')
export class SupplierSectorEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: false })
  codeSAP: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: ESupplierSectorStatus,
    default: ESupplierSectorStatus.ACTIVE,
  })
  status: ESupplierSectorStatus; //Trạng thái

  @ManyToOne(() => SupplierEntity, (supplier) => supplier.industries)
  supplier?: SupplierEntity;

  @ManyToOne(() => SectorEntity, (sector) => sector.industriesSupplier)
  sector?: SectorEntity;
}
