import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  OneToMany,
} from 'typeorm';
import { ECostStatus } from '../../domain/config/enums/cost.enum';
import { BaseEntity } from '../../infrastructure/entities/base.entity';
import { removeUnicode } from '../../utils/common';
import { BudgetCodeEntity } from './budget-code.entity';

@Entity('costs')
export class CostEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ name: 'group_cost', type: 'varchar', nullable: true })
  groupCost: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: ECostStatus,
    default: ECostStatus.ACTIVE,
  })
  status: ECostStatus; //Trạng thái

  @OneToMany(() => BudgetCodeEntity, (budgetCode) => budgetCode.cost)
  budgetCodes?: BudgetCodeEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
