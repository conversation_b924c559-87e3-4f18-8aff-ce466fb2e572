import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { EBudgetCodeStatus } from '../../domain/config/enums/budget-code.enum';
import { EBudgetType } from '../../domain/config/enums/budget.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { BudgetEntity } from './budget.entity';
import { BusinessOwnerEntity } from './business-owner.entity';
import { ConditionDetailEntity } from './condition-detail.entity';
import { CostEntity } from './cost.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { ReceiptSolomonEntity } from './receipt-solomon.entity';
import { SolomonPurchaseOrderEntity } from './solomon-purchase-order.entity';
import { SolomonPurchaseOrderItemEntity } from './solomon-purchase-order-item.entity';

@Entity({ name: 'budget_code' })
export class BudgetCodeEntity extends BaseEntity {
  @Column({ name: 'name', type: 'varchar', nullable: false, default: '' })
  name: string; //Tên

  @Column({ name: 'code', type: 'varchar', nullable: false })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string; //ID

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description: string; //Mô tả

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EBudgetCodeStatus,
    default: EBudgetCodeStatus.ACTIVE,
  })
  status: EBudgetCodeStatus; //Trạng thái

  @Column({
    name: 'budget_type',
    type: 'enum',
    default: EBudgetType.OPEX,
    nullable: false,
    enum: EBudgetType,
  })
  budgetType: EBudgetType; //Loại ngân sách

  @ManyToOne(() => CostEntity, (cost) => cost.budgetCodes)
  @JoinColumn({ name: 'cost_id' })
  cost?: CostEntity;

  @Column({
    name: 'cost_id',
    type: 'uuid',
    nullable: true,
  })
  costId?: string;

  @Column({ name: 'internal_order', type: 'varchar', nullable: true })
  internalOrder: string;

  @OneToMany(() => BudgetEntity, (budget) => budget.budgetCode)
  budgets?: BudgetEntity[];

  @ManyToOne(
    () => BusinessOwnerEntity,
    (businessOwner) => businessOwner.budgetCodes,
  )
  @JoinColumn({ name: 'business_owner_id' })
  businessOwner?: BusinessOwnerEntity;

  @Column({
    name: 'business_owner_id',
    type: 'uuid',
    nullable: true,
  })
  businessOwnerId: string;

  @ManyToMany(() => ConditionDetailEntity, (condition) => condition.budgetCodes)
  conditions?: ConditionDetailEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.budgetCode)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(
    () => PurchaseRequestDetailEntity,
    (prDetail) => prDetail.budgetCode,
  )
  purchaseRequestDetails?: PurchaseRequestDetailEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.budgetCode)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.budgetCode)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(
    () => ReceiptSolomonEntity,
    (receiptSolomon) => receiptSolomon.budgetCode,
  )
  receiptSolomons?: ReceiptSolomonEntity[];

  @OneToMany(
    () => SolomonPurchaseOrderEntity,
    (solomonPO) => solomonPO.budgetCode,
  )
  solomonPurchaseOrders?: SolomonPurchaseOrderEntity[];

  @OneToMany(
    () => SolomonPurchaseOrderItemEntity,
    (solomonPOItem) => solomonPOItem.budgetCode,
  )
  solomonPurchaseOrderItems?: SolomonPurchaseOrderItemEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  updateSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
