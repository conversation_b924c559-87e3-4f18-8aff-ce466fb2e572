import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  OneToMany,
} from 'typeorm';
import { EPositionStatus } from '../../domain/config/enums/position.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { StaffApprovalWorkflowEntity } from './staff-approval-workflow.entity';
import { StaffEntity } from './staff.entity';

@Entity('positions')
export class PositionEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: EPositionStatus,
    default: EPositionStatus.ACTIVE,
  })
  status: EPositionStatus; //Trạng thái

  @OneToMany(() => StaffEntity, (staff) => staff.position) // Define children explicitly
  staffs?: StaffEntity[];

  @OneToMany(
    () => StaffApprovalWorkflowEntity,
    (staffApprovalWorkflow) => staffApprovalWorkflow.position,
  )
  staffApprovalWorkflows?: StaffApprovalWorkflowEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }
}
