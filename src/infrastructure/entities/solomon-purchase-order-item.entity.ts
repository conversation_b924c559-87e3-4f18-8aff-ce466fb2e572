import { <PERSON>umn, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from './base.entity';
import { SolomonPurchaseOrderEntity } from './solomon-purchase-order.entity';
import { MaterialEntity } from './material.entity';
import { WarehouseEntity } from './warehouse.entity';
import { MeasureEntity } from './measure.entity';
import { TaxCodeEntity } from './tax-code.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';

@Entity('solomon_purchase_order_items')
export class SolomonPurchaseOrderItemEntity extends BaseEntity {
  @Column({ name: 'solomon_purchase_order_id', type: 'uuid', nullable: true })
  solomonPurchaseOrderId?: string;

  @ManyToOne(
    () => SolomonPurchaseOrderEntity,
    (solomonPurchaseOrder) => solomonPurchaseOrder.items,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'solomon_purchase_order_id' })
  solomonPurchaseOrder?: SolomonPurchaseOrderEntity;

  @Column({ name: 'material_id', type: 'uuid', nullable: true })
  materialId?: string; //Mã vật tư

  @ManyToOne(
    () => MaterialEntity,
    (material) => material.solomonPurchaseOrderItems,
  )
  @JoinColumn({ name: 'material_id' })
  material?: MaterialEntity;

  @Column({ name: 'material_code', type: 'varchar', nullable: true })
  materialCode?: string;

  @Column({ name: 'material_name', type: 'varchar', nullable: true })
  materialName?: string; //Mô tả sản phẩm cần mua đối với mua PO dich vụ không theo mã

  @Column({ name: 'warehouse_id', type: 'uuid', nullable: true })
  warehouseId?: string; //Mã kho

  @ManyToOne(
    () => WarehouseEntity,
    (warehouse) => warehouse.solomonPurchaseOrderItems,
  )
  @JoinColumn({ name: 'warehouse_id' })
  warehouse?: WarehouseEntity;

  @Column({ name: 'warehouse_code', type: 'varchar', nullable: true })
  warehouseCode?: string;

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description?: string; //Mô tả trong dòng item (note)

  @Column({ name: 'quantity', type: 'decimal', nullable: true })
  quantity?: number; //Số lượng mua

  @Column({ name: 'measure_id', type: 'uuid', nullable: true })
  measureId?: string; //Mã đơn vị tính

  @ManyToOne(
    () => MeasureEntity,
    (measure) => measure.solomonPurchaseOrderItems,
  )
  @JoinColumn({ name: 'measure_id' })
  measure?: MeasureEntity;

  @Column({ name: 'measure_code', type: 'varchar', nullable: true })
  measureCode?: string; //Mã đơn vị tính (Lưu ý: Chuyển đổi sang đơn vị của hệ thống Solomon)

  @Column({ name: 'unit_price', type: 'decimal', nullable: true })
  unitPrice?: number; //Đơn giá

  @Column({ name: 'total_amount_vat', type: 'decimal', nullable: true })
  totalAmountVat?: number; //Tổng tiền quy đổi * VAT

  @Column({ name: 'unit_weight', type: 'decimal', nullable: true, default: 0 })
  unitWeight?: number; //Mặc định = 0

  @Column({ name: 'ext_weight', type: 'decimal', nullable: true, default: 0 })
  extWeight?: number; //Mặc định = 0

  @Column({ name: 'delivery_time', type: 'date', nullable: true })
  deliveryTime?: Date; //Ngày giao hàng

  @Column({ name: 'tax_code_id', type: 'uuid', nullable: true })
  taxCodeId?: string; //Mã thuế

  @ManyToOne(
    () => TaxCodeEntity,
    (taxCode) => taxCode.solomonPurchaseOrderItems,
  )
  @JoinColumn({ name: 'tax_code_id' })
  taxCode?: TaxCodeEntity;

  @Column({ name: 'tax_code_code', type: 'varchar', nullable: true })
  taxCodeCode?: string;

  @Column({ name: 'cnv_fact', type: 'decimal', nullable: true, default: 1 })
  cnvFact?: number; //Mặc định = 1

  @Column({ name: 'budget_code_code', type: 'varchar', nullable: true })
  budgetCodeCode?: string; //Mã ngân sách

  @Column({ name: 'budget_code_id', type: 'uuid', nullable: true })
  budgetCodeId?: string; //Mã ngân sách

  @ManyToOne(
    () => BudgetCodeEntity,
    (budgetCode) => budgetCode.solomonPurchaseOrderItems,
  )
  @JoinColumn({ name: 'budget_code_id' })
  budgetCode?: BudgetCodeEntity;

  @ManyToOne(
    () => PurchaseOrderDetailEntity,
    (detail) => detail.solomonPurchaseOrderItems,
  )
  @JoinColumn({ name: 'purchase_order_detail_id' })
  purchaseOrderDetail?: PurchaseOrderDetailEntity;

  @Column({ name: 'purchase_order_detail_id', type: 'int4', nullable: true })
  purchaseOrderDetailId?: number;

  @Column({ name: 'message_type', type: 'varchar', nullable: true })
  messageType?: string; // Response from SAP

  @Column({ name: 'message', type: 'varchar', nullable: true })
  message?: string;
}
