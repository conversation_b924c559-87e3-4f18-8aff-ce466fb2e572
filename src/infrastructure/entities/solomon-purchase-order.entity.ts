import {
  AfterInsert,
  AfterLoad,
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { SolomonPurchaseOrderItemEntity } from './solomon-purchase-order-item.entity';
import { SupplierEntity } from './supplier.entity';
import { isJSON } from 'class-validator';

@Entity('solomon_purchase_orders')
export class SolomonPurchaseOrderEntity extends BaseEntity {
  @Column({ name: 'po_id', type: 'int4', nullable: false })
  poId: number; //Mã PO

  @OneToOne(() => PurchaseOrderEntity, (po) => po.solomonPo, { cascade: true })
  @JoinColumn({ name: 'po_id' })
  purchaseOrder?: PurchaseOrderEntity;

  @Column({ name: 'supplier_id', type: 'uuid', nullable: true })
  supplierId?: string; //Mã nhà cung cấp

  @ManyToOne(() => SupplierEntity, (supplier) => supplier.solomonPurchaseOrders)
  @JoinColumn({ name: 'supplier_id' })
  supplier?: SupplierEntity;

  @Column({ name: 'supplier_code', type: 'varchar', nullable: true })
  supplierCode?: string; //Mã nhà cung cấp

  @Column({ name: 'supplier_info', type: 'jsonb', nullable: true })
  supplierInfo?: string | object;

  @Column({ type: 'timestamptz', nullable: true, name: 'po_created_date' })
  poCreatedDate?: Date; //Ngày tạo PO

  @Column({ name: 'budget_code_code', type: 'varchar', nullable: true })
  budgetCodeCode?: string; //Mã ngân sách

  @Column({ name: 'budget_code_id', type: 'uuid', nullable: true })
  budgetCodeId?: string; //Mã ngân sách

  @ManyToOne(
    () => BudgetCodeEntity,
    (budgetCode) => budgetCode.solomonPurchaseOrders,
  )
  @JoinColumn({ name: 'budget_code_id' })
  budgetCode?: BudgetCodeEntity;

  //Chi tiết đơn hàng
  @OneToMany(
    () => SolomonPurchaseOrderItemEntity,
    (item) => item.solomonPurchaseOrder,
    { cascade: true },
  )
  items?: SolomonPurchaseOrderItemEntity[];

  @Column({ name: 'message_type', type: 'varchar', nullable: true })
  messageType?: string; //Response from SOLOMON

  @Column({ name: 'message', type: 'varchar', nullable: true })
  message?: string;

  @Column({ name: 'status', type: 'varchar', nullable: true })
  status?: string;

  @BeforeInsert()
  @BeforeUpdate()
  jsonStringify() {
    if (this.supplierInfo && typeof this.supplierInfo !== 'string') {
      this.supplierInfo = JSON.stringify(this.supplierInfo);
    }

    if (this.messageType && typeof this.messageType !== 'string') {
      this.messageType = JSON.stringify(this.messageType);
    }

    if (this.message && typeof this.message !== 'string') {
      this.message = JSON.stringify(this.message);
    }
  }

  @AfterLoad()
  @AfterInsert()
  parseJsonString() {
    if (this.supplierInfo && typeof this.supplierInfo === 'string') {
      this.supplierInfo = isJSON(this.supplierInfo)
        ? JSON.parse(this.supplierInfo)
        : this.supplierInfo;
    }

    if (this.messageType && typeof this.messageType === 'string') {
      this.messageType = isJSON(this.messageType)
        ? JSON.parse(this.messageType)
        : this.messageType;
    }

    if (this.message && typeof this.message === 'string') {
      this.message = isJSON(this.message)
        ? JSON.parse(this.message)
        : this.message;
    }
  }
}
