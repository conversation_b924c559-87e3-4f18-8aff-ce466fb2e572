import {
  AfterInsert,
  AfterLoad,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';
import { SapPurchaseOrderItemEntity } from './sap_purchase_order_item.entity';
import { EAccountAssignment } from '../../domain/config/enums/account-assignment.enum';
import { BudgetCodeEntity } from './budget-code.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { MaterialEntity } from './material.entity';
import { MaterialGroupEntity } from './material-group.entity';
import { PriceInformationRecordEntity } from './price_information_record.entity';
import { SupplierEntity } from './supplier.entity';
import { CurrencyUnitEntity } from './currency-unit.entity';
import { BudgetEntity } from './budget.entity';
import { isJSON } from 'class-validator';
import { MeasureEntity } from './measure.entity';
import { TaxCodeEntity } from './tax-code.entity';
import { WarehouseEntity } from './warehouse.entity';
import { SolomonPurchaseOrderItemEntity } from './solomon-purchase-order-item.entity';

@Entity('purchase_order_details')
export class PurchaseOrderDetailEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'budget_code_id', type: 'uuid', nullable: true })
  @Index()
  budgetCodeId?: string;

  @ManyToOne(
    () => BudgetCodeEntity,
    (budgetCode) => budgetCode.purchaseOrderDetails,
  )
  @JoinColumn({ name: 'budget_code_id' })
  budgetCode?: BudgetCodeEntity;

  @Column({ name: 'cost_center_id', type: 'uuid', nullable: true })
  @Index()
  costCenterId?: string;

  @ManyToOne(
    () => CostcenterSubaccountEntity,
    (costCenter) => costCenter.purchaseOrderDetails,
  )
  @JoinColumn({ name: 'cost_center_id' })
  costCenter?: CostcenterSubaccountEntity;

  @Column({ name: 'material_code_id', type: 'uuid', nullable: true })
  @Index()
  materialCodeId?: string;

  @ManyToOne(() => MaterialEntity, (material) => material.purchaseOrderDetails)
  @JoinColumn({ name: 'material_code_id' })
  material?: MaterialEntity;

  @Column({ name: 'material_name', type: 'varchar', nullable: true })
  materialName?: string;

  @Column({ name: 'material_group_id', type: 'uuid', nullable: true })
  @Index()
  materialGroupId?: string;

  @ManyToOne(
    () => MaterialGroupEntity,
    (materialGroup) => materialGroup.purchaseOrderDetails,
  )
  @JoinColumn({ name: 'material_group_id' })
  materialGroup?: MaterialGroupEntity;

  @Column({ type: 'varchar', nullable: true })
  materialGroupName?: string; //text

  @Column({ name: 'unit', type: 'varchar', nullable: true })
  unit?: string;

  @Column({ name: 'note', type: 'varchar', nullable: true })
  note?: string;

  @Column({ name: 'delivery_time', type: 'date', nullable: false })
  deliveryTime: Date;

  @Column({ name: 'budget', type: 'decimal', nullable: true })
  budget?: number;

  @Column({ name: 'remaining_budget', type: 'decimal', nullable: true })
  remainingBudget?: number;

  @Column({ name: 'remaining_actual_budget', type: 'decimal', nullable: true })
  remainingActualBudget?: number;

  @Column({ name: 'unit_price', type: 'decimal', nullable: true })
  unitPrice?: number;

  @Column({ name: 'vat', type: 'decimal', nullable: true })
  vat?: number;

  @Column({ name: 'total_amount', type: 'decimal', nullable: true })
  totalAmount?: number;

  @Column({ name: 'total_amount_vat', type: 'decimal', nullable: true })
  totalAmountVat?: number; //Tổng tiền quy đổi * VAT

  @Column({ name: 'pr_reference', type: 'numeric', nullable: true })
  prReference?: number;

  @Column({ name: 'pir_id', type: 'integer', nullable: true })
  @Index()
  pirId?: number;

  @ManyToOne(
    () => PriceInformationRecordEntity,
    (pir) => pir.purchaseOrderDetails,
  )
  @JoinColumn({ name: 'pir_id' })
  pir?: PriceInformationRecordEntity;

  @Column({ name: 'supplier_id', type: 'uuid', nullable: true })
  @Index()
  supplierId?: string;

  @ManyToOne(() => SupplierEntity, (supplier) => supplier.purchaseOrderDetails)
  @JoinColumn({ name: 'supplier_id' })
  supplier?: SupplierEntity;

  @Column({ name: 'supplier_info', type: 'jsonb', nullable: true })
  supplierInfo?: string | object;

  @Column({ name: 'estimated_price', type: 'decimal', nullable: true })
  estimatedPrice?: number;

  @Column({ name: 'purchase_price', type: 'decimal', nullable: true })
  purchasePrice?: number;

  @Column({ name: 'standard_quantity', type: 'decimal', nullable: true })
  standardQuantity?: number;

  @Column({ name: 'inventory_number', type: 'decimal', nullable: true })
  inventoryNumber?: number;

  @Column({
    name: 'quantity_offered_purchase',
    type: 'decimal',
    nullable: true,
  })
  quantityOfferedPurchase?: number;

  @Column({ name: 'quantity', type: 'decimal', nullable: true })
  quantity?: number; //Số lượng mua

  @Column({
    name: 'sap_created_quantity',
    type: 'float',
    default: 0,
    nullable: true,
  })
  sapCreatedQuantity?: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({
    nullable: true,
  })
  deletedAt?: Date;

  @ManyToOne(
    () => PurchaseOrderEntity,
    (purchaseOrder) => purchaseOrder.details,
    { onDelete: 'CASCADE' },
  )
  purchaseOrder?: PurchaseOrderEntity;

  @Column({ name: 'account_gl', type: 'varchar', nullable: true })
  accountGl?: string;

  @Column({ type: 'enum', enum: EAccountAssignment, nullable: true })
  accountAssignment: EAccountAssignment;

  @Column({ name: 'property', type: 'varchar', nullable: true })
  property?: string; //Tài sản - asset

  @Column({ name: 'internal_order', type: 'varchar', nullable: true })
  internalOrder?: string;

  @Column({ name: 'wbs', type: 'varchar', nullable: true })
  wbs?: string;

  @Column({ name: 'pr_detail_id', type: 'numeric', nullable: true })
  @Index()
  prDetailId?: number;

  @Column({
    name: 'requested_quantity',
    type: 'float',
    default: 0,
    nullable: true,
  })
  requestedQuantity?: number; // Số lượng yêu cầu: Nếu tạo từ PR item Mặc định load = SL yêu cầu (trong PR item) - SL đã tạo PO

  @Column({ name: 'currency_id', type: 'uuid', nullable: true })
  @Index()
  currencyId?: string;

  @ManyToOne(
    () => CurrencyUnitEntity,
    (currency) => currency.purchaseOrderDetails,
  )
  @JoinColumn({ name: 'currency_id' })
  currency?: CurrencyUnitEntity;

  @Column({ name: 'exchange_rate', type: 'float', default: 1, nullable: true })
  exchangeRate?: number;

  @Column({
    name: 'total_converted_amount',
    type: 'float',
    default: 1,
    nullable: true,
  })
  totalConvertedAmount?: number; //Tổng tiền * Tỷ giá

  @ManyToOne(
    () => PurchaseRequestDetailEntity,
    (prDetail) => prDetail.poDetails,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'pr_detail_id' })
  prDetail?: PurchaseRequestDetailEntity;

  // Lưu vết khi tính toán remaining budget
  @Column({ name: 'budget_id', type: 'uuid', nullable: true })
  @Index()
  budgetId?: string;

  @ManyToOne(
    () => BudgetEntity,
    (budgetData) => budgetData.purchaseOrderDetailBudgets,
  )
  @JoinColumn({ name: 'budget_id' })
  budgetData?: BudgetEntity;

  @Column({ name: 'adjust_budget_id', type: 'uuid', nullable: true })
  @Index()
  adjustBudgetId?: string;

  @ManyToOne(
    () => BudgetEntity,
    (budgetData) => budgetData.purchaseOrderDetailAdjustBudgets,
  )
  @JoinColumn({ name: 'adjust_budget_id' })
  adjustBudget?: BudgetEntity;

  @OneToMany(
    () => SapPurchaseOrderItemEntity,
    (sapPurchaseOrderItem) => sapPurchaseOrderItem.purchaseOrderDetail,
  )
  sapPurchaseOrderItems?: SapPurchaseOrderItemEntity[];

  @Column({ name: 'measure_id', type: 'uuid', nullable: true })
  @Index()
  measureId?: string;

  @ManyToOne(() => MeasureEntity, (measure) => measure.purchaseOrderDetails)
  @JoinColumn({ name: 'measure_id' })
  measure?: MeasureEntity;

  @Column({ name: 'warehouse_id', type: 'uuid', nullable: true })
  warehouseId?: string;

  @ManyToOne(
    () => WarehouseEntity,
    (warehouse) => warehouse.purchaseOrderDetails,
  )
  @JoinColumn({ name: 'warehouse_id' })
  warehouse?: WarehouseEntity;

  @Column({ name: 'tax_code_id', type: 'uuid', nullable: true })
  taxCodeId: string;

  @ManyToOne(() => TaxCodeEntity, (taxCode) => taxCode.purchaseOrderDetails)
  @JoinColumn({ name: 'tax_code_id' })
  taxCode?: TaxCodeEntity;

  @OneToMany(
    () => SolomonPurchaseOrderItemEntity,
    (solomonPurchaseOrderItem) => solomonPurchaseOrderItem.purchaseOrderDetail,
  )
  solomonPurchaseOrderItems?: SolomonPurchaseOrderItemEntity[];

  @AfterLoad()
  @AfterInsert()
  parseJsonString() {
    if (this.supplierInfo && typeof this.supplierInfo === 'string') {
      this.supplierInfo = isJSON(this.supplierInfo)
        ? JSON.parse(this.supplierInfo)
        : this.supplierInfo;
    }
  }
}
