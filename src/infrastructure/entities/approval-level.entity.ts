import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
export enum ApproveType {
  PR = 'PR',
  PO = 'PO',
  ORTHER = 'ORTHER',
}
@Entity('approval-level')
export class ApprovalLevelEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  level: number;

  @Column()
  role: string;

  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @Column({ nullable: true })
  email: string;

  @Column({ default: false })
  isSendMail: boolean;

  @Column({ default: false })
  isSendMailCreator: boolean;

  @Column({ default: false, nullable: true })
  isAccountantApproved: boolean;

  @Column({ default: false, nullable: true })
  allowSelect: boolean;

  @Column({ type: 'uuid', nullable: true })
  staffApprovalWorkflowId: string;

  @Column({ nullable: true })
  status: string;

  @Column({ nullable: true })
  name: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({
    type: 'enum',
    enum: ApproveType,
    default: ApproveType.ORTHER,
    name: 'approve_type',
  })
  approveType: ApproveType;

  @ManyToOne(
    () => PurchaseRequestEntity,
    (purchaseRequest) => purchaseRequest.levels,
  )
  @JoinColumn({ name: 'purchase_request_id' })
  purchaseRequest?: PurchaseRequestEntity;

  @Column({ name: 'purchase_request_id', nullable: true })
  @Index()
  purchaseRequestId: number;

  @ManyToOne(() => PurchaseOrderEntity, (purchaseOrder) => purchaseOrder.levels)
  @JoinColumn({ name: 'purchase_order_id' })
  purchaseOrder?: PurchaseOrderEntity;

  @Column({ name: 'purchase_order_id', nullable: true })
  @Index()
  purchaseOrderId: number;
}
