import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  Unique,
} from 'typeorm';
import {
  EBudgetCreateType,
  EBudgetType,
} from '../../domain/config/enums/budget.enum';
import { BaseEntity } from './base.entity';
import { BudgetCapexEntity } from './budget-capex.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { BudgetOpexEntity } from './budget-opex.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { CurrencyUnitEntity } from './currency-unit.entity';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';

@Entity({ name: 'budget' })
@Unique(['code'])
export class BudgetEntity extends BaseEntity {
  @Column({ name: 'code', type: 'varchar', nullable: false })
  code: string; //Mã code - Opex: OP-123456 (unique) với prefix là OP, Capex: CA-123456 (unique) với prefix là CA - và 123456 là số nhảy tăng dần từ 000001

  @Column({
    name: 'create_type',
    type: 'enum',
    nullable: false,
    enum: EBudgetCreateType,
  })
  createType: EBudgetCreateType; //Loại khởi tạo

  @Column({ name: 'adjust_budget_id', type: 'uuid', nullable: true })
  adjustBudgetId: string; //Ngân sách điều chỉnh

  @Column({
    name: 'budget_type',
    type: 'enum',
    nullable: false,
    enum: EBudgetType,
  })
  budgetType: EBudgetType; //Loại ngân sách

  @Column({ name: 'currency_unit_id', type: 'uuid', nullable: false })
  currencyUnitId: string; //Đơn vị tiền tệ

  @Column({ name: 'budget_code_id', type: 'uuid', nullable: false })
  budgetCodeId: string; //Mã ngân sách

  @Column({ name: 'costcenter_subaccount_id', type: 'uuid', nullable: false })
  costcenterSubaccountId: string; //Costcenter/Subaccount

  @Column({ name: 'note', type: 'varchar', nullable: true })
  note: string; //Ghi chú

  @Column({ name: 'effective_start_date', type: 'date', nullable: false })
  effectiveStartDate: Date; //Thời gian có hiệu lực

  @Column({ name: 'effective_end_date', type: 'date', nullable: false })
  effectiveEndDate: Date; //Thời gian hết hiệu lực

  @Column({ name: 'budget_opex_id', type: 'uuid', nullable: true })
  budgetOpexId: string; //Chi tiết ngân sách Opex

  @Column({ name: 'budget_capex_id', type: 'uuid', nullable: true })
  budgetCapexId: string; //Chi tiết ngân sách Capex

  @Column({ name: 'total_value', type: 'numeric', nullable: false })
  totalValue: number; //Tổng giá trị

  @Column({ name: 'is_lock', type: 'bool', default: false })
  isLock: boolean; //Đã công bố / Chưa công bố

  @OneToOne(() => BudgetOpexEntity)
  @JoinColumn({ name: 'budget_opex_id' })
  budgetOpex?: BudgetOpexEntity;

  @OneToOne(() => BudgetCapexEntity)
  @JoinColumn({ name: 'budget_capex_id' })
  budgetCapex?: BudgetCapexEntity;

  @OneToMany(() => BudgetEntity, (budget) => budget.parent)
  children?: BudgetEntity[]; //Những ngân sách tạo ra để điều chỉnh ngân sách này

  @ManyToOne(() => BudgetEntity, (budget) => budget.children)
  @JoinColumn({ name: 'adjust_budget_id' })
  parent?: BudgetEntity; //Ngân sách được điều chỉnh

  @ManyToOne(() => CurrencyUnitEntity, (currency) => currency.budgets)
  currencyUnit?: CurrencyUnitEntity;

  @ManyToOne(() => BudgetCodeEntity, (budgetCode) => budgetCode.budgets)
  budgetCode?: BudgetCodeEntity;

  @ManyToOne(() => CostcenterSubaccountEntity, (cost) => cost.budgets)
  costcenterSubaccount?: CostcenterSubaccountEntity;

  @Column({ name: 'note2', type: 'varchar', nullable: true })
  note2: string; //Ghi chú

  @OneToMany(
    () => PurchaseRequestDetailEntity,
    (prDetail) => prDetail.budgetData,
  )
  purchaseRequestDetailBudgets?: PurchaseRequestDetailEntity[];

  @OneToMany(
    () => PurchaseRequestDetailEntity,
    (prDetail) => prDetail.adjustBudget,
  )
  purchaseRequestDetailAdjustBudgets?: PurchaseRequestDetailEntity[];

  @OneToMany(() => PurchaseOrderDetailEntity, (prDetail) => prDetail.budgetData)
  purchaseOrderDetailBudgets: PurchaseOrderDetailEntity[];

  @OneToMany(
    () => PurchaseOrderDetailEntity,
    (prDetail) => prDetail.adjustBudget,
  )
  purchaseOrderDetailAdjustBudgets?: PurchaseOrderDetailEntity[];

  @AfterLoad()
  afterLoad() {
    if (this.totalValue) {
      this.totalValue = Number(this.totalValue);
    }
  }

  @AfterUpdate()
  afterUpdate() {
    if (this.totalValue) {
      this.totalValue = Number(this.totalValue);
    }
  }

  @AfterInsert()
  afterInsert() {
    if (this.totalValue) {
      this.totalValue = Number(this.totalValue);
    }
  }
}
