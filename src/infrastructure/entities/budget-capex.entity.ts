import { Column, Entity, OneToMany, OneToOne } from 'typeorm';
import { BaseEntity } from './base.entity';
import { BudgetEntity } from './budget.entity';
import { BudgetInvestmentEntity } from './budget-investment.entity';

@Entity({ name: 'budget_capex' })
export class BudgetCapexEntity extends BaseEntity {
  @Column({ name: 'use_time', type: 'varchar', nullable: true })
  useTime: string; //Thời gian sử dụng

  @Column({ name: 'start_date', type: 'date', nullable: true })
  startDate?: Date; //Thời điểm bắt đầu

  @Column({
    name: 'expected_acceptance_time',
    type: 'date',
    nullable: true,
  })
  expectedAcceptanceTime: Date; //Thời điểm nghiệm thu dự kiến

  @Column({
    name: 'classify',
    type: 'varchar',
    nullable: true,
  })
  classify?: string; //<PERSON><PERSON> loại

  @Column({
    name: 'priority',
    type: 'varchar',
    nullable: true,
  })
  priority: string; //<PERSON><PERSON><PERSON> độ ưu tiên

  @Column({
    name: 'investment_purpose',
    type: 'varchar',
    nullable: true,
  })
  investmentPurpose: string; //Mục đích đầu tư

  @Column({
    name: 'files',
    type: 'simple-array',
    nullable: true,
    default: [],
  })
  files: string[]; //File đính kèm

  @Column({
    name: 'note2',
    type: 'varchar',
    nullable: true,
  })
  note2: string;

  @Column({
    name: 'key_project',
    type: 'varchar',
    nullable: true,
  })
  keyProject: string;

  @OneToOne(() => BudgetEntity)
  budget?: BudgetEntity;

  @OneToMany(
    () => BudgetInvestmentEntity,
    (investments) => investments.budgetCapex,
  )
  budgetInvestments?: BudgetInvestmentEntity[];
}
