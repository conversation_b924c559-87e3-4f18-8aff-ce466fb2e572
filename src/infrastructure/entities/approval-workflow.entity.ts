import {
  Column,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { ProcessEntity } from './process.entity';
import { StaffApprovalWorkflowEntity } from './staff-approval-workflow.entity';

@Entity('approval_workflows')
export class ApprovalWorkflowEntity extends BaseEntity {
  @Column({ type: 'varchar', default: '', nullable: true })
  name: string;

  @Column({ type: 'boolean', default: false })
  sendEmailToCreator: boolean;

  @ManyToMany(() => ProcessEntity, (process) => process.approvalWorkflows)
  @JoinTable({
    name: 'process_approval_workflows',
    joinColumns: [{ name: 'approval_workflow_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'process_id', referencedColumnName: 'id' }],
  })
  processes?: ProcessEntity[];

  @ManyToOne(() => ProcessEntity, (process) => process.parentApprovalWorkflows)
  @JoinColumn({ name: 'parent_process_id' })
  parentProcess?: ProcessEntity;

  @Column({ type: 'uuid', nullable: true })
  parentProcessId?: string;

  @OneToMany(
    () => StaffApprovalWorkflowEntity,
    (staffApprovalWorkflow) => staffApprovalWorkflow.approvalWorkflow,
  )
  staffApprovalWorkflows?: StaffApprovalWorkflowEntity[];
}
