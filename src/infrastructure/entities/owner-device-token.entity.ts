import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { EPlatform } from '../../domain/config/enums/platform.enum';

export type OwnerDeviceTokenEntityDocument =
  HydratedDocument<OwnerDeviceTokenEntity>;
@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class OwnerDeviceTokenEntity {
  _id?: mongoose.Types.ObjectId;

  @Prop({ type: String, required: true, index: true })
  ownerId: string;

  @Prop({ type: String, required: true, index: true })
  deviceToken: string;

  @Prop({
    type: String,
    enum: Object.values(EPlatform),
    required: true,
  })
  platform: string;
}

export const OwnerDeviceTokenSchema = SchemaFactory.createForClass(
  OwnerDeviceTokenEntity,
);
