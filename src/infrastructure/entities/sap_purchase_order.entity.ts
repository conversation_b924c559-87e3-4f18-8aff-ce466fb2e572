import { isJ<PERSON><PERSON> } from 'class-validator';
import {
  AfterInsert,
  AfterLoad,
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { BaseApproveEntity } from './base-approve.entity';
import { CompanyEntity } from './company.entity';
import { PurchaseOrderTypeEntity } from './purchase-order-type.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { PurchasingDepartmentEntity } from './purchasing-department.entity';
import { PurchasingGroupEntity } from './purchasing-group.entity';
import { SapPurchaseOrderItemEntity } from './sap_purchase_order_item.entity';
import { SupplierEntity } from './supplier.entity';

@Entity('sap_purchase_orders')
export class SapPurchaseOrderEntity extends BaseApproveEntity {
  @Column({ name: 'po_id', type: 'int4', nullable: true })
  poId: number; // PO EP id

  @ManyToOne(() => PurchaseOrderEntity, (po) => po.sapPos)
  @JoinColumn({ name: 'po_id' })
  purchaseOrder?: PurchaseOrderEntity;

  @Column({ name: 'company_id', type: 'uuid', nullable: true })
  companyId: string; // Company id in BU Id

  @ManyToOne(() => CompanyEntity, (company) => company.sapPurchaseOrders)
  @JoinColumn({ name: 'company_id' })
  company?: CompanyEntity;

  @Column({ name: 'company_code', type: 'varchar', nullable: true })
  companyCode: string; // Company Code

  @Column({ name: 'po_type_id', type: 'uuid', nullable: true })
  poTypeId: string; //PO Type

  @ManyToOne(
    () => PurchaseOrderTypeEntity,
    (poType) => poType.sapPurchaseOrders,
  )
  @JoinColumn({ name: 'po_type_id' })
  poType?: PurchaseOrderTypeEntity;

  @Column({ name: 'po_type_code', type: 'varchar', nullable: true })
  poTypeCode: string; //PO Type Code

  @Column({ name: 'purchasing_department_id', type: 'uuid', nullable: true })
  purchasingDepartmentId: string; //Purchasing organization

  @ManyToOne(
    () => PurchasingDepartmentEntity,
    (purchasingDepartment) => purchasingDepartment.sapPurchaseOrders,
  )
  @JoinColumn({ name: 'purchasing_department_id' })
  purchasingDepartment?: PurchasingDepartmentEntity;

  @Column({
    name: 'purchasing_department_code',
    type: 'varchar',
    nullable: true,
  })
  purchasingDepartmentCode: string; //Purchasing organization

  @Column({ name: 'purchasing_group_id', type: 'uuid', nullable: true })
  purchasingGroupId: string; //Purchasing Group

  @ManyToOne(
    () => PurchasingGroupEntity,
    (purchasingGroup) => purchasingGroup.sapPurchaseOrders,
  )
  @JoinColumn({ name: 'purchasing_group_id' })
  purchasingGroup?: PurchasingGroupEntity;

  @Column({ name: 'purchasing_group_Code', type: 'varchar', nullable: true })
  purchasingGroupCode: string; //Purchasing Group

  @Column({ name: 'po_created_at', type: 'date', nullable: true })
  poCreatedAt: Date; //Ngày tạo PO

  @Column({ name: 'exchange_rate', type: 'float', nullable: true })
  exchangeRate: number; //Exchange rage

  @Column({ name: 'currency_code', type: 'varchar', nullable: true })
  currencyCode: string; // Currency Code

  @Column({ name: 'supplier_id', type: 'uuid', nullable: true })
  supplierId: string;

  @ManyToOne(() => SupplierEntity, (supplier) => supplier.sapPurchaseOrders)
  @JoinColumn({ name: 'supplier_id' })
  supplier?: SupplierEntity;

  @Column({ name: 'supplier_code', type: 'varchar', nullable: true })
  supplierCode: string; // Supplier Code

  @Column({ type: 'jsonb', nullable: true })
  supplierInfo: string | object;

  @OneToMany(
    () => SapPurchaseOrderItemEntity,
    (item) => item.sapPurchaseOrder,
    { cascade: true },
  )
  items?: SapPurchaseOrderItemEntity[];

  @Column({ name: 'message_type', type: 'varchar', nullable: true })
  messageType: string; // Response from SAP

  @Column({ name: 'message', type: 'varchar', nullable: true })
  message: string;

  @Column({ name: 'ebeln', type: 'varchar', nullable: true })
  ebeln?: string;

  @Column({ name: 'status', type: 'varchar', nullable: true })
  status?: string;

  @BeforeInsert()
  @BeforeUpdate()
  jsonStringify() {
    if (this.supplierInfo && typeof this.supplierInfo !== 'string') {
      this.supplierInfo = JSON.stringify(this.supplierInfo);
    }
  }

  @AfterLoad()
  @AfterInsert()
  parseJsonString() {
    if (this.supplierInfo && typeof this.supplierInfo === 'string') {
      this.supplierInfo = isJSON(this.supplierInfo)
        ? JSON.parse(this.supplierInfo)
        : this.supplierInfo;
    }
  }
}
