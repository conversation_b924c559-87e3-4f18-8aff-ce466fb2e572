import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  OneToMany,
} from 'typeorm';
import { ECurrencyUnitStatus } from '../../domain/config/enums/currency-unit.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { BudgetEntity } from './budget.entity';
import { CurrencyUnitExchangeEntity } from './currency-unit-exchange.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { SapPurchaseOrderItemEntity } from './sap_purchase_order_item.entity';
import { PriceInformationRecordEntity } from './price_information_record.entity';

@Entity({ name: 'currency_unit' })
export class CurrencyUnitEntity extends BaseEntity {
  @Column({ name: 'name', type: 'varchar', nullable: false })
  name: string; //Tiền tệ

  @Column({
    name: 'currency_code',
    type: 'varchar',
    nullable: false,
    unique: true,
  })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  currencyCode: string; //Mã tiền tệ

  @Column({ name: 'description', type: 'varchar', nullable: true })
  description: string; //Mô tả

  @Column({
    name: 'status',
    type: 'varchar',
    enum: ECurrencyUnitStatus,
    default: ECurrencyUnitStatus.ACTIVE,
  })
  status: ECurrencyUnitStatus; //Trạng thái

  @OneToMany(() => BudgetEntity, (budget) => budget.currencyUnit)
  budgets?: BudgetEntity[];

  @OneToMany(
    () => CurrencyUnitExchangeEntity,
    (currencyExchange) => currencyExchange.currencyUnit,
  )
  currencyExchanges?: CurrencyUnitExchangeEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.currency)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.currency)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(
    () => SapPurchaseOrderItemEntity,
    (sapPOItem) => sapPOItem.currency,
  )
  sapPurchaseOrderItems?: SapPurchaseOrderItemEntity[];

  @OneToMany(() => PriceInformationRecordEntity, (pir) => pir.currency)
  pirs?: PriceInformationRecordEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(`${this.name}`) +
      ' ' +
      removeUnicode(`${this.currencyCode}`);
  }
}
