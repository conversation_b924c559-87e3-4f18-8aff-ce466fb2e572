import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  JoinT<PERSON>,
  ManyToMany,
  ManyToOne,
} from 'typeorm';
import { EComparisonType } from '../../domain/config/enums/comparision-type.enum';
import { EConditionType } from '../../domain/config/enums/condition-type.enum';
import { BaseEntity } from './base.entity';
import { BudgetCodeEntity } from './budget-code.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { CompanyEntity } from './company.entity';
import { ConditionEntity } from './condition.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { DepartmentEntity } from './department.entity';
import { FunctionUnitEntity } from './function-unit.entity';
import { PlantEntity } from './plant.entity';
import { ProcessTypeEntity } from './process-type.entity';
import { PurchaseOrderTypeEntity } from './purchase-order-type.entity';
import { PurchaseRequestTypeEntity } from './purchase-request-type.entity';
import { SectorEntity } from './sector.entity';

@Entity('condition_details')
export class ConditionDetailEntity extends BaseEntity {
  @Column({
    type: 'enum',
    enum: EConditionType,
    default: EConditionType.SECTOR,
    nullable: true,
  })
  type: EConditionType;

  @Column({
    type: 'enum',
    enum: EComparisonType,
  })
  comparisonType: EComparisonType;

  @ManyToMany(() => SectorEntity, (sector) => sector.conditions)
  @JoinTable({
    name: 'sector_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'sector_id', referencedColumnName: 'id' }],
  })
  sectors?: SectorEntity[];

  @ManyToMany(() => CompanyEntity, (company) => company.conditions)
  @JoinTable({
    name: 'company_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'company_id', referencedColumnName: 'id' }],
  })
  companies?: CompanyEntity[];

  @ManyToMany(() => BusinessUnitEntity, (bu) => bu.conditions)
  @JoinTable({
    name: 'business_unit_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'business_unit_id', referencedColumnName: 'id' },
    ],
  })
  businessUnits?: BusinessUnitEntity[];

  @ManyToMany(() => DepartmentEntity, (department) => department.conditions)
  @JoinTable({
    name: 'department_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'department_id', referencedColumnName: 'id' }],
  })
  departments?: DepartmentEntity[];

  @ManyToMany(
    () => CostcenterSubaccountEntity,
    (costCenter) => costCenter.conditions,
  )
  @JoinTable({
    name: 'cost_center_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'cost_center_id', referencedColumnName: 'id' },
    ],
  })
  costCenters?: CostcenterSubaccountEntity[];

  @ManyToMany(() => BudgetCodeEntity, (budgetCode) => budgetCode.conditions)
  @JoinTable({
    name: 'budget_code_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'budget_code_id', referencedColumnName: 'id' },
    ],
  })
  budgetCodes?: BudgetCodeEntity[];

  @ManyToMany(() => PurchaseRequestTypeEntity, (prType) => prType.conditions)
  @JoinTable({
    name: 'purchase_request_type_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'purchase_request_type_id', referencedColumnName: 'id' },
    ],
  })
  prTypes?: PurchaseRequestTypeEntity[];

  @ManyToMany(() => PurchaseOrderTypeEntity, (poType) => poType.conditions)
  @JoinTable({
    name: 'purchase_order_type_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'purchase_order_type_id', referencedColumnName: 'id' },
    ],
  })
  poTypes?: PurchaseOrderTypeEntity[];

  @ManyToMany(
    () => ProcessTypeEntity,
    (processType) => processType.conditionDetails,
  )
  @JoinTable({
    name: 'process_type_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'process_type_id', referencedColumnName: 'id' },
    ],
  })
  processTypes?: ProcessTypeEntity[];

  @ManyToMany(() => PlantEntity, (plant) => plant.conditionDetails)
  @JoinTable({
    name: 'plant_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'plant_id', referencedColumnName: 'id' }],
  })
  plants: PlantEntity[];

  @ManyToMany(
    () => FunctionUnitEntity,
    (functionUnit) => functionUnit.conditionDetails,
  )
  @JoinTable({
    name: 'function_unit_conditions',
    joinColumns: [{ name: 'condition_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [
      { name: 'function_unit_id', referencedColumnName: 'id' },
    ],
  })
  functionUnits?: FunctionUnitEntity[];

  @Column({ name: 'value_pr', type: 'float', default: 0, nullable: true })
  valuePR?: number;

  @Column({ name: 'value_po', type: 'float', default: 0, nullable: true })
  valuePO?: number;

  @Column({ name: 'value_budget', type: 'float', default: 0, nullable: true })
  valueBudget?: number;

  @Column({ type: 'float', default: 0, nullable: true })
  budgetOverrun?: number;

  @Column({ type: 'float', default: 0, nullable: true })
  differenceAmount?: number;

  @Column({ type: 'float', default: 0, nullable: true })
  budgetOverrunRate?: number;

  @Column({ type: 'float', default: 0, nullable: true })
  firstBudget?: number;

  @ManyToOne(() => ConditionEntity, (condition) => condition.conditionDetails)
  @JoinColumn({ name: 'condition_id' })
  condition?: ConditionEntity;

  @Column({ type: 'float', default: 0, nullable: true })
  differenceAmountAllItems?: number;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  @Index({ where: 'deleted_at IS NULL' })
  conditionId?: string;
}
