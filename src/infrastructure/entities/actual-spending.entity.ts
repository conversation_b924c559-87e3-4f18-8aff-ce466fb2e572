import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import {
  EActualType,
  EStatusActualSpending,
} from '../../domain/config/enums/actual-spending.enum';
import { BaseEntity } from './base.entity';
import { BusinessUnitEntity } from './business-unit.entity';
import { CompanyEntity } from './company.entity';
import { CostcenterSubaccountEntity } from './costcenter-subaccount.entity';
import { CurrencyUnitEntity } from './currency-unit.entity';
import { SupplierEntity } from './supplier.entity';
import { BudgetCodeEntity } from './budget-code.entity';

@Entity('actual_spendings')
@Index(['documentNumber', 'postingDate', 'entryDate'])
@Index(['documentNumber', 'postingDate'])
@Index(['assetCode', 'postingDate'])
export class ActualSpendingEntity extends BaseEntity {
  // SAP thực chi
  @Column({ type: 'int', nullable: true })
  sapActualId: number;

  // Mã ngân sách
  @Column({ type: 'uuid', nullable: true })
  budgetCodeId: string;

  @ManyToOne(() => BudgetCodeEntity)
  @JoinColumn({ name: 'budget_code_id' })
  budgetCode: BudgetCodeEntity;

  // Mã ngân sách
  @Column({ type: 'uuid', nullable: false })
  @Index()
  companyId: string;

  @ManyToOne(() => CompanyEntity)
  @JoinColumn({ name: 'company_id' })
  company?: CompanyEntity;

  // Cost Center
  @Column({ type: 'uuid', nullable: true })
  @Index()
  costCenterId: string;

  @ManyToOne(() => CostcenterSubaccountEntity)
  @JoinColumn({ name: 'cost_center_id' })
  costCenter?: CostcenterSubaccountEntity;

  // BU
  @Column({ type: 'uuid', nullable: true })
  @Index()
  buId: string;

  @ManyToOne(() => BusinessUnitEntity)
  @JoinColumn({ name: 'bu_id' })
  bu?: BusinessUnitEntity;

  // Kỳ
  // @Column({ type: 'int', nullable: false })
  // periodMonth: number;

  // @Column({ type: 'int', nullable: false })
  // periodYear: number;

  // Năm tài chính
  // @Column({ type: 'int', nullable: true })
  // fiscalYear: number;

  // Internal Order
  @Column({ type: 'varchar', nullable: true })
  internalOrder: string;

  // Internal Oder Name
  @Column({ type: 'varchar', nullable: true })
  internalOrderName: string;

  // ID Nhà cung cấp
  @Column({ type: 'uuid', nullable: true })
  @Index()
  supplierId: string;

  @ManyToOne(() => SupplierEntity)
  @JoinColumn({ name: 'supplier_id' })
  supplier?: SupplierEntity;

  // Tên nhà cung cấp
  @Column({ type: 'varchar', nullable: true })
  supplierName: string;

  // PO SAP
  @Column({ type: 'int', nullable: true })
  poSapId: number; // approval-engine

  // Ngày chứng từ
  @Column({ type: 'date', nullable: true })
  docDate: Date;

  // Ngày ghi có
  @Column({ type: 'date', nullable: true })
  postingDate: Date;

  // Ngày nhập liệu
  @Column({ type: 'date', nullable: true })
  entryDate: Date;

  // Mã chứng từ thanh toán
  @Column({ type: 'varchar', nullable: true })
  paymentDoc: string;

  // Mã E-Invoice
  @Column({ type: 'varchar', nullable: true })
  eInvoiceNumber: string;

  // Mã chứng từ FI
  @Column({ type: 'varchar', nullable: true })
  documentNumber: string;

  // Mã số chứng từ nhập kho
  @Column({ type: 'varchar', nullable: true })
  inventoryDoc: string;

  // Mã số Invoice
  @Column({ type: 'varchar', nullable: true })
  invoiceNumber: string;

  // Loại giao dịch
  // @Column({
  //   type: 'enum',
  //   enum: ETransactionType,
  //   default: ETransactionType.MANUAL,
  // })
  // transactionType: ETransactionType;

  // GL Account
  @Column({ type: 'varchar', nullable: true })
  glAccount: string;

  // Tax Code
  @Column({ type: 'varchar', nullable: true })
  taxCode: string;

  // Tax Rate
  @Column({ type: 'decimal', default: 0, nullable: true })
  taxRate: number;

  // Số tiền invoice
  @Column({ type: 'decimal', default: 0, nullable: false })
  docAmount: number;

  // Đơn vị tiền tệ
  @Column({ type: 'uuid', nullable: false })
  @Index()
  currencyId: string;

  @ManyToOne(() => CurrencyUnitEntity)
  @JoinColumn({ name: 'currency_id' })
  currency?: CurrencyUnitEntity;

  // Số tiền quy đổi
  @Column({ type: 'decimal', default: 0, nullable: true })
  localCurrencyAmount: number;

  // Đơn vị tiền tệ quy đổi
  @Column({ type: 'uuid', nullable: true })
  @Index()
  localCurrencyId: string;

  @ManyToOne(() => CurrencyUnitEntity)
  @JoinColumn({ name: 'local_currency_id' })
  localCurrency?: CurrencyUnitEntity;

  // Tỷ giá quy đổi
  @Column({ type: 'decimal', default: 1, nullable: false })
  exchangeRate: number;

  // Mã nơi nhận
  @Column({ type: 'varchar', nullable: true })
  receiverCode: string;

  // Tên nơi nhận
  @Column({ type: 'varchar', nullable: true })
  receiverName: string;

  // Profit Center
  @Column({ type: 'varchar', nullable: true })
  profitCenter: string;

  // Diễn giải Profit Center
  @Column({ type: 'varchar', nullable: true })
  profitCenterDescription: string;

  // Nhóm Profit Center
  @Column({ type: 'varchar', nullable: true })
  profitCenterGroup: string;

  // Diễn giải nhóm Profit Center
  @Column({ type: 'varchar', nullable: true })
  profitCenterGroupDescription: string;

  // Tình trạng
  @Column({
    type: 'enum',
    enum: EStatusActualSpending,
    default: EStatusActualSpending.CONFIRMED,
  })
  status: EStatusActualSpending;

  // Document type
  @Column({ type: 'varchar', nullable: true })
  documentType: string;

  // Invoice Business transaction
  @Column({ type: 'varchar', nullable: true })
  invocieBusinessTransaction: string;

  @Column({ type: 'date', nullable: true })
  paymentDate: Date;

  @Column({ type: 'varchar', nullable: true })
  paymentBusinessTransaction: string;

  @Column({ type: 'varchar', nullable: true })
  payementDocType: string;

  @Column({ type: 'date', nullable: true })
  inventoryDocDate: Date;

  @Column({ type: 'varchar', nullable: true })
  poItem: string;

  @Column({ type: 'date', nullable: true })
  poDate: Date;

  @Column({ type: 'varchar', nullable: true })
  internalOrderType: string;

  @Column({ type: 'varchar', nullable: true })
  costCenterName: string;

  @Column({ type: 'varchar', nullable: true })
  functionalArea: string;

  @Column({ type: 'varchar', nullable: true })
  functionalAreaName: string;

  @Column({ type: 'varchar', nullable: true })
  taxCodeName: string;

  @Column({ type: 'decimal', default: 0, nullable: true })
  docPaymentAmount: number;

  @Column({ type: 'decimal', default: 0, nullable: true })
  localCurrencyPaymentAmount: number;

  @Column({ type: 'varchar', nullable: true })
  debitCreditInd: string;

  @Column({ type: 'varchar', nullable: true })
  accountType: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ type: 'varchar', nullable: true })
  note: string;

  @Column({ type: 'varchar', nullable: true })
  assetCode: string;

  @Column({
    type: 'enum',
    enum: EActualType,
    default: EActualType.INVOICE_PAYMENT_DAILY,
  })
  actualType: EActualType;

  @Column({ type: 'varchar', nullable: true })
  companyCode?: string;

  @Column({ type: 'varchar', nullable: true })
  costCenterCode?: string;

  @Column({ type: 'varchar', nullable: true })
  buCode?: string;

  @Column({ type: 'varchar', nullable: true })
  supplierCode?: string;

  @Column({ type: 'varchar', nullable: true })
  currencyCode?: string;

  @Column({ type: 'varchar', nullable: true })
  localCurrencyCode?: string;
}
