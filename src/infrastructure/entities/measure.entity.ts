import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  OneToMany,
} from 'typeorm';
import { BaseEntity } from '../../infrastructure/entities/base.entity';
import { removeUnicode } from '../../utils/common';
import { MaterialEntity } from './material.entity';
import { PurchaseOrderDetailEntity } from './purchase_order_detail.entity';
import { PurchaseRequestDetailEntity } from './purchase_request_detail.entity';
import { EMeasureStatus } from '../../domain/config/enums/measure.enum';
import { isJSON } from 'class-validator';
import { SolomonPurchaseOrderItemEntity } from './solomon-purchase-order-item.entity';

@Entity('measures')
export class MeasureEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EMeasureStatus,
    default: EMeasureStatus.ACTIVE,
  })
  status?: EMeasureStatus; //Trạng thái

  @Column({ name: 'code_conversions', type: 'jsonb', nullable: true })
  codeConversions?: string | object[]; //Chuyển đổi mã

  @OneToMany(() => MaterialEntity, (material) => material.measure)
  materials: MaterialEntity[];

  @OneToMany(() => PurchaseOrderDetailEntity, (poDetail) => poDetail.measure)
  purchaseOrderDetails?: PurchaseOrderDetailEntity[];

  @OneToMany(() => PurchaseRequestDetailEntity, (prDetail) => prDetail.measure)
  purchaseRequestDetails?: PurchaseRequestDetailEntity[];

  @OneToMany(
    () => SolomonPurchaseOrderItemEntity,
    (solomonPOItem) => solomonPOItem.measure,
  )
  solomonPurchaseOrderItems?: SolomonPurchaseOrderItemEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.name) + ' ' + removeUnicode(this.code);
  }

  @BeforeInsert()
  @BeforeUpdate()
  jsonStringify() {
    if (this.codeConversions && typeof this.codeConversions !== 'string') {
      this.codeConversions = JSON.stringify(this.codeConversions);
    }
  }

  @AfterLoad()
  @AfterInsert()
  @AfterUpdate()
  parseJsonString() {
    if (this.codeConversions && typeof this.codeConversions === 'string') {
      this.codeConversions = isJSON(this.codeConversions)
        ? JSON.parse(this.codeConversions)
        : this.codeConversions;
    }
  }
}
