import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { EPurchasingGroupStatus } from '../../domain/config/enums/purchasing.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { MaterialEntity } from './material.entity';
import { PriceInformationRecordEntity } from './price_information_record.entity';
import { PurchaseOrderEntity } from './purchase_order.entity';
import { PurchaseRequestEntity } from './purchase_request.entity';
import { SapPurchaseOrderEntity } from './sap_purchase_order.entity';
import { SectorEntity } from './sector.entity';

@Entity('purchasing_groups')
export class PurchasingGroupEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  code: string;

  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ name: 'sector_id', type: 'uuid', nullable: false })
  sectorId: string; //Nhóm vật tư

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EPurchasingGroupStatus,
    default: EPurchasingGroupStatus.ACTIVE,
  })
  status: EPurchasingGroupStatus; //Trạng thái

  @ManyToOne(() => SectorEntity, (sector) => sector.purchasingGroups)
  sector?: SectorEntity;

  @OneToMany(() => MaterialEntity, (material) => material.purchasingGroup)
  materials?: MaterialEntity[];

  @OneToMany(() => PurchaseRequestEntity, (pr) => pr.purchaseGroup)
  purchaseRequests?: PurchaseRequestEntity[];

  @OneToMany(() => PurchaseOrderEntity, (po) => po.purchaseGroup)
  purchaseOrders?: PurchaseOrderEntity[];

  @OneToMany(() => SapPurchaseOrderEntity, (sapPO) => sapPO.purchasingGroup)
  sapPurchaseOrders?: SapPurchaseOrderEntity[];

  @OneToMany(
    () => PriceInformationRecordEntity,
    (purchaseGroup) => purchaseGroup.purchaseGroup,
  )
  pirs?: PriceInformationRecordEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.code) + ' ' + removeUnicode(this.name);
  }
}
