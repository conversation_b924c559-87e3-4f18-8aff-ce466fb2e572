import { Injectable } from '@nestjs/common';
import { HttpService as NestHttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AxiosResponse } from 'axios';
import { Observable } from 'rxjs';

@Injectable()
export class HttpService {
  constructor(
    private readonly http: NestHttpService,
    private readonly configService: ConfigService
  ) { }

  get<T>(url: string, params?, headers?): Observable<AxiosResponse<T>> {
    return this.http.get<T>(url, { params, headers });
  }

  post<T>(url: string, data, headers?): Observable<AxiosResponse<T>> {
    return this.http.post<T>(url, data, { headers });
  }
}
