import { Injectable } from '@nestjs/common';
import { PurchaseOrderModel } from '../../../domain/model/purchase_order.model';
import {
  convertToGMT7,
  formatCurrencyVND,
  generateApprovalToken,
} from '../../../utils/common';
import {
  Priority,
  Status,
} from '../../../domain/config/enums/purchase-order.enum';

@Injectable()
export class emailTemplateService {
  getApprovalPREmailHtml(
    purchase,
    to: string,
    approvalUrl: string,
    level: any,
  ): string {
    const token = generateApprovalToken(to, purchase.id);
    const approvalUrlWithToken = `${approvalUrl}&token=${encodeURIComponent(token)}`;
    const total = purchase.details.reduce(
      (sum, item) => sum + Number(item.total_amount),
      0,
    );

    const budget = purchase.details[0].budget || 0;

    const remainingBudget = purchase.details[0].remainingBudget || 0;

    const detailsRows = purchase.details
      .map(
        (detail, index) => `
      <tr>
        <td>${index + 1}</td>
        <td>${detail?.budgetCode?.code || ''}</td>
        <td>${detail?.material?.code || ''}</td>
        <td>${detail?.materialName || ''}</td>
        <td>${detail?.materialGroup?.name || ''}</td>
        <td>${convertToGMT7(detail?.deliveryTime, 'DD-MM-YYYY') || ''}</td>
        <td>${formatCurrencyVND(detail?.estimatedPrice) || formatCurrencyVND(0)}</td>
        <td>${detail?.standardQuantity || 0}</td>
        <td>${detail?.inventoryNumber || 0}</td>
        <td>${detail?.quantity || 0}</td>
        <td class="right-align">${detail?.unit || ''}</td>
        <td class="left-align">${formatCurrencyVND(Number(detail?.totalAmount)) || formatCurrencyVND(0)}</td>
        <td>${detail?.note || ''}</td>
      </tr>
    `,
      )
      .join('');

    return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <style>
          body {
            margin: 0;
            color: rgba(50, 47, 49, 1);
          }

          h1,
          h2,
          h3,
          h4,
          h5 {
            margin: 0;
          }

          /* atomics */

          .p-6 {
            padding: 24px;
          }

          .pb-6 {
            padding-bottom: 24px;
          }

          .gap-6 {
            gap: 24px;
          }

          /* Grid system */

          .grid {
            display: grid;
          }

          .grid-cols-1 {
            grid-template-columns: repeat(1, minmax(0, 1fr));
          }

          @media (min-width: 992px) {
            .lg\:grid-cols-3 {
              grid-template-columns: repeat(3, minmax(0, 1fr));
            }
          }

          /* Components */

          .tag {
            padding: 2px 12px;
            border-radius: 500px;
            font-family: Open Sans;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            letter-spacing: 0.02em;
            text-align: center;
          }

          .tag-default {
            color: rgba(50, 47, 49, 1);
            background-color: rgba(241, 241, 241, 1);
          }

          .tag-success {
            color: rgba(0, 144, 12, 1);
            background-color: rgba(228, 255, 227, 1);
          }

          .detail-label {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            letter-spacing: 0.02em;
            text-align: left;
          }

          .detail-value {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            text-align: left;
            padding: 8px 0;
          }

          .detail-link {
            display: inline-block;
            font-size: inherit;
            font-weight: inherit;
            line-height: inherit;
            color: rgba(0, 144, 12, 1);
            text-decoration: none;
          }

          .ap-table-wrapper {
            overflow: hidden;
            border: 1px solid rgba(246, 246, 246, 1);
          }

          .ap-table-container {
            overflow-x: auto;
            width: 100%;
          }

          .ap-table {
            border-collapse: collapse;
            width: 100%;
          }

          .ap-table th {
            text-align: left;
            padding: 16px;
            background-color: rgba(230, 237, 234, 1);
          }

          .ap-table td {
            text-align: left;
            padding: 16px;
          }

          .ap-table th, 
          .ap-table td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
          }

          .ap-table .general-info-cell {
            display: flex;
            align-items: center;
            gap: 80px;
          }

          .detail-wrapper {
            background-color: rgba(255, 255, 255, 1);
          }

          .detail-card-wrapper {
            border: 1px solid rgba(241, 241, 241, 1);
            border-radius: 12px;
          }

          .detail-card-title-wrapper {
            display: flex;
            align-items: center;
            gap: 12px;
          }

          .detail-card-title {
            font-size: 28px;
            font-weight: 700;
            line-height: 36px;
            letter-spacing: -0.02em;
            text-align: left;
            color: rgba(50, 47, 49, 1);
          }
        </style>
      </head>
      <body>
        <div class="detail-wrapper p-6">
          <p>Dear <strong>${/*level?.data?.data?.firstName || ''*/ level?.name || ''} ${level?.data?.data?.lastName || ''}</strong>,</p>
          <p>Anh/Chị có một PR đang chờ duyệt có thông tin như sau:</p>
          <table style="width: 100%; border-spacing: 24px;">
            <tr>
              <td>
                <div class="detail-label">Người yêu cầu: ${purchase?.requester?.lastName || ''} ${purchase?.requester?.firstName || ''}</div>
              </td>
              <td>
                <div class="detail-label">Kiểm tra ngân sách: ${purchase?.isCheckBudget ? 'Có' : 'Không'}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="detail-label">Ngày yêu cầu: ${convertToGMT7(purchase?.createdAt, 'HH:mm DD-MM-YYYY')}</div>
              </td>
              <td>
                <div class="detail-label">Mã ngân sách: ${purchase?.budgetCode?.name || ''}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="detail-label">Đơn vị kinh doanh: ${purchase?.businessUnit?.name || ''}</div>
              </td>
              <td>
                <div class="detail-label">Tổng ngân sách: ${formatCurrencyVND(budget)}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="detail-label">Loại PR: ${purchase?.typePr?.name || ''}</div>
              </td>
              <td>
                <div class="detail-label">Ngân sách dự kiến mua: ${formatCurrencyVND(total)}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="detail-label">Độ ưu tiên: ${purchase ? (purchase.priority === Priority.Urgent ? 'Khẩn cấp' : purchase.priority === Priority.Important ? 'Quan trọng' : 'Bình thường') : ''}</div>
              </td>
              <td>
                <div class="detail-label">Ngân sách còn lại: ${formatCurrencyVND(remainingBudget)}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="detail-label">Lý do/mục đích mua sắm: ${purchase?.reason || ''}</div>
              </td>
              <td>
                <div class="detail-label">File đính kèm: ${purchase?.attachments || ''}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="detail-label">Mã trại: ${purchase?.plant?.code || ''}</div>
              </td>
              <td>
                <div class="detail-label">Mã loại quy trình: ${purchase?.processType?.code || ''}</div>
              </td>
            </tr>
          </table>
          <div class="pb-6">
            <div>
              <div class="ap-table-wrapper">
                <div class="ap-table-container">
                  <table class="ap-table" style="width: 100%; border-spacing: 24px;">
                    <thead>
                      <tr>
                        <th style="width: 50px">STT</th>
                        <th style="width: 120px">Mã ngân sách</th>
                        <th style="width: 120px">Mã vật tư</th>
                        <th style="width: 120px">Tên vật tư</th>
                        <th style="width: 140px">Nhóm vật tư</th>
                        <th style="width: 100px">Ngày giao hàng</th>
                        <th style="width: 100px">Đơn giá ước tính</th>
                        <th style="width: 100px">SL định mức</th>
                        <th style="width: 100px">SL tồn kho</th>
                        <th style="width: 100px">SL yêu cầu</th>
                        <th style="width: 80px">Đơn vị tính</th>
                        <th style="width: 100px">Tổng tiền</th>
                        <th style="width: 140px">Ghi chú</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${detailsRows}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <p>Vui lòng <a href="${approvalUrlWithToken}">nhấn vào đây</a> để xem chi tiết và phê duyệt.</p>
        </div>
      </body>
    </html>
  `;
  }

  getApprovalPOEmailHtml(
    purchase: PurchaseOrderModel,
    to: string,
    approvalUrl: string,
    level: any,
  ): string {
    const token = generateApprovalToken(to, purchase.id);
    const approvalUrlWithToken = `${approvalUrl}&token=${encodeURIComponent(token)}`;
    const total = purchase.details.reduce(
      (sum, item) => sum + Number(item.totalAmount),
      0,
    );

    const budget = purchase.details[0].budget || 0;

    const totalAmountVAT = purchase.details.reduce((sum, item) => {
      const vatRate = item.vat / 100; // Chuyển tỷ lệ VAT từ phần trăm sang tỷ lệ thập phân
      const totalAmountWithVAT = item.totalAmount * (1 + vatRate);
      return sum + totalAmountWithVAT;
    }, 0);

    const remainingBudget = purchase.details[0].remainingBudget || 0;

    const detailsRows = purchase.details
      .map(
        (detail, index) => `
    <tr>
      <td>${index + 1}</td>
      <td>${detail?.budgetCode?.code || ''}</td>
      <td>${detail?.material?.code || ''}</td>
      <td>${detail?.materialName || ''}</td>
      <td>${detail?.materialGroup?.name || ''}</td>
      <td>${convertToGMT7(detail?.deliveryTime, 'DD-MM-YYYY') || ''}</td>
      <td>${detail?.pir?.id || '-'}</td>
      <td>${detail?.supplier?.name || ''}</td>
      <td>${formatCurrencyVND(detail?.estimatedPrice) || formatCurrencyVND(0)}</td>
      <td>${formatCurrencyVND(detail?.purchasePrice) || formatCurrencyVND(0)}</td>
      <td>${detail?.quantity || 0}</td>
      <td>${detail?.unit || ''}</td>
      <td>${detail?.vat || 0}</td>
      <td>${formatCurrencyVND(detail?.totalAmount * (1 + detail?.vat / 100)) || formatCurrencyVND(0)}</td>
      <td>${detail?.note || ''}</td>
    </tr>
  `,
      )
      .join('');

    return `
    <!DOCTYPE html>
    <html lang="en">
      <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <style>
        body {
          margin: 0;
          color: rgba(50, 47, 49, 1);
        }

        h1,
        h2,
        h3,
        h4,
        h5 {
          margin: 0;
        }

        /* atomics */

        .p-6 {
          padding: 24px;
        }

        .pb-6 {
          padding-bottom: 24px;
        }

        .gap-6 {
          gap: 24px;
        }

        /* Grid system */

        .grid {
          display: grid;
        }

        .grid-cols-1 {
          grid-template-columns: repeat(1, minmax(0, 1fr));
        }

        @media (min-width: 992px) {
          .lg\:grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr));
          }
        }

        /* Components */

        .tag {
          padding: 2px 12px;
          border-radius: 500px;
          font-family: Open Sans;
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          letter-spacing: 0.02em;
          text-align: center;
        }

        .tag-default {
          color: rgba(50, 47, 49, 1);
          background-color: rgba(241, 241, 241, 1);
        }

        .tag-success {
          color: rgba(0, 144, 12, 1);
          background-color: rgba(228, 255, 227, 1);
        }

        .detail-label {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          letter-spacing: 0.02em;
          text-align: left;
        }

        .detail-value {
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          text-align: left;
          padding: 8px 0;
        }

        .detail-link {
          display: inline-block;
          font-size: inherit;
          font-weight: inherit;
          line-height: inherit;
          color: rgba(0, 144, 12, 1);
          text-decoration: none;
        }

        .ap-table-wrapper {
          overflow: hidden;
          border: 1px solid rgba(246, 246, 246, 1);
        }

        .ap-table-container {
          overflow-x: auto;
          width: 100%;
        }

        .ap-table {
          border-collapse: collapse;
          width: 100%;
        }

        .ap-table th {
          text-align: left;
          padding: 16px;
          background-color: rgba(230, 237, 234, 1);
        }

        .ap-table td {
          text-align: left;
          padding: 16px;
        }

        .ap-table th, 
          .ap-table td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
          }

        .ap-table .general-info-cell {
          display: flex;
          align-items: center;
          gap: 80px;
        }

        .detail-wrapper {
          background-color: rgba(255, 255, 255, 1);
        }

        .detail-card-wrapper {
          border: 1px solid rgba(241, 241, 241, 1);
          border-radius: 12px;
        }

        .detail-card-title-wrapper {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .detail-card-title {
          font-size: 28px;
          font-weight: 700;
          line-height: 36px;
          letter-spacing: -0.02em;
          text-align: left;
          color: rgba(50, 47, 49, 1);
        }
      </style>
    </head>
      <body>
      <div class="detail-wrapper p-6">
      <p>Dear <strong>${/*level?.data?.data?.firstName*/ level?.name || ''} ${level?.data?.data?.lastName || ''}</strong>,</p>
      <p>Anh/Chị có một PO đang chờ duyệt có thông tin như sau:</p>
        <table style="width: 100%; border-spacing: 24px;">
          <tr>
            <td>
              <div class="detail-label">Loại PO: ${purchase?.typePo?.name || ''}</div>
            </td>
            <td>
              <div class="detail-label">Tổng ngân sách: ${formatCurrencyVND(budget)}</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="detail-label">Người yêu cầu: ${purchase?.requester?.lastName || ''} ${purchase?.requester?.firstName || ''}</div>
            </td>
            <td>
              <div class="detail-label">Ngân sách dự kiến mua: ${formatCurrencyVND(total)}</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="detail-label">Ngày yêu cầu: ${convertToGMT7(purchase?.createdAt, 'HH:mm DD-MM-YYYY')}</div>
            </td>
            <td>
              <div class="detail-label">Ngân sách còn lại: ${formatCurrencyVND(remainingBudget)}</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="detail-label">Lý do/mục đích mua sắm: ${purchase?.reason || ''}</div>
            </td>
            <td>
              <div class="detail-label">Tổng tiền chưa gồm VAT: ${formatCurrencyVND(total)}</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="detail-label"></div>
            </td>
            <td>
              <div class="detail-label">Tổng tiền bao gồm VAT: ${formatCurrencyVND(totalAmountVAT)}</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="detail-label">Mã trại: ${purchase?.plant?.code || ''}</div>
            </td>
            <td>
              <div class="detail-label">Mã loại quy trình: ${purchase?.processType?.code || ''}</div>
            </td>
          </tr>
        </table>
        <div class="pb-6">
          <div>
            <div class="detail-card-title-wrapper pb-6">
              <h3 class="detail-card-title">Chi tiết PO</h3>
            </div>
            <div class="ap-table-wrapper">
              <div class="ap-table-container">
              <table class="ap-table" style="width: 100%; border-spacing: 24px;">
                  <thead>
                    <tr>
                      <th style="width: 50px">STT</th>
                      <th style="width: 120px">Mã ngân sách</th>
                      <th style="width: 120px">Mã vật tư</th>
                      <th style="width: 120px">Tên vật tư</th>
                      <th style="width: 140px">Nhóm vật tư</th>
                      <th style="width: 100px">Ngày giao hàng</th>
                      <th style="width: 100px">Hồ sơ giá</th>
                      <th style="width: 100px">Nhà cung cấp</th>
                      <th style="width: 100px">Đơn Giá ước tính</th>
                      <th style="width: 100px">Đơn Giá mua</th>
                      <th style="width: 100px">Số lượng yêu cầu</th>
                      <th style="width: 80px">ĐVT</th>
                      <th style="width: 100px">VAT (%)</th>
                      <th style="width: 100px">Tổng tiền (+VAT)</th>
                      <th style="width: 140px">Ghi chú</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${detailsRows}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <p>Vui lòng <a href="${approvalUrlWithToken}">nhấn vào đây</a> để xem chi tiết và phê duyệt.</p>
      </div>
    </body>
    </html>
  `;
  }

  getSendMailRequesterHtml(
    purchase,
    to: string,
    approvalUrl: string,
    requester: any,
    status: Status,
  ): string {
    const token = generateApprovalToken(to, purchase.id);
    const approvalUrlWithToken = `${approvalUrl}&token=${encodeURIComponent(token)}`;

    return `
   <html>
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <link
         href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,400;1,500;1,700&display=swap"
         rel="stylesheet"
         />
      <link
         href="https://fonts.googleapis.com/css2?family=Be+Vietnam:wght@100;300;400;500;600;700;800&display=swap"
         rel="stylesheet"
         />
      <style>
         #resetpw {
         width: 40%;
         min-width: 600px;
         max-width: 700px;
         margin: auto;
         font-family: 'Roboto', sans-serif;
         font-size: 14px;
         padding: 40px;
         background-color: #d9f3e1;
         }
         div {
         box-sizing: border-box;
         }
         header a {
         float: right;
         margin-top: 10px;
         text-decoration: none;
         color: #6658f3;
         font-weight: 700;
         font-family: 'Roboto', sans-serif;
         }
         .invitation {
         margin-top: 30px;
         background-color: #ffffff;
         border-radius: 14px 14px 40px 14px;
         }
         .invitation img {
         width: 50%;
         margin: 0 auto;
         }
         .invitation p {
         margin: 8px 0;
         line-height: 170%;
         }
         .invitation .info {
         padding: 20px 40px 30px;
         }
         .invitation .info h1 {
         color: #070d14;
         font-size: 32px;
         margin: 0px;
         margin-bottom: 25px;
         }
         .invitation .info__thanks {
         color: #3c3b3b;
         margin-top: 20px;
         }
         .invitation .info__link {
         color: #07a7c1;
         width: 100%;
         padding: 10px 15px;
         background-color: #f3fcfe;
         display: block;
         box-sizing: border-box;
         font-weight: 500;
         }
         .invitation .info__contact {
         border-top: 1px solid #f0f3f8;
         padding-top: 25px;
         margin-top: 80px;
         }
         .invitation .info__button {
         height: 42px;
         width: 180px;
         display: block;
         background-color: #00a033;
         margin: 15px 0;
         text-decoration: none;
         border: none;
         color: #ffffff;
         font-weight: bold;
         font-size: 16px;
         border-radius: 4px;
         cursor: pointer;
         padding: 10px;
         box-sizing: border-box;
         text-align: center;
         }
         .invitation .info__button:focus {
         outline: none;
         }
         .invitation .info__expired {
         font-style: italic;
         }
         footer p {
         color: #727070;
         font-size: 12px;
         text-align: center;
         margin: 5px 0;
         }
         footer {
         margin-top: 20px;
         }
         footer div {
         display: flex;
         justify-content: center !important;
         }
         footer div a {
         margin: 5px;
         color: #727070 !important;
         font-size: 12px;
         cursor: pointer;
         }
         @media screen and (max-width: 575px) {
         html {
         font-size: 40%;
         }
         #resetpw {
         width: 100%;
         min-width: unset;
         padding: 20px 0;
         }
         .resetpw {
         padding: 0;
         }
         header {
         padding: 0 15px;
         }
         }
      </style>
   </head>
   <body>
      <div id="resetpw">
         <div class="resetpw">
            <div class="invitation">
               <div style="width: 100%; padding: 20px 30px 0 30px"><img src="https://e-purchase.exceltech.vn/assets/logo-CXB2NTOy.svg" /></div>
               <div class="info">
                  <h1>Thông báo</h1>
                  <p>
                     Dear Mr/Ms ${requester?.firstName || ''} ${requester?.lastName || ''}, <br />
                     ${purchase?.typePr ? 'PR' : 'PO'} mã ${purchase?.id || ''} của bạn đã ${status == 'Approved' ? 'được phê duyệt' : status == 'Rejected' ? 'bị từ chối' : status == 'Closed' ? 'bị đóng' : 'bị huỷ'}. <br />
                     Vui lòng <a href="${approvalUrlWithToken}">nhấn vào đây</a> để xem chi tiết và phê duyệt.
                  </b>
                  </p>
               </div>
            </div>
         </div>
      </div>
   </body>
</html>
  `;
  }
}
