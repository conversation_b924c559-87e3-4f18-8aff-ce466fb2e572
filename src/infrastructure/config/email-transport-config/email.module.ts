import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { emailTransportService } from './email.service';
import { emailTemplateService } from './email-template.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
  ],
  providers: [emailTransportService, emailTemplateService],
  exports: [emailTransportService, emailTemplateService]
})
export class EmailTransportgModule { }
