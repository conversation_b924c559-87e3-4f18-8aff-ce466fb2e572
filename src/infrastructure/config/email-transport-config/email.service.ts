import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class emailTransportService {
  private transporter;

  constructor(private configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: 'smtp.office365.com',
      port: 587,
      auth: {
        user: this.configService.get<string>('MAIL_SMTP_USER'),
        pass: this.configService.get<string>('MAIL_SMTP_PASS'),
      },
      pool: true,
      maxConnections: 3, // Giới hạn số kết nối đồng thời
      maxMessages: 100, // Mỗi connection gửi tối đa 100 mail
      rateLimit: 5, // Tối đa 5 email/giây
    });
  }

  getTransporter() {
    return this.transporter;
  }
}
