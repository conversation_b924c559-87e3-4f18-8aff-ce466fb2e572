import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseConfig } from '../../../domain/config/database.interface';

@Injectable()
export class EnvironmentConfigService implements DatabaseConfig {
  private configService: ConfigService = new ConfigService();
  // constructor(private configService: ConfigService) {}

  getDatabaseHost(): string {
    return this.configService.get<string>('DATABASE_HOST');
  }

  getDatabasePort(): number {
    return this.configService.get<number>('DATABASE_PORT');
  }

  getDatabaseUser(): string {
    return this.configService.get<string>('DATABASE_USER');
  }

  getDatabasePassword(): string {
    return this.configService.get<string>('DATABASE_PASSWORD');
  }

  getDatabaseName(): string {
    return this.configService.get<string>('DATABASE_NAME');
  }

  getDatabaseSchema(): string {
    return this.configService.get<string>('DATABASE_SCHEMA');
  }

  getDatabaseSync(): boolean {
    return this.configService.get<string>('DATABASE_SYNCHRONIZE') === 'true';
  }

  getSapBasicUsername(): string {
    return this.configService.get<string>('SAP_BASIC_USERNAME');
  }
  getSapBasicPassword(): string {
    if (this.configService.get<string>('ENV') == 'PRODUCTION') {
      return this.configService.get<string>('SAP_BASIC_PASSWORD');
    }
    //@TODO: build jenkin có $ bị lỗi không add env được
    return '@GrEEnf$ed@2025';
  }

  getSapBasicUsernameFood(): string {
    return this.configService.get<string>('SAP_BASIC_USERNAME_FOOD');
  }
  getSapBasicPasswordFood(): string {
    if (this.configService.get<string>('ENV') == 'PRODUCTION') {
      return this.configService.get<string>('SAP_BASIC_PASSWORD_FOOD');
    }
    //@TODO: build jenkin có $ bị lỗi không add env được
    return 'GREENfEED@2025';
  }

  //BASIC AUTH
  getBasicUser(): string {
    return this.configService.get<string>('BASIC_USER');
  }
  getBasicPass(): string {
    return this.configService.get<string>('BASIC_PASS');
  }
  ///Solomon Basic Auth
  getSolomonBasicUsername(): string {
    return this.configService.get<string>('SOLOMON_BASIC_USERNAME');
  }
  getSolomonBasicPassword(): string {
    if (this.configService.get<string>('ENV') == 'PRODUCTION') {
      return this.configService.get<string>('SOLOMON_BASIC_PASSWORD');
    }
    //@TODO: build jenkin có $ bị lỗi không add env được
    return 'Jr5$eTq9!Bz1';
  }
}
