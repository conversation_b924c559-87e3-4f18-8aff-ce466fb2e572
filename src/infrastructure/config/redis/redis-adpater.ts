import { IoAdapter } from '@nestjs/platform-socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { EnvironmentConfigService } from '../environment-config/environment-config.service';

export class RedisIoAdapter extends IoAdapter {
  private adapterConstructor: ReturnType<typeof createAdapter>;
  private config: EnvironmentConfigService = new EnvironmentConfigService();

  // async connectToRedis(): Promise<void> {
  //   const pubClient = createClient({
  //     url: `redis://${this.config.getRedisUserName()}:${this.config.getRedisPassword()}@${this.config.getRedisHost()}:${this.config.getRedisPort()}`,
  //   });
  //   const subClient = pubClient.duplicate();

  //   await Promise.all([pubClient.connect(), subClient.connect()]);

  //   this.adapterConstructor = createAdapter(pubClient, subClient);
  // }

  // createIOServer(port: number, options?: ServerOptions): any {
  //   const server = super.createIOServer(port, options);
  //   server.adapter(this.adapterConstructor);
  //   return server;
  // }

  // redisOptions = () => {
  //   let redisOptions: RedisOptions['options'] = {
  //     host: this.config.getRedisHost(),
  //     port: Number(this.config.getRedisPort()),
  //   };

  //   if (!!this.config.getRedisUserName && !!this.config.getRedisPassword) {
  //     redisOptions = {
  //       ...redisOptions,
  //       username: this.config.getRedisUserName(),
  //       password: this.config.getRedisPassword(),
  //       keepAlive: 1,
  //       retryAttempts: 20,
  //       retryDelay: 3000,
  //       enableAutoPipelining: true,
  //       reconnectOnError: (err: Error) => {
  //         console.log(24, err);
  //         return true;
  //       },
  //     };
  //   }

  //   if (!!this.config.getRedisTLS && this.config.getRedisTLS) {
  //     redisOptions = {
  //       ...redisOptions,
  //       tls: {},
  //     };
  //   }

  //   return redisOptions;
  // };
}
