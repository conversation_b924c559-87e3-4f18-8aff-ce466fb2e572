import { Module } from '@nestjs/common';
import { RedisUsecases } from '../../../usecases/redis.usecases';

@Module({
  imports: [
    // CacheModule.registerAsync({
    //   imports: [EnvironmentConfigModule],
    //   inject: [EnvironmentConfigService],
    //   useFactory: async (config: EnvironmentConfigService) => ({
    //     host: config.getRedisHost(),
    //     port: config.getRedisPort(),
    //   }),
    //   isGlobal: true,
    // }),
  ],
  providers: [RedisUsecases],
  exports: [RedisUsecases],
})
export class RedisModule {}
