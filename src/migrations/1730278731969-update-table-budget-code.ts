import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableBudgetCode1730278731969 implements MigrationInterface {
    name = 'UpdateTableBudgetCode1730278731969'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code" DROP CONSTRAINT "FK_6407d802b09366170ad7abd6973"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "costcenter_subaccount_id"`);
        await queryRunner.query(`CREATE TYPE "public"."budget_code_budget_type_enum" AS ENUM('OPEX', 'CAPEX')`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "budget_type" "public"."budget_code_budget_type_enum" NOT NULL DEFAULT 'OPEX'`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "cost_id" uuid`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "internal_order" character varying`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD CONSTRAINT "FK_f5c0388c10d544edfdff774fa90" FOREIGN KEY ("cost_id") REFERENCES "costs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code" DROP CONSTRAINT "FK_f5c0388c10d544edfdff774fa90"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "internal_order"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "cost_id"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "budget_type"`);
        await queryRunner.query(`DROP TYPE "public"."budget_code_budget_type_enum"`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "costcenter_subaccount_id" uuid`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD CONSTRAINT "FK_6407d802b09366170ad7abd6973" FOREIGN KEY ("costcenter_subaccount_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
