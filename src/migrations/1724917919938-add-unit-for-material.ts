import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUnitForMaterial1724917919938 implements MigrationInterface {
    name = 'AddUnitForMaterial1724917919938'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" ADD "unit" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "unit"`);
    }

}
