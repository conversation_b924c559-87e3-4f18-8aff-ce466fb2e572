import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableMaterialGroup1733209242315 implements MigrationInterface {
    name = 'UpdateTableMaterialGroup1733209242315'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_groups" DROP CONSTRAINT "FK_e1096daedc8d91f3da633521b43"`);
        await queryRunner.query(`CREATE TABLE "process_type_material_groups" ("material_group_id" uuid NOT NULL, "process_type_id" uuid NOT NULL, CONSTRAINT "PK_ad7c127ee4b74de78704a6717bd" PRIMARY KEY ("material_group_id", "process_type_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_c51505b338a072bb73f25f6a9e" ON "process_type_material_groups" ("material_group_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_504b916a92f7c3300d3f94eb54" ON "process_type_material_groups" ("process_type_id") `);
        await queryRunner.query(`CREATE TABLE "business_owner_material_groups" ("material_group_id" uuid NOT NULL, "business_owner_id" uuid NOT NULL, CONSTRAINT "PK_614001593ce817b43d07d97eda3" PRIMARY KEY ("material_group_id", "business_owner_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_11c04422efd31330092bcc4eb5" ON "business_owner_material_groups" ("material_group_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_9b8e9f28efb6dd1a1609a81792" ON "business_owner_material_groups" ("business_owner_id") `);
        await queryRunner.query(`ALTER TABLE "material_groups" DROP COLUMN "process_type_id"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "tax_rate"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "tax_rate" numeric DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "process_type_material_groups" ADD CONSTRAINT "FK_c51505b338a072bb73f25f6a9ea" FOREIGN KEY ("material_group_id") REFERENCES "material_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "process_type_material_groups" ADD CONSTRAINT "FK_504b916a92f7c3300d3f94eb540" FOREIGN KEY ("process_type_id") REFERENCES "process_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_owner_material_groups" ADD CONSTRAINT "FK_11c04422efd31330092bcc4eb5d" FOREIGN KEY ("material_group_id") REFERENCES "material_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "business_owner_material_groups" ADD CONSTRAINT "FK_9b8e9f28efb6dd1a1609a81792f" FOREIGN KEY ("business_owner_id") REFERENCES "business_owners"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "business_owner_material_groups" DROP CONSTRAINT "FK_9b8e9f28efb6dd1a1609a81792f"`);
        await queryRunner.query(`ALTER TABLE "business_owner_material_groups" DROP CONSTRAINT "FK_11c04422efd31330092bcc4eb5d"`);
        await queryRunner.query(`ALTER TABLE "process_type_material_groups" DROP CONSTRAINT "FK_504b916a92f7c3300d3f94eb540"`);
        await queryRunner.query(`ALTER TABLE "process_type_material_groups" DROP CONSTRAINT "FK_c51505b338a072bb73f25f6a9ea"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "tax_rate"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "tax_rate" character varying`);
        await queryRunner.query(`ALTER TABLE "material_groups" ADD "process_type_id" uuid`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9b8e9f28efb6dd1a1609a81792"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_11c04422efd31330092bcc4eb5"`);
        await queryRunner.query(`DROP TABLE "business_owner_material_groups"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_504b916a92f7c3300d3f94eb54"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c51505b338a072bb73f25f6a9e"`);
        await queryRunner.query(`DROP TABLE "process_type_material_groups"`);
        await queryRunner.query(`ALTER TABLE "material_groups" ADD CONSTRAINT "FK_e1096daedc8d91f3da633521b43" FOREIGN KEY ("process_type_id") REFERENCES "process_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
