import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePrPoDetail1756277905493 implements MigrationInterface {
  name = 'UpdatePrPoDetail1756277905493';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD "warehouse_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD "warehouse_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_72c233b79b9bc2bf1d8a95331dc" FOREIGN KEY ("warehouse_id") REFERENCES "warehouses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD CONSTRAINT "FK_9bcaf9cb3cae9a16e947dbc7bf2" FOREIGN KEY ("warehouse_id") REFERENCES "warehouses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP CONSTRAINT "FK_9bcaf9cb3cae9a16e947dbc7bf2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_72c233b79b9bc2bf1d8a95331dc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP COLUMN "warehouse_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP COLUMN "warehouse_id"`,
    );
  }
}
