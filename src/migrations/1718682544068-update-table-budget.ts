import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableBudget1718682544068 implements MigrationInterface {
    name = 'UpdateTableBudget1718682544068'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget" DROP CONSTRAINT "FK_2c7ad1c8678519c009c54632242"`);
        await queryRunner.query(`ALTER TABLE "budget" RENAME COLUMN "adjust_buget_id" TO "adjust_budget_id"`);
        await queryRunner.query(`ALTER TABLE "budget" ADD CONSTRAINT "FK_8cc093a4da682e6d7d6515c08a5" FOREIGN KEY ("adjust_budget_id") REFERENCES "budget"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget" DROP CONSTRAINT "FK_8cc093a4da682e6d7d6515c08a5"`);
        await queryRunner.query(`ALTER TABLE "budget" RENAME COLUMN "adjust_budget_id" TO "adjust_buget_id"`);
        await queryRunner.query(`ALTER TABLE "budget" ADD CONSTRAINT "FK_2c7ad1c8678519c009c54632242" FOREIGN KEY ("adjust_buget_id") REFERENCES "budget"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
