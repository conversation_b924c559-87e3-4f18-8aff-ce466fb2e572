import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableMaterial1724750699014 implements MigrationInterface {
    name = 'UpdateTableMaterial1724750699014'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" ADD "company_id" uuid`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "business_unit_id" uuid`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "department_id" uuid`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_d428775a3fe92626ec53f47bb17" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_dfa1b5d84703b987d699e8b4202" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_5fc67f5e96cd7d2d51de7b8d700" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_5fc67f5e96cd7d2d51de7b8d700"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_dfa1b5d84703b987d699e8b4202"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_d428775a3fe92626ec53f47bb17"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "department_id"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "business_unit_id"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "company_id"`);
    }

}
