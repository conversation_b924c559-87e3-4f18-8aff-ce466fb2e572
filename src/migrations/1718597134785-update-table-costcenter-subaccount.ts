import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableCostcenterSubaccount1718597134785 implements MigrationInterface {
    name = 'UpdateTableCostcenterSubaccount1718597134785'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" DROP CONSTRAINT "FK_c870593c9de2c3b23c229cb973d"`);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" DROP CONSTRAINT "FK_e4cf6940572c83082a01a4d9568"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "name"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "code" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "UQ_fce55f56a5375bd325e5c4a3dc4" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "description" character varying`);
        await queryRunner.query(`CREATE TYPE "public"."costcenter_subaccount_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "status" "public"."costcenter_subaccount_status_enum" DEFAULT 'ACTIVE'`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "sector_id" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "company_id" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "business_unit_id" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "department_id" uuid NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_fce55f56a5375bd325e5c4a3dc" ON "costcenter_subaccount" ("code") `);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_a55253675dc0a97d13eba132ed3" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_8b7f70450f8e34511293f03af7d" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_c3d092c04244caf63c23d78c250" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_bb41334fabe5eb37708eee8e727" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" ADD CONSTRAINT "FK_c870593c9de2c3b23c229cb973d" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" ADD CONSTRAINT "FK_e4cf6940572c83082a01a4d9568" FOREIGN KEY ("business_owner_id") REFERENCES "business_owners"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" DROP CONSTRAINT "FK_e4cf6940572c83082a01a4d9568"`);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" DROP CONSTRAINT "FK_c870593c9de2c3b23c229cb973d"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_bb41334fabe5eb37708eee8e727"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_c3d092c04244caf63c23d78c250"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_8b7f70450f8e34511293f03af7d"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_a55253675dc0a97d13eba132ed3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fce55f56a5375bd325e5c4a3dc"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "department_id"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "business_unit_id"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "company_id"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "sector_id"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."costcenter_subaccount_status_enum"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "UQ_fce55f56a5375bd325e5c4a3dc4"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "name" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" ADD CONSTRAINT "FK_e4cf6940572c83082a01a4d9568" FOREIGN KEY ("business_owner_id") REFERENCES "business_owners"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" ADD CONSTRAINT "FK_c870593c9de2c3b23c229cb973d" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
