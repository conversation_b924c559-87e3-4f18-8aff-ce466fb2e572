import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateActualSpending1735182410028 implements MigrationInterface {
  name = 'UpdateActualSpending1735182410028';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_a7e0a24897e53770fb6b93e130" ON "actual_spendings" ("company_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7478310621c6b1a47401bc8154" ON "actual_spendings" ("cost_center_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7b2de07c88acc1be5f661908af" ON "actual_spendings" ("bu_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a3bb09ca54373ce67ae0963611" ON "actual_spendings" ("supplier_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_81df34dd55c33c92e0aeb05e6c" ON "actual_spendings" ("currency_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_47714043a5199b70bcec3bb4b9" ON "actual_spendings" ("local_currency_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b184f62c9d9dc9a49733b898ae" ON "actual_spendings" ("asset_code", "posting_date") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8f595a2bdda12dca1a442b4a95" ON "actual_spendings" ("document_number", "posting_date") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_8f595a2bdda12dca1a442b4a95"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b184f62c9d9dc9a49733b898ae"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_47714043a5199b70bcec3bb4b9"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_81df34dd55c33c92e0aeb05e6c"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a3bb09ca54373ce67ae0963611"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7b2de07c88acc1be5f661908af"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7478310621c6b1a47401bc8154"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a7e0a24897e53770fb6b93e130"`,
    );
  }
}
