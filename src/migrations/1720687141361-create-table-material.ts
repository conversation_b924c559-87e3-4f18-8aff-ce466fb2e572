import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableMaterial1720687141361 implements MigrationInterface {
    name = 'CreateTableMaterial1720687141361'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."materials_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "materials" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "material_type_id" uuid NOT NULL, "unit" character varying NOT NULL, "material_group_id" uuid NOT NULL, "sector_id" uuid NOT NULL, "status" "public"."materials_status_enum" NOT NULL, "search_value" character varying, CONSTRAINT "PK_2fd1a93ecb222a28bef28663fa0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_5f59d61b51ffcb1db994286221" ON "materials" ("code") `);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_021872b8b643bb253dbce571f2c" FOREIGN KEY ("material_type_id") REFERENCES "material_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_3a7993738b9d93e864811a03d52" FOREIGN KEY ("material_group_id") REFERENCES "material_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_b93bdab4a0d8d6a424714dfc8d9" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_b93bdab4a0d8d6a424714dfc8d9"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_3a7993738b9d93e864811a03d52"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_021872b8b643bb253dbce571f2c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5f59d61b51ffcb1db994286221"`);
        await queryRunner.query(`DROP TABLE "materials"`);
        await queryRunner.query(`DROP TYPE "public"."materials_status_enum"`);
    }

}
