import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePrQuantity1753063885364 implements MigrationInterface {
  name = 'UpdatePrQuantity1753063885364';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "quantity" TYPE numeric USING "quantity"::numeric`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "quantity" TYPE integer USING "quantity"::integer`,
    );
  }
}
