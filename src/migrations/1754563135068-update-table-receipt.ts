import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableReceipt1754563135068 implements MigrationInterface {
  name = 'UpdateTableReceipt1754563135068';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" ADD "errors" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" DROP COLUMN "errors"`,
    );
  }
}
