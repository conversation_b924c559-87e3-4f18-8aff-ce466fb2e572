import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateConditionDetailTable1741927071270
  implements MigrationInterface
{
  name = 'UpdateConditionDetailTable1741927071270';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "condition_details" ADD "first_budget" double precision DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."condition_details_type_enum" RENAME TO "condition_details_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."condition_details_type_enum" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN', 'CHECK_BUDGET', 'DIFFERENCE_AMOUNT', 'BUDGET_OVERRUN_RATE', 'PROCESS_TYPE', 'PLANT', 'FUNCTION_UNIT', 'DIFFERENCE_AMOUNT_ALL_ITEMS', 'FIRST_BUDGET')`,
    );
    await queryRunner.query(
      `ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum" USING "type"::"text"::"public"."condition_details_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."condition_details_type_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."condition_details_type_enum_old" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN', 'CHECK_BUDGET', 'DIFFERENCE_AMOUNT', 'BUDGET_OVERRUN_RATE', 'PROCESS_TYPE', 'PLANT', 'FUNCTION_UNIT', 'DIFFERENCE_AMOUNT_ALL_ITEMS')`,
    );
    await queryRunner.query(
      `ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum_old" USING "type"::"text"::"public"."condition_details_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`,
    );
    await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."condition_details_type_enum_old" RENAME TO "condition_details_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "condition_details" DROP COLUMN "first_budget"`,
    );
  }
}
