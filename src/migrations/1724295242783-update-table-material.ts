import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableMaterial1724295242783 implements MigrationInterface {
    name = 'UpdateTableMaterial1724295242783'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."profit_centers_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "profit_centers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "description" character varying, "status" "public"."profit_centers_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "UQ_f66f7e8de6ea09c3d3defe3f6fa" UNIQUE ("code"), CONSTRAINT "PK_67f3059748317db0bc4081c69c6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f66f7e8de6ea09c3d3defe3f6f" ON "profit_centers" ("code") `);
        await queryRunner.query(`ALTER TABLE "materials" ADD "profit_center_id" uuid`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_7826375ecc2a259a1ab01bb4d91" FOREIGN KEY ("profit_center_id") REFERENCES "profit_centers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_7826375ecc2a259a1ab01bb4d91"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "profit_center_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f66f7e8de6ea09c3d3defe3f6f"`);
        await queryRunner.query(`DROP TABLE "profit_centers"`);
        await queryRunner.query(`DROP TYPE "public"."profit_centers_status_enum"`);
    }

}
