import { MigrationInterface, QueryRunner } from 'typeorm';

export class FunctionRemoveUnicode1719373096234 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION remove_unicode(input_str TEXT)
            RETURNS TEXT AS $$
            DECLARE
                result TEXT := lower(input_str);
            BEGIN
                result := regexp_replace(result, 'à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ', 'a', 'g');
                result := regexp_replace(result, 'è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ', 'e', 'g');
                result := regexp_replace(result, 'ì|í|ị|ỉ|ĩ', 'i', 'g');
                result := regexp_replace(result, 'ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ', 'o', 'g');
                result := regexp_replace(result, 'ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ', 'u', 'g');
                result := regexp_replace(result, 'ỳ|ý|ỵ|ỷ|ỹ', 'y', 'g');
                result := regexp_replace(result, 'đ', 'd', 'g');
                result := regexp_replace(result, '!|@|%|\\^|\\*|\\(|\\)|\\+|=|<|>|\\?|\\/|,|\\.|:|;|\\-|''|\\s|"|&|#|\\[|\\]|~|\\$|_', '', 'g');
                result := regexp_replace(result, '-+', '', 'g'); -- replace multiple hyphens with a single hyphen
                result := regexp_replace(result, '^\\-+|\\-+$', '', 'g'); -- remove leading or trailing hyphens
                RETURN result;
            END;
            $$ LANGUAGE plpgsql;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP FUNCTION IF EXISTS remove_unicode(TEXT);
        `);
  }
}
