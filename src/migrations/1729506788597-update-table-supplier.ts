import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableSupplier1729506788597 implements MigrationInterface {
    name = 'UpdateTableSupplier1729506788597'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "suppliers" ADD "description" character varying`);
        await queryRunner.query(`ALTER TABLE "suppliers" ADD "tax_code" character varying`);
        await queryRunner.query(`ALTER TYPE "public"."supplier_sectors_status_enum" RENAME TO "supplier_sectors_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."supplier_sectors_status_enum" AS ENUM('ACTIVE', 'INACTIVE')`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" TYPE "public"."supplier_sectors_status_enum" USING "status"::"text"::"public"."supplier_sectors_status_enum"`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" SET DEFAULT 'ACTIVE'`);
        await queryRunner.query(`DROP TYPE "public"."supplier_sectors_status_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."supplier_sectors_status_enum_old" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" TYPE "public"."supplier_sectors_status_enum_old" USING "status"::"text"::"public"."supplier_sectors_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" SET DEFAULT 'ACTIVE'`);
        await queryRunner.query(`DROP TYPE "public"."supplier_sectors_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."supplier_sectors_status_enum_old" RENAME TO "supplier_sectors_status_enum"`);
        await queryRunner.query(`ALTER TABLE "suppliers" DROP COLUMN "tax_code"`);
        await queryRunner.query(`ALTER TABLE "suppliers" DROP COLUMN "description"`);
    }

}
