import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateMaterial1729224948404 implements MigrationInterface {
    name = 'UpdateMaterial1729224948404'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_dfa1b5d84703b987d699e8b4202"`);
        await queryRunner.query(`CREATE TABLE "material_business_units" ("material_id" uuid NOT NULL, "business_unit_id" uuid NOT NULL, CONSTRAINT "PK_af2001f68e8581a5554e7cd0b2b" PRIMARY KEY ("material_id", "business_unit_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_cb6cee4780e27b08ace2569f15" ON "material_business_units" ("material_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_e7e464fd8a6ecf12cdec3276a6" ON "material_business_units" ("business_unit_id") `);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "business_unit_id"`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "description" character varying`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "purchasing_group_id" uuid`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "purchasing_department_id" uuid`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_dbfbb3cf79de03d8c198686aed8" FOREIGN KEY ("purchasing_group_id") REFERENCES "purchasing_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_97cde7b55722c776294335a5bc7" FOREIGN KEY ("purchasing_department_id") REFERENCES "purchasing_departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_business_units" ADD CONSTRAINT "FK_cb6cee4780e27b08ace2569f157" FOREIGN KEY ("material_id") REFERENCES "materials"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "material_business_units" ADD CONSTRAINT "FK_e7e464fd8a6ecf12cdec3276a6e" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_business_units" DROP CONSTRAINT "FK_e7e464fd8a6ecf12cdec3276a6e"`);
        await queryRunner.query(`ALTER TABLE "material_business_units" DROP CONSTRAINT "FK_cb6cee4780e27b08ace2569f157"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_97cde7b55722c776294335a5bc7"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_dbfbb3cf79de03d8c198686aed8"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "purchasing_department_id"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "purchasing_group_id"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "business_unit_id" uuid`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e7e464fd8a6ecf12cdec3276a6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cb6cee4780e27b08ace2569f15"`);
        await queryRunner.query(`DROP TABLE "material_business_units"`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_dfa1b5d84703b987d699e8b4202" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
