import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableNotificationForm1723433830335 implements MigrationInterface {
    name = 'CreateTableNotificationForm1723433830335'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "notification_forms" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "name" character varying NOT NULL, "type" character varying NOT NULL, "description" character varying, "status" character varying DEFAULT 'ACTIVE', "content" character varying, CONSTRAINT "PK_638f26643daf09431fe8fd8fe87" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "notification_forms"`);
    }

}
