import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTablePlant1720755803389 implements MigrationInterface {
    name = 'CreateTablePlant1720755803389'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."plants_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "plants" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "sector_id" uuid NOT NULL, "status" "public"."plants_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_7056d6b283b48ee2bb0e53bee60" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_01730098ec1effab1a37d1b450" ON "plants" ("code") `);
        await queryRunner.query(`ALTER TABLE "plants" ADD CONSTRAINT "FK_1135501f366d6ed785ac1bd49da" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "plants" DROP CONSTRAINT "FK_1135501f366d6ed785ac1bd49da"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_01730098ec1effab1a37d1b450"`);
        await queryRunner.query(`DROP TABLE "plants"`);
        await queryRunner.query(`DROP TYPE "public"."plants_status_enum"`);
    }

}
