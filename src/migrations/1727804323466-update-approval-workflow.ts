import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateApprovalWorkflow1727804323466 implements MigrationInterface {
    name = 'UpdateApprovalWorkflow1727804323466'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "approval_workflows" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "send_email_to_creator" boolean NOT NULL DEFAULT false, "parent_process_id" uuid, CONSTRAINT "PK_f326145fd927565de30b360264b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "staff_approval_workflows" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "receive_email" boolean NOT NULL DEFAULT false, "level" integer DEFAULT '0', "staff_id" uuid, "approval_workflow_id" uuid, CONSTRAINT "PK_681d5d31cb071bbec934d095155" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "process_approval_workflows" ("approval_workflow_id" uuid NOT NULL, "process_id" uuid NOT NULL, CONSTRAINT "PK_825173e0461aeccca6871993be1" PRIMARY KEY ("approval_workflow_id", "process_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_58c5e1672ce78f603115460e07" ON "process_approval_workflows" ("approval_workflow_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_2672976858ba0a35d4674564db" ON "process_approval_workflows" ("process_id") `);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_type_enum" RENAME TO "condition_details_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum" USING "type"::"text"::"public"."condition_details_type_enum"`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "approval_workflows" ADD CONSTRAINT "FK_5b72572392cb97b8a42784504a8" FOREIGN KEY ("parent_process_id") REFERENCES "processes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD CONSTRAINT "FK_2658b756272ae3d811a24e5875a" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD CONSTRAINT "FK_be805ef3d6eb928c86e4bc871a8" FOREIGN KEY ("approval_workflow_id") REFERENCES "approval_workflows"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "process_approval_workflows" ADD CONSTRAINT "FK_58c5e1672ce78f603115460e079" FOREIGN KEY ("approval_workflow_id") REFERENCES "approval_workflows"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "process_approval_workflows" ADD CONSTRAINT "FK_2672976858ba0a35d4674564db9" FOREIGN KEY ("process_id") REFERENCES "processes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "process_approval_workflows" DROP CONSTRAINT "FK_2672976858ba0a35d4674564db9"`);
        await queryRunner.query(`ALTER TABLE "process_approval_workflows" DROP CONSTRAINT "FK_58c5e1672ce78f603115460e079"`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP CONSTRAINT "FK_be805ef3d6eb928c86e4bc871a8"`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP CONSTRAINT "FK_2658b756272ae3d811a24e5875a"`);
        await queryRunner.query(`ALTER TABLE "approval_workflows" DROP CONSTRAINT "FK_5b72572392cb97b8a42784504a8"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum_old" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum_old" USING "type"::"text"::"public"."condition_details_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_type_enum_old" RENAME TO "condition_details_type_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2672976858ba0a35d4674564db"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_58c5e1672ce78f603115460e07"`);
        await queryRunner.query(`DROP TABLE "process_approval_workflows"`);
        await queryRunner.query(`DROP TABLE "staff_approval_workflows"`);
        await queryRunner.query(`DROP TABLE "approval_workflows"`);
    }

}
