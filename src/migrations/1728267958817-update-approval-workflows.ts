import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateApprovalWorkflows1728267958817 implements MigrationInterface {
    name = 'UpdateApprovalWorkflows1728267958817'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."staff_approval_workflows_status_enum" AS ENUM('LEVEL_1', 'LEVEL_2', 'LEVEL_3', 'LEVEL_4', 'ASSIGN', 'POSITION')`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD "status" "public"."staff_approval_workflows_status_enum" DEFAULT 'LEVEL_1'`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD "position_id" uuid`);
        await queryRunner.query(`CREATE TYPE "public"."staff_approval_workflows_return_rule_enum" AS ENUM('0', '1', '2')`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD "return_rule" "public"."staff_approval_workflows_return_rule_enum" DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "approval_workflows" ADD "name" character varying DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD CONSTRAINT "FK_27b590361a72200526d3164aca8" FOREIGN KEY ("position_id") REFERENCES "positions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP CONSTRAINT "FK_27b590361a72200526d3164aca8"`);
        await queryRunner.query(`ALTER TABLE "approval_workflows" DROP COLUMN "name"`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP COLUMN "return_rule"`);
        await queryRunner.query(`DROP TYPE "public"."staff_approval_workflows_return_rule_enum"`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP COLUMN "position_id"`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."staff_approval_workflows_status_enum"`);
    }

}
