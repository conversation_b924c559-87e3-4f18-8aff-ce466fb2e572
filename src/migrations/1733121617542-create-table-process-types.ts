import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableProcessTypes1733121617542 implements MigrationInterface {
    name = 'CreateTableProcessTypes1733121617542'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."process_types_status_enum" AS ENUM('ACTIVE', 'INACTIVE')`);
        await queryRunner.query(`CREATE TABLE "process_types" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, "has_inventory_standard" boolean DEFAULT false, "status" "public"."process_types_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_ec1f6afa2802c5d6a1fd315aece" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_5971d2cdc49161bec41ec4ea5c" ON "process_types" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE TABLE "process_type_conditions" ("condition_id" uuid NOT NULL, "process_type_id" uuid NOT NULL, CONSTRAINT "PK_efbc94add729b90ca34675585fb" PRIMARY KEY ("condition_id", "process_type_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_c946febc3bfdd576876fb6955f" ON "process_type_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_dc6388d0ee53ad0f0980c41d5f" ON "process_type_conditions" ("process_type_id") `);
        await queryRunner.query(`ALTER TABLE "material_groups" ADD "process_type_id" uuid`);
        await queryRunner.query(`ALTER TABLE "material_groups" ADD CONSTRAINT "FK_e1096daedc8d91f3da633521b43" FOREIGN KEY ("process_type_id") REFERENCES "process_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "process_type_conditions" ADD CONSTRAINT "FK_c946febc3bfdd576876fb6955f9" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "process_type_conditions" ADD CONSTRAINT "FK_dc6388d0ee53ad0f0980c41d5f7" FOREIGN KEY ("process_type_id") REFERENCES "process_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "process_type_conditions" DROP CONSTRAINT "FK_dc6388d0ee53ad0f0980c41d5f7"`);
        await queryRunner.query(`ALTER TABLE "process_type_conditions" DROP CONSTRAINT "FK_c946febc3bfdd576876fb6955f9"`);
        await queryRunner.query(`ALTER TABLE "material_groups" DROP CONSTRAINT "FK_e1096daedc8d91f3da633521b43"`);
        await queryRunner.query(`ALTER TABLE "material_groups" DROP COLUMN "process_type_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_dc6388d0ee53ad0f0980c41d5f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c946febc3bfdd576876fb6955f"`);
        await queryRunner.query(`DROP TABLE "process_type_conditions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5971d2cdc49161bec41ec4ea5c"`);
        await queryRunner.query(`DROP TABLE "process_types"`);
        await queryRunner.query(`DROP TYPE "public"."process_types_status_enum"`);
    }

}
