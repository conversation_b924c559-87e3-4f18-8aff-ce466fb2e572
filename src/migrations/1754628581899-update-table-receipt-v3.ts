import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableReceiptV31754628581899 implements MigrationInterface {
  name = 'UpdateTableReceiptV31754628581899';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" DROP COLUMN "errors"`,
    );
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" DROP COLUMN "po_id_error"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" ADD "po_id_error" integer`,
    );
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" ADD "errors" jsonb`,
    );
  }
}
