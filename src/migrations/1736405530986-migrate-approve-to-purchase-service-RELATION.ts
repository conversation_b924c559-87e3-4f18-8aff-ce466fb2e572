import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateApproveToPurchaseServiceRELATION1736405530986
  implements MigrationInterface
{
  name = 'MigrateApproveToPurchaseServiceRELATION1736405530986';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" RENAME COLUMN "currency" TO "currency_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" RENAME COLUMN "sector" TO "sector_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" RENAME COLUMN "pir" TO "pir_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ALTER COLUMN "currency_id" TYPE uuid USING "currency_id"::uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "sector_id" TYPE uuid USING "sector_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ADD CONSTRAINT "FK_109e7e25c2595bb62b4fa76b30f" FOREIGN KEY ("material_id") REFERENCES "materials"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ADD CONSTRAINT "FK_0c5e82dcdc3761aad23ed9319a9" FOREIGN KEY ("cost_center_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ADD CONSTRAINT "FK_ad6e13180ed8a414779725be19f" FOREIGN KEY ("material_group_id") REFERENCES "material_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ADD CONSTRAINT "FK_361c280e0d12509597cd97e08af" FOREIGN KEY ("currency_id") REFERENCES "currency_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ADD CONSTRAINT "FK_24bd8e17fcef5792847cbe6ecbf" FOREIGN KEY ("bu_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ADD CONSTRAINT "FK_f33ddbc7053a90d012c8da0c460" FOREIGN KEY ("material_code_id") REFERENCES "materials"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ADD CONSTRAINT "FK_4444c0a9b96a5b856718f482186" FOREIGN KEY ("purchase_organization_id") REFERENCES "purchasing_departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ADD CONSTRAINT "FK_2d2bce2526d28528c54b32dbfb2" FOREIGN KEY ("plant_id") REFERENCES "plants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" ADD CONSTRAINT "FK_ff2c65a85654e73c9102aa743f0" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" ADD CONSTRAINT "FK_3211614f9e20fde0f876411b434" FOREIGN KEY ("po_type_id") REFERENCES "purchase_order_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" ADD CONSTRAINT "FK_a05cae3a0ad08bca815f5992ab0" FOREIGN KEY ("purchasing_department_id") REFERENCES "purchasing_departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" ADD CONSTRAINT "FK_782e7505b1e035de7d33cd7ed5d" FOREIGN KEY ("purchasing_group_id") REFERENCES "purchasing_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" ADD CONSTRAINT "FK_f972ad1272550e7ceb2d284a320" FOREIGN KEY ("supplier_id") REFERENCES "suppliers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_16b49bf0c3fd2d0f78116211eec" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_25ea06d8d6643ef4ccc30bbbcfc" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_aac55ab2b25818239f9e9babd1a" FOREIGN KEY ("requester_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_1a81a5e67b54feb8fe5e2ffb602" FOREIGN KEY ("type_po_id") REFERENCES "purchase_order_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_790c68f2cf2b6c7f951dc49a6b9" FOREIGN KEY ("cost_center_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_4b8fbde773a847fe5e110582117" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_b24010834b0702f01eaad01c413" FOREIGN KEY ("purchase_org_id") REFERENCES "purchasing_departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_7c98437e6492717f2ea691a0115" FOREIGN KEY ("purchase_group_id") REFERENCES "purchasing_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_00f647cd3bb4b61c87b9f4d4a43" FOREIGN KEY ("purchaser_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_323bca1951ab6cb0d56ea85d23c" FOREIGN KEY ("currency_id") REFERENCES "currency_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_1c8286362f2667b1f952649ea3d" FOREIGN KEY ("process_type_id") REFERENCES "process_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_cc07d708e3f7c7040ba49a8c7aa" FOREIGN KEY ("plant_id") REFERENCES "plants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_2223defdd12616fb7c13a100951" FOREIGN KEY ("function_unit_id") REFERENCES "function_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD CONSTRAINT "FK_7556c2e751a8f4e30aba41d69f9" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_8f1b8fc8a5bf2d8ec7ebb63152c" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_b8f08b2fb2b996b9b7b56881306" FOREIGN KEY ("cost_center_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_6536900656f3224493fc42417c4" FOREIGN KEY ("material_code_id") REFERENCES "materials"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_b90bde57029ea1280d9d61fbb9c" FOREIGN KEY ("material_group_id") REFERENCES "material_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_e2fef007791c69e6bc233ca21ee" FOREIGN KEY ("pir_id") REFERENCES "price_information_records"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_0940d2151c34c4df95cafcfb02d" FOREIGN KEY ("supplier_id") REFERENCES "suppliers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_2adb10c12c93a8374a2fd72824f" FOREIGN KEY ("currency_id") REFERENCES "currency_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_84e37eaadebdb701b52d2053540" FOREIGN KEY ("budget_id") REFERENCES "budget"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_affc3367d2f0b2dd6b7f127e8aa" FOREIGN KEY ("adjust_budget_id") REFERENCES "budget"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD CONSTRAINT "FK_a3393b3b15158b36dc1806d2663" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD CONSTRAINT "FK_08864cd58a21cf1461dfebe5447" FOREIGN KEY ("cost_center_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD CONSTRAINT "FK_9673cc5985e815abb2b9ac9207a" FOREIGN KEY ("material_code_id") REFERENCES "materials"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD CONSTRAINT "FK_5f92ed28d3e9d95fa7914b53a11" FOREIGN KEY ("material_group_id") REFERENCES "material_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD CONSTRAINT "FK_6eff1022a7129fb5db133ecdca7" FOREIGN KEY ("budget_id") REFERENCES "budget"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD CONSTRAINT "FK_0bc0e7b84e94dda35a48b4daecc" FOREIGN KEY ("adjust_budget_id") REFERENCES "budget"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_9c0506e06012bb72cf22c15f6a9" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_8242bc6362ab353728164c10a53" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_f4a79f7b7ce5945c1c2f7e07174" FOREIGN KEY ("requester_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_a8e963e3c448c38088f309e4776" FOREIGN KEY ("type_pr_id") REFERENCES "purchase_request_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_735f0de16162cf2ea8e91f44cb9" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_5c071010551edec7247549a57c5" FOREIGN KEY ("cost_center_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_3d735ec72ca11a7cdb868746cab" FOREIGN KEY ("purchase_org_id") REFERENCES "purchasing_departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_d5d18fe42586ced0ec954a9cd83" FOREIGN KEY ("purchase_group_id") REFERENCES "purchasing_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_08c95dee42cc71f4f9519866d5f" FOREIGN KEY ("purchaser_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_d8931ec0006b39d698430a86c01" FOREIGN KEY ("process_type_id") REFERENCES "process_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_3633ce83f12b4c6f28a555bbffa" FOREIGN KEY ("plant_id") REFERENCES "plants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_70a5b47e27c14132ee49bbce02d" FOREIGN KEY ("function_unit_id") REFERENCES "function_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ADD CONSTRAINT "FK_3ee8e94c75dcdbe9029954d0f87" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_3ee8e94c75dcdbe9029954d0f87"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_70a5b47e27c14132ee49bbce02d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_3633ce83f12b4c6f28a555bbffa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_d8931ec0006b39d698430a86c01"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_08c95dee42cc71f4f9519866d5f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_d5d18fe42586ced0ec954a9cd83"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_3d735ec72ca11a7cdb868746cab"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_5c071010551edec7247549a57c5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_735f0de16162cf2ea8e91f44cb9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_a8e963e3c448c38088f309e4776"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_f4a79f7b7ce5945c1c2f7e07174"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_8242bc6362ab353728164c10a53"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_requests" DROP CONSTRAINT "FK_9c0506e06012bb72cf22c15f6a9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP CONSTRAINT "FK_0bc0e7b84e94dda35a48b4daecc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP CONSTRAINT "FK_6eff1022a7129fb5db133ecdca7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP CONSTRAINT "FK_5f92ed28d3e9d95fa7914b53a11"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP CONSTRAINT "FK_9673cc5985e815abb2b9ac9207a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP CONSTRAINT "FK_08864cd58a21cf1461dfebe5447"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP CONSTRAINT "FK_a3393b3b15158b36dc1806d2663"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_affc3367d2f0b2dd6b7f127e8aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_84e37eaadebdb701b52d2053540"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_2adb10c12c93a8374a2fd72824f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_0940d2151c34c4df95cafcfb02d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_e2fef007791c69e6bc233ca21ee"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_b90bde57029ea1280d9d61fbb9c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_6536900656f3224493fc42417c4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_b8f08b2fb2b996b9b7b56881306"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_8f1b8fc8a5bf2d8ec7ebb63152c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_7556c2e751a8f4e30aba41d69f9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_2223defdd12616fb7c13a100951"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_cc07d708e3f7c7040ba49a8c7aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_1c8286362f2667b1f952649ea3d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_323bca1951ab6cb0d56ea85d23c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_00f647cd3bb4b61c87b9f4d4a43"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_7c98437e6492717f2ea691a0115"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_b24010834b0702f01eaad01c413"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_4b8fbde773a847fe5e110582117"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_790c68f2cf2b6c7f951dc49a6b9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_1a81a5e67b54feb8fe5e2ffb602"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_aac55ab2b25818239f9e9babd1a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_25ea06d8d6643ef4ccc30bbbcfc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP CONSTRAINT "FK_16b49bf0c3fd2d0f78116211eec"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" DROP CONSTRAINT "FK_f972ad1272550e7ceb2d284a320"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" DROP CONSTRAINT "FK_782e7505b1e035de7d33cd7ed5d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" DROP CONSTRAINT "FK_a05cae3a0ad08bca815f5992ab0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" DROP CONSTRAINT "FK_3211614f9e20fde0f876411b434"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_orders" DROP CONSTRAINT "FK_ff2c65a85654e73c9102aa743f0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" DROP CONSTRAINT "FK_2d2bce2526d28528c54b32dbfb2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" DROP CONSTRAINT "FK_4444c0a9b96a5b856718f482186"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" DROP CONSTRAINT "FK_f33ddbc7053a90d012c8da0c460"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" DROP CONSTRAINT "FK_24bd8e17fcef5792847cbe6ecbf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" DROP CONSTRAINT "FK_361c280e0d12509597cd97e08af"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" DROP CONSTRAINT "FK_ad6e13180ed8a414779725be19f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" DROP CONSTRAINT "FK_0c5e82dcdc3761aad23ed9319a9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" DROP CONSTRAINT "FK_109e7e25c2595bb62b4fa76b30f"`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "sector_id" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ALTER COLUMN "currency_id" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" RENAME COLUMN "pir_id" TO "pir"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" RENAME COLUMN "sector_id" TO "sector"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" RENAME COLUMN "currency_id" TO "currency"`,
    );
  }
}
