import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableSupplier1720760524189 implements MigrationInterface {
    name = 'CreateTableSupplier1720760524189'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."suppliers_type_enum" AS ENUM('POTENTIAL', 'OFFICIAL')`);
        await queryRunner.query(`CREATE TYPE "public"."suppliers_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "suppliers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "type" "public"."suppliers_type_enum" DEFAULT 'POTENTIAL', "address" character varying NOT NULL, "phone" character varying NOT NULL, "fax" character varying NOT NULL, "business_license_number" character varying NOT NULL, "tax_code" character varying NOT NULL, "contact_person" character varying NOT NULL, "transaction_currency" character varying NOT NULL, "payment_method" character varying NOT NULL, "note" character varying NOT NULL, "status" "public"."suppliers_status_enum" DEFAULT 'ACTIVE', "search_value" character varying NOT NULL, CONSTRAINT "PK_b70ac51766a9e3144f778cfe81e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_6f01a03dcb1aa33822e19534cd" ON "suppliers" ("code") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_6f01a03dcb1aa33822e19534cd"`);
        await queryRunner.query(`DROP TABLE "suppliers"`);
        await queryRunner.query(`DROP TYPE "public"."suppliers_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."suppliers_type_enum"`);
    }

}
