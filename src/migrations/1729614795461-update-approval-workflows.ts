import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateApprovalWorkflows1729614795461 implements MigrationInterface {
    name = 'UpdateApprovalWorkflows1729614795461'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "staff_selected_staff_approval_workflows" ("staff_approval_workflow_id" uuid NOT NULL, "staff_id" uuid NOT NULL, CONSTRAINT "PK_66f3d669d2d03182b02d7928078" PRIMARY KEY ("staff_approval_workflow_id", "staff_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_cde30add026a85ba5d4deb76c6" ON "staff_selected_staff_approval_workflows" ("staff_approval_workflow_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_e606ed2e744e3d34519ab1eb65" ON "staff_selected_staff_approval_workflows" ("staff_id") `);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD "accountant_approved" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD "allow_select" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "staff_selected_staff_approval_workflows" ADD CONSTRAINT "FK_cde30add026a85ba5d4deb76c6e" FOREIGN KEY ("staff_approval_workflow_id") REFERENCES "staff_approval_workflows"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "staff_selected_staff_approval_workflows" ADD CONSTRAINT "FK_e606ed2e744e3d34519ab1eb657" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staff_selected_staff_approval_workflows" DROP CONSTRAINT "FK_e606ed2e744e3d34519ab1eb657"`);
        await queryRunner.query(`ALTER TABLE "staff_selected_staff_approval_workflows" DROP CONSTRAINT "FK_cde30add026a85ba5d4deb76c6e"`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP COLUMN "allow_select"`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP COLUMN "accountant_approved"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e606ed2e744e3d34519ab1eb65"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cde30add026a85ba5d4deb76c6"`);
        await queryRunner.query(`DROP TABLE "staff_selected_staff_approval_workflows"`);
    }

}
