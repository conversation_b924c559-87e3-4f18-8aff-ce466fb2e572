import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateBudgetCodeColumnName1719806737046 implements MigrationInterface {
    name = 'UpdateBudgetCodeColumnName1719806737046'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "name" character varying NOT NULL DEFAULT ''`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "name"`);
    }

}
