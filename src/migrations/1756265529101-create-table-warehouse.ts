import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableWarehouse1756265529101 implements MigrationInterface {
  name = 'CreateTableWarehouse1756265529101';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."warehouses_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`,
    );
    await queryRunner.query(
      `CREATE TABLE "warehouses" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, "status" "public"."warehouses_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_56ae21ee2432b2270b48867e4be" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_5700614c802ce8826767c27879" ON "warehouses" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE "warehouse_sectors" ("warehouse_id" uuid NOT NULL, "sector_id" uuid NOT NULL, CONSTRAINT "PK_77483e47a3a296b86e8d230a32b" PRIMARY KEY ("warehouse_id", "sector_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a4551a362c36b55e2438bbdc44" ON "warehouse_sectors" ("warehouse_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f184db0df6575fbac4868f029b" ON "warehouse_sectors" ("sector_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "warehouse_sectors" ADD CONSTRAINT "FK_a4551a362c36b55e2438bbdc443" FOREIGN KEY ("warehouse_id") REFERENCES "warehouses"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "warehouse_sectors" ADD CONSTRAINT "FK_f184db0df6575fbac4868f029bd" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "warehouse_sectors" DROP CONSTRAINT "FK_f184db0df6575fbac4868f029bd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "warehouse_sectors" DROP CONSTRAINT "FK_a4551a362c36b55e2438bbdc443"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_f184db0df6575fbac4868f029b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a4551a362c36b55e2438bbdc44"`,
    );
    await queryRunner.query(`DROP TABLE "warehouse_sectors"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5700614c802ce8826767c27879"`,
    );
    await queryRunner.query(`DROP TABLE "warehouses"`);
    await queryRunner.query(`DROP TYPE "public"."warehouses_status_enum"`);
  }
}
