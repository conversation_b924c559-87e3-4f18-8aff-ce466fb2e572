import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableApprovalProcessDetail1741157515780
  implements MigrationInterface
{
  name = 'CreateTableApprovalProcessDetail1741157515780';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "approval_process_details" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "name" character varying NOT NULL, "sector_id" uuid NOT NULL, "function_unit_id" uuid NOT NULL, "plant_id" uuid NOT NULL, "business_unit_id" uuid NOT NULL, "pr_created_by_id" uuid NOT NULL, "po_created_by_id" uuid NOT NULL, "pr_approver_1_id" uuid NOT NULL, "pr_approver_2_id" uuid, "pr_approver_3_id" uuid NOT NULL, "pr_approver_4_id" uuid NOT NULL, "pr_approver_5_id" uuid NOT NULL, "pr_approver_6_id" uuid NOT NULL, "pr_approver_7_id" uuid, "po_approver_1_id" uuid NOT NULL, "po_approver_2_id" uuid, "po_approver_3_id" uuid, CONSTRAINT "PK_b1e4e7990ede56d6fa0ff6cd640" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "approval_process_detail_pr_type" ("approval_process_detail_id" uuid NOT NULL, "purchase_request_type_id" uuid NOT NULL, CONSTRAINT "PK_676ee40c5ee9087411dac53afe7" PRIMARY KEY ("approval_process_detail_id", "purchase_request_type_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4828876c34243c0e7c15a59dfb" ON "approval_process_detail_pr_type" ("approval_process_detail_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_79295d9ff69cae57088d9167b6" ON "approval_process_detail_pr_type" ("purchase_request_type_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_fa9f5def5dbd8d9e39c628ef770" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_51f1cacafbf04bd0a7ba146b583" FOREIGN KEY ("function_unit_id") REFERENCES "function_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_5a61aef5bc801a6e5e95685c156" FOREIGN KEY ("plant_id") REFERENCES "plants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_b2ce3f0dd1b6a47262c936e182a" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_1a0e83718fb23830d8c393feb0d" FOREIGN KEY ("pr_created_by_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_b379029e6f09440963b93561ffe" FOREIGN KEY ("po_created_by_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_db37d12b191f13c72298429e202" FOREIGN KEY ("pr_approver_1_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_e9e8bcf2cde0a74eda7df3b43a0" FOREIGN KEY ("pr_approver_2_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_c8543fc67122f2090a23ddecba9" FOREIGN KEY ("pr_approver_3_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_7298eea16111b0a30c147644b38" FOREIGN KEY ("pr_approver_4_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_6c5a849ed047d489e731309052f" FOREIGN KEY ("pr_approver_5_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_5fd770b2736a6b1a525bae17127" FOREIGN KEY ("pr_approver_6_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_9ea9e7cd106364e87d590e4dc24" FOREIGN KEY ("pr_approver_7_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_e70e8943f3f867c7c80b39617f6" FOREIGN KEY ("po_approver_1_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_8b9c9ab04cebe3af9d41f976eb7" FOREIGN KEY ("po_approver_2_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_12bd13efbef02f69cdeb89d8b24" FOREIGN KEY ("po_approver_3_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_detail_pr_type" ADD CONSTRAINT "FK_4828876c34243c0e7c15a59dfb9" FOREIGN KEY ("approval_process_detail_id") REFERENCES "approval_process_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_detail_pr_type" ADD CONSTRAINT "FK_79295d9ff69cae57088d9167b6a" FOREIGN KEY ("purchase_request_type_id") REFERENCES "purchase_request_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "approval_process_detail_pr_type" DROP CONSTRAINT "FK_79295d9ff69cae57088d9167b6a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_detail_pr_type" DROP CONSTRAINT "FK_4828876c34243c0e7c15a59dfb9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_12bd13efbef02f69cdeb89d8b24"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_8b9c9ab04cebe3af9d41f976eb7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_e70e8943f3f867c7c80b39617f6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_9ea9e7cd106364e87d590e4dc24"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_5fd770b2736a6b1a525bae17127"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_6c5a849ed047d489e731309052f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_7298eea16111b0a30c147644b38"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_c8543fc67122f2090a23ddecba9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_e9e8bcf2cde0a74eda7df3b43a0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_db37d12b191f13c72298429e202"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_b379029e6f09440963b93561ffe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_1a0e83718fb23830d8c393feb0d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_b2ce3f0dd1b6a47262c936e182a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_5a61aef5bc801a6e5e95685c156"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_51f1cacafbf04bd0a7ba146b583"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_fa9f5def5dbd8d9e39c628ef770"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_79295d9ff69cae57088d9167b6"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4828876c34243c0e7c15a59dfb"`,
    );
    await queryRunner.query(`DROP TABLE "approval_process_detail_pr_type"`);
    await queryRunner.query(`DROP TABLE "approval_process_details"`);
  }
}
