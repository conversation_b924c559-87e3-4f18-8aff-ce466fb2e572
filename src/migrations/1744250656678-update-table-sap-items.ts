import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableSapItems1744250656678 implements MigrationInterface {
  name = 'UpdateTableSapItems1744250656678';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ADD "tax_code_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ADD "tax_code_code" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ADD CONSTRAINT "FK_74d9b9fb475303d034fe2aee612" FOREIGN KEY ("tax_code_id") REFERENCES "tax_codes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" DROP CONSTRAINT "FK_74d9b9fb475303d034fe2aee612"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" DROP COLUMN "tax_code_code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" DROP COLUMN "tax_code_id"`,
    );
  }
}
