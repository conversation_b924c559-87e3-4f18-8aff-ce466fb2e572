import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableCostCenter1730274417980 implements MigrationInterface {
    name = 'UpdateTableCostCenter1730274417980'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "note1" character varying`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "note2" character varying`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "note3" character varying`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "note4" character varying`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "note5" character varying`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "note6" character varying`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_8b7f70450f8e34511293f03af7d"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_c3d092c04244caf63c23d78c250"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_bb41334fabe5eb37708eee8e727"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ALTER COLUMN "company_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ALTER COLUMN "business_unit_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ALTER COLUMN "department_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_8b7f70450f8e34511293f03af7d" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_c3d092c04244caf63c23d78c250" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_bb41334fabe5eb37708eee8e727" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_bb41334fabe5eb37708eee8e727"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_c3d092c04244caf63c23d78c250"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "FK_8b7f70450f8e34511293f03af7d"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ALTER COLUMN "department_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ALTER COLUMN "business_unit_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ALTER COLUMN "company_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_bb41334fabe5eb37708eee8e727" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_c3d092c04244caf63c23d78c250" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "FK_8b7f70450f8e34511293f03af7d" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "note6"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "note5"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "note4"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "note3"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "note2"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "note1"`);
    }

}
