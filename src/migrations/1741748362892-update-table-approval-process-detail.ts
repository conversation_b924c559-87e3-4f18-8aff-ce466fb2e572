import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableApprovalProcessDetail1741748362892
  implements MigrationInterface
{
  name = 'UpdateTableApprovalProcessDetail1741748362892';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_5a61aef5bc801a6e5e95685c156"`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval_process_details" RENAME COLUMN "plant_id" TO "department_id"`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_1e68c90b789f9bb8034a0958b6c" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_1e68c90b789f9bb8034a0958b6c"`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval_process_details" RENAME COLUMN "department_id" TO "plant_id"`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_5a61aef5bc801a6e5e95685c156" FOREIGN KEY ("plant_id") REFERENCES "plants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
