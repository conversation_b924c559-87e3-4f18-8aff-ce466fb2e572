import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableMaterial1721622217775 implements MigrationInterface {
    name = 'UpdateTableMaterial1721622217775'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."materials_through_purchasing_enum" AS ENUM('YES_THROUGH_PURCHASING', 'NO_THROUGH_PURCHASING')`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "through_purchasing" "public"."materials_through_purchasing_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."materials_check_budget_enum" AS ENUM('YES_CHECK_BUDGET', 'NO_CHECK_BUDGET')`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "check_budget" "public"."materials_check_budget_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "check_budget"`);
        await queryRunner.query(`DROP TYPE "public"."materials_check_budget_enum"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "through_purchasing"`);
        await queryRunner.query(`DROP TYPE "public"."materials_through_purchasing_enum"`);
    }

}
