import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateReceiptSolomon1754549435743 implements MigrationInterface {
  name = 'CreateReceiptSolomon1754549435743';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "receipt_solomons" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "po_id" integer, "receipt_created_at" date NOT NULL, "receipt_code" character varying, "total_amount" numeric, "budget_code_code" character varying, "budget_code_id" uuid, CONSTRAINT "PK_c0ca920bf800d05d9b8d168b046" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" ADD CONSTRAINT "FK_15faf4d5ac0aac181bad45f053c" FOREIGN KEY ("po_id") REFERENCES "purchase_orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" ADD CONSTRAINT "FK_b6d07bff8fdc237295bf43ae2cc" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" DROP CONSTRAINT "FK_b6d07bff8fdc237295bf43ae2cc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" DROP CONSTRAINT "FK_15faf4d5ac0aac181bad45f053c"`,
    );
    await queryRunner.query(`DROP TABLE "receipt_solomons"`);
  }
}
