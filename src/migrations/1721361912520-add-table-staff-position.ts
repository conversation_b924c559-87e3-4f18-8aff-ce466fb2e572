import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTableStaffPosition1721361912520 implements MigrationInterface {
    name = 'AddTableStaffPosition1721361912520'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."positions_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "positions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, "status" "public"."positions_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_17e4e62ccd5749b289ae3fae6f3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_e21258bdc3692b44960c623940" ON "positions" ("code") `);
        await queryRunner.query(`CREATE TYPE "public"."staff_hierarchies_level_enum" AS ENUM('1', '2', '3', '4')`);
        await queryRunner.query(`CREATE TABLE "staff_hierarchies" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "level" "public"."staff_hierarchies_level_enum" NOT NULL, "manager_id" uuid, "subordinate_id" uuid, CONSTRAINT "PK_71bbd951aa5109bc24ab0cc3ef2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "staffs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "first_name" character varying, "last_name" character varying, "email" character varying NOT NULL, "phone" character varying NOT NULL, "status" character varying DEFAULT 'ACTIVE', "position_id" uuid, CONSTRAINT "UQ_ff86b63b4287eca70e0479dd05d" UNIQUE ("code"), CONSTRAINT "UQ_fc7b6dc314d349acb74a6124fe9" UNIQUE ("email"), CONSTRAINT "PK_f3fec5e06209b46afdf8accf117" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "staff_sectors" ("staff_id" uuid NOT NULL, "sector_id" uuid NOT NULL, CONSTRAINT "PK_e0b3ad4ee19ff95d1519a5b4af7" PRIMARY KEY ("staff_id", "sector_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_6ecdd1f8fa248d6dcc6d13448c" ON "staff_sectors" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_05dd850a010a9371c61f5e16d5" ON "staff_sectors" ("sector_id") `);
        await queryRunner.query(`CREATE TABLE "staff_companies" ("staff_id" uuid NOT NULL, "company_id" uuid NOT NULL, CONSTRAINT "PK_2db52ca179fafea3e01956fe047" PRIMARY KEY ("staff_id", "company_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_97dd63e863fd4284a758b24f8f" ON "staff_companies" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_8f7c65bb3714eb5ffaa741da75" ON "staff_companies" ("company_id") `);
        await queryRunner.query(`CREATE TABLE "staff_business_units" ("staff_id" uuid NOT NULL, "business_unit_id" uuid NOT NULL, CONSTRAINT "PK_d071bca1c8c5cf1c0f1569d7678" PRIMARY KEY ("staff_id", "business_unit_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_8eeee5996d31cb88a40a76a4e7" ON "staff_business_units" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_a1f1429e0c5b74d8fbc8373d8b" ON "staff_business_units" ("business_unit_id") `);
        await queryRunner.query(`CREATE TABLE "staff_departments" ("staff_id" uuid NOT NULL, "department_id" uuid NOT NULL, CONSTRAINT "PK_e3bdb8eb769c949ab87c134e5c8" PRIMARY KEY ("staff_id", "department_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_584a6ed656f4acb08b9820f7b5" ON "staff_departments" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_03b7bec36d08ea7f240de822bc" ON "staff_departments" ("department_id") `);
        await queryRunner.query(`CREATE TABLE "staff_function_units" ("staff_id" uuid NOT NULL, "function_unit_id" uuid NOT NULL, CONSTRAINT "PK_b426996398790e409e67dbaf4a7" PRIMARY KEY ("staff_id", "function_unit_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_184779865dc89e4bef26d313f9" ON "staff_function_units" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_1364f3f861e5d7e2fa09c42d9a" ON "staff_function_units" ("function_unit_id") `);
        await queryRunner.query(`CREATE TABLE "staff_business_owners" ("staff_id" uuid NOT NULL, "business_owner_id" uuid NOT NULL, CONSTRAINT "PK_3309a229a39cf61bf8b2ab1f0ae" PRIMARY KEY ("staff_id", "business_owner_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_92b6eb3fb3c07d2bf48a1a57c3" ON "staff_business_owners" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_c011906bb90c6c3ddf037bdb83" ON "staff_business_owners" ("business_owner_id") `);
        await queryRunner.query(`ALTER TABLE "suppliers" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "suppliers" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "suppliers" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_opex" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "budget_opex" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_opex" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_opex" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_investment" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "budget_investment" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_investment" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_investment" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_capex" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "budget_capex" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_capex" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_capex" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "currency_unit" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "currency_unit" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "currency_unit" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "business_owners" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "business_owners" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "business_owners" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "budget" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "business_unit" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "business_unit" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "business_unit" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "department" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "department" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "department" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "material_groups" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "material_groups" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "material_groups" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "material_types" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "material_types" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "material_types" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "plants" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "plants" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "plants" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "sectors" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "sectors" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "sectors" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "function_unit" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "function_unit" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "function_unit" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "increasement_code" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "increasement_code" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "increasement_code" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "increasement_code" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "business_owners" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "material_groups" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "material_types" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "plants" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "sectors" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" ALTER COLUMN "created_by" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "staff_hierarchies" ADD CONSTRAINT "FK_d323f1d70e431b1e21430eff919" FOREIGN KEY ("manager_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_hierarchies" ADD CONSTRAINT "FK_851f1087cedb960bdf83e835b70" FOREIGN KEY ("subordinate_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staffs" ADD CONSTRAINT "FK_3a91e6f8ccf5412830b25b0d6c4" FOREIGN KEY ("position_id") REFERENCES "positions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_sectors" ADD CONSTRAINT "FK_6ecdd1f8fa248d6dcc6d13448c7" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "staff_sectors" ADD CONSTRAINT "FK_05dd850a010a9371c61f5e16d52" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_companies" ADD CONSTRAINT "FK_97dd63e863fd4284a758b24f8fc" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "staff_companies" ADD CONSTRAINT "FK_8f7c65bb3714eb5ffaa741da75d" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_business_units" ADD CONSTRAINT "FK_8eeee5996d31cb88a40a76a4e7c" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "staff_business_units" ADD CONSTRAINT "FK_a1f1429e0c5b74d8fbc8373d8bf" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_departments" ADD CONSTRAINT "FK_584a6ed656f4acb08b9820f7b53" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "staff_departments" ADD CONSTRAINT "FK_03b7bec36d08ea7f240de822bc9" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_function_units" ADD CONSTRAINT "FK_184779865dc89e4bef26d313f98" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "staff_function_units" ADD CONSTRAINT "FK_1364f3f861e5d7e2fa09c42d9a2" FOREIGN KEY ("function_unit_id") REFERENCES "function_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staff_business_owners" ADD CONSTRAINT "FK_92b6eb3fb3c07d2bf48a1a57c39" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "staff_business_owners" ADD CONSTRAINT "FK_c011906bb90c6c3ddf037bdb83d" FOREIGN KEY ("business_owner_id") REFERENCES "business_owners"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staff_business_owners" DROP CONSTRAINT "FK_c011906bb90c6c3ddf037bdb83d"`);
        await queryRunner.query(`ALTER TABLE "staff_business_owners" DROP CONSTRAINT "FK_92b6eb3fb3c07d2bf48a1a57c39"`);
        await queryRunner.query(`ALTER TABLE "staff_function_units" DROP CONSTRAINT "FK_1364f3f861e5d7e2fa09c42d9a2"`);
        await queryRunner.query(`ALTER TABLE "staff_function_units" DROP CONSTRAINT "FK_184779865dc89e4bef26d313f98"`);
        await queryRunner.query(`ALTER TABLE "staff_departments" DROP CONSTRAINT "FK_03b7bec36d08ea7f240de822bc9"`);
        await queryRunner.query(`ALTER TABLE "staff_departments" DROP CONSTRAINT "FK_584a6ed656f4acb08b9820f7b53"`);
        await queryRunner.query(`ALTER TABLE "staff_business_units" DROP CONSTRAINT "FK_a1f1429e0c5b74d8fbc8373d8bf"`);
        await queryRunner.query(`ALTER TABLE "staff_business_units" DROP CONSTRAINT "FK_8eeee5996d31cb88a40a76a4e7c"`);
        await queryRunner.query(`ALTER TABLE "staff_companies" DROP CONSTRAINT "FK_8f7c65bb3714eb5ffaa741da75d"`);
        await queryRunner.query(`ALTER TABLE "staff_companies" DROP CONSTRAINT "FK_97dd63e863fd4284a758b24f8fc"`);
        await queryRunner.query(`ALTER TABLE "staff_sectors" DROP CONSTRAINT "FK_05dd850a010a9371c61f5e16d52"`);
        await queryRunner.query(`ALTER TABLE "staff_sectors" DROP CONSTRAINT "FK_6ecdd1f8fa248d6dcc6d13448c7"`);
        await queryRunner.query(`ALTER TABLE "staffs" DROP CONSTRAINT "FK_3a91e6f8ccf5412830b25b0d6c4"`);
        await queryRunner.query(`ALTER TABLE "staff_hierarchies" DROP CONSTRAINT "FK_851f1087cedb960bdf83e835b70"`);
        await queryRunner.query(`ALTER TABLE "staff_hierarchies" DROP CONSTRAINT "FK_d323f1d70e431b1e21430eff919"`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" ALTER COLUMN "created_by" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "sectors" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "plants" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "material_types" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "material_groups" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "business_owners" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "increasement_code" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "increasement_code" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "increasement_code" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "increasement_code" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "function_unit" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "function_unit" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "function_unit" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "sectors" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "sectors" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "sectors" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "plants" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "plants" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "plants" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "material_types" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "material_types" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "material_types" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "material_groups" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "material_groups" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "material_groups" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "department" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "department" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "department" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "business_unit" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "business_unit" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "business_unit" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "business_owners" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "business_owners" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "business_owners" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "currency_unit" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "currency_unit" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "currency_unit" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "budget_capex" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "budget_capex" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "budget_capex" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "budget_capex" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "budget_investment" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "budget_investment" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "budget_investment" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "budget_investment" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "budget_opex" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "budget_opex" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "budget_opex" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "budget_opex" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "suppliers" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "suppliers" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "suppliers" DROP COLUMN "created_by"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c011906bb90c6c3ddf037bdb83"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_92b6eb3fb3c07d2bf48a1a57c3"`);
        await queryRunner.query(`DROP TABLE "staff_business_owners"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1364f3f861e5d7e2fa09c42d9a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_184779865dc89e4bef26d313f9"`);
        await queryRunner.query(`DROP TABLE "staff_function_units"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_03b7bec36d08ea7f240de822bc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_584a6ed656f4acb08b9820f7b5"`);
        await queryRunner.query(`DROP TABLE "staff_departments"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a1f1429e0c5b74d8fbc8373d8b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8eeee5996d31cb88a40a76a4e7"`);
        await queryRunner.query(`DROP TABLE "staff_business_units"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8f7c65bb3714eb5ffaa741da75"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_97dd63e863fd4284a758b24f8f"`);
        await queryRunner.query(`DROP TABLE "staff_companies"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_05dd850a010a9371c61f5e16d5"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6ecdd1f8fa248d6dcc6d13448c"`);
        await queryRunner.query(`DROP TABLE "staff_sectors"`);
        await queryRunner.query(`DROP TABLE "staffs"`);
        await queryRunner.query(`DROP TABLE "staff_hierarchies"`);
        await queryRunner.query(`DROP TYPE "public"."staff_hierarchies_level_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e21258bdc3692b44960c623940"`);
        await queryRunner.query(`DROP TABLE "positions"`);
        await queryRunner.query(`DROP TYPE "public"."positions_status_enum"`);
    }

}
