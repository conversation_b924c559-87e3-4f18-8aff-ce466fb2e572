import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableCostCenter1732505385892 implements MigrationInterface {
    name = 'UpdateTableCostCenter1732505385892'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_d278254cc6180f9c2a1d7df670"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "effective_start_date" date`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "effective_end_date" date`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "effective_end_date"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "effective_start_date"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_d278254cc6180f9c2a1d7df670" ON "costcenter_subaccount" ("code") WHERE (deleted_at IS NULL)`);
    }

}
