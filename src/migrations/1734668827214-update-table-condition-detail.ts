import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableConditionDetail1734668827214 implements MigrationInterface {
    name = 'UpdateTableConditionDetail1734668827214'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "function_unit_conditions" ("condition_id" uuid NOT NULL, "function_unit_id" uuid NOT NULL, CONSTRAINT "PK_d10a60c3fb0651d0b183d65b246" PRIMARY KEY ("condition_id", "function_unit_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d1d45333e9f8f68338d94687b4" ON "function_unit_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_37111b90a5f9807ba84eecd4f3" ON "function_unit_conditions" ("function_unit_id") `);
        await queryRunner.query(`ALTER TABLE "condition_details" ADD "difference_amount_all_items" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_type_enum" RENAME TO "condition_details_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN', 'CHECK_BUDGET', 'DIFFERENCE_AMOUNT', 'BUDGET_OVERRUN_RATE', 'PROCESS_TYPE', 'PLANT', 'FUNCTION_UNIT', 'DIFFERENCE_AMOUNT_ALL_ITEMS')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum" USING "type"::"text"::"public"."condition_details_type_enum"`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "function_unit_conditions" ADD CONSTRAINT "FK_d1d45333e9f8f68338d94687b48" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "function_unit_conditions" ADD CONSTRAINT "FK_37111b90a5f9807ba84eecd4f30" FOREIGN KEY ("function_unit_id") REFERENCES "function_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "function_unit_conditions" DROP CONSTRAINT "FK_37111b90a5f9807ba84eecd4f30"`);
        await queryRunner.query(`ALTER TABLE "function_unit_conditions" DROP CONSTRAINT "FK_d1d45333e9f8f68338d94687b48"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum_old" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN', 'CHECK_BUDGET', 'DIFFERENCE_AMOUNT', 'BUDGET_OVERRUN_RATE', 'PROCESS_TYPE', 'PLANT')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum_old" USING "type"::"text"::"public"."condition_details_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_type_enum_old" RENAME TO "condition_details_type_enum"`);
        await queryRunner.query(`ALTER TABLE "condition_details" DROP COLUMN "difference_amount_all_items"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_37111b90a5f9807ba84eecd4f3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d1d45333e9f8f68338d94687b4"`);
        await queryRunner.query(`DROP TABLE "function_unit_conditions"`);
    }

}
