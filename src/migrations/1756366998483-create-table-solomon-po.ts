import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableSolomonPo1756366998483 implements MigrationInterface {
  name = 'CreateTableSolomonPo1756366998483';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "solomon_purchase_orders" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "po_id" integer NOT NULL, "supplier_id" uuid, "supplier_code" character varying, "supplier_info" jsonb, "po_created_date" TIMESTAMP WITH TIME ZONE, "budget_code_code" character varying, "budget_code_id" uuid, "message_type" character varying, "message" character varying, "status" character varying, CONSTRAINT "REL_eabd1e666523c7e70cc8d94487" UNIQUE ("po_id"), CONSTRAINT "PK_f6e64d6ae9e40ec9cdf4400ddcb" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "solomon_purchase_order_items" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "solomon_purchase_order_id" uuid, "material_id" uuid, "material_code" character varying, "material_name" character varying, "warehouse_id" uuid, "warehouse_code" character varying, "description" character varying, "quantity" numeric, "measure_id" uuid, "measure_code" character varying, "unit_price" numeric, "total_amount_vat" numeric, "unit_weight" numeric DEFAULT '0', "ext_weight" numeric DEFAULT '0', "delivery_time" date, "tax_code_id" uuid, "tax_code_code" character varying, "cnv_fact" numeric DEFAULT '1', "budget_code_code" character varying, "budget_code_id" uuid, "purchase_order_detail_id" integer, "message_type" character varying, "message" character varying, CONSTRAINT "PK_12a4c7ed9370f102a95dfbbf072" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_orders" ADD CONSTRAINT "FK_eabd1e666523c7e70cc8d944876" FOREIGN KEY ("po_id") REFERENCES "purchase_orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_orders" ADD CONSTRAINT "FK_f96bb1b5c47b94398f8e97e76e9" FOREIGN KEY ("supplier_id") REFERENCES "suppliers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_orders" ADD CONSTRAINT "FK_a00fc93561f822ce3d9b814dd9d" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" ADD CONSTRAINT "FK_ac466924ee71e33ba435dc91fc4" FOREIGN KEY ("solomon_purchase_order_id") REFERENCES "solomon_purchase_orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" ADD CONSTRAINT "FK_b41071bb4c1f2fcfa1b3cc6d4a9" FOREIGN KEY ("material_id") REFERENCES "materials"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" ADD CONSTRAINT "FK_48f25aef522e056427bc5cbd91f" FOREIGN KEY ("warehouse_id") REFERENCES "warehouses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" ADD CONSTRAINT "FK_b1f3c6bc69c5e644efe2b033bf7" FOREIGN KEY ("measure_id") REFERENCES "measures"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" ADD CONSTRAINT "FK_a96d238f42d6476f6ca2bdf8f0a" FOREIGN KEY ("tax_code_id") REFERENCES "tax_codes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" ADD CONSTRAINT "FK_31e9294069c6810326f8fdce1cc" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" ADD CONSTRAINT "FK_9e1354ea4f3d16044bbe49f82ee" FOREIGN KEY ("purchase_order_detail_id") REFERENCES "purchase_order_details"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" DROP CONSTRAINT "FK_9e1354ea4f3d16044bbe49f82ee"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" DROP CONSTRAINT "FK_31e9294069c6810326f8fdce1cc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" DROP CONSTRAINT "FK_a96d238f42d6476f6ca2bdf8f0a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" DROP CONSTRAINT "FK_b1f3c6bc69c5e644efe2b033bf7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" DROP CONSTRAINT "FK_48f25aef522e056427bc5cbd91f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" DROP CONSTRAINT "FK_b41071bb4c1f2fcfa1b3cc6d4a9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_order_items" DROP CONSTRAINT "FK_ac466924ee71e33ba435dc91fc4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_orders" DROP CONSTRAINT "FK_a00fc93561f822ce3d9b814dd9d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_orders" DROP CONSTRAINT "FK_f96bb1b5c47b94398f8e97e76e9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "solomon_purchase_orders" DROP CONSTRAINT "FK_eabd1e666523c7e70cc8d944876"`,
    );
    await queryRunner.query(`DROP TABLE "solomon_purchase_order_items"`);
    await queryRunner.query(`DROP TABLE "solomon_purchase_orders"`);
  }
}
