import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableTaxCode1743672007387 implements MigrationInterface {
  name = 'CreateTableTaxCode1743672007387';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "tax_codes" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "description" character varying, "tax_rate" numeric NOT NULL, CONSTRAINT "PK_657e27374d6b19b854df6111043" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_55d995ced9ae3dc38794c3d908" ON "tax_codes" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD "tax_code_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_274e5566afe98e85851e2d76594" FOREIGN KEY ("tax_code_id") REFERENCES "tax_codes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_274e5566afe98e85851e2d76594"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP COLUMN "tax_code_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_55d995ced9ae3dc38794c3d908"`,
    );
    await queryRunner.query(`DROP TABLE "tax_codes"`);
  }
}
