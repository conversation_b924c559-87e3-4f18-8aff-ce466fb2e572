import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateStatusCompanySectorBusinessOwner1718679343927 implements MigrationInterface {
    name = 'UpdateStatusCompanySectorBusinessOwner1718679343927'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "business_owners" RENAME COLUMN "is_enabled" TO "status"`);
        await queryRunner.query(`ALTER TABLE "sectors" RENAME COLUMN "is_enabled" TO "status"`);
        await queryRunner.query(`ALTER TABLE "companies" RENAME COLUMN "is_enabled" TO "status"`);
        await queryRunner.query(`ALTER TABLE "business_owners" DROP COLUMN "status"`);
        await queryRunner.query(`CREATE TYPE "public"."business_owners_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "business_owners" ADD "status" "public"."business_owners_status_enum" DEFAULT 'ACTIVE'`);
        await queryRunner.query(`ALTER TABLE "sectors" DROP COLUMN "status"`);
        await queryRunner.query(`CREATE TYPE "public"."sectors_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "sectors" ADD "status" "public"."sectors_status_enum" DEFAULT 'ACTIVE'`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "status"`);
        await queryRunner.query(`CREATE TYPE "public"."companies_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "status" "public"."companies_status_enum" DEFAULT 'ACTIVE'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."companies_status_enum"`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "status" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "sectors" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."sectors_status_enum"`);
        await queryRunner.query(`ALTER TABLE "sectors" ADD "status" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "business_owners" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."business_owners_status_enum"`);
        await queryRunner.query(`ALTER TABLE "business_owners" ADD "status" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "companies" RENAME COLUMN "status" TO "is_enabled"`);
        await queryRunner.query(`ALTER TABLE "sectors" RENAME COLUMN "status" TO "is_enabled"`);
        await queryRunner.query(`ALTER TABLE "business_owners" RENAME COLUMN "status" TO "is_enabled"`);
    }

}
