import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateApproveToPurchaseServiceINDEX1736413589522
  implements MigrationInterface
{
  name = 'MigrateApproveToPurchaseServiceINDEX1736413589522';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_f33ddbc7053a90d012c8da0c46" ON "price_information_records" ("material_code_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4444c0a9b96a5b856718f48218" ON "price_information_records" ("purchase_organization_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2d2bce2526d28528c54b32dbfb" ON "price_information_records" ("plant_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b55c251d27d27edb9d4255b81f" ON "price_information_records" ("currency_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a3393b3b15158b36dc1806d266" ON "purchase_request_details" ("budget_code_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_08864cd58a21cf1461dfebe544" ON "purchase_request_details" ("cost_center_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9673cc5985e815abb2b9ac9207" ON "purchase_request_details" ("material_code_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5f92ed28d3e9d95fa7914b53a1" ON "purchase_request_details" ("material_group_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6eff1022a7129fb5db133ecdca" ON "purchase_request_details" ("budget_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0bc0e7b84e94dda35a48b4daec" ON "purchase_request_details" ("adjust_budget_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9c0506e06012bb72cf22c15f6a" ON "purchase_requests" ("sector_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8242bc6362ab353728164c10a5" ON "purchase_requests" ("business_unit_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f4a79f7b7ce5945c1c2f7e0717" ON "purchase_requests" ("requester_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a8e963e3c448c38088f309e477" ON "purchase_requests" ("type_pr_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_735f0de16162cf2ea8e91f44cb" ON "purchase_requests" ("budget_code_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5c071010551edec7247549a57c" ON "purchase_requests" ("cost_center_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3d735ec72ca11a7cdb868746ca" ON "purchase_requests" ("purchase_org_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d5d18fe42586ced0ec954a9cd8" ON "purchase_requests" ("purchase_group_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_08c95dee42cc71f4f9519866d5" ON "purchase_requests" ("purchaser_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d8931ec0006b39d698430a86c0" ON "purchase_requests" ("process_type_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3633ce83f12b4c6f28a555bbff" ON "purchase_requests" ("plant_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_70a5b47e27c14132ee49bbce02" ON "purchase_requests" ("function_unit_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3ee8e94c75dcdbe9029954d0f8" ON "purchase_requests" ("department_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_16b49bf0c3fd2d0f78116211ee" ON "purchase_orders" ("sector_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_aac55ab2b25818239f9e9babd1" ON "purchase_orders" ("requester_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1a81a5e67b54feb8fe5e2ffb60" ON "purchase_orders" ("type_po_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_790c68f2cf2b6c7f951dc49a6b" ON "purchase_orders" ("cost_center_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4b8fbde773a847fe5e11058211" ON "purchase_orders" ("budget_code_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b24010834b0702f01eaad01c41" ON "purchase_orders" ("purchase_org_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7c98437e6492717f2ea691a011" ON "purchase_orders" ("purchase_group_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_00f647cd3bb4b61c87b9f4d4a4" ON "purchase_orders" ("purchaser_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_323bca1951ab6cb0d56ea85d23" ON "purchase_orders" ("currency_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1c8286362f2667b1f952649ea3" ON "purchase_orders" ("process_type_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cc07d708e3f7c7040ba49a8c7a" ON "purchase_orders" ("plant_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2223defdd12616fb7c13a10095" ON "purchase_orders" ("function_unit_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7556c2e751a8f4e30aba41d69f" ON "purchase_orders" ("department_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8f1b8fc8a5bf2d8ec7ebb63152" ON "purchase_order_details" ("budget_code_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b8f08b2fb2b996b9b7b5688130" ON "purchase_order_details" ("cost_center_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6536900656f3224493fc42417c" ON "purchase_order_details" ("material_code_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b90bde57029ea1280d9d61fbb9" ON "purchase_order_details" ("material_group_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e2fef007791c69e6bc233ca21e" ON "purchase_order_details" ("pir_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0940d2151c34c4df95cafcfb02" ON "purchase_order_details" ("supplier_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2adb10c12c93a8374a2fd72824" ON "purchase_order_details" ("currency_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_84e37eaadebdb701b52d205354" ON "purchase_order_details" ("budget_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_affc3367d2f0b2dd6b7f127e8a" ON "purchase_order_details" ("adjust_budget_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ADD CONSTRAINT "FK_b55c251d27d27edb9d4255b81f9" FOREIGN KEY ("currency_id") REFERENCES "currency_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "price_information_records" DROP CONSTRAINT "FK_b55c251d27d27edb9d4255b81f9"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_affc3367d2f0b2dd6b7f127e8a"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_84e37eaadebdb701b52d205354"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2adb10c12c93a8374a2fd72824"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0940d2151c34c4df95cafcfb02"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e2fef007791c69e6bc233ca21e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b90bde57029ea1280d9d61fbb9"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6536900656f3224493fc42417c"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b8f08b2fb2b996b9b7b5688130"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_8f1b8fc8a5bf2d8ec7ebb63152"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7556c2e751a8f4e30aba41d69f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2223defdd12616fb7c13a10095"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_cc07d708e3f7c7040ba49a8c7a"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1c8286362f2667b1f952649ea3"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_323bca1951ab6cb0d56ea85d23"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_00f647cd3bb4b61c87b9f4d4a4"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7c98437e6492717f2ea691a011"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b24010834b0702f01eaad01c41"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4b8fbde773a847fe5e11058211"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_790c68f2cf2b6c7f951dc49a6b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1a81a5e67b54feb8fe5e2ffb60"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_aac55ab2b25818239f9e9babd1"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_16b49bf0c3fd2d0f78116211ee"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3ee8e94c75dcdbe9029954d0f8"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_70a5b47e27c14132ee49bbce02"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3633ce83f12b4c6f28a555bbff"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d8931ec0006b39d698430a86c0"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_08c95dee42cc71f4f9519866d5"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d5d18fe42586ced0ec954a9cd8"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3d735ec72ca11a7cdb868746ca"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5c071010551edec7247549a57c"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_735f0de16162cf2ea8e91f44cb"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a8e963e3c448c38088f309e477"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_f4a79f7b7ce5945c1c2f7e0717"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_8242bc6362ab353728164c10a5"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9c0506e06012bb72cf22c15f6a"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0bc0e7b84e94dda35a48b4daec"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6eff1022a7129fb5db133ecdca"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5f92ed28d3e9d95fa7914b53a1"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9673cc5985e815abb2b9ac9207"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_08864cd58a21cf1461dfebe544"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a3393b3b15158b36dc1806d266"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b55c251d27d27edb9d4255b81f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2d2bce2526d28528c54b32dbfb"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4444c0a9b96a5b856718f48218"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_f33ddbc7053a90d012c8da0c46"`,
    );
  }
}
