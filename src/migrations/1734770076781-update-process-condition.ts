import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateProcessCondition1734770076781 implements MigrationInterface {
    name = 'UpdateProcessCondition1734770076781'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."idx_process_approval_workflow"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_sector"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_company"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_business_unit"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_department"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_cost_center"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_budget_code"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_pr_type"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_po_type"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_process_type"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_plant"`);
        await queryRunner.query(`DROP INDEX "public"."idx_condition_function_unit"`);
        await queryRunner.query(`ALTER TYPE "public"."supplier_sectors_status_enum" RENAME TO "supplier_sectors_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."supplier_sectors_status_enum" AS ENUM('ACTIVE', 'INACTIVE')`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" TYPE "public"."supplier_sectors_status_enum" USING "status"::"text"::"public"."supplier_sectors_status_enum"`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" SET DEFAULT 'ACTIVE'`);
        await queryRunner.query(`DROP TYPE "public"."supplier_sectors_status_enum_old"`);
        await queryRunner.query(`CREATE INDEX "IDX_c3d47993f5b40b9eb8038f69d4" ON "process_conditions" ("process_id") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_e28468d4b1a29e43f900ce67fd" ON "process_conditions" ("condition_id") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_6629a49fe92f7ef30e0b0c8ee8" ON "conditions" ("process_condition_id") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_39d5946a16052105fadc725f43" ON "condition_details" ("condition_id") WHERE deleted_at IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_39d5946a16052105fadc725f43"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6629a49fe92f7ef30e0b0c8ee8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e28468d4b1a29e43f900ce67fd"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c3d47993f5b40b9eb8038f69d4"`);
        await queryRunner.query(`CREATE TYPE "public"."supplier_sectors_status_enum_old" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" TYPE "public"."supplier_sectors_status_enum_old" USING "status"::"text"::"public"."supplier_sectors_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ALTER COLUMN "status" SET DEFAULT 'ACTIVE'`);
        await queryRunner.query(`DROP TYPE "public"."supplier_sectors_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."supplier_sectors_status_enum_old" RENAME TO "supplier_sectors_status_enum"`);
        await queryRunner.query(`CREATE INDEX "idx_condition_function_unit" ON "function_unit_conditions" ("condition_id", "function_unit_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_plant" ON "plant_conditions" ("condition_id", "plant_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_process_type" ON "process_type_conditions" ("condition_id", "process_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_po_type" ON "purchase_order_type_conditions" ("condition_id", "purchase_order_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_pr_type" ON "purchase_request_type_conditions" ("condition_id", "purchase_request_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_budget_code" ON "budget_code_conditions" ("condition_id", "budget_code_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_cost_center" ON "cost_center_conditions" ("condition_id", "cost_center_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_department" ON "department_conditions" ("condition_id", "department_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_business_unit" ON "business_unit_conditions" ("condition_id", "business_unit_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_company" ON "company_conditions" ("condition_id", "company_id") `);
        await queryRunner.query(`CREATE INDEX "idx_condition_sector" ON "sector_conditions" ("condition_id", "sector_id") `);
        await queryRunner.query(`CREATE INDEX "idx_process_approval_workflow" ON "process_approval_workflows" ("approval_workflow_id", "process_id") `);
    }

}
