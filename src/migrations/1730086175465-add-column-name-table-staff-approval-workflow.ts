import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnNameTableStaffApprovalWorkflow1730086175465 implements MigrationInterface {
    name = 'AddColumnNameTableStaffApprovalWorkflow1730086175465'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD "name" character varying DEFAULT ''`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP COLUMN "name"`);
    }

}
