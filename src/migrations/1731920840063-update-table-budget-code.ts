import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableBudgetCode1731920840063 implements MigrationInterface {
    name = 'UpdateTableBudgetCode1731920840063'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "business_owner_id" uuid`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD CONSTRAINT "FK_af0cd05d5b22b22c08f2241f950" FOREIGN KEY ("business_owner_id") REFERENCES "business_owners"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code" DROP CONSTRAINT "FK_af0cd05d5b22b22c08f2241f950"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "business_owner_id"`);
    }

}
