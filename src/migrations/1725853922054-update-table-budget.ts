import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableBudget1725853922054 implements MigrationInterface {
    name = 'UpdateTableBudget1725853922054'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_opex" DROP COLUMN "level"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_opex" ADD "level" character varying NOT NULL`);
    }

}
