import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableActualSpending1735030297845
  implements MigrationInterface
{
  name = 'UpdateTableActualSpending1735030297845';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD "asset_code" character varying`,
    );
    await queryRunner.query(`
            CREATE INDEX idx_process_approval_workflow 
            ON process_approval_workflows (process_id, approval_workflow_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_sector
            ON sector_conditions (condition_id, sector_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_company
            ON company_conditions (condition_id, company_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_business_unit
            ON business_unit_conditions (condition_id, business_unit_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_department
            ON department_conditions (condition_id, department_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_cost_center
            ON cost_center_conditions (condition_id, cost_center_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_budget_code
            ON budget_code_conditions (condition_id, budget_code_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_pr_type
            ON purchase_request_type_conditions (condition_id, purchase_request_type_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_po_type
            ON purchase_order_type_conditions (condition_id, purchase_order_type_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_process_type
            ON process_type_conditions (condition_id, process_type_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_plant
            ON plant_conditions (condition_id, plant_id);
          `);
    await queryRunner.query(`
            CREATE INDEX idx_condition_function_unit
            ON function_unit_conditions (condition_id, function_unit_id);
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP COLUMN "asset_code"`,
    );
    await queryRunner.query(`
            DROP INDEX idx_process_approval_workflow;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_sector;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_company;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_business_unit;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_department;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_cost_center;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_budget_code;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_pr_type;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_po_type;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_process_type;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_plant;
          `);
    await queryRunner.query(`
            DROP INDEX idx_condition_function_unit;
          `);
  }
}
