import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdteTableBudgetCapex1735892784200 implements MigrationInterface {
  name = 'UpdteTableBudgetCapex1735892784200';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "budget_capex" ADD "note2" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "budget_capex" ADD "key_project" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "budget_capex" DROP COLUMN "key_project"`,
    );
    await queryRunner.query(`ALTER TABLE "budget_capex" DROP COLUMN "note2"`);
  }
}
