import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTablePoDetail1756979686546 implements MigrationInterface {
  name = 'UpdateTablePoDetail1756979686546';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."sap_purchase_order_items_account_assignment_enum" RENAME TO "sap_purchase_order_items_account_assignment_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."sap_purchase_order_items_account_assignment_enum" AS ENUM('NULL', 'K', 'A', 'F', 'X', 'P')`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ALTER COLUMN "account_assignment" TYPE "public"."sap_purchase_order_items_account_assignment_enum" USING "account_assignment"::"text"::"public"."sap_purchase_order_items_account_assignment_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."sap_purchase_order_items_account_assignment_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."purchase_order_details_account_assignment_enum" RENAME TO "purchase_order_details_account_assignment_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."purchase_order_details_account_assignment_enum" AS ENUM('NULL', 'K', 'A', 'F', 'X', 'P')`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "account_assignment" TYPE "public"."purchase_order_details_account_assignment_enum" USING "account_assignment"::"text"::"public"."purchase_order_details_account_assignment_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."purchase_order_details_account_assignment_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."purchase_order_details_account_assignment_enum_old" AS ENUM('NULL', 'K', 'A', 'F', 'X')`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "account_assignment" TYPE "public"."purchase_order_details_account_assignment_enum_old" USING "account_assignment"::"text"::"public"."purchase_order_details_account_assignment_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."purchase_order_details_account_assignment_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."purchase_order_details_account_assignment_enum_old" RENAME TO "purchase_order_details_account_assignment_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."sap_purchase_order_items_account_assignment_enum_old" AS ENUM('NULL', 'K', 'A', 'F', 'X')`,
    );
    await queryRunner.query(
      `ALTER TABLE "sap_purchase_order_items" ALTER COLUMN "account_assignment" TYPE "public"."sap_purchase_order_items_account_assignment_enum_old" USING "account_assignment"::"text"::"public"."sap_purchase_order_items_account_assignment_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."sap_purchase_order_items_account_assignment_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."sap_purchase_order_items_account_assignment_enum_old" RENAME TO "sap_purchase_order_items_account_assignment_enum"`,
    );
  }
}
