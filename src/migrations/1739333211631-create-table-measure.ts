import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableMeasure1739333211631 implements MigrationInterface {
  name = 'CreateTableMeasure1739333211631';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "measures" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, CONSTRAINT "PK_ec34a47385441849095def20546" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_87d0b601c3b6dd2d6cb64dc18e" ON "measures" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`ALTER TABLE "materials" ADD "measure_id" uuid`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "measure_id"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_87d0b601c3b6dd2d6cb64dc18e"`,
    );
    await queryRunner.query(`DROP TABLE "measures"`);
  }
}
