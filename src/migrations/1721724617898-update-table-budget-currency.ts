import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableBudgetCurrency1721724617898 implements MigrationInterface {
    name = 'UpdateTableBudgetCurrency1721724617898'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" DROP COLUMN "status"`);
        await queryRunner.query(`ALTER TABLE "currency_unit" DROP COLUMN "effective_start_date"`);
        await queryRunner.query(`ALTER TABLE "currency_unit" DROP COLUMN "effective_end_date"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."budget_status_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."budget_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "budget" ADD "status" "public"."budget_status_enum" NOT NULL`);
        await queryRunner.query(`ALTER TABLE "currency_unit" ADD "effective_end_date" TIMESTAMP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "currency_unit" ADD "effective_start_date" TIMESTAMP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ADD "status" character varying NOT NULL DEFAULT 'ACTIVE'`);
    }

}
