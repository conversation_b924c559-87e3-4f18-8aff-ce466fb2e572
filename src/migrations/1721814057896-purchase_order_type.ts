import { MigrationInterface, QueryRunner } from "typeorm";

export class PurchaseOrderType1721814057896 implements MigrationInterface {
    name = 'PurchaseOrderType1721814057896'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."purchase_order_type_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "purchase_order_type" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "status" "public"."purchase_order_type_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_f15a2b8c3afcbb7ab49a7fbcab1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_24872056ccb676a909d3674736" ON "purchase_order_type" ("code") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_24872056ccb676a909d3674736"`);
        await queryRunner.query(`DROP TABLE "purchase_order_type"`);
        await queryRunner.query(`DROP TYPE "public"."purchase_order_type_status_enum"`);
    }

}
