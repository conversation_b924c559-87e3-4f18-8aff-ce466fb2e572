import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableActualSpending1733480814241 implements MigrationInterface {
    name = 'UpdateTableActualSpending1733480814241'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "budget_code_id"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "period_month"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "period_year"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "fiscal_year"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "transaction_type"`);
        await queryRunner.query(`DROP TYPE "public"."actual_spendings_transaction_type_enum"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "document_type" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "invocie_business_transaction" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "payment_date" date`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "payment_business_transaction" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "payement_doc_type" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "inventory_doc_date" date`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "po_item" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "po_date" date`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "internal_order_type" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "cost_center_name" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "functional_area" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "functional_area_name" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "tax_code_name" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "doc_payment_amount" numeric DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "local_currency_payment_amount" numeric DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "debit_credit_ind" numeric DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "account_type" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "description" character varying`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "note" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "note"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "account_type"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "debit_credit_ind"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "local_currency_payment_amount"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "doc_payment_amount"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "tax_code_name"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "functional_area_name"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "functional_area"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "cost_center_name"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "internal_order_type"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "po_date"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "po_item"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "inventory_doc_date"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "payement_doc_type"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "payment_business_transaction"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "payment_date"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "invocie_business_transaction"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "document_type"`);
        await queryRunner.query(`CREATE TYPE "public"."actual_spendings_transaction_type_enum" AS ENUM('A', 'MANUAL')`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "transaction_type" "public"."actual_spendings_transaction_type_enum" NOT NULL DEFAULT 'MANUAL'`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "fiscal_year" integer`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "period_year" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "period_month" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "budget_code_id" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
