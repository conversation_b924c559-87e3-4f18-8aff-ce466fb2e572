import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableMaterials1757564816248 implements MigrationInterface {
  name = 'UpdateTableMaterials1757564816248';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "material_function_units" ("material_id" uuid NOT NULL, "function_unit_id" uuid NOT NULL, CONSTRAINT "PK_d96624f3bfeca1a9a397119ef46" PRIMARY KEY ("material_id", "function_unit_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cf29cdea7c61dbc90b61011059" ON "material_function_units" ("material_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3f277212d5ddc31df8555fdc43" ON "material_function_units" ("function_unit_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "material_function_units" ADD CONSTRAINT "FK_cf29cdea7c61dbc90b610110596" FOREIGN KEY ("material_id") REFERENCES "materials"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "material_function_units" ADD CONSTRAINT "FK_3f277212d5ddc31df8555fdc435" FOREIGN KEY ("function_unit_id") REFERENCES "function_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "material_function_units" DROP CONSTRAINT "FK_3f277212d5ddc31df8555fdc435"`,
    );
    await queryRunner.query(
      `ALTER TABLE "material_function_units" DROP CONSTRAINT "FK_cf29cdea7c61dbc90b610110596"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3f277212d5ddc31df8555fdc43"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_cf29cdea7c61dbc90b61011059"`,
    );
    await queryRunner.query(`DROP TABLE "material_function_units"`);
  }
}
