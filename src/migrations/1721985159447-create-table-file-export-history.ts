import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableFileExportHistory1721985159447 implements MigrationInterface {
    name = 'CreateTableFileExportHistory1721985159447'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."file-export-histories_status_enum" AS ENUM('SUCCESS', 'FAIL', 'IN_PROCESS', 'WAITING')`);
        await queryRunner.query(`CREATE TYPE "public"."file-export-histories_import_type_enum" AS ENUM('INVENTORY_STANDARD')`);
        await queryRunner.query(`CREATE TABLE "file-export-histories" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "file_name" character varying NOT NULL, "file_path" character varying NOT NULL, "status" "public"."file-export-histories_status_enum" DEFAULT 'WAITING', "import_type" "public"."file-export-histories_import_type_enum" NOT NULL, "errors" character varying, CONSTRAINT "PK_3d904ac02cf9514550bcdb2d6db" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "file-export-histories"`);
        await queryRunner.query(`DROP TYPE "public"."file-export-histories_import_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."file-export-histories_status_enum"`);
    }

}
