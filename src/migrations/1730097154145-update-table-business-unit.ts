import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableBusinessUnit1730097154145
  implements MigrationInterface
{
  name = 'UpdateTableBusinessUnit1730097154145';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "business_unit" ADD "company_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_unit" ADD CONSTRAINT "FK_16281f986314eab010c5c97604e" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "business_unit" DROP CONSTRAINT "FK_16281f986314eab010c5c97604e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_unit" DROP COLUMN "company_id"`,
    );
  }
}
