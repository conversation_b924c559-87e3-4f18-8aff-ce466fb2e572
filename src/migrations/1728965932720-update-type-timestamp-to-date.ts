import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTypeTimestampToDate1728965932720
  implements MigrationInterface
{
  name = 'UpdateTypeTimestampToDate1728965932720';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "budget_capex" ALTER COLUMN "start_date" TYPE date USING "start_date"::date`,
    );

    await queryRunner.query(
      `ALTER TABLE "budget_capex" ALTER COLUMN "expected_acceptance_time" TYPE date USING "expected_acceptance_time"::date`,
    );

    await queryRunner.query(
      `ALTER TABLE "currency_unit_exchange" ALTER COLUMN "effective_start_date" TYPE date USING "effective_start_date"::date`,
    );

    await queryRunner.query(
      `ALTER TABLE "currency_unit_exchange" ALTER COLUMN "effective_end_date" TYPE date USING "effective_end_date"::date`,
    );

    await queryRunner.query(
      `ALTER TABLE "budget" ALTER COLUMN "effective_start_date" TYPE date USING "effective_start_date"::date`,
    );

    await queryRunner.query(
      `ALTER TABLE "budget" ALTER COLUMN "effective_end_date" TYPE date USING "effective_end_date"::date`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "budget" ALTER COLUMN "effective_end_date" TYPE TIMESTAMP USING "effective_end_date"::timestamp`,
    );

    await queryRunner.query(
      `ALTER TABLE "budget" ALTER COLUMN "effective_start_date" TYPE TIMESTAMP USING "effective_start_date"::timestamp`,
    );

    await queryRunner.query(
      `ALTER TABLE "currency_unit_exchange" ALTER COLUMN "effective_end_date" TYPE TIMESTAMP USING "effective_end_date"::timestamp`,
    );

    await queryRunner.query(
      `ALTER TABLE "currency_unit_exchange" ALTER COLUMN "effective_start_date" TYPE TIMESTAMP USING "effective_start_date"::timestamp`,
    );

    await queryRunner.query(
      `ALTER TABLE "budget_capex" ALTER COLUMN "expected_acceptance_time" TYPE TIMESTAMP USING "expected_acceptance_time"::timestamp`,
    );

    await queryRunner.query(
      `ALTER TABLE "budget_capex" ALTER COLUMN "start_date" TYPE TIMESTAMP USING "start_date"::timestamp NOT NULL`,
    );
  }
}
