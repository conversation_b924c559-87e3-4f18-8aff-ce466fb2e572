import { MigrationInterface, QueryRunner } from 'typeorm';

export class EnhanceStaff1728531532433 implements MigrationInterface {
  name = 'EnhanceStaff1728531532433';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "staff_purchasers" ("staff_id" uuid NOT NULL, "purchaser_id" uuid NOT NULL, CONSTRAINT "PK_8ab812546553908d5e41cea3113" PRIMARY KEY ("staff_id", "purchaser_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9a22aac2d2f181b6c4a3318756" ON "staff_purchasers" ("staff_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c0c86003ba623bee758223a6af" ON "staff_purchasers" ("purchaser_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_purchasers" ADD CONSTRAINT "FK_9a22aac2d2f181b6c4a3318756d" FOREIGN KEY ("staff_id") REFERENCES "staffs"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_purchasers" ADD CONSTRAINT "FK_c0c86003ba623bee758223a6af8" FOREIGN KEY ("purchaser_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "staff_purchasers" DROP CONSTRAINT "FK_c0c86003ba623bee758223a6af8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_purchasers" DROP CONSTRAINT "FK_9a22aac2d2f181b6c4a3318756d"`,
    );

    await queryRunner.query(
      `DROP INDEX "public"."IDX_c0c86003ba623bee758223a6af"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9a22aac2d2f181b6c4a3318756"`,
    );
    await queryRunner.query(`DROP TABLE "staff_purchasers"`);
  }
}
