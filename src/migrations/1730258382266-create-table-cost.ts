import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableCost1730258382266 implements MigrationInterface {
    name = 'CreateTableCost1730258382266'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."costs_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "costs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "name" character varying NOT NULL, "group_cost" character varying, "description" character varying, "status" "public"."costs_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_05cc8aa05396a72553cdff6d5be" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_c8d33026ff25c9a43b58fc559c" ON "costs" ("code") WHERE deleted_at IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_c8d33026ff25c9a43b58fc559c"`);
        await queryRunner.query(`DROP TABLE "costs"`);
        await queryRunner.query(`DROP TYPE "public"."costs_status_enum"`);
    }

}
