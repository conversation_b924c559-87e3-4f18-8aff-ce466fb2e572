import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEnumStaffApprovalWorkflow1741595144887
  implements MigrationInterface
{
  name = 'UpdateEnumStaffApprovalWorkflow1741595144887';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."staff_approval_workflows_approver_enum" RENAME TO "staff_approval_workflows_approver_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."staff_approval_workflows_approver_enum" AS ENUM('LEVEL_1', 'LEVEL_2', 'LEVEL_3', 'LEVEL_4', 'ASSIGN', 'POSITION', 'APPROVER_1', 'APPROVER_2', 'APPROVER_3', 'APPROVER_4', 'APPROVER_5', 'APPROVER_6', 'APPROVER_7')`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_approval_workflows" ALTER COLUMN "approver" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_approval_workflows" ALTER COLUMN "approver" TYPE "public"."staff_approval_workflows_approver_enum" USING "approver"::"text"::"public"."staff_approval_workflows_approver_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_approval_workflows" ALTER COLUMN "approver" SET DEFAULT 'LEVEL_1'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."staff_approval_workflows_approver_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."staff_approval_workflows_approver_enum_old" AS ENUM('LEVEL_1', 'LEVEL_2', 'LEVEL_3', 'LEVEL_4', 'ASSIGN', 'POSITION')`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_approval_workflows" ALTER COLUMN "approver" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_approval_workflows" ALTER COLUMN "approver" TYPE "public"."staff_approval_workflows_approver_enum_old" USING "approver"::"text"::"public"."staff_approval_workflows_approver_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_approval_workflows" ALTER COLUMN "approver" SET DEFAULT 'LEVEL_1'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."staff_approval_workflows_approver_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."staff_approval_workflows_approver_enum_old" RENAME TO "staff_approval_workflows_approver_enum"`,
    );
  }
}
