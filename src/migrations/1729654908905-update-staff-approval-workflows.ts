import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateStaffApprovalWorkflows1729654908905 implements MigrationInterface {
    name = 'UpdateStaffApprovalWorkflows1729654908905'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" ADD "allow_edit" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" DROP COLUMN "allow_edit"`);
    }

}
