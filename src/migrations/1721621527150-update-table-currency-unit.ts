import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableCurrencyUnit1721621527150 implements MigrationInterface {
    name = 'UpdateTableCurrencyUnit1721621527150'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit" DROP COLUMN "general_exchange"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit" ADD "general_exchange" numeric NOT NULL DEFAULT '1'`);
    }

}
