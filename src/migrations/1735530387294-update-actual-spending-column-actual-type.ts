import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateActualSpendingColumnActualType1735530387294
  implements MigrationInterface
{
  name = 'UpdateActualSpendingColumnActualType1735530387294';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."actual_spendings_actual_type_enum" AS ENUM('INVOICE_PAYMENT_DAILY', 'ASSET_MONTHLY', 'FUNCTIONALAREA_UPDATE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD "actual_type" "public"."actual_spendings_actual_type_enum" NOT NULL DEFAULT 'INVOICE_PAYMENT_DAILY'`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8ee883f15148226ef248510508" ON "actual_spendings" ("document_number", "posting_date", "entry_date") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_8ee883f15148226ef248510508"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP COLUMN "actual_type"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."actual_spendings_actual_type_enum"`,
    );
  }
}
