import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableApprovalProcessDetail1741158361576
  implements MigrationInterface
{
  name = 'UpdateTableApprovalProcessDetail1741158361576';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_c8543fc67122f2090a23ddecba9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_7298eea16111b0a30c147644b38"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_6c5a849ed047d489e731309052f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_5fd770b2736a6b1a525bae17127"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ALTER COLUMN "pr_approver_3_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ALTER COLUMN "pr_approver_4_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ALTER COLUMN "pr_approver_5_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ALTER COLUMN "pr_approver_6_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_c8543fc67122f2090a23ddecba9" FOREIGN KEY ("pr_approver_3_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_7298eea16111b0a30c147644b38" FOREIGN KEY ("pr_approver_4_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_6c5a849ed047d489e731309052f" FOREIGN KEY ("pr_approver_5_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_5fd770b2736a6b1a525bae17127" FOREIGN KEY ("pr_approver_6_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_5fd770b2736a6b1a525bae17127"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_6c5a849ed047d489e731309052f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_7298eea16111b0a30c147644b38"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" DROP CONSTRAINT "FK_c8543fc67122f2090a23ddecba9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ALTER COLUMN "pr_approver_6_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ALTER COLUMN "pr_approver_5_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ALTER COLUMN "pr_approver_4_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ALTER COLUMN "pr_approver_3_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_5fd770b2736a6b1a525bae17127" FOREIGN KEY ("pr_approver_6_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_6c5a849ed047d489e731309052f" FOREIGN KEY ("pr_approver_5_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_7298eea16111b0a30c147644b38" FOREIGN KEY ("pr_approver_4_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approval_process_details" ADD CONSTRAINT "FK_c8543fc67122f2090a23ddecba9" FOREIGN KEY ("pr_approver_3_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
