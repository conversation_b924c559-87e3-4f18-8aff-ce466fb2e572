import { MigrationInterface, QueryRunner } from 'typeorm';
import {
  ENotificationFormType,
  getNameOfNotificationFormType,
} from '../domain/config/enums/notification-form.enum';
import { removeUnicode } from '../utils/common';

export class InitDataNotificationForm1723522013954
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    for (const type of Object.values(ENotificationFormType)) {
      const detail = await queryRunner.query(
        `SELECT * FROM notification_forms WHERE type = '${type}'`,
      );

      if (detail.length === 0) {
        await queryRunner.query(
          `INSERT INTO notification_forms (type, name, search_value) VALUES ('${type}', '${getNameOfNotificationFormType(type)}', '${removeUnicode(getNameOfNotificationFormType(type))}')`,
        );
      }
    }
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    for (const type of Object.values(ENotificationFormType)) {
      await queryRunner.query(
        `DELETE FROM notification_forms WHERE type = '${type}'`,
      );
    }
  }
}
