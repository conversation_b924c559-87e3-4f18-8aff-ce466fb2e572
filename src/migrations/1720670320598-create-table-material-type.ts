import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableMaterialType1720670320598 implements MigrationInterface {
    name = 'CreateTableMaterialType1720670320598'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."material_types_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "material_types" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "status" "public"."material_types_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_201f7626a396dc21baf168a6e1b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_7ae83cf35bd0b2a96971c05cff" ON "material_types" ("code") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_7ae83cf35bd0b2a96971c05cff"`);
        await queryRunner.query(`DROP TABLE "material_types"`);
        await queryRunner.query(`DROP TYPE "public"."material_types_status_enum"`);
    }

}
