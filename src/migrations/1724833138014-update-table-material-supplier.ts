import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableMaterialSupplier1724833138014 implements MigrationInterface {
    name = 'UpdateTableMaterialSupplier1724833138014'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."materials_status_enum"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "unit"`);
        await queryRunner.query(`ALTER TABLE "suppliers" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."suppliers_status_enum"`);
        await queryRunner.query(`ALTER TABLE "suppliers" DROP COLUMN "tax_code"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "suppliers" ADD "tax_code" character varying`);
        await queryRunner.query(`CREATE TYPE "public"."suppliers_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "suppliers" ADD "status" "public"."suppliers_status_enum" DEFAULT 'ACTIVE'`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "unit" character varying`);
        await queryRunner.query(`CREATE TYPE "public"."materials_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "status" "public"."materials_status_enum" NOT NULL`);
    }

}
