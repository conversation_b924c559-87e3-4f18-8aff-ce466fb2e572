import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateUniqueCode1724919760479 implements MigrationInterface {
    name = 'UpdateUniqueCode1724919760479'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_e21258bdc3692b44960c623940"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f66f7e8de6ea09c3d3defe3f6f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_62690f4fe31da9eb824d909285"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d3cd31ed2081b77d843431e39c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_787b967bf26d322de3afb4d7dc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4f72d1816fed6fe0d889433e8f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fce55f56a5375bd325e5c4a3dc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_01730098ec1effab1a37d1b450"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7fcca14f4d665d22904d65cfe2"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1c4fb7880833cca581505e0a3a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_182f6a983ef4914b39c1aae548"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8e1ae84aec89fdf84608e63a52"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_24872056ccb676a909d3674736"`);
        await queryRunner.query(`ALTER TABLE "profit_centers" DROP CONSTRAINT "UQ_f66f7e8de6ea09c3d3defe3f6fa"`);
        await queryRunner.query(`ALTER TABLE "department" DROP CONSTRAINT "UQ_62690f4fe31da9eb824d909285f"`);
        await queryRunner.query(`ALTER TABLE "business_unit" DROP CONSTRAINT "UQ_d3cd31ed2081b77d843431e39ce"`);
        await queryRunner.query(`ALTER TABLE "function_unit" DROP CONSTRAINT "UQ_787b967bf26d322de3afb4d7dc0"`);
        await queryRunner.query(`ALTER TABLE "staffs" DROP CONSTRAINT "UQ_ff86b63b4287eca70e0479dd05d"`);
        await queryRunner.query(`ALTER TABLE "staffs" DROP CONSTRAINT "UQ_fc7b6dc314d349acb74a6124fe9"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP CONSTRAINT "UQ_4f72d1816fed6fe0d889433e8f2"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP CONSTRAINT "UQ_fce55f56a5375bd325e5c4a3dc4"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_e90c481a9fe0b50563960b66df" ON "currency_unit" ("currency_code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_90b5d6fd14c594df5eb4e3c581" ON "positions" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_201a5844e8c9d0a30bb556518b" ON "profit_centers" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_3d13d7d1b2ef281571c988e927" ON "department" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_5e0f2b6b24d62eca7b3be7560d" ON "business_unit" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_82b77ab9518838adcffaa9761d" ON "function_unit" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_5a589dd8c10a98ae4421c71179" ON "staffs" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_4fe2b0bbd702ee9629419a51db" ON "staffs" ("email") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_b60ac294fa0c0bf784cf3efeec" ON "budget_code" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_d278254cc6180f9c2a1d7df670" ON "costcenter_subaccount" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_14117efcb54b68c999e8bd8b27" ON "plants" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_4f010c99a8515b982fe03d6e84" ON "purchasing_departments" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_521092495d6f5db3a73ccba08f" ON "purchasing_groups" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_f7d88f52cc861365ed72ab58ce" ON "sectors" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_198c08025ebed178a92f41bd4f" ON "purchase_request_type" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_7650355fea7cb9d04b50721aa6" ON "purchase_order_type" ("code") WHERE deleted_at IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_7650355fea7cb9d04b50721aa6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_198c08025ebed178a92f41bd4f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7d88f52cc861365ed72ab58ce"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_521092495d6f5db3a73ccba08f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4f010c99a8515b982fe03d6e84"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_14117efcb54b68c999e8bd8b27"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d278254cc6180f9c2a1d7df670"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b60ac294fa0c0bf784cf3efeec"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4fe2b0bbd702ee9629419a51db"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5a589dd8c10a98ae4421c71179"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_82b77ab9518838adcffaa9761d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5e0f2b6b24d62eca7b3be7560d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3d13d7d1b2ef281571c988e927"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_201a5844e8c9d0a30bb556518b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_90b5d6fd14c594df5eb4e3c581"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e90c481a9fe0b50563960b66df"`);
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD CONSTRAINT "UQ_fce55f56a5375bd325e5c4a3dc4" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD CONSTRAINT "UQ_4f72d1816fed6fe0d889433e8f2" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "staffs" ADD CONSTRAINT "UQ_fc7b6dc314d349acb74a6124fe9" UNIQUE ("email")`);
        await queryRunner.query(`ALTER TABLE "staffs" ADD CONSTRAINT "UQ_ff86b63b4287eca70e0479dd05d" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "function_unit" ADD CONSTRAINT "UQ_787b967bf26d322de3afb4d7dc0" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "business_unit" ADD CONSTRAINT "UQ_d3cd31ed2081b77d843431e39ce" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "department" ADD CONSTRAINT "UQ_62690f4fe31da9eb824d909285f" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "profit_centers" ADD CONSTRAINT "UQ_f66f7e8de6ea09c3d3defe3f6fa" UNIQUE ("code")`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_24872056ccb676a909d3674736" ON "purchase_order_type" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8e1ae84aec89fdf84608e63a52" ON "purchase_request_type" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_182f6a983ef4914b39c1aae548" ON "sectors" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_1c4fb7880833cca581505e0a3a" ON "purchasing_groups" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_7fcca14f4d665d22904d65cfe2" ON "purchasing_departments" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_01730098ec1effab1a37d1b450" ON "plants" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_fce55f56a5375bd325e5c4a3dc" ON "costcenter_subaccount" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_4f72d1816fed6fe0d889433e8f" ON "budget_code" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_787b967bf26d322de3afb4d7dc" ON "function_unit" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_d3cd31ed2081b77d843431e39c" ON "business_unit" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_62690f4fe31da9eb824d909285" ON "department" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_f66f7e8de6ea09c3d3defe3f6f" ON "profit_centers" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_e21258bdc3692b44960c623940" ON "positions" ("code") `);
    }

}
