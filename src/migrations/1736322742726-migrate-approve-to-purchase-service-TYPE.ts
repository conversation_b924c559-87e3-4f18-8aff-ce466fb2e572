import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateApproveToPurchaseServiceTYPE1736322742726
  implements MigrationInterface
{
  name = 'MigrateApproveToPurchaseServiceTYPE1736322742726';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "approval-level" DROP COLUMN "approval_flow_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "budget_code_id" TYPE uuid USING "budget_code_id"::uuid`,
    );

    await queryRunner.query(`
        UPDATE purchase_request_details
        SET cost_center_id = NULL
        WHERE NOT cost_center_id ~ '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    `);
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "cost_center_id" TYPE uuid USING "cost_center_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "material_code_id" TYPE uuid USING "material_code_id"::uuid`,
    );

    await queryRunner.query(`
        UPDATE purchase_request_details
        SET material_group_id = NULL
        WHERE NOT material_group_id ~ '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    `);
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "material_group_id" TYPE uuid USING "material_group_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "budget_id" TYPE uuid USING "budget_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "adjust_budget_id" TYPE uuid USING "adjust_budget_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "history-approve" ALTER COLUMN "user_id" TYPE uuid USING "user_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "sector_id" TYPE uuid USING "sector_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "business_unit_id" TYPE uuid USING "business_unit_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "requester_id" TYPE uuid USING "requester_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "type_pr_id" TYPE uuid USING "type_pr_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "budget_code_id" TYPE uuid USING "budget_code_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "cost_center_id" TYPE uuid USING "cost_center_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "purchase_org_id" TYPE uuid USING "purchase_org_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "purchase_group_id" TYPE uuid USING "purchase_group_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "purchaser_id" TYPE uuid USING "purchaser_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "process_type_id" TYPE uuid USING "process_type_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "plant_id" TYPE uuid USING "plant_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "function_unit_id" TYPE uuid USING "function_unit_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "department_id" TYPE uuid USING "department_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval-level" ALTER COLUMN "user_id" TYPE uuid USING "user_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval-level" ALTER COLUMN "staff_approval_workflow_id" TYPE uuid USING "staff_approval_workflow_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "business_unit_id" TYPE uuid USING "business_unit_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "requester_id" TYPE uuid USING "requester_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "type_po_id" TYPE uuid USING "type_po_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "cost_center_id" TYPE uuid USING "cost_center_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "budget_code_id" TYPE uuid USING "budget_code_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "purchase_org_id" TYPE uuid USING "purchase_org_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "purchase_group_id" TYPE uuid USING "purchase_group_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "purchaser_id" TYPE uuid USING "purchaser_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TYPE "public"."purchase_orders_paymentmethod_enum" RENAME TO "purchase_orders_paymentmethod_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."purchase_orders_payment_method_enum" AS ENUM('OFFER', 'COMPETITIVE_QUOTATION', 'OPEN_BIDDING', 'SELECTIVE_BIDDING', 'DIRECT_APPOINTMENT')`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "payment_method" TYPE "public"."purchase_orders_payment_method_enum" USING "payment_method"::"text"::"public"."purchase_orders_payment_method_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."purchase_orders_paymentmethod_enum_old"`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "currency_id" TYPE uuid USING "currency_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "process_type_id" TYPE uuid USING "process_type_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "plant_id" TYPE uuid USING "plant_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "function_unit_id" TYPE uuid USING "function_unit_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "department_id" TYPE uuid USING "department_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "budget_code_id" TYPE uuid USING "budget_code_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "cost_center_id" TYPE uuid USING "cost_center_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "material_code_id" TYPE uuid USING "material_code_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "material_group_id" TYPE uuid USING "material_group_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "supplier_id" TYPE uuid USING "supplier_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "currency_id" TYPE uuid USING "currency_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "budget_id" TYPE uuid USING "budget_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "adjust_budget_id" TYPE uuid USING "adjust_budget_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "material_code_id" TYPE uuid USING "material_code_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "purchase_organization_id" TYPE uuid USING "purchase_organization_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "plant_id" TYPE uuid USING "plant_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "currency_id" TYPE uuid USING "currency_id"::uuid`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "currency_id" TYPE character varying NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "plant_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "purchase_organization_id" TYPE character varying NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "material_code_id" TYPE varying NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "adjust_budget_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "budget_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "currency_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "supplier_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "material_group_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "material_code_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "cost_center_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "budget_code_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "department_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "function_unit_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "plant_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "process_type_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "currency_id" TYPE character varying`,
    );

    await queryRunner.query(
      `CREATE TYPE "public"."purchase_orders_paymentmethod_enum_old" AS ENUM('OFFER', 'COMPETITIVE_QUOTATION', 'OPEN_BIDDING', 'SELECTIVE_BIDDING', 'DIRECT_APPOINTMENT')`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "payment_method" TYPE "public"."purchase_orders_paymentmethod_enum_old" USING "payment_method"::"text"::"public"."purchase_orders_paymentmethod_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."purchase_orders_payment_method_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."purchase_orders_paymentmethod_enum_old" RENAME TO "purchase_orders_paymentmethod_enum"`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "purchaser_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "purchase_group_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "purchase_org_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "budget_code_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "cost_center_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "type_po_id" TYPE character varying NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "requester_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ALTER COLUMN "business_unit_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval-level" ALTER COLUMN "staff_approval_workflow_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval-level" ALTER COLUMN "user_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "department_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "function_unit_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "plant_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "process_type_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "purchaser_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "purchase_group_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "purchase_org_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "cost_center_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "budget_code_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "type_pr_id" TYPE character varying NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "requester_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "business_unit_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_requests" ALTER COLUMN "sector_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "history-approve" ALTER COLUMN "user_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "adjust_budget_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "budget_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "material_group_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "material_code_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "cost_center_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "budget_code_id" TYPE character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "approval-level" ADD "approval_flow_id" integer`,
    );
  }
}
