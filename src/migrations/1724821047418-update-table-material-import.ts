import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableMaterialImport1724821047418 implements MigrationInterface {
    name = 'UpdateTableMaterialImport1724821047418'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" ALTER COLUMN "name" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "materials" ALTER COLUMN "unit" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" ALTER COLUMN "unit" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "materials" ALTER COLUMN "name" SET NOT NULL`);
    }

}
