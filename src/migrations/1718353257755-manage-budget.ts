import { MigrationInterface, QueryRunner } from "typeorm";

export class ManageBudget1718353257755 implements MigrationInterface {
    name = 'ManageBudget1718353257755'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "budget_opex" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "level" character varying NOT NULL, "form" character varying, "operations" character varying, CONSTRAINT "PK_9fadd9cd82d97606a2ba34ad662" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "budget_investment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "investment" character varying NOT NULL, "quantity" integer NOT NULL, "price" numeric NOT NULL, "transportation_costs" numeric NOT NULL, "budget_capex_id" uuid NOT NULL, CONSTRAINT "PK_0db39afc23b78280401e4d04469" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "budget_capex" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "use_time" character varying, "start_date" TIMESTAMP NOT NULL, "expected_acceptance_time" TIMESTAMP, "classify" character varying NOT NULL, "priority" character varying, "investment_purpose" character varying, "files" text DEFAULT '[]', CONSTRAINT "PK_bddc197bdabaf034b613be16525" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "currency_unit_exchange" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "currency_unit_id" uuid, "exchange_rate" numeric NOT NULL DEFAULT '1', "effective_start_date" TIMESTAMP NOT NULL, "effective_end_date" TIMESTAMP NOT NULL, "status" character varying NOT NULL DEFAULT 'ACTIVE', CONSTRAINT "PK_5f2c377c1b32c85ca5a426c9566" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "currency_unit" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "currency_code" character varying NOT NULL, "description" character varying NOT NULL, "status" character varying NOT NULL DEFAULT 'ACTIVE', "search_value" character varying, "general_exchange" numeric NOT NULL DEFAULT '1', "effective_start_date" TIMESTAMP NOT NULL, "effective_end_date" TIMESTAMP NOT NULL, CONSTRAINT "UQ_0db62ea0dd4c7883707c4f282ec" UNIQUE ("currency_code"), CONSTRAINT "PK_c7e44b9bbfff018bee9bbfbf35b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "budget_code" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "search_value" character varying, CONSTRAINT "PK_5c5ecef8ee4225f4c025a8c4268" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."budget_create_type_enum" AS ENUM('NEW', 'INCREASE', 'DECREASE')`);
        await queryRunner.query(`CREATE TYPE "public"."budget_budget_type_enum" AS ENUM('OPEX', 'CAPEX')`);
        await queryRunner.query(`CREATE TYPE "public"."budget_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "budget" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "create_type" "public"."budget_create_type_enum" NOT NULL, "adjust_buget_id" uuid, "budget_type" "public"."budget_budget_type_enum" NOT NULL, "currency_unit_id" uuid NOT NULL, "budget_code_id" uuid NOT NULL, "costcenter_subaccount_id" uuid NOT NULL, "note" character varying, "effective_start_date" TIMESTAMP NOT NULL, "effective_end_date" TIMESTAMP NOT NULL, "status" "public"."budget_status_enum" NOT NULL, "budget_opex_id" uuid, "budget_capex_id" uuid, "total_value" numeric NOT NULL, "is_lock" boolean NOT NULL DEFAULT false, "search_value" character varying, CONSTRAINT "UQ_a8b96820b2d7cc66ad39df9760a" UNIQUE ("code"), CONSTRAINT "REL_b894d95fba154f42f26fc01b6e" UNIQUE ("budget_opex_id"), CONSTRAINT "REL_b823f84ab8cfbad377e1d88e8c" UNIQUE ("budget_capex_id"), CONSTRAINT "PK_9af87bcfd2de21bd9630dddaa0e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "costcenter_subaccount" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "search_value" character varying, CONSTRAINT "PK_d3db7b3b24efb4b10b9e07117d7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "increasement_code" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "sequencing" numeric NOT NULL, "code_type" character varying NOT NULL, CONSTRAINT "PK_9c6430e89557e8eb71844c94dd9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "budget_investment" ADD CONSTRAINT "FK_53f41bb5744147c9fdbf3d61450" FOREIGN KEY ("budget_capex_id") REFERENCES "budget_capex"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ADD CONSTRAINT "FK_ccfc478dd151913cf1dc9a9751e" FOREIGN KEY ("currency_unit_id") REFERENCES "currency_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget" ADD CONSTRAINT "FK_b894d95fba154f42f26fc01b6ed" FOREIGN KEY ("budget_opex_id") REFERENCES "budget_opex"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget" ADD CONSTRAINT "FK_b823f84ab8cfbad377e1d88e8c1" FOREIGN KEY ("budget_capex_id") REFERENCES "budget_capex"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget" ADD CONSTRAINT "FK_2c7ad1c8678519c009c54632242" FOREIGN KEY ("adjust_buget_id") REFERENCES "budget"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget" ADD CONSTRAINT "FK_e28c7841a5f0b81d8cc68cb941a" FOREIGN KEY ("currency_unit_id") REFERENCES "currency_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget" ADD CONSTRAINT "FK_acc09361f8a74d847ee02aab8a3" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget" ADD CONSTRAINT "FK_19d8d3bf85ebea34ad6a1860803" FOREIGN KEY ("costcenter_subaccount_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget" DROP CONSTRAINT "FK_19d8d3bf85ebea34ad6a1860803"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP CONSTRAINT "FK_acc09361f8a74d847ee02aab8a3"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP CONSTRAINT "FK_e28c7841a5f0b81d8cc68cb941a"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP CONSTRAINT "FK_2c7ad1c8678519c009c54632242"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP CONSTRAINT "FK_b823f84ab8cfbad377e1d88e8c1"`);
        await queryRunner.query(`ALTER TABLE "budget" DROP CONSTRAINT "FK_b894d95fba154f42f26fc01b6ed"`);
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" DROP CONSTRAINT "FK_ccfc478dd151913cf1dc9a9751e"`);
        await queryRunner.query(`ALTER TABLE "budget_investment" DROP CONSTRAINT "FK_53f41bb5744147c9fdbf3d61450"`);
        await queryRunner.query(`DROP TABLE "increasement_code"`);
        await queryRunner.query(`DROP TABLE "costcenter_subaccount"`);
        await queryRunner.query(`DROP TABLE "budget"`);
        await queryRunner.query(`DROP TYPE "public"."budget_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."budget_budget_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."budget_create_type_enum"`);
        await queryRunner.query(`DROP TABLE "budget_code"`);
        await queryRunner.query(`DROP TABLE "currency_unit"`);
        await queryRunner.query(`DROP TABLE "currency_unit_exchange"`);
        await queryRunner.query(`DROP TABLE "budget_capex"`);
        await queryRunner.query(`DROP TABLE "budget_investment"`);
        await queryRunner.query(`DROP TABLE "budget_opex"`);
    }

}
