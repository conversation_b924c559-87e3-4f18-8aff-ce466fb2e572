import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTablePurchasingDepartment1720753370399 implements MigrationInterface {
    name = 'CreateTablePurchasingDepartment1720753370399'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."purchasing_departments_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "purchasing_departments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "sector_id" uuid NOT NULL, "status" "public"."purchasing_departments_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_eeaa492c702396ad69d9c1f537a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_7fcca14f4d665d22904d65cfe2" ON "purchasing_departments" ("code") `);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" ADD CONSTRAINT "FK_aff0471a0adf0dc0a3f2a925fe1" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchasing_departments" DROP CONSTRAINT "FK_aff0471a0adf0dc0a3f2a925fe1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7fcca14f4d665d22904d65cfe2"`);
        await queryRunner.query(`DROP TABLE "purchasing_departments"`);
        await queryRunner.query(`DROP TYPE "public"."purchasing_departments_status_enum"`);
    }

}
