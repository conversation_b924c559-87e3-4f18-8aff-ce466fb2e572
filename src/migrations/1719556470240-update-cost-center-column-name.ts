import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateCostCenterColumnName1719556470240 implements MigrationInterface {
    name = 'UpdateCostCenterColumnName1719556470240'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" ADD "name" character varying NOT NULL DEFAULT ''`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "costcenter_subaccount" DROP COLUMN "name"`);
    }

}
