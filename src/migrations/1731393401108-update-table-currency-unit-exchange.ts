import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableCurrencyUnitExchange1731393401108 implements MigrationInterface {
    name = 'UpdateTableCurrencyUnitExchange1731393401108'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ALTER COLUMN "effective_end_date" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ALTER COLUMN "effective_end_date" SET NOT NULL`);
    }

}
