import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateApproveToPurchaseServiceRECHECK1737011899580
  implements MigrationInterface
{
  name = 'MigrateApproveToPurchaseServiceRECHECK1737011899580';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "pir_business_units" ("pir_id" integer NOT NULL, "business_unit_id" uuid NOT NULL, CONSTRAINT "PK_1c7dfd4c35de728b03849e93989" PRIMARY KEY ("pir_id", "business_unit_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3856c5d92f9d19204b14dfdcd6" ON "pir_business_units" ("pir_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ea4ae3f390bb3c3cbac1b8b050" ON "pir_business_units" ("business_unit_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" RENAME COLUMN "vendor_code" TO "vendor_code_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "vendor_code_id" TYPE uuid USING "vendor_code_id"::uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" RENAME COLUMN "business_units" TO "business_unit_ids"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" RENAME COLUMN "purchase_group" TO "purchase_group_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "purchase_group_id" TYPE uuid USING "purchase_group_id"::uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ADD CONSTRAINT "FK_33570f3af3c0ae6188840a0a77b" FOREIGN KEY ("vendor_code_id") REFERENCES "suppliers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ADD CONSTRAINT "FK_c834941c8cd43738ab7c9c9e55e" FOREIGN KEY ("purchase_group_id") REFERENCES "purchasing_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pir_business_units" ADD CONSTRAINT "FK_3856c5d92f9d19204b14dfdcd6d" FOREIGN KEY ("pir_id") REFERENCES "price_information_records"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "pir_business_units" ADD CONSTRAINT "FK_ea4ae3f390bb3c3cbac1b8b0501" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pir_business_units" DROP CONSTRAINT "FK_ea4ae3f390bb3c3cbac1b8b0501"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pir_business_units" DROP CONSTRAINT "FK_3856c5d92f9d19204b14dfdcd6d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" DROP CONSTRAINT "FK_c834941c8cd43738ab7c9c9e55e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" DROP CONSTRAINT "FK_33570f3af3c0ae6188840a0a77b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" RENAME COLUMN "purchase_group_id" TO "purchase_group"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "purchase_group" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" RENAME COLUMN "business_unit_ids" TO "business_units"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" RENAME COLUMN "vendor_code_id" TO "vendor_code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_information_records" ALTER COLUMN "vendor_code" TYPE character varying`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ea4ae3f390bb3c3cbac1b8b050"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3856c5d92f9d19204b14dfdcd6"`,
    );
    await queryRunner.query(`DROP TABLE "pir_business_units"`);
  }
}
