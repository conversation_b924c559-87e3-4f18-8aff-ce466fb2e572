import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableActualSpending1733726426896 implements MigrationInterface {
    name = 'UpdateTableActualSpending1733726426896'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "debit_credit_ind"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "debit_credit_ind" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "debit_credit_ind"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "debit_credit_ind" numeric DEFAULT '0'`);
    }

}
