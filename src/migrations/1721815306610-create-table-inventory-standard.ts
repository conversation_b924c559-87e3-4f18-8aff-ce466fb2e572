import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableInventoryStandard1721815306610 implements MigrationInterface {
    name = 'CreateTableInventoryStandard1721815306610'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."inventory_standards_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "inventory_standards" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "material_id" uuid NOT NULL, "unit" character varying, "sector_id" uuid NOT NULL, "company_id" uuid, "business_unit_id" uuid, "department_id" uuid, "standard_quantity" integer NOT NULL, "status" "public"."inventory_standards_status_enum" NOT NULL, "path" ltree, "inventory_quantity" integer NOT NULL, CONSTRAINT "PK_fed969f03588ada21c6bf394e6d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" ADD CONSTRAINT "FK_3ac3a20979649f2dc53e0898ee8" FOREIGN KEY ("material_id") REFERENCES "materials"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" ADD CONSTRAINT "FK_78a7a81904502cede1e37cbe875" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" ADD CONSTRAINT "FK_cc2495c182dba160ffab2b027b7" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" ADD CONSTRAINT "FK_e604961a419f130f4d78cfeb790" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" ADD CONSTRAINT "FK_c01148dc44239ae643609a5a967" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "inventory_standards" DROP CONSTRAINT "FK_c01148dc44239ae643609a5a967"`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" DROP CONSTRAINT "FK_e604961a419f130f4d78cfeb790"`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" DROP CONSTRAINT "FK_cc2495c182dba160ffab2b027b7"`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" DROP CONSTRAINT "FK_78a7a81904502cede1e37cbe875"`);
        await queryRunner.query(`ALTER TABLE "inventory_standards" DROP CONSTRAINT "FK_3ac3a20979649f2dc53e0898ee8"`);
        await queryRunner.query(`DROP TABLE "inventory_standards"`);
        await queryRunner.query(`DROP TYPE "public"."inventory_standards_status_enum"`);
    }

}
