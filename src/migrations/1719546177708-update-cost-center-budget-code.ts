import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateCostCenterBudgetCode1719546177708 implements MigrationInterface {
    name = 'UpdateCostCenterBudgetCode1719546177708'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "costcenter_subaccount_id" uuid`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD CONSTRAINT "FK_6407d802b09366170ad7abd6973" FOREIGN KEY ("costcenter_subaccount_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code" DROP CONSTRAINT "FK_6407d802b09366170ad7abd6973"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "costcenter_subaccount_id"`);
    }

}
