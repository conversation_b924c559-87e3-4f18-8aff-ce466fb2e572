import { MigrationInterface, QueryRunner } from 'typeorm';

export class ActualSpendingTable1731898823402 implements MigrationInterface {
  name = 'ActualSpendingTable1731898823402';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."actual_spendings_transaction_type_enum" AS ENUM('A', 'MANUAL')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."actual_spendings_status_enum" AS ENUM('CONFIRMED', 'UNCONFIRMED')`,
    );
    await queryRunner.query(
      `CREATE TABLE "actual_spendings" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "sap_actual_id" integer NOT NULL, "budget_code_id" uuid NOT NULL, "company_id" uuid NOT NULL, "cost_center_id" uuid, "bu_id" uuid, "period_month" integer NOT NULL, "period_year" integer NOT NULL, "fiscal_year" integer, "internal_order" character varying, "internal_order_name" character varying, "supplier_id" uuid, "supplier_code" character varying, "supplier_name" character varying, "po_sap_id" integer, "doc_date" date, "posting_date" date, "entry_date" date, "payment_doc" character varying, "e_invoice_number" character varying, "document_number" character varying, "invoice_number" character varying, "transaction_type" "public"."actual_spendings_transaction_type_enum" NOT NULL DEFAULT 'MANUAL', "gl_account" character varying, "tax_code" character varying, "tax_rate" character varying, "doc_amount" numeric NOT NULL DEFAULT '0', "currency_id" uuid NOT NULL, "local_currency_amount" numeric DEFAULT '0', "local_currency_id" uuid, "exchange_rate" numeric NOT NULL DEFAULT '1', "receiver_code" character varying, "receiver_name" character varying, "profit_center" character varying, "profit_center_description" character varying, "profit_center_group" character varying, "profit_center_group_description" character varying, "status" "public"."actual_spendings_status_enum" NOT NULL DEFAULT 'CONFIRMED', CONSTRAINT "PK_828d74e11212cdc3ff6c627f7e1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_a7e0a24897e53770fb6b93e1304" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_7478310621c6b1a47401bc81546" FOREIGN KEY ("cost_center_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_7b2de07c88acc1be5f661908af2" FOREIGN KEY ("bu_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_a3bb09ca54373ce67ae09636111" FOREIGN KEY ("supplier_id") REFERENCES "suppliers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_81df34dd55c33c92e0aeb05e6c0" FOREIGN KEY ("currency_id") REFERENCES "currency_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_47714043a5199b70bcec3bb4b94" FOREIGN KEY ("local_currency_id") REFERENCES "currency_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_47714043a5199b70bcec3bb4b94"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_81df34dd55c33c92e0aeb05e6c0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_a3bb09ca54373ce67ae09636111"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_7b2de07c88acc1be5f661908af2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_7478310621c6b1a47401bc81546"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_a7e0a24897e53770fb6b93e1304"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36"`,
    );
    await queryRunner.query(`DROP TABLE "actual_spendings"`);
    await queryRunner.query(
      `DROP TYPE "public"."actual_spendings_status_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."actual_spendings_transaction_type_enum"`,
    );
  }
}
