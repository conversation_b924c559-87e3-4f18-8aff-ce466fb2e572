import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateFieldRequiredTable1721725762342 implements MigrationInterface {
    name = 'UpdateFieldRequiredTable1721725762342'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "business_owners" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "material_groups" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "material_types" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "plants" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "sectors" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "address" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "phone" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "fax" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "business_license_number" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "tax_code" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "contact_person" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "transaction_currency" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "payment_method" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "note" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "note" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "payment_method" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "transaction_currency" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "contact_person" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "tax_code" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "business_license_number" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "fax" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "phone" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "address" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "sectors" ALTER COLUMN "description" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" ALTER COLUMN "description" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchasing_departments" ALTER COLUMN "description" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "plants" ALTER COLUMN "description" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "material_types" ALTER COLUMN "description" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "material_groups" ALTER COLUMN "description" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "business_owners" ALTER COLUMN "description" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "description" SET NOT NULL`);
    }

}
