import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateApproveToPurchaseServiceMEASURE1739347747984
  implements MigrationInterface
{
  name = 'MigrateApproveToPurchaseServiceMEASURE1739347747984';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" RENAME COLUMN "measure_code" TO "measure_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" RENAME COLUMN "measure_code" TO "measure_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "measure_id" TYPE uuid USING "measure_id"::uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "measure_id" TYPE uuid USING "measure_id"::uuid`,
    );

    await queryRunner.query(
      `ALTER TYPE "public"."file-import-histories_import_type_enum" RENAME TO "file-import-histories_import_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."file-import-histories_import_type_enum" AS ENUM('OPEX', 'CAPEX', 'BUDGET_CODE', 'COST_CENTER_SUB_ACCOUNT', 'SUPPLIER', 'MATERIAL', 'COST', 'EXCHANGE_RATE', 'INVENTORY_STANDARD', 'BUSINESS_OWNER', 'MATERIAL_GROUP', 'PURCHASING_DEPARTMENT', 'MATERIAL_TYPE', 'PURCHASING_GROUP', 'DEPARTMENT', 'COMPANY', 'BUSINESS_UNIT', 'ACTUAL_SPENDING', 'MEASURE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "file-import-histories" ALTER COLUMN "import_type" TYPE "public"."file-import-histories_import_type_enum" USING "import_type"::"text"::"public"."file-import-histories_import_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."file-import-histories_import_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_769d0e6190cbfe1a15aeecbf29" ON "purchase_order_details" ("measure_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b88ba4e6ffc3755bcdc88576ae" ON "purchase_request_details" ("measure_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ADD CONSTRAINT "FK_769d0e6190cbfe1a15aeecbf297" FOREIGN KEY ("measure_id") REFERENCES "measures"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ADD CONSTRAINT "FK_b88ba4e6ffc3755bcdc88576ae1" FOREIGN KEY ("measure_id") REFERENCES "measures"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "materials" ADD CONSTRAINT "FK_5308e4516b133a7b51d56d5c7fd" FOREIGN KEY ("measure_id") REFERENCES "measures"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "materials" DROP CONSTRAINT "FK_5308e4516b133a7b51d56d5c7fd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" DROP CONSTRAINT "FK_b88ba4e6ffc3755bcdc88576ae1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" DROP CONSTRAINT "FK_769d0e6190cbfe1a15aeecbf297"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b88ba4e6ffc3755bcdc88576ae"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_769d0e6190cbfe1a15aeecbf29"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."file-import-histories_import_type_enum_old" AS ENUM('OPEX', 'CAPEX', 'BUDGET_CODE', 'COST_CENTER_SUB_ACCOUNT', 'SUPPLIER', 'MATERIAL', 'COST', 'EXCHANGE_RATE', 'INVENTORY_STANDARD', 'BUSINESS_OWNER', 'MATERIAL_GROUP', 'PURCHASING_DEPARTMENT', 'MATERIAL_TYPE', 'PURCHASING_GROUP', 'DEPARTMENT', 'COMPANY', 'BUSINESS_UNIT', 'ACTUAL_SPENDING')`,
    );
    await queryRunner.query(
      `ALTER TABLE "file-import-histories" ALTER COLUMN "import_type" TYPE "public"."file-import-histories_import_type_enum_old" USING "import_type"::"text"::"public"."file-import-histories_import_type_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."file-import-histories_import_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."file-import-histories_import_type_enum_old" RENAME TO "file-import-histories_import_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" RENAME COLUMN "measure_id" TO "measure_code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" RENAME COLUMN "measure_id" TO "measure_code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_order_details" ALTER COLUMN "measure_code" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchase_request_details" ALTER COLUMN "measure_code" TYPE character varying`,
    );
  }
}
