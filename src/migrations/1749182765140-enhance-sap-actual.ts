import { MigrationInterface, QueryRunner } from 'typeorm';

export class EnhanceSapActual1749182765140 implements MigrationInterface {
  name = 'EnhanceSapActual1749182765140';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD "budget_code_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."actual_spendings_status_enum" RENAME TO "actual_spendings_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."actual_spendings_status_enum" AS ENUM('CONFIRMED', 'UNCONFIRMED', 'SAP_SYNC_FAIL')`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" TYPE "public"."actual_spendings_status_enum" USING "status"::"text"::"public"."actual_spendings_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" SET DEFAULT 'CONFIRMED'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."actual_spendings_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."actual_spendings_status_enum_old" AS ENUM('CONFIRMED', 'UNCONFIRMED')`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" TYPE "public"."actual_spendings_status_enum_old" USING "status"::"text"::"public"."actual_spendings_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" SET DEFAULT 'CONFIRMED'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."actual_spendings_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."actual_spendings_status_enum_old" RENAME TO "actual_spendings_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP COLUMN "budget_code_id"`,
    );
  }
}
