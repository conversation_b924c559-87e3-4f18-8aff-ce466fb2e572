import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableCostCenter1757576990750 implements MigrationInterface {
  name = 'UpdateTableCostCenter1757576990750';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "costcenter_function_units" ("costcenter_id" uuid NOT NULL, "function_unit_id" uuid NOT NULL, CONSTRAINT "PK_6be87fdc2c366d1407a8718afc3" PRIMARY KEY ("costcenter_id", "function_unit_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5308f02e6382cf3ef006cd3664" ON "costcenter_function_units" ("costcenter_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_239a03d88fec1e88b66a94f4df" ON "costcenter_function_units" ("function_unit_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "costcenter_function_units" ADD CONSTRAINT "FK_5308f02e6382cf3ef006cd36646" FOREIGN KEY ("costcenter_id") REFERENCES "costcenter_subaccount"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "costcenter_function_units" ADD CONSTRAINT "FK_239a03d88fec1e88b66a94f4df5" FOREIGN KEY ("function_unit_id") REFERENCES "function_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "costcenter_function_units" DROP CONSTRAINT "FK_239a03d88fec1e88b66a94f4df5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "costcenter_function_units" DROP CONSTRAINT "FK_5308f02e6382cf3ef006cd36646"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_239a03d88fec1e88b66a94f4df"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5308f02e6382cf3ef006cd3664"`,
    );
    await queryRunner.query(`DROP TABLE "costcenter_function_units"`);
  }
}
