import { MigrationInterface, QueryRunner } from "typeorm";

export class PurchaseRequestType1721815296637 implements MigrationInterface {
    name = 'PurchaseRequestType1721815296637'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."purchase_request_type_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "purchase_request_type" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "status" "public"."purchase_request_type_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_41ca667ea845e6ef42bce4b6812" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8e1ae84aec89fdf84608e63a52" ON "purchase_request_type" ("code") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_8e1ae84aec89fdf84608e63a52"`);
        await queryRunner.query(`DROP TABLE "purchase_request_type"`);
        await queryRunner.query(`DROP TYPE "public"."purchase_request_type_status_enum"`);
    }

}
