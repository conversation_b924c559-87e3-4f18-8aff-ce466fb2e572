import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableSupplier1724746688924 implements MigrationInterface {
    name = 'UpdateTableSupplier1724746688924'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_ffa35ea3cd780960028bf56af9"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b2a60cfe5be75988c5162c70ee"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7ae83cf35bd0b2a96971c05cff"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5f59d61b51ffcb1db994286221"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_80af3e6808151c3210b4d5a218"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6f01a03dcb1aa33822e19534cd"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_b3396880d5215ee95e17bebc9c" ON "business_owners" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_db504b00433eded4d088433549" ON "material_groups" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_9ec8e82f074a8ac9217cc4279f" ON "material_types" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_d364900c883297dd18d8a1f136" ON "materials" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_3d939313ff7a98e8656ca33197" ON "companies" ("code") WHERE deleted_at IS NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_46399dedc7d52cf7ff38f1f759" ON "suppliers" ("code") WHERE deleted_at IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_46399dedc7d52cf7ff38f1f759"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3d939313ff7a98e8656ca33197"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d364900c883297dd18d8a1f136"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9ec8e82f074a8ac9217cc4279f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_db504b00433eded4d088433549"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b3396880d5215ee95e17bebc9c"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_6f01a03dcb1aa33822e19534cd" ON "suppliers" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_80af3e6808151c3210b4d5a218" ON "companies" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_5f59d61b51ffcb1db994286221" ON "materials" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_7ae83cf35bd0b2a96971c05cff" ON "material_types" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_b2a60cfe5be75988c5162c70ee" ON "material_groups" ("code") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_ffa35ea3cd780960028bf56af9" ON "business_owners" ("code") `);
    }

}
