import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableStaff1724225026318 implements MigrationInterface {
    name = 'UpdateTableStaff1724225026318'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staffs" ADD "po_creater_id" uuid`);
        await queryRunner.query(`ALTER TABLE "staffs" ADD "purchaser_id" uuid`);
        await queryRunner.query(`ALTER TABLE "staffs" ADD CONSTRAINT "FK_0eaa714d0e90ea95c20a0bcefc5" FOREIGN KEY ("po_creater_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staffs" ADD CONSTRAINT "FK_fafb093e1b1e71315a84e12f7d3" FOREIGN KEY ("purchaser_id") REFERENCES "staffs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staffs" DROP CONSTRAINT "FK_fafb093e1b1e71315a84e12f7d3"`);
        await queryRunner.query(`ALTER TABLE "staffs" DROP CONSTRAINT "FK_0eaa714d0e90ea95c20a0bcefc5"`);
        await queryRunner.query(`ALTER TABLE "staffs" DROP COLUMN "purchaser_id"`);
        await queryRunner.query(`ALTER TABLE "staffs" DROP COLUMN "po_creater_id"`);
    }

}
