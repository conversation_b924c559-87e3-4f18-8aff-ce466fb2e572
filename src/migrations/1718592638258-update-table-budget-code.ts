import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableBudgetCode1718592638258 implements MigrationInterface {
    name = 'UpdateTableBudgetCode1718592638258'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "budget_code_business_owner" ("business_owner_id" uuid NOT NULL, "budget_code_id" uuid NOT NULL, CONSTRAINT "PK_14c6d7167ba58da9c0e0d623ed4" PRIMARY KEY ("business_owner_id", "budget_code_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_e4cf6940572c83082a01a4d956" ON "budget_code_business_owner" ("business_owner_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_c870593c9de2c3b23c229cb973" ON "budget_code_business_owner" ("budget_code_id") `);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "name"`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "code" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD CONSTRAINT "UQ_4f72d1816fed6fe0d889433e8f2" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "description" character varying`);
        await queryRunner.query(`CREATE TYPE "public"."budget_code_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "status" "public"."budget_code_status_enum" DEFAULT 'ACTIVE'`);
        await queryRunner.query(`CREATE INDEX "IDX_4f72d1816fed6fe0d889433e8f" ON "budget_code" ("code") `);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" ADD CONSTRAINT "FK_e4cf6940572c83082a01a4d9568" FOREIGN KEY ("business_owner_id") REFERENCES "business_owners"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" ADD CONSTRAINT "FK_c870593c9de2c3b23c229cb973d" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" DROP CONSTRAINT "FK_c870593c9de2c3b23c229cb973d"`);
        await queryRunner.query(`ALTER TABLE "budget_code_business_owner" DROP CONSTRAINT "FK_e4cf6940572c83082a01a4d9568"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4f72d1816fed6fe0d889433e8f"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."budget_code_status_enum"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP CONSTRAINT "UQ_4f72d1816fed6fe0d889433e8f2"`);
        await queryRunner.query(`ALTER TABLE "budget_code" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "budget_code" ADD "name" character varying NOT NULL`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c870593c9de2c3b23c229cb973"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e4cf6940572c83082a01a4d956"`);
        await queryRunner.query(`DROP TABLE "budget_code_business_owner"`);
    }

}
