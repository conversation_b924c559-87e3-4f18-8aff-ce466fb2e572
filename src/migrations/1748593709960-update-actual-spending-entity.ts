import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateActualSpendingEntity1748593709960
  implements MigrationInterface
{
  name = 'UpdateActualSpendingEntity1748593709960';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD "company_code" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD "cost_center_code" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD "bu_code" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD "currency_code" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD "local_currency_code" character varying`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."file-import-histories_import_type_enum" RENAME TO "file-import-histories_import_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."file-import-histories_import_type_enum" AS ENUM('OPEX', 'CAPEX', 'BUDGET_CODE', 'COST_CENTER_SUB_ACCOUNT', 'SUPPLIER', 'MATERIAL', 'COST', 'EXCHANGE_RATE', 'INVENTORY_STANDARD', 'BUSINESS_OWNER', 'MATERIAL_GROUP', 'PURCHASING_DEPARTMENT', 'MATERIAL_TYPE', 'PURCHASING_GROUP', 'DEPARTMENT', 'COMPANY', 'BUSINESS_UNIT', 'ACTUAL_SPENDING', 'MEASURE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "file-import-histories" ALTER COLUMN "import_type" TYPE "public"."file-import-histories_import_type_enum" USING "import_type"::"text"::"public"."file-import-histories_import_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."file-import-histories_import_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."actual_spendings_status_enum" RENAME TO "actual_spendings_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."actual_spendings_status_enum" AS ENUM('CONFIRMED', 'UNCONFIRMED', 'SAP_SYNC_FAIL')`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" TYPE "public"."actual_spendings_status_enum" USING "status"::"text"::"public"."actual_spendings_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" SET DEFAULT 'CONFIRMED'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."actual_spendings_status_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."actual_spendings_status_enum_old" AS ENUM('CONFIRMED', 'UNCONFIRMED')`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" TYPE "public"."actual_spendings_status_enum_old" USING "status"::"text"::"public"."actual_spendings_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "status" SET DEFAULT 'CONFIRMED'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."actual_spendings_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."actual_spendings_status_enum_old" RENAME TO "actual_spendings_status_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."file-import-histories_import_type_enum_old" AS ENUM('OPEX', 'CAPEX', 'BUDGET_CODE', 'COST_CENTER_SUB_ACCOUNT', 'SUPPLIER', 'MATERIAL', 'COST', 'EXCHANGE_RATE', 'INVENTORY_STANDARD', 'BUSINESS_OWNER', 'MATERIAL_GROUP', 'PURCHASING_DEPARTMENT', 'MATERIAL_TYPE', 'PURCHASING_GROUP', 'DEPARTMENT', 'COMPANY', 'BUSINESS_UNIT', 'ACTUAL_SPENDING')`,
    );
    await queryRunner.query(
      `ALTER TABLE "file-import-histories" ALTER COLUMN "import_type" TYPE "public"."file-import-histories_import_type_enum_old" USING "import_type"::"text"::"public"."file-import-histories_import_type_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."file-import-histories_import_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."file-import-histories_import_type_enum_old" RENAME TO "file-import-histories_import_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP COLUMN "local_currency_code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP COLUMN "currency_code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP COLUMN "bu_code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP COLUMN "cost_center_code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP COLUMN "company_code"`,
    );
  }
}
