import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTablePurchasingGroup1720693089300 implements MigrationInterface {
    name = 'CreateTablePurchasingGroup1720693089300'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."purchasing_groups_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "purchasing_groups" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "sector_id" uuid NOT NULL, "status" "public"."purchasing_groups_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_e5fa4eef79098575fb94c2e530b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_1c4fb7880833cca581505e0a3a" ON "purchasing_groups" ("code") `);
        await queryRunner.query(`ALTER TABLE "materials" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "purchasing_groups" ADD CONSTRAINT "FK_48ce56ff34443a3bbc6843d3aef" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchasing_groups" DROP CONSTRAINT "FK_48ce56ff34443a3bbc6843d3aef"`);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "created_by"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1c4fb7880833cca581505e0a3a"`);
        await queryRunner.query(`DROP TABLE "purchasing_groups"`);
        await queryRunner.query(`DROP TYPE "public"."purchasing_groups_status_enum"`);
    }

}
