import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateMigrationTable1750749701109 implements MigrationInterface {
  name = 'UpdateMigrationTable1750749701109';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "budget_code_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" DROP CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36"`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ALTER COLUMN "budget_code_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "actual_spendings" ADD CONSTRAINT "FK_7acfb1c1e512e273bcff63e0b36" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
