import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableMaterial1724389292792 implements MigrationInterface {
    name = 'UpdateTableMaterial1724389292792'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."material_sectors_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "material_sectors" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code_sap" character varying NOT NULL, "status" "public"."material_sectors_status_enum" DEFAULT 'ACTIVE', "material_id" uuid, "sector_id" uuid, CONSTRAINT "PK_305bb4bd55068893a891a0ff33d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "material_sectors" ADD CONSTRAINT "FK_3fe6bddfeab2ae8851af410d166" FOREIGN KEY ("material_id") REFERENCES "materials"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_sectors" ADD CONSTRAINT "FK_d9cb5604620d0ba684e00477e0d" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_sectors" DROP CONSTRAINT "FK_d9cb5604620d0ba684e00477e0d"`);
        await queryRunner.query(`ALTER TABLE "material_sectors" DROP CONSTRAINT "FK_3fe6bddfeab2ae8851af410d166"`);
        await queryRunner.query(`DROP TABLE "material_sectors"`);
        await queryRunner.query(`DROP TYPE "public"."material_sectors_status_enum"`);
    }

}
