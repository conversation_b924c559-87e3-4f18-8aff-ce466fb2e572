import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateApprovalWorkflowsColumnApprover1728268922147 implements MigrationInterface {
    name = 'UpdateApprovalWorkflowsColumnApprover1728268922147'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" RENAME COLUMN "status" TO "approver"`);
        await queryRunner.query(`ALTER TYPE "public"."staff_approval_workflows_status_enum" RENAME TO "staff_approval_workflows_approver_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."staff_approval_workflows_approver_enum" RENAME TO "staff_approval_workflows_status_enum"`);
        await queryRunner.query(`ALTER TABLE "staff_approval_workflows" RENAME COLUMN "approver" TO "status"`);
    }

}
