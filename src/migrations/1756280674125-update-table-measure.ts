import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableMeasure1756280674125 implements MigrationInterface {
  name = 'UpdateTableMeasure1756280674125';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."measures_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "measures" ADD "status" "public"."measures_status_enum" DEFAULT 'ACTIVE'`,
    );
    await queryRunner.query(
      `ALTER TABLE "measures" ADD "code_conversions" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "measures" DROP COLUMN "code_conversions"`,
    );
    await queryRunner.query(`ALTER TABLE "measures" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."measures_status_enum"`);
  }
}
