import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableBudget1731571809574 implements MigrationInterface {
    name = 'UpdateTableBudget1731571809574'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget" ADD "note2" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budget" DROP COLUMN "note2"`);
    }

}
