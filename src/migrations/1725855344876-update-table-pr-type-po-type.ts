import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTablePrTypePoType1725855344876 implements MigrationInterface {
    name = 'UpdateTablePrTypePoType1725855344876'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."purchase_order_type_form_enum" AS ENUM('NORMAL', 'BUY_TOOLS')`);
        await queryRunner.query(`ALTER TABLE "purchase_order_type" ADD "form" "public"."purchase_order_type_form_enum" DEFAULT 'NORMAL'`);
        await queryRunner.query(`CREATE TYPE "public"."purchase_request_type_form_enum" AS ENUM('NORMAL', 'BUY_TOOLS')`);
        await queryRunner.query(`ALTER TABLE "purchase_request_type" ADD "form" "public"."purchase_request_type_form_enum" DEFAULT 'NORMAL'`);
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_request_type" DROP COLUMN "form"`);
        await queryRunner.query(`DROP TYPE "public"."purchase_request_type_form_enum"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_type" DROP COLUMN "form"`);
        await queryRunner.query(`DROP TYPE "public"."purchase_order_type_form_enum"`);
    }

}
