import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateDataRequiredMasterData1719893393367 implements MigrationInterface {
    name = 'UpdateDataRequiredMasterData1719893393367'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit" ALTER COLUMN "description" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit" ALTER COLUMN "description" SET NOT NULL`);
    }

}
