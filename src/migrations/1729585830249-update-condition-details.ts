import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateConditionDetails1729585830249 implements MigrationInterface {
    name = 'UpdateConditionDetails1729585830249'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."condition_details_type_enum" RENAME TO "condition_details_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN', 'CHECK_BUDGET')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum" USING "type"::"text"::"public"."condition_details_type_enum"`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_comparison_type_enum" RENAME TO "condition_details_comparison_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_comparison_type_enum" AS ENUM('EQUAL', 'NOT_EQUAL', 'LESS_THAN', 'LESS_THAN_OR_EQUAL', 'GREATER_THAN', 'GREATER_THAN_OR_EQUAL', 'INCLUSION', 'EXCLUSION', '1', '0')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "comparison_type" TYPE "public"."condition_details_comparison_type_enum" USING "comparison_type"::"text"::"public"."condition_details_comparison_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_comparison_type_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."condition_details_comparison_type_enum_old" AS ENUM('EQUAL', 'NOT_EQUAL', 'LESS_THAN', 'LESS_THAN_OR_EQUAL', 'GREATER_THAN', 'GREATER_THAN_OR_EQUAL', 'INCLUSION', 'EXCLUSION')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "comparison_type" TYPE "public"."condition_details_comparison_type_enum_old" USING "comparison_type"::"text"::"public"."condition_details_comparison_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_comparison_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_comparison_type_enum_old" RENAME TO "condition_details_comparison_type_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum_old" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum_old" USING "type"::"text"::"public"."condition_details_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_type_enum_old" RENAME TO "condition_details_type_enum"`);
    }

}
