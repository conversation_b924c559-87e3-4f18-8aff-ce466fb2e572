import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateProcessManagement1727713101697 implements MigrationInterface {
    name = 'UpdateProcessManagement1727713101697'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."processes_status_enum" AS ENUM('ACTIVE', 'INACTIVE')`);
        await queryRunner.query(`CREATE TYPE "public"."processes_type_enum" AS ENUM('PR', 'PO')`);
        await queryRunner.query(`CREATE TABLE "processes" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "name" character varying DEFAULT 'Start', "description" character varying, "parent_id" uuid, "allow_inherit" boolean NOT NULL DEFAULT false, "path" ltree, "status" "public"."processes_status_enum" DEFAULT 'ACTIVE', "type" "public"."processes_type_enum" DEFAULT 'PR', CONSTRAINT "PK_566885de50f7d20a6df306c12e6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "process_conditions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "is_effected" boolean DEFAULT true, "process_id" uuid, "condition_id" uuid, CONSTRAINT "REL_c2090185b0e6c760f56dfafbfc" UNIQUE ("condition_id"), CONSTRAINT "PK_d7605effe6298ac8c9be0242837" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "conditions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "process_condition_id" uuid, CONSTRAINT "REL_e2645a474e5f7536bcac98c0e3" UNIQUE ("process_condition_id"), CONSTRAINT "PK_3938bdf2933c08ac7af7e0e15e7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET')`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_comparison_type_enum" AS ENUM('EQUAL', 'NOT_EQUAL', 'LESS_THAN', 'LESS_THAN_OR_EQUAL', 'GREATER_THAN', 'GREATER_THAN_OR_EQUAL', 'INCLUSION', 'EXCLUSION')`);
        await queryRunner.query(`CREATE TABLE "condition_details" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "type" "public"."condition_details_type_enum" DEFAULT 'SECTOR', "comparison_type" "public"."condition_details_comparison_type_enum" NOT NULL, "value_pr" double precision DEFAULT '0', "value_po" double precision DEFAULT '0', "value_budget" double precision DEFAULT '0', "budget_overrun" double precision DEFAULT '0', "condition_id" uuid, CONSTRAINT "PK_c62d956c07bb260102d65feac21" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "sector_conditions" ("condition_id" uuid NOT NULL, "sector_id" uuid NOT NULL, CONSTRAINT "PK_f63a831256f6c643785487c5d58" PRIMARY KEY ("condition_id", "sector_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_46bf392241d0902ff9221b94d4" ON "sector_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_3a0b7cb2b2cedabce5adb8af7d" ON "sector_conditions" ("sector_id") `);
        await queryRunner.query(`CREATE TABLE "company_conditions" ("condition_id" uuid NOT NULL, "company_id" uuid NOT NULL, CONSTRAINT "PK_d70108dbf82911305bf6e3f1acf" PRIMARY KEY ("condition_id", "company_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_ef13e3ad9620a7da150d83af4c" ON "company_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_91894b1cf08b7f0bd127233d3a" ON "company_conditions" ("company_id") `);
        await queryRunner.query(`CREATE TABLE "business_unit_conditions" ("condition_id" uuid NOT NULL, "business_unit_id" uuid NOT NULL, CONSTRAINT "PK_ff54fb535a88e8d2a9c9bc27c23" PRIMARY KEY ("condition_id", "business_unit_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_186a86d6aac76d817f0fd39f38" ON "business_unit_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_c2ca9fb3aaf565f46cbd7bfb9e" ON "business_unit_conditions" ("business_unit_id") `);
        await queryRunner.query(`CREATE TABLE "department_conditions" ("condition_id" uuid NOT NULL, "department_id" uuid NOT NULL, CONSTRAINT "PK_d9ada31e89f6d64b7fd63670807" PRIMARY KEY ("condition_id", "department_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_61d1b14b84f0c0719215aa0a0a" ON "department_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_d344e50167cb08adb718151a5f" ON "department_conditions" ("department_id") `);
        await queryRunner.query(`CREATE TABLE "cost_center_conditions" ("condition_id" uuid NOT NULL, "cost_center_id" uuid NOT NULL, CONSTRAINT "PK_4de575e8038710d838d8a291b61" PRIMARY KEY ("condition_id", "cost_center_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d6b1b808621a587f51be6843e3" ON "cost_center_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_8b183ca83eb03a916c4ec70a7b" ON "cost_center_conditions" ("cost_center_id") `);
        await queryRunner.query(`CREATE TABLE "budget_code_conditions" ("condition_id" uuid NOT NULL, "budget_code_id" uuid NOT NULL, CONSTRAINT "PK_f660ff7bd5fbe6e93f141c94a19" PRIMARY KEY ("condition_id", "budget_code_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_6764a2a3de23b0ccbe1baf818f" ON "budget_code_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_a0d6f9a74af739d85e2b3b12d5" ON "budget_code_conditions" ("budget_code_id") `);
        await queryRunner.query(`CREATE TABLE "purchase_request_type_conditions" ("condition_id" uuid NOT NULL, "purchase_request_type_id" uuid NOT NULL, CONSTRAINT "PK_aea713bcee1d12c9e32ed12a018" PRIMARY KEY ("condition_id", "purchase_request_type_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_22ff970add6dbcca8e960efe56" ON "purchase_request_type_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_cf4a0a5a57ce939afbab4574bc" ON "purchase_request_type_conditions" ("purchase_request_type_id") `);
        await queryRunner.query(`CREATE TABLE "purchase_order_type_conditions" ("condition_id" uuid NOT NULL, "purchase_order_type_id" uuid NOT NULL, CONSTRAINT "PK_319043bc3cb087a8473ed091ee2" PRIMARY KEY ("condition_id", "purchase_order_type_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_1f6efd4e75f363511a2afbdafe" ON "purchase_order_type_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_fd68ae982ab64c74c847d22514" ON "purchase_order_type_conditions" ("purchase_order_type_id") `);
        await queryRunner.query(`ALTER TABLE "purchase_request_type" ALTER COLUMN "name" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "processes" ADD CONSTRAINT "FK_399184eb3067bc70cae280529eb" FOREIGN KEY ("parent_id") REFERENCES "processes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "process_conditions" ADD CONSTRAINT "FK_4d7fe1794d8ede4d1066a0f5823" FOREIGN KEY ("process_id") REFERENCES "processes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "process_conditions" ADD CONSTRAINT "FK_c2090185b0e6c760f56dfafbfc4" FOREIGN KEY ("condition_id") REFERENCES "conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conditions" ADD CONSTRAINT "FK_e2645a474e5f7536bcac98c0e31" FOREIGN KEY ("process_condition_id") REFERENCES "process_conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "condition_details" ADD CONSTRAINT "FK_8b1cf64706e01509fddd7bd59d4" FOREIGN KEY ("condition_id") REFERENCES "conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "sector_conditions" ADD CONSTRAINT "FK_46bf392241d0902ff9221b94d47" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "sector_conditions" ADD CONSTRAINT "FK_3a0b7cb2b2cedabce5adb8af7d0" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "company_conditions" ADD CONSTRAINT "FK_ef13e3ad9620a7da150d83af4c4" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "company_conditions" ADD CONSTRAINT "FK_91894b1cf08b7f0bd127233d3a2" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_unit_conditions" ADD CONSTRAINT "FK_186a86d6aac76d817f0fd39f382" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "business_unit_conditions" ADD CONSTRAINT "FK_c2ca9fb3aaf565f46cbd7bfb9e5" FOREIGN KEY ("business_unit_id") REFERENCES "business_unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "department_conditions" ADD CONSTRAINT "FK_61d1b14b84f0c0719215aa0a0a2" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "department_conditions" ADD CONSTRAINT "FK_d344e50167cb08adb718151a5f0" FOREIGN KEY ("department_id") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cost_center_conditions" ADD CONSTRAINT "FK_d6b1b808621a587f51be6843e37" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "cost_center_conditions" ADD CONSTRAINT "FK_8b183ca83eb03a916c4ec70a7b5" FOREIGN KEY ("cost_center_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_code_conditions" ADD CONSTRAINT "FK_6764a2a3de23b0ccbe1baf818f9" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "budget_code_conditions" ADD CONSTRAINT "FK_a0d6f9a74af739d85e2b3b12d52" FOREIGN KEY ("budget_code_id") REFERENCES "budget_code"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "purchase_request_type_conditions" ADD CONSTRAINT "FK_22ff970add6dbcca8e960efe563" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "purchase_request_type_conditions" ADD CONSTRAINT "FK_cf4a0a5a57ce939afbab4574bc5" FOREIGN KEY ("purchase_request_type_id") REFERENCES "purchase_request_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "purchase_order_type_conditions" ADD CONSTRAINT "FK_1f6efd4e75f363511a2afbdafe8" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "purchase_order_type_conditions" ADD CONSTRAINT "FK_fd68ae982ab64c74c847d225146" FOREIGN KEY ("purchase_order_type_id") REFERENCES "purchase_order_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "purchase_order_type_conditions" DROP CONSTRAINT "FK_fd68ae982ab64c74c847d225146"`);
        await queryRunner.query(`ALTER TABLE "purchase_order_type_conditions" DROP CONSTRAINT "FK_1f6efd4e75f363511a2afbdafe8"`);
        await queryRunner.query(`ALTER TABLE "purchase_request_type_conditions" DROP CONSTRAINT "FK_cf4a0a5a57ce939afbab4574bc5"`);
        await queryRunner.query(`ALTER TABLE "purchase_request_type_conditions" DROP CONSTRAINT "FK_22ff970add6dbcca8e960efe563"`);
        await queryRunner.query(`ALTER TABLE "budget_code_conditions" DROP CONSTRAINT "FK_a0d6f9a74af739d85e2b3b12d52"`);
        await queryRunner.query(`ALTER TABLE "budget_code_conditions" DROP CONSTRAINT "FK_6764a2a3de23b0ccbe1baf818f9"`);
        await queryRunner.query(`ALTER TABLE "cost_center_conditions" DROP CONSTRAINT "FK_8b183ca83eb03a916c4ec70a7b5"`);
        await queryRunner.query(`ALTER TABLE "cost_center_conditions" DROP CONSTRAINT "FK_d6b1b808621a587f51be6843e37"`);
        await queryRunner.query(`ALTER TABLE "department_conditions" DROP CONSTRAINT "FK_d344e50167cb08adb718151a5f0"`);
        await queryRunner.query(`ALTER TABLE "department_conditions" DROP CONSTRAINT "FK_61d1b14b84f0c0719215aa0a0a2"`);
        await queryRunner.query(`ALTER TABLE "business_unit_conditions" DROP CONSTRAINT "FK_c2ca9fb3aaf565f46cbd7bfb9e5"`);
        await queryRunner.query(`ALTER TABLE "business_unit_conditions" DROP CONSTRAINT "FK_186a86d6aac76d817f0fd39f382"`);
        await queryRunner.query(`ALTER TABLE "company_conditions" DROP CONSTRAINT "FK_91894b1cf08b7f0bd127233d3a2"`);
        await queryRunner.query(`ALTER TABLE "company_conditions" DROP CONSTRAINT "FK_ef13e3ad9620a7da150d83af4c4"`);
        await queryRunner.query(`ALTER TABLE "sector_conditions" DROP CONSTRAINT "FK_3a0b7cb2b2cedabce5adb8af7d0"`);
        await queryRunner.query(`ALTER TABLE "sector_conditions" DROP CONSTRAINT "FK_46bf392241d0902ff9221b94d47"`);
        await queryRunner.query(`ALTER TABLE "condition_details" DROP CONSTRAINT "FK_8b1cf64706e01509fddd7bd59d4"`);
        await queryRunner.query(`ALTER TABLE "conditions" DROP CONSTRAINT "FK_e2645a474e5f7536bcac98c0e31"`);
        await queryRunner.query(`ALTER TABLE "process_conditions" DROP CONSTRAINT "FK_c2090185b0e6c760f56dfafbfc4"`);
        await queryRunner.query(`ALTER TABLE "process_conditions" DROP CONSTRAINT "FK_4d7fe1794d8ede4d1066a0f5823"`);
        await queryRunner.query(`ALTER TABLE "processes" DROP CONSTRAINT "FK_399184eb3067bc70cae280529eb"`);
        await queryRunner.query(`ALTER TABLE "purchase_request_type" ALTER COLUMN "name" SET DEFAULT ''`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fd68ae982ab64c74c847d22514"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1f6efd4e75f363511a2afbdafe"`);
        await queryRunner.query(`DROP TABLE "purchase_order_type_conditions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cf4a0a5a57ce939afbab4574bc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_22ff970add6dbcca8e960efe56"`);
        await queryRunner.query(`DROP TABLE "purchase_request_type_conditions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a0d6f9a74af739d85e2b3b12d5"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6764a2a3de23b0ccbe1baf818f"`);
        await queryRunner.query(`DROP TABLE "budget_code_conditions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8b183ca83eb03a916c4ec70a7b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d6b1b808621a587f51be6843e3"`);
        await queryRunner.query(`DROP TABLE "cost_center_conditions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d344e50167cb08adb718151a5f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_61d1b14b84f0c0719215aa0a0a"`);
        await queryRunner.query(`DROP TABLE "department_conditions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c2ca9fb3aaf565f46cbd7bfb9e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_186a86d6aac76d817f0fd39f38"`);
        await queryRunner.query(`DROP TABLE "business_unit_conditions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_91894b1cf08b7f0bd127233d3a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ef13e3ad9620a7da150d83af4c"`);
        await queryRunner.query(`DROP TABLE "company_conditions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3a0b7cb2b2cedabce5adb8af7d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_46bf392241d0902ff9221b94d4"`);
        await queryRunner.query(`DROP TABLE "sector_conditions"`);
        await queryRunner.query(`DROP TABLE "condition_details"`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_comparison_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum"`);
        await queryRunner.query(`DROP TABLE "conditions"`);
        await queryRunner.query(`DROP TABLE "process_conditions"`);
        await queryRunner.query(`DROP TABLE "processes"`);
        await queryRunner.query(`DROP TYPE "public"."processes_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."processes_status_enum"`);
    }

}
