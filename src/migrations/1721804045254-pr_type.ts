import { MigrationInterface, QueryRunner } from 'typeorm';

export class PrType1721804045254 implements MigrationInterface {
  name = 'PrType1721804045254';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."pr_type_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`,
    );
    await queryRunner.query(
      `CREATE TABLE "pr_type" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "status" "public"."pr_type_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_98944d5727f21b373c78fa9be5c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_f3609c99a4210f3936ae4ce131" ON "pr_type" ("code") `,
    );
    await queryRunner.query(
      `ALTER TABLE "companies" ALTER COLUMN "status" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "companies" ALTER COLUMN "status" SET NOT NULL`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_f3609c99a4210f3936ae4ce131"`,
    );
    await queryRunner.query(`DROP TABLE "pr_type"`);
    await queryRunner.query(`DROP TYPE "public"."pr_type_status_enum"`);
  }
}
