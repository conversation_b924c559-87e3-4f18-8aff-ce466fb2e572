import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTypeSupplier1730188596762 implements MigrationInterface {
    name = 'UpdateTypeSupplier1730188596762'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."suppliers_type_enum" RENAME TO "suppliers_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."suppliers_type_enum" AS ENUM('POTENTIAL', 'OFFICIAL', 'WALKING')`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "type" TYPE "public"."suppliers_type_enum" USING "type"::"text"::"public"."suppliers_type_enum"`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "type" SET DEFAULT 'POTENTIAL'`);
        await queryRunner.query(`DROP TYPE "public"."suppliers_type_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."suppliers_type_enum_old" AS ENUM('POTENTIAL', 'OFFICIAL')`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "type" TYPE "public"."suppliers_type_enum_old" USING "type"::"text"::"public"."suppliers_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "type" SET DEFAULT 'POTENTIAL'`);
        await queryRunner.query(`DROP TYPE "public"."suppliers_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."suppliers_type_enum_old" RENAME TO "suppliers_type_enum"`);
    }

}
