import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateMaterialWithSectors1721637759796 implements MigrationInterface {
    name = 'UpdateMaterialWithSectors1721637759796'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "materials" DROP CONSTRAINT "FK_b93bdab4a0d8d6a424714dfc8d9"`);
        await queryRunner.query(`CREATE TABLE "material_sector" ("material_id" uuid NOT NULL, "sector_id" uuid NOT NULL, CONSTRAINT "PK_252ec14b59c720712c7ac328e22" PRIMARY KEY ("material_id", "sector_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_a97f38b93c02d9c2498c336599" ON "material_sector" ("material_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_1a07934cfe89e6310fd8d4a52a" ON "material_sector" ("sector_id") `);
        await queryRunner.query(`ALTER TABLE "materials" DROP COLUMN "sector_id"`);
        await queryRunner.query(`ALTER TABLE "material_sector" ADD CONSTRAINT "FK_a97f38b93c02d9c2498c3365990" FOREIGN KEY ("material_id") REFERENCES "materials"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "material_sector" ADD CONSTRAINT "FK_1a07934cfe89e6310fd8d4a52a2" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_sector" DROP CONSTRAINT "FK_1a07934cfe89e6310fd8d4a52a2"`);
        await queryRunner.query(`ALTER TABLE "material_sector" DROP CONSTRAINT "FK_a97f38b93c02d9c2498c3365990"`);
        await queryRunner.query(`ALTER TABLE "materials" ADD "sector_id" uuid NOT NULL`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1a07934cfe89e6310fd8d4a52a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a97f38b93c02d9c2498c336599"`);
        await queryRunner.query(`DROP TABLE "material_sector"`);
        await queryRunner.query(`ALTER TABLE "materials" ADD CONSTRAINT "FK_b93bdab4a0d8d6a424714dfc8d9" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
