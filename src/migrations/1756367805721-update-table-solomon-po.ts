import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableSolomonPo1756367805721 implements MigrationInterface {
  name = 'UpdateTableSolomonPo1756367805721';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" ADD "is_created_solomon" boolean DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchase_orders" DROP COLUMN "is_created_solomon"`,
    );
  }
}
