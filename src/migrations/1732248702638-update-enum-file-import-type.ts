import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateEnumFileImportType1732248702638 implements MigrationInterface {
    name = 'UpdateEnumFileImportType1732248702638'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "actual_spendings" ADD "inventory_doc" character varying`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "type" SET DEFAULT 'OFFICIAL'`);
        await queryRunner.query(`ALTER TYPE "public"."file-import-histories_import_type_enum" RENAME TO "file-import-histories_import_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."file-import-histories_import_type_enum" AS ENUM('OPEX', 'CAPEX', 'BUDGET_CODE', 'COST_CENTER_SUB_ACCOUNT', 'SUPPLIER', 'MATERIAL', 'COST', 'EXCHANGE_RATE', 'INVENTORY_STANDARD', 'BUSINESS_OWNER', 'MATERIAL_GROUP', 'PURCHASING_DEPARTMENT', 'MATERIAL_TYPE', 'PURCHASING_GROUP', 'DEPARTMENT', 'COMPANY', 'BUSINESS_UNIT', 'ACTUAL_SPENDING')`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" ALTER COLUMN "import_type" TYPE "public"."file-import-histories_import_type_enum" USING "import_type"::"text"::"public"."file-import-histories_import_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."file-import-histories_import_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ALTER COLUMN "sap_actual_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ALTER COLUMN "status" SET DEFAULT 'CONFIRMED'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "actual_spendings" ALTER COLUMN "status" SET DEFAULT 'UNCONFIRMED'`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" ALTER COLUMN "sap_actual_id" SET NOT NULL`);
        await queryRunner.query(`CREATE TYPE "public"."file-import-histories_import_type_enum_old" AS ENUM('OPEX', 'CAPEX', 'BUDGET_CODE', 'COST_CENTER_SUB_ACCOUNT', 'SUPPLIER', 'MATERIAL', 'COST', 'EXCHANGE_RATE', 'INVENTORY_STANDARD', 'BUSINESS_OWNER', 'MATERIAL_GROUP', 'PURCHASING_DEPARTMENT', 'MATERIAL_TYPE', 'PURCHASING_GROUP', 'DEPARTMENT', 'COMPANY', 'BUSINESS_UNIT')`);
        await queryRunner.query(`ALTER TABLE "file-import-histories" ALTER COLUMN "import_type" TYPE "public"."file-import-histories_import_type_enum_old" USING "import_type"::"text"::"public"."file-import-histories_import_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."file-import-histories_import_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."file-import-histories_import_type_enum_old" RENAME TO "file-import-histories_import_type_enum"`);
        await queryRunner.query(`ALTER TABLE "suppliers" ALTER COLUMN "type" SET DEFAULT 'POTENTIAL'`);
        await queryRunner.query(`ALTER TABLE "actual_spendings" DROP COLUMN "inventory_doc"`);
    }

}
