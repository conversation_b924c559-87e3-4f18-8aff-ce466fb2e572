import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableCurrencyExchange1732766288436 implements MigrationInterface {
    name = 'UpdateTableCurrencyExchange1732766288436'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" ADD "exchange_budget" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "currency_unit_exchange" DROP COLUMN "exchange_budget"`);
    }

}
