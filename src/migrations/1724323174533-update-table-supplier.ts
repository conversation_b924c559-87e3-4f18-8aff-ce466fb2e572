import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableSupplier1724323174533 implements MigrationInterface {
    name = 'UpdateTableSupplier1724323174533'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."supplier_sectors_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "supplier_sectors" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "code_sap" character varying NOT NULL, "status" "public"."supplier_sectors_status_enum" DEFAULT 'ACTIVE', "supplier_id" uuid, "sector_id" uuid, CONSTRAINT "PK_727a67a89b3b3e09ceac21a9e50" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ADD CONSTRAINT "FK_d79b6823d06a028a0519c28ad53" FOREIGN KEY ("supplier_id") REFERENCES "suppliers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" ADD CONSTRAINT "FK_c12f7aad4917b32eb059addb4ef" FOREIGN KEY ("sector_id") REFERENCES "sectors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_sectors" DROP CONSTRAINT "FK_c12f7aad4917b32eb059addb4ef"`);
        await queryRunner.query(`ALTER TABLE "supplier_sectors" DROP CONSTRAINT "FK_d79b6823d06a028a0519c28ad53"`);
        await queryRunner.query(`DROP TABLE "supplier_sectors"`);
        await queryRunner.query(`DROP TYPE "public"."supplier_sectors_status_enum"`);
    }

}
