import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableReceiptV21754563456959 implements MigrationInterface {
  name = 'UpdateTableReceiptV21754563456959';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" ADD "po_id_error" integer`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "receipt_solomons" DROP COLUMN "po_id_error"`,
    );
  }
}
