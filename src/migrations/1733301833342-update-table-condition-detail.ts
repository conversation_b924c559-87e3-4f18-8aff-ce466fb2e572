import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableConditionDetail1733301833342 implements MigrationInterface {
    name = 'UpdateTableConditionDetail1733301833342'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "plant_conditions" ("condition_id" uuid NOT NULL, "plant_id" uuid NOT NULL, CONSTRAINT "PK_19e8168888843b81093e31e365b" PRIMARY KEY ("condition_id", "plant_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d9048cf0535cc29199f9c93e4c" ON "plant_conditions" ("condition_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_2a034db4eae078e58b2c9ba928" ON "plant_conditions" ("plant_id") `);
        await queryRunner.query(`ALTER TABLE "condition_details" ADD "difference_amount" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "condition_details" ADD "budget_overrun_rate" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_type_enum" RENAME TO "condition_details_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN', 'CHECK_BUDGET', 'DIFFERENCE_AMOUNT', 'BUDGET_OVERRUN_RATE', 'PROCESS_TYPE', 'PLANT')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum" USING "type"::"text"::"public"."condition_details_type_enum"`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "plant_conditions" ADD CONSTRAINT "FK_d9048cf0535cc29199f9c93e4c3" FOREIGN KEY ("condition_id") REFERENCES "condition_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "plant_conditions" ADD CONSTRAINT "FK_2a034db4eae078e58b2c9ba928a" FOREIGN KEY ("plant_id") REFERENCES "plants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "plant_conditions" DROP CONSTRAINT "FK_2a034db4eae078e58b2c9ba928a"`);
        await queryRunner.query(`ALTER TABLE "plant_conditions" DROP CONSTRAINT "FK_d9048cf0535cc29199f9c93e4c3"`);
        await queryRunner.query(`CREATE TYPE "public"."condition_details_type_enum_old" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'PR_TYPE', 'PO_TYPE', 'COST_CENTER', 'BUDGET_CODE', 'VALUE_PR', 'VALUE_PO', 'VALUE_BUDGET', 'BUDGET_OVERRUN', 'CHECK_BUDGET')`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" TYPE "public"."condition_details_type_enum_old" USING "type"::"text"::"public"."condition_details_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "condition_details" ALTER COLUMN "type" SET DEFAULT 'SECTOR'`);
        await queryRunner.query(`DROP TYPE "public"."condition_details_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."condition_details_type_enum_old" RENAME TO "condition_details_type_enum"`);
        await queryRunner.query(`ALTER TABLE "condition_details" DROP COLUMN "budget_overrun_rate"`);
        await queryRunner.query(`ALTER TABLE "condition_details" DROP COLUMN "difference_amount"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2a034db4eae078e58b2c9ba928"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d9048cf0535cc29199f9c93e4c"`);
        await queryRunner.query(`DROP TABLE "plant_conditions"`);
    }

}
