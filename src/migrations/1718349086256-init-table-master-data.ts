import { MigrationInterface, QueryRunner } from "typeorm";

export class InitTableMasterData1718349086256 implements MigrationInterface {
    name = 'InitTableMasterData1718349086256'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "sectors" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "is_enabled" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_923fdda0dc12f59add7b3a1782f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_182f6a983ef4914b39c1aae548" ON "sectors" ("code") `);
        await queryRunner.query(`CREATE TABLE "companies" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "is_enabled" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_d4bc3e82a314fa9e29f652c2c22" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_80af3e6808151c3210b4d5a218" ON "companies" ("code") `);
        await queryRunner.query(`CREATE TYPE "public"."department_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "department" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, "status" "public"."department_status_enum" DEFAULT 'ACTIVE', "search_value" character varying, CONSTRAINT "UQ_62690f4fe31da9eb824d909285f" UNIQUE ("code"), CONSTRAINT "PK_9a2213262c1593bffb581e382f5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_62690f4fe31da9eb824d909285" ON "department" ("code") `);
        await queryRunner.query(`CREATE TYPE "public"."function_unit_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "function_unit" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, "status" "public"."function_unit_status_enum" DEFAULT 'ACTIVE', "search_value" character varying, CONSTRAINT "UQ_787b967bf26d322de3afb4d7dc0" UNIQUE ("code"), CONSTRAINT "PK_9579ee74fd30aa7c0fd1fb45bd3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_787b967bf26d322de3afb4d7dc" ON "function_unit" ("code") `);
        await queryRunner.query(`CREATE TABLE "business_owners" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "is_enabled" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_177492f4438d9e389e4c63d1064" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_ffa35ea3cd780960028bf56af9" ON "business_owners" ("code") `);
        await queryRunner.query(`CREATE TYPE "public"."business_unit_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "business_unit" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, "status" "public"."business_unit_status_enum" DEFAULT 'ACTIVE', "search_value" character varying, CONSTRAINT "UQ_d3cd31ed2081b77d843431e39ce" UNIQUE ("code"), CONSTRAINT "PK_842dd03ad7a179a295f55c01008" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d3cd31ed2081b77d843431e39c" ON "business_unit" ("code") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_d3cd31ed2081b77d843431e39c"`);
        await queryRunner.query(`DROP TABLE "business_unit"`);
        await queryRunner.query(`DROP TYPE "public"."business_unit_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ffa35ea3cd780960028bf56af9"`);
        await queryRunner.query(`DROP TABLE "business_owners"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_787b967bf26d322de3afb4d7dc"`);
        await queryRunner.query(`DROP TABLE "function_unit"`);
        await queryRunner.query(`DROP TYPE "public"."function_unit_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_62690f4fe31da9eb824d909285"`);
        await queryRunner.query(`DROP TABLE "department"`);
        await queryRunner.query(`DROP TYPE "public"."department_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_80af3e6808151c3210b4d5a218"`);
        await queryRunner.query(`DROP TABLE "companies"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_182f6a983ef4914b39c1aae548"`);
        await queryRunner.query(`DROP TABLE "sectors"`);
    }

}
