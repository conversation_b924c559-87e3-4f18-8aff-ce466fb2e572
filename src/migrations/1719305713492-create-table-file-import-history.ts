import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableFileImportHistory1719305713492 implements MigrationInterface {
    name = 'CreateTableFileImportHistory1719305713492'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."file-import-histories_status_enum" AS ENUM('SUCCESS', 'FAIL', 'IN_PROCESS', 'WAITING')`);
        await queryRunner.query(`CREATE TYPE "public"."file-import-histories_import_type_enum" AS ENUM('OPEX', 'CAPEX', 'BUDGET_CODE', 'COST_CENTER_SUB_ACCOUNT')`);
        await queryRunner.query(`CREATE TABLE "file-import-histories" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "file_name" character varying NOT NULL, "file_path" character varying NOT NULL, "status" "public"."file-import-histories_status_enum" DEFAULT 'WAITING', "created_by" jsonb NOT NULL, "import_type" "public"."file-import-histories_import_type_enum" NOT NULL, CONSTRAINT "PK_1a6521b48d01fdf6871358fbb98" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "file-import-histories"`);
        await queryRunner.query(`DROP TYPE "public"."file-import-histories_import_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."file-import-histories_status_enum"`);
    }

}
