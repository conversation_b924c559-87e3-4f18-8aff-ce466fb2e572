import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableFileExportHistory1722220661038 implements MigrationInterface {
    name = 'UpdateTableFileExportHistory1722220661038'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "file-export-histories" RENAME COLUMN "import_type" TO "export_type"`);
        await queryRunner.query(`ALTER TYPE "public"."file-export-histories_import_type_enum" RENAME TO "file-export-histories_export_type_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."file-export-histories_export_type_enum" RENAME TO "file-export-histories_import_type_enum"`);
        await queryRunner.query(`ALTER TABLE "file-export-histories" RENAME COLUMN "export_type" TO "import_type"`);
    }

}
