import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateColumnErrorsHistoryImport1719976284141 implements MigrationInterface {
    name = 'UpdateColumnErrorsHistoryImport1719976284141'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "file-import-histories" ADD "errors" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "file-import-histories" DROP COLUMN "errors"`);
    }

}
