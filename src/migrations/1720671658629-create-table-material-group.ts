import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableMaterialGroup1720671658629 implements MigrationInterface {
    name = 'CreateTableMaterialGroup1720671658629'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."material_groups_status_enum" AS ENUM('ACTIVE', 'IN_ACTIVE')`);
        await queryRunner.query(`CREATE TABLE "material_groups" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "status" "public"."material_groups_status_enum" DEFAULT 'ACTIVE', CONSTRAINT "PK_46bd96c12053bbd9ccca7bb7a1b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_b2a60cfe5be75988c5162c70ee" ON "material_groups" ("code") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_b2a60cfe5be75988c5162c70ee"`);
        await queryRunner.query(`DROP TABLE "material_groups"`);
        await queryRunner.query(`DROP TYPE "public"."material_groups_status_enum"`);
    }

}
