import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableMaterial1722220897295 implements MigrationInterface {
    name = 'UpdateTableMaterial1722220897295'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."materials_check_budget_enum" RENAME TO "materials_check_budget_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."materials_check_budget_enum" AS ENUM('YES_CHECK_BUDGET', 'NO_CHECK_BUDGET', 'OPTIONAL')`);
        await queryRunner.query(`ALTER TABLE "materials" ALTER COLUMN "check_budget" TYPE "public"."materials_check_budget_enum" USING "check_budget"::"text"::"public"."materials_check_budget_enum"`);
        await queryRunner.query(`DROP TYPE "public"."materials_check_budget_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."materials_check_budget_enum_old" AS ENUM('YES_CHECK_BUDGET', 'NO_CHECK_BUDGET')`);
        await queryRunner.query(`ALTER TABLE "materials" ALTER COLUMN "check_budget" TYPE "public"."materials_check_budget_enum_old" USING "check_budget"::"text"::"public"."materials_check_budget_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."materials_check_budget_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."materials_check_budget_enum_old" RENAME TO "materials_check_budget_enum"`);
    }

}
