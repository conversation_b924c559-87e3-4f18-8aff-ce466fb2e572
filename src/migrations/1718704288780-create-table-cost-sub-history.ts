import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableCostSubHistory1718704288780 implements MigrationInterface {
    name = 'CreateTableCostSubHistory1718704288780'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "cost_sub_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "old_data" jsonb, "new_data" jsonb, "costcenter_subaccount_id" uuid NOT NULL, "user_info" jsonb, CONSTRAINT "PK_6002a640bcb4cff11ef9ef50cb9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "cost_sub_history" ADD CONSTRAINT "FK_b7c9841ed6c5ac9146d3758cf5e" FOREIGN KEY ("costcenter_subaccount_id") REFERENCES "costcenter_subaccount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cost_sub_history" DROP CONSTRAINT "FK_b7c9841ed6c5ac9146d3758cf5e"`);
        await queryRunner.query(`DROP TABLE "cost_sub_history"`);
    }

}
