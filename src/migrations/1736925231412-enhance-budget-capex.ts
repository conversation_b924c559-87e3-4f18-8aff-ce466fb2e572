import { MigrationInterface, QueryRunner } from 'typeorm';

export class EnhanceBudgetCapex1736925231412 implements MigrationInterface {
  name = 'EnhanceBudgetCapex1736925231412';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "budget_investment" ALTER COLUMN "price" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "budget_investment" ALTER COLUMN "transportation_costs" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "budget_capex" ALTER COLUMN "start_date" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "budget_capex" ALTER COLUMN "classify" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "budget_capex" ALTER COLUMN "classify" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "budget_capex" ALTER COLUMN "start_date" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "budget_investment" ALTER COLUMN "transportation_costs" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "budget_investment" ALTER COLUMN "price" SET NOT NULL`,
    );
  }
}
