import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Min,
} from 'class-validator';
import { EInventoryStandardStatus } from '../../../domain/config/enums/inventory-standard.enum';

export class CreateInventoryStandardDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Vật tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.MATERIAL_ID.MUST_BE_UUID' })
  materialId: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Đơn vị tính',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.UNIT.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.UNIT.MUST_BE_STRING' })
  unit?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Mảng',
  })
  @IsNotEmpty({ message: 'VALIDATE.SECTOR_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.SECTOR_ID.MUST_BE_UUID' })
  sectorId: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Công ty',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.COMPANY_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.COMPANY_ID.MUST_BE_UUID' })
  companyId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Đơn vị kinh doanh',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.BUSINESS_UNIT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.BUSINESS_UNIT_ID.MUST_BE_UUID' })
  businessUnitId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Phòng ban',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.DEPARTMENT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.DEPARTMENT_ID.MUST_BE_UUID' })
  departmentId?: string;

  @ApiProperty({
    description: 'Số lượng định mức',
    type: Number,
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.STANDARD_QUANTITY.IS_REQUIRED' })
  @IsInt({})
  @Min(0)
  standardQuantity: number;

  @ApiProperty({
    description: 'Số lượng tồn kho',
    type: Number,
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.INVENTORY_QUANTITY.IS_REQUIRED' })
  @IsInt({})
  @Min(0)
  inventoryQuantity: number;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Trạng thái',
  })
  @IsNotEmpty()
  @IsEnum(EInventoryStandardStatus, {
    message: 'VALIDATE.STATUS.INVALID_VALUE',
  })
  status: EInventoryStandardStatus;

  createdAt?: string;
  updatedAt?: string;
}
