import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { Transform } from 'class-transformer';
import { EInventoryStandardStatus } from '../../../domain/config/enums/inventory-standard.enum';

export class GetInventoryStandardListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  materialIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  sectorIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  companyIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  departmentIds?: string[];

  @ApiProperty({
    type: [EInventoryStandardStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EInventoryStandardStatus, { each: true })
  statuses?: EInventoryStandardStatus[];

  sectorCodes?: string[];
  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
  materialTypeCodes?: string[];
  materialGroupCodes?: string[];
  materialCodes?: string[];
}
