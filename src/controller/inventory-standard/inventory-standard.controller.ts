import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
  Response,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpStatus,
  Request,
} from '@nestjs/common';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { InventoryStandardUsecases } from '../../usecases/inventory-standard.usecases';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EInventoryStandardPermission } from '../../utils/constants/permission.enum';
import { CreateInventoryStandardDto } from './dtos/create-inventory-standard.dto';
import { UpdateInventoryStandardDto } from './dtos/update-inventory-standard.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { GetDetailInventoryStandardDto } from './dtos/get-detail-inventory-standard.dto';
import { GetInventoryStandardListDto } from './dtos/get-inventory-standard-list.dto';
import { DeleteInventoryStandardDto } from './dtos/delete-inventory-standard.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('/inventory-standard')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Inventory Standard')
export class InventoryStandardController {
  constructor(
    private readonly inventoryStandardUsecases: InventoryStandardUsecases,
  ) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EInventoryStandardPermission.CREATE]))
  async create(
    @Body() data: CreateInventoryStandardDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.inventoryStandardUsecases.createInventoryStandard(
      data,
      jwtPayload,
    );
  }

  @Post(':id/update')
  @UseGuards(NewPermissionGuard([EInventoryStandardPermission.EDIT]))
  async update(
    @Body() data: UpdateInventoryStandardDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.inventoryStandardUsecases.updateInventoryStandard(
      param.id,
      data,
      jwtPayload,
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EInventoryStandardPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailInventoryStandardDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.inventoryStandardUsecases.getInventoryStandardDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EInventoryStandardPermission.VIEW]))
  async getList(
    @Query() param: GetInventoryStandardListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.inventoryStandardUsecases.getInventoryStandards(
      param,
      jwtPayload,
    );
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EInventoryStandardPermission.DELETE]))
  async delete(
    @Param() param: DeleteInventoryStandardDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.inventoryStandardUsecases.deleteInventoryStandard(
      param.id,
      jwtPayload,
    );
  }

  @Get('/export-inventory-standard')
  @UseGuards(NewPermissionGuard([EInventoryStandardPermission.EXPORT]))
  async exportExcelInventoryStandard(
    @Query() param: GetInventoryStandardListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result =
      await this.inventoryStandardUsecases.exportExcelInventoryStandard(
        param,
        jwtPayload,
      );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-inventory-standard')
  @UseGuards(NewPermissionGuard([EInventoryStandardPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importInventoryStandard(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.inventoryStandardUsecases.importInventoryStandard(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
