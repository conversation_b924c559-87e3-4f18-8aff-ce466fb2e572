import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { CreateMaterialSectorDto } from '../../material-sector/dtos/create-material-sector.dto';

export class CreateMaterialDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã Vật tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên Vật tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
    description: '<PERSON><PERSON><PERSON> vật tư',
  })
  @IsUUID('4', { message: 'VALIDATE.MATERIAL_TYPE_ID.MUST_BE_UUID' })
  materialTypeId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Đơn vị tính',
  })
  @IsNotEmpty({ message: 'VALIDATE.UNIT.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.UNIT.MUST_BE_STRING' })
  unit: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Nhóm vật tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_GROUP_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.MATERIAL_GROUP_ID.MUST_BE_UUID' })
  materialGroupId: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Mô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: [CreateMaterialSectorDto],
    description: 'Ngành',
    required: true,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.INDUSTRIES.MUST_BE_ARRAY' })
  @ValidateNested({ each: true })
  @Type(() => CreateMaterialSectorDto)
  industries?: CreateMaterialSectorDto[];

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'Đơn vị kinh doanh ',
  // })
  // @IsOptional()
  // @IsArray({ message: 'VALIDATE.BUSINESS_UNIT_IDS.MUST_BE_ARRAY' })
  // @IsUUID('4', {
  //   message: 'VALIDATE.BUSINESS_UNIT_ID.MUST_BE_UUID',
  //   each: true,
  // })
  businessUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Khối chức năng',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.FUNCTION_UNIT_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.FUNCTION_UNIT_ID.MUST_BE_UUID',
    each: true,
  })
  functionUnitIds?: string[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'Bộ phân thu mua',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PURCHASING_DEPARTMENT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PURCHASING_DEPARTMENT_ID.MUST_BE_UUID' })
  purchasingDepartmentId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Nhóm thu mua',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PURCHASING_GROUP_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PURCHASING_GROUP_ID.MUST_BE_UUID' })
  purchasingGroupId?: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'Qua thu mua',
  // })
  // @IsOptional()
  // @IsEnum(EMaterialThroughPurchasing, {
  //   message: 'VALIDATE.THROUGH_PURCHASING.INVALID_VALUE',
  // })
  // throughPurchasing?: EMaterialThroughPurchasing;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'Kiểm tra ngân sách',
  // })
  // @IsOptional()
  // @IsEnum(EMaterialCheckBudget, {
  //   message: 'VALIDATE.CHECK_BUDGET.INVALID_VALUE',
  // })
  // checkBudget?: EMaterialCheckBudget;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Profit Center ',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PROFIT_CENTER_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PROFIT_CENTER_ID.MUST_BE_UUID' })
  profitCenterId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Công ty',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.COMPANY_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.COMPANY_ID.MUST_BE_UUID' })
  companyId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Phòng ban',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.DEPARTMENT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.DEPARTMENT_ID.MUST_BE_UUID' })
  departmentId?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Đơn vị tính',
  })
  @IsUUID('4', { message: 'VALIDATE.MEASURE_ID.MUST_BE_UUID' })
  measureId?: string;

  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  createdBy?: object;
}
