import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { EMaterialStatus } from '../../../domain/config/enums/material.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetMaterialListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EMaterialStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EMaterialStatus, { each: true })
  statuses?: EMaterialStatus[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  profitCenterIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  materialGroupIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  materialTypeIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  companyIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  departmentIds?: string[];

  // @ApiProperty({
  //   type: [EMaterialThroughPurchasing],
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray()
  // @IsEnum(EMaterialThroughPurchasing, { each: true })
  // throughPurchasings?: EMaterialThroughPurchasing[];

  // @ApiProperty({
  //   type: [EMaterialCheckBudget],
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray()
  // @IsEnum(EMaterialCheckBudget, { each: true })
  // checkBudgets?: EMaterialCheckBudget[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];

  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
  materialTypeCodes?: string[];
  materialGroupCodes?: string[];
  materialCodes?: string[];
  purchasingDepartmentCodes?: string[];
  purchasingGroupCodes?: string[];
  businessOwnerCodes?: string[];
  sectorCodes?: string[];
  functionUnitCodes?: string[];
}
