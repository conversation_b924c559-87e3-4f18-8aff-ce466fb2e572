import {
  Body,
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Patch,
  Param,
  Get,
  Query,
  Delete,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpStatus,
  Response,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { MaterialUsecases } from '../../usecases/material.usecases';
import { CreateMaterialDto } from './dtos/create-material.dto';
import { UpdateMaterialDto } from './dtos/update-material.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { GetDetailMaterialDto } from './dtos/get-detail-material.dto';
import { GetMaterialListDto } from './dtos/get-material-list.dto';
import { DeleteMaterialDto } from './dtos/delete-material.dto';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EMaterialPermission } from '../../utils/constants/permission.enum';
import { FileInterceptor } from '@nestjs/platform-express';
import { ListByCodesDto } from '../../domain/dtos/base-dto-by-codes.dto';
import { GetMaterialListByIdsDto } from './dtos/get-material-list-by-ids.dto';

@Controller('/material')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Material')
export class MaterialController {
  constructor(private readonly materialUsecases: MaterialUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EMaterialPermission.CREATE]))
  async create(
    @Body() data: CreateMaterialDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.materialUsecases.createMaterial(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EMaterialPermission.EDIT]))
  async update(
    @Body() data: UpdateMaterialDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.materialUsecases.updateMaterial(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EMaterialPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailMaterialDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialUsecases.getMaterialDetail(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EMaterialPermission.VIEW]))
  async getList(
    @Query() param: GetMaterialListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialUsecases.getMaterials(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EMaterialPermission.DELETE]))
  async delete(
    @Param() param: DeleteMaterialDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.materialUsecases.deleteMaterial(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-material')
  @UseGuards(NewPermissionGuard([EMaterialPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importMaterial(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.materialUsecases.importMaterial(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/list-by-codes')
  async listByCodes(
    @Body() data: ListByCodesDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialUsecases.listByCodes(
      data.codes || [],
      jwtPayload,
    );
  }

  @Get('/export-materials')
  @UseGuards(
    NewPermissionGuard([
      [EMaterialPermission.VIEW, EMaterialPermission.EXPORT],
    ]),
  )
  async exportMaterials(
    @Query() param: GetMaterialListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.materialUsecases.exportMaterials(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  ///For validate detail PR PO
  @Post('/list-by-ids')
  async getListByIds(
    @Body() param: GetMaterialListByIdsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialUsecases.getListByIds(param, jwtPayload);
  }
}
