import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsEnum, IsIn, IsInt, IsOptional } from 'class-validator';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetProcessTypeListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EStatus, { each: true })
  statuses?: EStatus[];

  @ApiProperty({
    description: 'Has Inventory Standard',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => +value)
  @IsInt({ message: 'VALIDATE.HAS_INVENTORY_STANDARD.MUST_BE_INT' })
  @IsIn([0, 1], { message: 'VALIDATE.HAS_INVENTORY_STANDARD.INVALID_VALUE' })
  hasInventoryStandard: number;

  codes?: string[];
  ids?: string[]; //For create/update staff
}
