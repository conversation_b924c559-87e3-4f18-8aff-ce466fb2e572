import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { EStatus } from '../../../domain/config/enums/status.enum';

export class CreateProcessTypeDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code of processType',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of processType',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of processType',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    description: 'Has Inventory Standard',
    type: Boolean,
    required: true,
    default: false,
  })
  @IsNotEmpty({ message: 'VALIDATE.HAS_INVENTORY_STANDARD.IS_REQUIRED' })
  @IsBoolean({ message: 'VALIDATE.HAS_INVENTORY_STANDARD.MUST_BE_BOOLEAN' })
  hasInventoryStandard: boolean;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EStatus;

  createdBy?: object;
}
