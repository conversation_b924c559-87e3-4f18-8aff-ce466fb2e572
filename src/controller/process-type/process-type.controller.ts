import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ProcessTypeUsecases } from '../../usecases/process-type.usecases';
import { EProcessTypePermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateProcessTypeDto } from './dtos/create-process-type.dto';
import { DeleteProcessTypeDto } from './dtos/delete-process-type.dto';
import { GetDetailProcessTypeDto } from './dtos/get-detail-process-type.dto';
import { GetProcessTypeListDto } from './dtos/get-process-type-list.dto';
import { UpdateProcessTypeDto } from './dtos/update-process-type.dto';

@Controller('/process-type')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('ProcessType')
export class ProcessTypeController {
  constructor(private readonly processTypeUsecases: ProcessTypeUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EProcessTypePermission.CREATE]))
  async create(
    @Body() data: CreateProcessTypeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.processTypeUsecases.createProcessType(data, jwtPayload);
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EProcessTypePermission.EDIT]))
  async update(
    @Body() data: UpdateProcessTypeDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.processTypeUsecases.updateProcessType(
      param.id,
      data,
      jwtPayload,
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EProcessTypePermission.VIEW]))
  async getDetail(@Param() param: GetDetailProcessTypeDto) {
    return await this.processTypeUsecases.getDetailProcessType(param);
  }

  @Get('/list')
  //   @UseGuards(NewPermissionGuard([EProcessTypePermission.VIEW]))
  async getList(@Query() param: GetProcessTypeListDto) {
    return await this.processTypeUsecases.getProcessTypes(param);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EProcessTypePermission.DELETE]))
  async delete(@Param() param: DeleteProcessTypeDto) {
    return await this.processTypeUsecases.deleteProcessType(param.id);
  }
}
