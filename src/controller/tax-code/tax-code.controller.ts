import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { TaxCodeUsecases } from '../../usecases/tax-code.usecases';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { ETaxCodePermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { CreateTaxCodeDto } from './dtos/create-tax-code.dto';
import { GetDetailTaxCodeDto } from './dtos/get-detail-tax-code.dto';
import { GetTaxCodeListDto } from './dtos/get-tax-code-list.dto';
import { DeleteTaxCodeDto } from './dtos/delete-tax-code.dto';
import { UpdateTaxCodeDto } from './dtos/update-tax-code.dto';

@Controller('/tax-code')
@UseInterceptors(TransformationInterceptor, AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Tax Code')
export class TaxCodeController {
  constructor(private readonly taxCodeUsecases: TaxCodeUsecases) {}

  @Post('/create')
  @UseInterceptors(TransactionInterceptor)
  // @UseGuards(NewPermissionGuard([ETaxCodePermission.CREATE]))
  async create(@Body() data: CreateTaxCodeDto, @NewAuthUser() jwtPayload: any) {
    return await this.taxCodeUsecases.createTaxCode(data);
  }

  @Get(':id/detail')
  @UseInterceptors(TransactionInterceptor)
  async getDetail(
    @Param() param: GetDetailTaxCodeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.taxCodeUsecases.getTaxCodeDetail(param, jwtPayload);
  }

  @Get('/list')
  // @UseGuards(NewPermissionGuard([ETaxCodePermission.VIEW]))
  async getList(
    @Query() param: GetTaxCodeListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.taxCodeUsecases.getTaxCodes(param, jwtPayload);
  }

  @Delete(':id/delete')
  @UseInterceptors(TransactionInterceptor)
  // @UseGuards(NewPermissionGuard([ETaxCodePermission.DELETE]))
  async delete(
    @Param() param: DeleteTaxCodeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.taxCodeUsecases.deleteTaxCode(param, jwtPayload);
  }

  @Put(':id/update')
  @UseInterceptors(TransactionInterceptor)
  // @UseGuards(NewPermissionGuard([ETaxCodePermission.EDIT]))
  async update(
    @Param('id') taxCodeId: string,
    @Body() data: UpdateTaxCodeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.taxCodeUsecases.updateTaxCode(taxCodeId, data);
  }
}
