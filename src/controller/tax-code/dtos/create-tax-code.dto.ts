import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  Max<PERSON>ength,
  Min,
} from 'class-validator';

export class CreateTaxCodeDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  @MaxLength(2, { message: 'VALIDATE.CODE.MAX_LENGTH' })
  code: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Mô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: Number,
    required: true,
    description: 'Tỷ lệ thuế',
    default: 0,
  })
  @IsNotEmpty({ message: 'VALIDATE.TAX_RATE.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.TAX_RATE.MUST_BE_NUMBER' })
  @Min(0)
  taxRate: number;

  createdAt?: string;
  updatedAt?: string;
}
