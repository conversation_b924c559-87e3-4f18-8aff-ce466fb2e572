import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  Request,
  Response,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { CompanyUsecases } from '../../usecases/company.usecases';
import { ECompanyPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateCompanyDto } from './dtos/create-company.dto';
import { DeleteCompanyDto } from './dtos/delete-company.dto';
import { GetCompanyListDto } from './dtos/get-company-list.dto';
import { GetDetailCompanyDto } from './dtos/get-detail-company.dto';
import { UpdateCompanyDto } from './dtos/update-company.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('/company')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Company')
export class CompanyController {
  constructor(private readonly companyUsecases: CompanyUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([ECompanyPermission.CREATE]))
  async create(@Body() data: CreateCompanyDto, @Request() req) {
    return await this.companyUsecases.createCompany(
      data,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([ECompanyPermission.EDIT]))
  async update(
    @Body() data: UpdateCompanyDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.companyUsecases.updateCompany(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([ECompanyPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailCompanyDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.companyUsecases.getDetailCompany(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([ECompanyPermission.VIEW]))
  async getList(
    @Query() param: GetCompanyListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.companyUsecases.getCompanies(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([ECompanyPermission.DELETE]))
  async delete(
    @Param() param: DeleteCompanyDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.companyUsecases.deleteCompany(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-company')
  @UseGuards(NewPermissionGuard([ECompanyPermission.EXPORT]))
  async exportCompany(
    @Query() param: GetCompanyListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.companyUsecases.exportCompany(param, jwtPayload);

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-company')
  @UseGuards(NewPermissionGuard([ECompanyPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importCompany(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.companyUsecases.importCompany(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
