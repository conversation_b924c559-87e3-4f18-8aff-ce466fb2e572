import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { ECompanyStatus } from '../../../domain/config/enums/company.enum';

export class CreateCompanyDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code of company',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of company',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of company',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: ECompanyStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ECompanyStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: ECompanyStatus;

  createdAt?: string;
  updatedAt?: string;
}
