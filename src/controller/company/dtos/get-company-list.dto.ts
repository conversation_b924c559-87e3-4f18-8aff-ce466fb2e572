import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { ECompanyStatus } from '../../../domain/config/enums/company.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetCompanyListDto extends OmitType(PaginationDto, ['from', 'to']) {
  @ApiProperty({
    type: [ECompanyStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ECompanyStatus, { each: true })
  statuses?: ECompanyStatus[];

  codes?: string[];
  ids: string[];
}
