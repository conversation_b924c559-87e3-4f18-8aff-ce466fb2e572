import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { FileExportHistoryUsecases } from '../../usecases/file-export-history.usecases';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetListFileExportHistoryDto } from './dtos/get-list-file-export-history.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { UpdateStatusFileExportHistoryDto } from './dtos/update-status-file-export-hsitory.dto';

export {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  UseInterceptors,
} from '@nestjs/common';
export { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('/file-export-history')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
// @UseGuards(NewAuthGuard)
@ApiTags('File Export History')
export class FileExportHistoryController {
  constructor(
    private readonly fileExportHistoryUsecases: FileExportHistoryUsecases,
  ) {}

  @Get('/list')
  async getList(@Query() param: GetListFileExportHistoryDto) {
    return await this.fileExportHistoryUsecases.getListFileExportHistory(param);
  }

  @Delete(':id/delete')
  async delete(@Param() param: GetUuidDto) {
    await this.fileExportHistoryUsecases.deleteFileExportHistory(param.id);
    return { message: 'Successfully!!!' };
  }

  @Get(':id/detail')
  async getDetail(@Param() param: GetUuidDto) {
    return await this.fileExportHistoryUsecases.getFileExportHistoryDetail(
      param.id,
    );
  }

  @Put('/update-status/:id')
  async updateBudgetOpex(
    @Body() data: UpdateStatusFileExportHistoryDto,
    @Param('id') id: string,
  ) {
    return await this.fileExportHistoryUsecases.updateFileExportHistory(
      id,
      data,
    );
  }
}
