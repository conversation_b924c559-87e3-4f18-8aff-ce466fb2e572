import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { Any } from 'typeorm';
import { EFileExportStatus } from '../../../domain/config/enums/file-export-history.enum';

export class UpdateStatusFileExportHistoryDto {
  @ApiProperty({
    type: Any,
    required: false,
    description: 'Lỗi',
  })
  @IsOptional()
  errors?: string | object[];

  @ApiProperty({
    type: String,
    required: true,
    description: 'Trạng thái',
    enum: EFileExportStatus,
  })
  @IsNotEmpty({ message: 'VALIDATE.STATUS.IS_REQUIRED' })
  @IsEnum(EFileExportStatus, {
    message: 'VALIDATE.STATUS.INVALID_VALUE',
  })
  status: EFileExportStatus;
}
