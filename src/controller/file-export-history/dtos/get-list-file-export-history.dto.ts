import { ApiProperty, OmitType } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsEnum } from 'class-validator';
import { EFileImportType } from '../../../domain/config/enums/file-import.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { EFileExportType } from '../../../domain/config/enums/file-export-history.enum';

export class GetListFileExportHistoryDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EFileExportType],
    required: true,
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(EFileExportType, { each: true })
  importTypes: EFileExportType[];
}
