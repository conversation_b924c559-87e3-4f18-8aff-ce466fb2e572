import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsString } from 'class-validator';
import { EStatusActualSpending } from '../../../domain/config/enums/actual-spending.enum';
import { EBudgetType } from '../../../domain/config/enums/budget.enum';

export class GetActualSpendingListForSyncSapDto {
  @ApiPropertyOptional({
    type: String,
    description: 'Cost Center Code',
  })
  @IsOptional()
  @IsString()
  costCenterCode?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Cost Center Code',
  })
  @IsOptional()
  @IsString()
  companyCode?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Cost Center Code',
  })
  @IsOptional()
  @IsString()
  buCode?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Cost Center Code',
  })
  @IsOptional()
  @IsString()
  supplierCode?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Cost Center Code',
  })
  @IsOptional()
  @IsString()
  currencyCode?: string;

  @ApiProperty({
    type: [EStatusActualSpending],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EStatusActualSpending, { each: true })
  statuses?: EStatusActualSpending[];

  @ApiPropertyOptional({
    type: String,
    description: 'Budget Code Code',
  })
  @IsOptional()
  @IsString()
  budgetCodeCode?: string;

  @ApiProperty({
    type: EBudgetType,
    required: false,
  })
  @IsOptional()
  @IsEnum(EBudgetType)
  budgetType?: EBudgetType;
}
