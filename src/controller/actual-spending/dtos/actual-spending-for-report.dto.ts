import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { EStatusActualSpending } from '../../../domain/config/enums/actual-spending.enum';

export class ActualSpendingForReportDto {
  @ApiProperty({
    type: Date,
    description: 'Ngày hiệu lực ngân sách đang xét',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.POSTING_DATE_FROM.IS_REQUIRED' })
  @IsDateString()
  postingDateFrom: string;

  @ApiProperty({
    type: Date,
    description: '<PERSON><PERSON>y hệt hiệu lực ngân sách đang xét',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.POSTING_DATE_TO.IS_REQUIRED' })
  @IsDateString()
  postingDateTo: string;

  @ApiProperty({
    type: [EStatusActualSpending],
    required: true,
  })
  @IsArray()
  @IsEnum(EStatusActualSpending, { each: true })
  statuses: EStatusActualSpending[];

  @ApiProperty({
    type: String,
    description: 'Code của Budget Code ',
    required: false,
  })
  @IsOptional()
  @IsString()
  functionalArea?: string;

  @ApiProperty({
    type: String,
    description: 'Internal Order ',
    required: false,
  })
  @IsOptional()
  @IsString()
  internalOrder?: string;

  @ApiProperty({
    type: String,
    description: 'Budget Code Code',
    required: false,
  })
  @IsOptional()
  @IsString()
  budgetCodeCode?: string;

  sectorCodes?: string[];
  businessUnitCodes?: string[];
  businessOwnerCodes?: string[];
  companyCodes?: string[];
  budgetCodeCodes?: string[];
}
