import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsUUID } from 'class-validator';

export class ConfirmMultiActualSpendingDto {
  @ApiProperty({ type: [String], required: true, description: 'IDs' })
  @IsArray({ message: 'VALIDATE.IDS.IS_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.IDS.IS_GREATER_THAN_OR_EQUAL' })
  @IsUUID('4', { each: true, message: 'VALIDATE.IDS.MUST_BE_UUID' })
  ids: string[];
}
