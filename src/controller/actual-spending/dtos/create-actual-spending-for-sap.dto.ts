import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Matches,
  Min,
} from 'class-validator';
import {
  EActualType,
  EStatusActualSpending,
} from '../../../domain/config/enums/actual-spending.enum';

export class CreateActualSpendingForSapDto {
  @ApiProperty({ description: 'Mã thực chi sap' })
  @IsNotEmpty({ message: 'VALIDATE.SAP_ACTUAL_ID.IS_REQUIRED' })
  @IsInt({ message: 'VALIDATE.SAP_ACTUAL_ID.MUST_BE_INT' })
  sapActualId: number;

  // @ApiProperty({ description: 'Mã ngân sách' })
  // @IsNotEmpty({ message: 'VALIDATE.BUDGET_CODE.IS_REQUIRED' })
  // @IsString({ message: 'VALIDATE.BUDGET_CODE.MUST_BE_STRING' })
  // budgetCodeCode: string;

  @ApiProperty({ description: 'Mã công ty' })
  @IsNotEmpty({ message: 'VALIDATE.COMPANY_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.COMPANY_CODE.MUST_BE_STRING' })
  companyCode?: string;

  @ApiPropertyOptional({ description: 'Cost Center ID' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.COST_CENTER_CODE.MUST_BE_STRING' })
  costCenterCode?: string;

  @ApiPropertyOptional({ description: 'BU ID' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.BU_CODE.MUST_BE_STRING' })
  buCode?: string;

  // @ApiProperty({ description: 'Kỳ', example: '01/2024' })
  // @IsNotEmpty({ message: 'VALIDATE.PERIOD.IS_REQUIRED' })
  // @IsString({ message: 'VALIDATE.PERIOD.MUST_BE_STRING' })
  // @Matches(/^(0[1-9]|1[0-2])\/\d{4}$/, {
  //   message: 'Kỳ phải có định dạng MM/YYYY',
  // })
  // period: string;

  // @ApiPropertyOptional({ description: 'Năm tài chính', example: 2024 })
  // @IsOptional()
  // @IsInt({ message: 'VALIDATE.FISCAL_YEAR.MUST_BE_INT' })
  // fiscalYear?: number;

  @ApiPropertyOptional({ description: 'Internal Order', example: 'INT-12345' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.INTERNAL_ORDER.MUST_BE_STRING' })
  internalOrder?: string;

  @ApiPropertyOptional({
    description: 'Internal Order Name',
    example: 'Order Name',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.INTERNAL_ORDER_NAME.MUST_BE_STRING' })
  internalOrderName?: string;

  @ApiPropertyOptional({ description: 'Mã nhà cung cấp', example: 'SUP-001' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.SUPPLIER_CODE.MUST_BE_STRING' })
  supplierCode?: string;

  @ApiPropertyOptional({
    description: 'Tên nhà cung cấp',
    example: 'Nhà cung cấp A',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.SUPPLIER_NAME.MUST_BE_STRING' })
  supplierName?: string;

  @ApiPropertyOptional({ description: 'PO SAP ID' })
  @IsOptional()
  @IsInt({ message: 'VALIDATE.PO_SAP_ID.MUST_BE_INT' })
  poSapId?: number;

  @ApiPropertyOptional({ description: 'Ngày chứng từ', example: '2024-11-13' })
  @IsOptional()
  // @IsDateString()
  @IsString({ message: 'VALIDATE.DOC_DATE.MUST_BE_STRING' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'docDate phải có định dạng YYYY-MM-DD',
  })
  docDate?: string;

  @ApiPropertyOptional({ description: 'Ngày ghi có', example: '2024-11-14' })
  @IsNotEmpty({ message: 'VALIDATE.POSTING_DATE.IS_REQUIRED' })
  // @IsDateString()
  @IsString({ message: 'VALIDATE.POSTING_DATE.MUST_BE_STRING' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'postingDate phải có định dạng YYYY-MM-DD',
  })
  postingDate?: string;

  @ApiPropertyOptional({ description: 'Ngày nhập liệu', example: '2024-11-15' })
  @IsOptional()
  // @IsDateString()
  @IsString({ message: 'VALIDATE.ENTRY_DATE.MUST_BE_STRING' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'entryDate phải có định dạng YYYY-MM-DD',
  })
  entryDate?: string;

  @ApiPropertyOptional({
    description: 'Mã chứng từ thanh toán',
    example: 'PAY-123',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.PAYMENT_DOC.MUST_BE_STRING' })
  paymentDoc?: string;

  @ApiPropertyOptional({ description: 'Mã E-Invoice', example: 'INV-001' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.E_INVOICE_NUMBER.MUST_BE_STRING' })
  eInvoiceNumber?: string;

  @ApiPropertyOptional({ description: 'Mã chứng từ FI', example: 'FI-12345' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DOCUMENT_NUMBER.MUST_BE_STRING' })
  documentNumber?: string;

  @ApiPropertyOptional({
    description: 'Mã số chứng từ nhập kho',
    example: 'FI-12345',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.INVENTORY_DOC.MUST_BE_STRING' })
  inventoryDoc?: string;

  @ApiPropertyOptional({ description: 'Mã số Invoice', example: 'INV-002' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.INVOICE_NUMBER.MUST_BE_STRING' })
  invoiceNumber?: string;

  // @ApiProperty({ description: 'Loại giao dịch', enum: ETransactionType })
  // @IsNotEmpty({ message: 'VALIDATE.TRANSACTION_TYPE.IS_REQUIRED' })
  // @IsEnum(ETransactionType, {
  //   message: 'VALIDATE.TRANSACTION_TYPE.MUST_BE_ENUM_TRANSACTION_TYPE',
  // })
  // transactionType: ETransactionType;

  @ApiPropertyOptional({ description: 'GL Account', example: 'GL-001' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.GL_ACCOUNT.MUST_BE_STRING' })
  glAccount?: string;

  @ApiPropertyOptional({ description: 'Tax Code', example: 'TX-001' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.TAX_CODE.MUST_BE_STRING' })
  taxCode?: string;

  @ApiPropertyOptional({ description: 'Tax Rate', example: 10 })
  @IsOptional()
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'VALIDATE.TAX_RATE.MUST_BE_NUMBER' },
  )
  taxRate: number = 0;

  @ApiProperty({ description: 'Số tiền thanh toán', example: 1000.0 })
  @IsNotEmpty({ message: 'VALIDATE.DOC_AMOUNT.IS_REQUIRED' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'VALIDATE.DOC_AMOUNT.MUST_BE_NUMBER' },
  )
  docAmount: number;

  @ApiProperty({ description: 'Đơn vị tiền tệ ID' })
  @IsNotEmpty({ message: 'VALIDATE.CURRENCY_ID.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CURRENCY_CODE.MUST_BE_STRING' })
  currencyCode?: string;

  @ApiPropertyOptional({ description: 'Số tiền quy đổi', example: 1000.0 })
  @IsOptional()
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    {
      message: 'VALIDATE.LOCAL_CURRENCY_AMOUNT.MUST_BE_NUMBER',
    },
  )
  localCurrencyAmount?: number;

  @ApiPropertyOptional({
    description: 'Đơn vị tiền tệ quy đổi',
    example: 'VND',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.LOCAL_CURRENCY_CODE.MUST_BE_STRING',
  })
  localCurrencyCode?: string;

  @ApiProperty({ description: 'Tỷ giá quy đổi', example: 1.2 })
  @IsNotEmpty({ message: 'VALIDATE.EXCHANGE_RATE.IS_REQUIRED' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'VALIDATE.AMOUNT.MUST_BE_NUMBER' },
  )
  @Min(1, { message: 'VALIDATE.EXCHANGE_RATE.MUST_BE_GREATER_THAN_1' })
  exchangeRate: number;

  @ApiPropertyOptional({ description: 'Mã nơi nhận', example: 'RCV-001' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.RECEIVER_CODE.MUST_BE_STRING' })
  receiverCode?: string;

  @ApiPropertyOptional({ description: 'Tên nơi nhận', example: 'Receiver A' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.RECEIVER_NAME.MUST_BE_STRING' })
  receiverName?: string;

  @ApiPropertyOptional({ description: 'Profit Center', example: 'PC-001' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.PROFIT_CENTER.MUST_BE_STRING' })
  profitCenter?: string;

  @ApiPropertyOptional({
    description: 'Diễn giải Profit Center',
    example: 'Description A',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.PROFIT_CENTER_DESCRIPTION.MUST_BE_STRING' })
  profitCenterDescription?: string;

  @ApiPropertyOptional({
    description: 'Nhóm Profit Center',
    example: 'Group A',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.PROFIT_CENTER_GROUP.MUST_BE_STRING' })
  profitCenterGroup?: string;

  @ApiPropertyOptional({
    description: 'Diễn giải nhóm Profit Center',
    example: 'Group Description',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.PROFIT_CENTER_GROUP_DESCRIPTION.MUST_BE_STRING',
  })
  profitCenterGroupDescription?: string;

  @ApiProperty({
    description: 'Tình trạng',
    example: EStatusActualSpending.UNCONFIRMED,
    enum: EStatusActualSpending,
  })
  @IsOptional()
  @IsEnum(EStatusActualSpending, {
    message: 'VALIDATE.STATUS.MUST_BE_CONFIRMED_OR_UNCONFIRMED',
  })
  status: EStatusActualSpending = EStatusActualSpending.CONFIRMED;

  @ApiPropertyOptional({
    description: 'Document Type',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.DOCUMENT_TYPE.MUST_BE_STRING',
  })
  documentType?: string;

  // Invoice Business transaction
  @ApiPropertyOptional({
    description: 'Invocie Business Transaction',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.INVOICE_BUSINESS_TRANSACTION.MUST_BE_STRING',
  })
  invocieBusinessTransaction?: string;

  @ApiPropertyOptional({ description: 'Payment Date', example: '2024-11-13' })
  @IsOptional()
  // @IsDateString()
  @IsString({ message: 'VALIDATE.PAYMENT_DATE.MUST_BE_STRING' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'paymentDate phải có định dạng YYYY-MM-DD',
  })
  paymentDate?: string;

  @ApiPropertyOptional({
    description: 'Payment Business Transaction',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.PAYMENT_BUSINESS_TRANSACTION.MUST_BE_STRING',
  })
  paymentBusinessTransaction?: string;

  @ApiPropertyOptional({
    description: 'Payement Doc Type',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.PAYMENT_DOC_TYPE.MUST_BE_STRING',
  })
  payementDocType?: string;

  @ApiPropertyOptional({
    description: 'Inventory Doc Date',
    example: '2024-11-13',
  })
  @IsOptional()
  // @IsDateString()
  @IsString({ message: 'VALIDATE.INVENTORY_DOC_DATE.MUST_BE_STRING' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'inventoryDocDate phải có định dạng YYYY-MM-DD',
  })
  inventoryDocDate?: string;

  @ApiPropertyOptional({
    description: 'poItem',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.PO_ITEM.MUST_BE_STRING',
  })
  poItem?: string;

  @ApiPropertyOptional({
    description: 'poDate',
    example: '2024-11-13',
  })
  @IsOptional()
  // @IsDateString()
  @IsString({ message: 'VALIDATE.PO_DATE.MUST_BE_STRING' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'poDate phải có định dạng YYYY-MM-DD',
  })
  poDate?: string;

  @ApiPropertyOptional({
    description: 'internalOrderType',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.INTERNAL_ORDER_TYPE.MUST_BE_STRING',
  })
  internalOrderType?: string;

  @ApiPropertyOptional({
    description: 'costCenterName',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.COST_CENTER_NAME.MUST_BE_STRING',
  })
  costCenterName?: string;

  @ApiPropertyOptional({
    description: 'functionalArea',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.FUNCTIONAL_AREA.MUST_BE_STRING',
  })
  functionalArea?: string;

  @ApiPropertyOptional({
    description: 'functionalAreaName',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.FUNCTIONAL_AREA_NAME.MUST_BE_STRING',
  })
  functionalAreaName?: string;

  @ApiPropertyOptional({
    description: 'taxCodeName',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.TAX_CODE_NAME.MUST_BE_STRING',
  })
  taxCodeName?: string;

  @ApiPropertyOptional({ description: 'docPaymentAmount', example: 1000.0 })
  @IsOptional()
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    {
      message: 'VALIDATE.DOC_PAYMENT_AMOUNT.MUST_BE_NUMBER',
    },
  )
  docPaymentAmount?: number;

  @ApiPropertyOptional({
    description: 'localCurrencyPaymentAmount',
    example: 1000.0,
  })
  @IsOptional()
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    {
      message: 'VALIDATE.LOCAL_CURRENCY_PAYMENT_AMOUNT.MUST_BE_NUMBER',
    },
  )
  localCurrencyPaymentAmount?: number;

  @ApiPropertyOptional({
    description: 'debitCreditInd',
    example: 'debitCreditInd',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.DEBIT_CREDIT_IND.MUST_BE_STRING',
  })
  debitCreditInd?: string;

  @ApiPropertyOptional({
    description: 'accountType',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.ACCOUNT_TYPE.MUST_BE_STRING',
  })
  accountType?: string;

  @ApiPropertyOptional({
    description: 'description',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'note',
  })
  @IsOptional()
  @IsString({
    message: 'VALIDATE.NOTE.MUST_BE_STRING',
  })
  note?: string;

  @ApiPropertyOptional({ description: 'Mã Asset', example: 'AC-001' })
  @IsOptional()
  @IsString({ message: 'VALIDATE.ASSET_CODE.MUST_BE_STRING' })
  assetCode?: string;

  actualType?: EActualType;
}
