import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';
import { CreateActualSpendingForSapDto } from './create-actual-spending-for-sap.dto';
import { EActualType } from '../../../domain/config/enums/actual-spending.enum';

export class CreateActualSpendingListDto {
  @ApiProperty({
    type: [CreateActualSpendingForSapDto],
    description: 'Thực chi từ SAP',
    required: true,
  })
  @IsArray({ message: 'VALIDATE.INVESTMENTS.MUST_BE_ARRAY' })
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateActualSpendingForSapDto)
  actualSpendingList: CreateActualSpendingForSapDto[];

  @ApiProperty({
    enum: EActualType,
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.ACTUAL_TYPE.IS_REQUIRED' })
  @IsEnum(EActualType)
  actualType: EActualType;
}
