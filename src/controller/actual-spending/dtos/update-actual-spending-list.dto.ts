import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, ValidateNested } from 'class-validator';
import { UpdateActualSpendingDto } from './update-actual-spending.dto';

export class UpdateActualSpendingListDto {
  @ApiProperty({
    type: [UpdateActualSpendingDto],
    description: 'Thực chi từ SAP',
    required: true,
  })
  @IsArray({ message: 'VALIDATE.INVESTMENTS.MUST_BE_ARRAY' })
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => UpdateActualSpendingDto)
  actualSpendingList: UpdateActualSpendingDto[];
}
