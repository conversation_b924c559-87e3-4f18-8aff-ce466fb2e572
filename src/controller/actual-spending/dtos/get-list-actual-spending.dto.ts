import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID, Matches } from 'class-validator';
import { EStatusActualSpending } from '../../../domain/config/enums/actual-spending.enum';
import { EBudgetType } from '../../../domain/config/enums/budget.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { ETransactionType } from '../../../domain/config/enums/transaction-type.enum';

export class GetActualSpendingListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiPropertyOptional({
    type: String,
    example: '2023-12',
    description: 'Kỳ (định dạng YYYY-MM)',
  })
  @IsOptional()
  @Matches(/^\d{4}-(0[1-9]|1[0-2])$/, {
    message: 'VALIDATE.TO.INVALID_FORMAT',
  })
  period: string;

  @ApiProperty({
    type: [EStatusActualSpending],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EStatusActualSpending, { each: true })
  statuses?: EStatusActualSpending[];

  @ApiProperty({
    type: [ETransactionType],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ETransactionType, { each: true })
  transactionTypes?: ETransactionType[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  sectorIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessOwnerIds?: string[];

  // @ApiProperty({
  //   type: [String],
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray()
  // @IsUUID(4, { each: true })
  // currencyIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  budgetCodeIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  costCenterIds?: string[];

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID(4)
  currencyId?: string;

  budgetCodeType?: EBudgetType;

  sectorCodes?: string[];
  businessUnitCodes?: string[];
  businessOwnerCodes?: string[];
  companyCodes?: string[];
  budgetCodeCodes?: string[];
}
