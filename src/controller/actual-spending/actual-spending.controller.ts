import {
  Body,
  Controller,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  Request,
  Response,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { EBudgetType } from '../../domain/config/enums/budget.enum';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ActualSpendingUsecases } from '../../usecases/actual-spending.usecases';
import { EActualSpendingPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { ActualSpendingForReportDto } from './dtos/actual-spending-for-report.dto';
import { ConfirmMultiActualSpendingDto } from './dtos/confirm-multi-actual-spending.dto';
import { CreateActualSpendingListDto } from './dtos/create-actual-spending-list.dto';
import { GetActualSpendingListDto } from './dtos/get-list-actual-spending.dto';

@Controller('/actual-spending')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Actual Spending')
export class ActualSpendingController {
  constructor(
    private readonly actualSpendingUsecases: ActualSpendingUsecases,
  ) {}

  @Post('/sap-create')
  @UseGuards(NewPermissionGuard([EActualSpendingPermission.CREATE]))
  async create(
    @Body() data: CreateActualSpendingListDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
    @Response() res,
  ) {
    const now = Date.now();
    const result =
      await this.actualSpendingUsecases.createActualSpendingListByCode(
        data,
        jwtPayload,
        req.headers['authorization'],
      );

    const errors =
      result?.filter((item) => item.errorStatus === 'FAILED') || [];

    const success =
      result?.filter((item) => item.errorStatus === 'SUCCESSFUL') || [];

    res.locals['data'] = result;
    return res
      .status(
        errors.length && success.length
          ? 207
          : !errors.length
            ? HttpStatus.OK
            : HttpStatus.BAD_REQUEST,
      )
      .send({ data: result, message: null, duration: `${Date.now() - now}ms` });
  }

  @Get('/list-capex')
  @UseGuards(NewPermissionGuard([EActualSpendingPermission.VIEW]))
  async getListCapex(
    @Query() conditions: GetActualSpendingListDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.actualSpendingUsecases.getListActualSpending(
      { ...conditions, budgetCodeType: EBudgetType.CAPEX },
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/list-opex')
  @UseGuards(NewPermissionGuard([EActualSpendingPermission.VIEW]))
  async getListOpex(
    @Query() conditions: GetActualSpendingListDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.actualSpendingUsecases.getListActualSpending(
      { ...conditions, budgetCodeType: EBudgetType.OPEX },
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EActualSpendingPermission.VIEW]))
  async getDetail(
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.actualSpendingUsecases.getDetailActualSpending(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch('/confirm-multi')
  @UseGuards(NewPermissionGuard([EActualSpendingPermission.CONFIRM]))
  async confirmMulti(@Body() data: ConfirmMultiActualSpendingDto) {
    return await this.actualSpendingUsecases.confirmMultiActualSpending(data);
  }

  @Post('/import')
  @UseGuards(NewPermissionGuard([EActualSpendingPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async import(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.actualSpendingUsecases.importActualSpending(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-capex-actual')
  @UseGuards(NewPermissionGuard([EActualSpendingPermission.EXPORT]))
  async exportCapexActual(
    @Query() param: GetActualSpendingListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    param.budgetCodeType = EBudgetType.CAPEX;
    const result = await this.actualSpendingUsecases.exportActual(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Get('/export-opex-actual')
  @UseGuards(NewPermissionGuard([EActualSpendingPermission.EXPORT]))
  async exportOpexActual(
    @Query() param: GetActualSpendingListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    param.budgetCodeType = EBudgetType.OPEX;
    const result = await this.actualSpendingUsecases.exportActual(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Get('/actual-spending-for-report')
  async actualSpendingForReport(
    @Query() conditions: ActualSpendingForReportDto,
  ) {
    return await this.actualSpendingUsecases.actualSpendingForReport(
      conditions,
    );
  }
}
