import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  Request,
  Response,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { ListByCodesDto } from '../../domain/dtos/base-dto-by-codes.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { CurrencyUnitUsecases } from '../../usecases/currency-unit.usecases';
import { ECurrencyUnitPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateCurrencyUnitDto } from './dtos/create-currency-unit.dto';
import { DeleteCurrencyUnitDto } from './dtos/delete-currency-unit.dto';
import { GetCurrencyUnitListDto } from './dtos/get-currency-unit-list.dto';
import { GetDetailCurrencyUnitDto } from './dtos/get-detail-currency-unit.dto';
import { UpdateCurrencyUnitDto } from './dtos/update-currency-unit.dto';
import { GetCurrencyListByIdsDto } from './dtos/get-currency-unit-list-by-ids.dto';

@Controller('/currency-unit')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Currency')
export class CurencyUnitController {
  constructor(private readonly currencyUnitUsecases: CurrencyUnitUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([ECurrencyUnitPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(
    @Body() data: CreateCurrencyUnitDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.currencyUnitUsecases.createCurrencyUnit(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([ECurrencyUnitPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailCurrencyUnitDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.currencyUnitUsecases.getCurrencyUnitDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([ECurrencyUnitPermission.VIEW]))
  async getList(
    @Query() param: GetCurrencyUnitListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.currencyUnitUsecases.getCurrencyUnits(param, jwtPayload);
  }

  @Delete(':id/delete')
  @UseGuards(NewPermissionGuard([ECurrencyUnitPermission.DELETE]))
  async delete(
    @Param() param: DeleteCurrencyUnitDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.currencyUnitUsecases.deleteCurrencyUnit(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Put(':id/update')
  @UseGuards(NewPermissionGuard([ECurrencyUnitPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param() param: GetUuidDto,
    @Body() data: UpdateCurrencyUnitDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.currencyUnitUsecases.updateCurrencyUnit(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/list-by-codes')
  async listByCodes(
    @Body() data: ListByCodesDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.currencyUnitUsecases.listByCodes(
      data.codes || [],
      jwtPayload,
    );
  }

  @Get('/export-currency-unit')
  @UseGuards(NewPermissionGuard([ECurrencyUnitPermission.EXPORT]))
  async exportCurrencyUnit(
    @Query() param: GetCurrencyUnitListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.currencyUnitUsecases.exportCurrencyUnit(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-currency-exchange')
  @UseGuards(NewPermissionGuard([ECurrencyUnitPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importCurrencyExchange(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.currencyUnitUsecases.importCurrencyExchange(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  ///For validate detail PR PO
  @Post('/list-by-ids')
  async getListByIds(
    @Body() param: GetCurrencyListByIdsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.currencyUnitUsecases.getListByIds(param, jwtPayload);
  }
}
