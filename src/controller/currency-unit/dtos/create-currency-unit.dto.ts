import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ECurrencyUnitStatus } from '../../../domain/config/enums/currency-unit.enum';
import { CreateCurrencyUnitExchangeDto } from '../../currency-unit-exchange/dtos/create-currency-unit-exchange.dto';

export class CreateCurrencyUnitDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Tiền tệ',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON>ô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã tiền tệ',
  })
  @IsNotEmpty({ message: 'VALIDATE.CURRENCY_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CURRENCY_CODE.MUST_BE_STRING' })
  currencyCode: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: ECurrencyUnitStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ECurrencyUnitStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: ECurrencyUnitStatus;

  @ApiProperty({
    type: [CreateCurrencyUnitExchangeDto],
    description: 'Quy đổi tiền tệ',
    required: true,
  })
  @IsArray({ message: 'VALIDATE.CURRENCY_UNIT_EXCHANGE.MUST_BE_ARRAY' })
  @ArrayMinSize(0)
  @ValidateNested({ each: true })
  @Type(() => CreateCurrencyUnitExchangeDto)
  currencyUnitExchanges: CreateCurrencyUnitExchangeDto[];
}
