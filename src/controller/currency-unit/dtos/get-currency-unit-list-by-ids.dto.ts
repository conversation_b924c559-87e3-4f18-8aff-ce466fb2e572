import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsOptional, IsUUID } from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetCurrencyListByIdsDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];
}
