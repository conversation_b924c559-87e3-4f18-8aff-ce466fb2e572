import { ArrayMinSize, IsArray, ValidateNested } from 'class-validator';
import { UpdateCurrencyUnitExchangeDto } from '../../currency-unit-exchange/dtos/update-currency-unit-exchange.dto';
import { CreateCurrencyUnitDto } from './create-currency-unit.dto';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCurrencyUnitDto extends CreateCurrencyUnitDto {
  @ApiProperty({
    type: [UpdateCurrencyUnitExchangeDto],
    description: 'Quy đổi tiền tệ',
    required: true,
  })
  @IsArray({ message: 'VALIDATE.CURRENCY_UNIT_EXCHANGE.MUST_BE_ARRAY' })
  @ArrayMinSize(0)
  @ValidateNested({ each: true })
  @Type(() => UpdateCurrencyUnitExchangeDto)
  currencyUnitExchanges: UpdateCurrencyUnitExchangeDto[];
}
