import { ApiProperty, OmitType } from '@nestjs/swagger';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { EBudgetCreateType } from '../../../domain/config/enums/budget.enum';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { ECurrencyUnitStatus } from '../../../domain/config/enums/currency-unit.enum';

export class GetCurrencyUnitListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [ECurrencyUnitStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ECurrencyUnitStatus, { each: true })
  statuses?: ECurrencyUnitStatus[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];

  codes?: string[];
}
