import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  Request,
  Response,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { DepartmentUsecases } from '../../usecases/department.usecases';
import { EDepartmentPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateDepartmentDto } from './dtos/create-department.dto';
import { DeleteDepartmentDto } from './dtos/delete-department.dto';
import { GetDepartmentListDto } from './dtos/get-department-list.dto';
import { GetDetailDepartmentDto } from './dtos/get-detail-department.dto';
import { UpdateDepartmentDto } from './dtos/update-department.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('/department')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Department')
export class DepartmentController {
  constructor(private readonly departmentUsecases: DepartmentUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EDepartmentPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(@Body() data: CreateDepartmentDto, @Request() req) {
    return await this.departmentUsecases.createDepartment(
      data,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EDepartmentPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailDepartmentDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.departmentUsecases.getDetailDepartment(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EDepartmentPermission.VIEW]))
  async getList(
    @Query() param: GetDepartmentListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.departmentUsecases.getDepartments(param, jwtPayload);
  }

  @Delete(':id/delete')
  @UseGuards(NewPermissionGuard([EDepartmentPermission.DELETE]))
  async delete(
    @Param() param: DeleteDepartmentDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    await this.departmentUsecases.deleteDepartment(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
    return { message: 'Successfully!!!' };
  }

  @Put(':id/update')
  @UseGuards(NewPermissionGuard([EDepartmentPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param('id') departmentId: string,
    @Body() data: UpdateDepartmentDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.departmentUsecases.updateDepartment(
      data,
      departmentId,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-department')
  @UseGuards(NewPermissionGuard([EDepartmentPermission.EXPORT]))
  async exportDepartment(
    @Query() param: GetDepartmentListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.departmentUsecases.exportDepartment(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-department')
  @UseGuards(NewPermissionGuard([EDepartmentPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importDepartment(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.departmentUsecases.importDepartment(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
