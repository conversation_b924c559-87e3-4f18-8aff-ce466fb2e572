import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { EDepartmentStatus } from '../../../domain/config/enums/department.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetDepartmentListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EDepartmentStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EDepartmentStatus, { each: true })
  statuses?: EDepartmentStatus[];

  codes?: string[];
  ids?: string[];
}
