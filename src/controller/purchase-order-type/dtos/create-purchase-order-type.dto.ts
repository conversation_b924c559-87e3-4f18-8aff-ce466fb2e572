import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import {
  EPurchaseOrderStatus,
  EPurchaseOrderTypeForm,
} from '../../../domain/config/enums/purchasing.enum';

export class CreatePurchaseOrderTypeDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON><PERSON> tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EPurchaseOrderStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EPurchaseOrderStatus, {
    message: 'VALIDATE.STATUS.INVALID_VALUE',
  })
  status?: EPurchaseOrderStatus;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Hình thức',
    default: EPurchaseOrderTypeForm.NORMAL,
  })
  @IsOptional()
  @IsEnum(EPurchaseOrderTypeForm, {
    message: 'VALIDATE.FORM.INVALID_VALUE',
  })
  form?: EPurchaseOrderTypeForm;
}
