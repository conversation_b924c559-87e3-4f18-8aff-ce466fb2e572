import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import {
  EPurchaseOrderStatus,
  EPurchaseOrderTypeForm,
} from '../../../domain/config/enums/purchasing.enum';

export class GetPurchaseOrderTypeListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EPurchaseOrderStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPurchaseOrderStatus, { each: true })
  statuses?: EPurchaseOrderStatus[];

  @ApiProperty({
    type: [EPurchaseOrderTypeForm],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPurchaseOrderTypeForm, { each: true })
  forms?: EPurchaseOrderTypeForm[];

  codes?: string[];
}
