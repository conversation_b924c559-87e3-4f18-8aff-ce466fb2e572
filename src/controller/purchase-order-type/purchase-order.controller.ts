import {
  Body,
  Controller,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Get,
  Query,
  Delete,
} from '@nestjs/common';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { CreatePurchaseOrderTypeDto } from './dtos/create-purchase-order-type.dto';
import { UpdatePurchaseOrderTypeDto } from './dtos/update-purchase-order-type.dto';
import { GetDetailPurchaseOrderTypeDto } from './dtos/get-detail-purchase-order-type.dto';
import { GetPurchaseOrderTypeListDto } from './dtos/get-purchase-order-type-list.dto';
import { DeletePurchaseOrderTypeDto } from './dtos/delete-purchase-order-type.dto';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EPurchaseOrderTypePermission } from '../../utils/constants/permission.enum';
import { PurchaseOrderTypeUsecases } from '../../usecases/purchase-order-type.usecases';

@Controller('/purchase-order-type')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Purchase Order Type')
export class PurchaseOrderTypeController {
  constructor(
    private readonly _purchaseOrderTypeUsecases: PurchaseOrderTypeUsecases,
  ) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EPurchaseOrderTypePermission.CREATE]))
  async create(
    @Body() data: CreatePurchaseOrderTypeDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this._purchaseOrderTypeUsecases.createPurchaseType(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EPurchaseOrderTypePermission.EDIT]))
  async update(
    @Body() data: UpdatePurchaseOrderTypeDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this._purchaseOrderTypeUsecases.updatePurchaseType(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EPurchaseOrderTypePermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailPurchaseOrderTypeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this._purchaseOrderTypeUsecases.getPurchaseOrderTypeDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EPurchaseOrderTypePermission.VIEW]))
  async getList(
    @Query() param: GetPurchaseOrderTypeListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this._purchaseOrderTypeUsecases.getPurchaseOrderTypes(
      param,
      jwtPayload,
    );
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EPurchaseOrderTypePermission.DELETE]))
  async delete(
    @Param() param: DeletePurchaseOrderTypeDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this._purchaseOrderTypeUsecases.deletePurchaseOrderType(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
