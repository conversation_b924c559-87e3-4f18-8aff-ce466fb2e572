import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class DeleteOnwerDeviceTokenDto {
  platform?: EPlatform;
  ownerId?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Device token',
  })
  @IsNotEmpty()
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  deviceToken: string;
}
