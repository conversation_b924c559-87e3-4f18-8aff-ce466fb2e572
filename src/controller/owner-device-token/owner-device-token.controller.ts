import {
  Body,
  Controller,
  Delete,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateOnwerDeviceTokenDto } from './dtos/create-owner-device-token.dto';
import { DeleteOnwerDeviceTokenDto } from './dtos/delete-owner-device-token.dto';
import { OwnerDeviceTokenUsecases } from '../../usecases/owner-device-token.usecases';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { EPlatform } from '../../domain/config/enums/platform.enum';

@Controller('/owner-device-token')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Owner Device Token')
export class OwnerDeviceTokenController {
  constructor(
    private readonly ownerDeviceTokenUsecases: OwnerDeviceTokenUsecases,
  ) {}

  @Post('/create')
  async create(
    @Body() data: CreateOnwerDeviceTokenDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    data.ownerId = jwtPayload?.staffId || '';
    data.platform = jwtPayload?.platform || EPlatform.E_PURCHASE;
    return await this.ownerDeviceTokenUsecases.createOwnerDeviceToken(data);
  }

  @Delete('/delete')
  async delete(
    @Body() data: DeleteOnwerDeviceTokenDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    data.ownerId = jwtPayload?.staffId || '';
    data.platform = jwtPayload?.platform || EPlatform.E_PURCHASE;
    return await this.ownerDeviceTokenUsecases.deleteOnwerDeviceToken(data);
  }
}
