import {
  Controller,
  Get,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ApiLogUsecases } from '../../usecases/api-log.usecase';
import { EAdmin } from '../../utils/constants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { GetListApiLogDto } from './dtos/get-list-api-log.dto';

@Controller('/logs')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Api Log')
export class ApiLogController {
  constructor(private readonly apiLogUsecases: ApiLogUsecases) {}

  @Get()
  @UseGuards(NewPermissionGuard([EAdmin.ADMIN, EAdmin.ADMIN_SUPER]))
  async getApiLogs(@Query() conditions: GetListApiLogDto) {
    return await this.apiLogUsecases.getApiLogs(conditions);
  }
}
