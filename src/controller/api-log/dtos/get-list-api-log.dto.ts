import { ApiPropertyOptional, OmitType, PartialType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsIn, IsInt, IsOptional, IsString } from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetListApiLogDto extends PartialType(
  OmitType(PaginationDto, ['searchString', 'isEmptyResult']),
) {
  @ApiPropertyOptional({
    type: String,
    description: 'Controler muốn tìm kiếm',
  })
  @IsOptional()
  @IsString()
  controller?: string;
  @ApiPropertyOptional({ type: String, description: 'Path muốn tìm kiếm' })
  @IsOptional()
  @IsString()
  route?: string;
  @ApiPropertyOptional({ type: String, description: 'Method muốn tìm kiếm' })
  @IsOptional()
  @IsString()
  method?: string;
  @ApiPropertyOptional({
    type: String,
    description: 'Http status muốn tìm kiếm',
  })
  @IsOptional()
  @IsInt()
  statusCode?: number;

  @ApiPropertyOptional({
    type: Number,
    default: 1,
    example: 1,
    description: 'Is Success',
  })
  @IsOptional()
  @Transform(({ value }) => +value)
  @IsIn([0, 1], { message: 'VALIDATE.IS_SUCCESS.INVALID_VALUE' })
  isSuccess?: number;
}
