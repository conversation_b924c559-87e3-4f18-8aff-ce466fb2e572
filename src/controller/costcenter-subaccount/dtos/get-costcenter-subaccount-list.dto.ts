import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { ECostcenterSubaccountStatus } from '../../../domain/config/enums/costcenter-subaccount.enum';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsOptional,
  IsUUID,
} from 'class-validator';

export class GetCostcenterSubaccountListDto extends PaginationDto {
  @ApiProperty({
    type: [ECostcenterSubaccountStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ECostcenterSubaccountStatus, { each: true })
  statuses?: ECostcenterSubaccountStatus[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  sectorIds?: string[];

  // @ApiProperty({
  //   type: [String],
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray()
  // @IsUUID(4, { each: true })
  // companyIds?: string[];

  // @ApiProperty({
  //   type: [String],
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray()
  // @IsUUID(4, { each: true })
  // businessUnitIds?: string[];

  // @ApiProperty({
  //   type: [String],
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray()
  // @IsUUID(4, { each: true })
  // departmentIds?: string[];

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31',
    description: 'Ngày bắt đầu (khởi tạo) đối tượng cần tìm kiếm',
  })
  @IsDateString()
  @IsOptional()
  createdAt?: string;

  sectorCodes?: string[];
  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
  functionUnitCodes?: string[];
}
