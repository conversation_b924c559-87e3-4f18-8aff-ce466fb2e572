import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ECostcenterSubaccountStatus } from '../../../domain/config/enums/costcenter-subaccount.enum';
import { FunctionUnitModel } from '../../../domain/model/function-unit.model';

export class CreateCostcenterSubaccountDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of cost center / sub account',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'ID',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON>ô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: ECostcenterSubaccountStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ECostcenterSubaccountStatus, {
    message: 'VALIDATE.STATUS.INVALID_VALUE',
  })
  status?: ECostcenterSubaccountStatus;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Mảng',
  })
  @IsOptional()
  // @IsNotEmpty({ message: 'VALIDATE.SECTOR_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.SECTOR_ID.MUST_BE_UUID' })
  sectorId: string;

  // @ApiProperty({
  //   type: String,
  //   required: true,
  //   description: 'Công ty',
  // })
  // @IsNotEmpty({ message: 'VALIDATE.COMPANY_ID.IS_REQUIRED' })
  // @IsUUID('4', { message: 'VALIDATE.COMPANY_ID.MUST_BE_UUID' })
  // companyId: string;

  // @ApiProperty({
  //   type: String,
  //   required: true,
  //   description: 'Đơn vị kinh doanh',
  // })
  // @IsNotEmpty({ message: 'VALIDATE.BUSINESS_UNIT_ID.IS_REQUIRED' })
  // @IsUUID('4', { message: 'VALIDATE.BUSINESS_UNIT_ID.MUST_BE_UUID' })
  // businessUnitId: string;

  // @ApiProperty({
  //   type: String,
  //   required: true,
  //   description: 'Phòng ban',
  // })
  // @IsNotEmpty({ message: 'VALIDATE.DEPARTMENT_ID.IS_REQUIRED' })
  // @IsUUID('4', { message: 'VALIDATE.DEPARTMENT_ID.MUST_BE_UUID' })
  // departmentId: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE.MUST_BE_STRING' })
  note1?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE.MUST_BE_STRING' })
  note2?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE.MUST_BE_STRING' })
  note3?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE.MUST_BE_STRING' })
  note4?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE.MUST_BE_STRING' })
  note5?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE.MUST_BE_STRING' })
  note6?: string;

  @ApiProperty({
    type: Date,
    description: 'Thời gian có hiệu lực',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_START_DATE.IS_REQUIRED' })
  // @Transform(({ value }) => new Date(value).toDateString())
  @IsDateString()
  effectiveStartDate: Date;

  @ApiProperty({
    type: Date,
    description: 'Thời gian hết hiệu lực',
    required: false,
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_END_DATE.IS_REQUIRED' })
  // @Transform(({ value }) => new Date(value).toDateString())
  @IsDateString()
  effectiveEndDate?: Date;

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Khối chức năng',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.FUNCTION_UNIT_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.FUNCTION_UNIT_ID.MUST_BE_UUID',
    each: true,
  })
  functionUnitIds?: string[];

  functionUnits?: FunctionUnitModel[];

  createdAt?: string;
  updatedAt?: string;
}
