import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  Request,
  Response,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';

import { FileInterceptor } from '@nestjs/platform-express';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { CostcenterSubaccountUsecases } from '../../usecases/costcenter-subaccount.usecases';
import { ECostcenterSubaccountPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateCostcenterSubaccountDto } from './dtos/create-costcenter-subaccount.dto';
import { DeleteCostcenterSubaccountDto } from './dtos/delete-costcenter-subaccount.dto';
import { GetCostCenterListByIdsDto } from './dtos/get-cost-center-list-by-ids.dto';
import { GetCostcenterSubaccountListDto } from './dtos/get-costcenter-subaccount-list.dto';
import { GetDetailCostcenterSubaccountDto } from './dtos/get-detail-costcenter-subaccount.dto';
import { UpdateCostcenterSubaccountDto } from './dtos/update-costcenter-subaccount.dto';

@Controller('/costcenter-subaccount')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Cost center/Sub account')
export class CostcenterSubaccountController {
  constructor(
    private readonly costcenterSubaccountUsecases: CostcenterSubaccountUsecases,
  ) {}

  @Post('/create')
  @UseInterceptors(TransactionInterceptor)
  @UseGuards(NewPermissionGuard([ECostcenterSubaccountPermission.CREATE]))
  async create(
    @Body() data: CreateCostcenterSubaccountDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.costcenterSubaccountUsecases.createCostcenterSubaccount(
      data,
      jwtPayload,
      false,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  @UseInterceptors(TransactionInterceptor)
  // @UseGuards(NewPermissionGuard([ECostcenterSubaccountPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailCostcenterSubaccountDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.costcenterSubaccountUsecases.getCostcenterSubaccountDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([ECostcenterSubaccountPermission.VIEW]))
  async getList(
    @Query() param: GetCostcenterSubaccountListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.costcenterSubaccountUsecases.getCostcenterSubaccounts(
      param,
      jwtPayload,
    );
  }

  @Delete(':id/delete')
  @UseInterceptors(TransactionInterceptor)
  @UseGuards(NewPermissionGuard([ECostcenterSubaccountPermission.DELETE]))
  async delete(
    @Param() param: DeleteCostcenterSubaccountDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.costcenterSubaccountUsecases.deleteCostcenterSubaccount(
      param,
      jwtPayload,
    );
  }

  @Put(':id/update')
  @UseInterceptors(TransactionInterceptor)
  @UseGuards(NewPermissionGuard([ECostcenterSubaccountPermission.EDIT]))
  @UseGuards(NewAuthGuard)
  async update(
    @Param() param: GetUuidDto,
    @Body() data: UpdateCostcenterSubaccountDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.costcenterSubaccountUsecases.updateCostcenterSubaccount(
      param.id,
      data,
      jwtPayload,
    );
  }

  @Get(':id/history')
  @UseGuards(NewPermissionGuard([ECostcenterSubaccountPermission.VIEW]))
  async getHistory(@Param('id') costcenterSubaccountId: string) {
    return await this.costcenterSubaccountUsecases.getHistory(
      costcenterSubaccountId,
    );
  }

  @Post('/import-costcenter-subaccount')
  @UseGuards(NewPermissionGuard([ECostcenterSubaccountPermission.IMPORT]))
  @UseInterceptors(TransactionInterceptor)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importCostcenterSubaccount(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.costcenterSubaccountUsecases.importCostcenterSubaccount(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-cost-center')
  @UseGuards(NewPermissionGuard([ECostcenterSubaccountPermission.EXPORT]))
  async exportCostCenter(
    @Query() param: GetCostcenterSubaccountListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.costcenterSubaccountUsecases.exportCostCenter(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  ///For validate detail PR PO
  @Post('/list-by-ids')
  async getListByIds(
    @Body() param: GetCostCenterListByIdsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.costcenterSubaccountUsecases.getListByIds(
      param,
      jwtPayload,
    );
  }
}
