import {
  Body,
  Controller,
  Post,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { EFileImportType } from '../../domain/config/enums/file-import.enum';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ImportUsecases } from '../../usecases/import.usecases';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { ImportActualSpendingDto } from './dtos/import-actual-spending.dto';
import { ImportBudgetCodeDto } from './dtos/import-budget-code.dto';
import { ImportBusinessOwnerDto } from './dtos/import-business-owner.dto';
import { ImportBusinessUnitDto } from './dtos/import-business-unit.dto';
import { ImportCapexDto } from './dtos/import-capex.dto';
import { ImportCompanyDto } from './dtos/import-company.dto';
import { ImportCostDto } from './dtos/import-cost.dto';
import { ImportCostcenterSubaccountDto } from './dtos/import-costcenter-subaccount.dto';
import { ImportCurrencyUnitExchangeDto } from './dtos/import-currency-exchange.dto';
import { ImportDepartmentDto } from './dtos/import-department.dto';
import { ImportInventoryStandardDto } from './dtos/import-inventory-standard.dto';
import { ImportMaterialGroupDto } from './dtos/import-material-group.dto';
import { ImportMaterialTypeDto } from './dtos/import-material-type.dto';
import { ImportMaterialDto } from './dtos/import-material.dto';
import { ImportOpexDto } from './dtos/import-opex.dto';
import { ImportSupplierDto } from './dtos/import-supplier.dto';

@Controller('/import')
@UseInterceptors(TransformationInterceptor, TransactionInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@ApiTags('Import')
export class ImportController {
  constructor(private readonly importUsecases: ImportUsecases) {}

  @Post('/import-opex')
  @UseGuards(NewAuthGuard)
  async importOpex(@Body() body: ImportOpexDto) {
    return await this.importUsecases.import(body, EFileImportType.OPEX);
  }

  @Post('/import-capex')
  @UseGuards(NewAuthGuard)
  async importCapex(@Body() body: ImportCapexDto) {
    return await this.importUsecases.import(body, EFileImportType.CAPEX);
  }

  @Post('/import-budget-code')
  @UseGuards(NewAuthGuard)
  async importBudgetCode(
    @Body() body: ImportBudgetCodeDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.BUDGET_CODE,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-costcenter-subaccount')
  @UseGuards(NewAuthGuard)
  async importCostcenterSubaccount(
    @Body() body: ImportCostcenterSubaccountDto,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.COST_CENTER_SUB_ACCOUNT,
    );
  }

  @Post('/import-supplier')
  @UseGuards(NewAuthGuard)
  async importSupplier(
    @Body() body: ImportSupplierDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.SUPPLIER,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-material')
  @UseGuards(NewAuthGuard)
  async importMaterial(
    @Body() body: ImportMaterialDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.MATERIAL,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-cost')
  @UseGuards(NewAuthGuard)
  async importCost(
    @Body() body: ImportCostDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.COST,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-currency-exchange')
  @UseGuards(NewAuthGuard)
  async importCurrencyUnitExchange(
    @Body() body: ImportCurrencyUnitExchangeDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.EXCHANGE_RATE,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-inventory-standard')
  @UseGuards(NewAuthGuard)
  async importInventoryStandard(
    @Body() body: ImportInventoryStandardDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.INVENTORY_STANDARD,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-business-owner')
  @UseGuards(NewAuthGuard)
  async importBusinessOwner(
    @Body() body: ImportBusinessOwnerDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.BUSINESS_OWNER,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-material-group')
  @UseGuards(NewAuthGuard)
  async importMaterialGroup(
    @Body() body: ImportMaterialGroupDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.MATERIAL_GROUP,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-material-type')
  @UseGuards(NewAuthGuard)
  async importMaterialType(
    @Body() body: ImportMaterialTypeDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.MATERIAL_TYPE,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-department')
  @UseGuards(NewAuthGuard)
  async importDepartment(
    @Body() body: ImportDepartmentDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.DEPARTMENT,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-company')
  @UseGuards(NewAuthGuard)
  async importCompany(
    @Body() body: ImportCompanyDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.COMPANY,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-business-unit')
  @UseGuards(NewAuthGuard)
  async importBusinessUnit(
    @Body() body: ImportBusinessUnitDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.BUSINESS_UNIT,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-actual-spending')
  @UseGuards(NewAuthGuard)
  async importActualSpending(
    @Body() body: ImportActualSpendingDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.importUsecases.import(
      body,
      EFileImportType.ACTUAL_SPENDING,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
