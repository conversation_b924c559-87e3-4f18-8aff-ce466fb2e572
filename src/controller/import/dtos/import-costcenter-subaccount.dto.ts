import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateCostcenterSubaccountDto } from '../../costcenter-subaccount/dtos/create-costcenter-subaccount.dto';
import { ImportBaseDto } from './import-base.dto';
import { UpdateCostcenterSubaccountDto } from '../../costcenter-subaccount/dtos/update-costcenter-subaccount.dto';

export class ImportCostcenterSubaccountDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateCostcenterSubaccountDto],
  })
  @IsArray()
  @Type(() => CreateCostcenterSubaccountDto)
  dataCostcenterSubaccounts: CreateCostcenterSubaccountDto[];

  @ApiProperty({
    type: [UpdateCostcenterSubaccountDto],
  })
  @IsArray()
  @Type(() => UpdateCostcenterSubaccountDto)
  dataUpdateCostcenterSubaccounts: UpdateCostcenterSubaccountDto[];
}
