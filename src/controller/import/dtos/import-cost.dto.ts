import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateCostDto } from '../../cost/dtos/create-cost.dto';
import { UpdateCostDto } from '../../cost/dtos/update-cost.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportCostDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateCostDto],
  })
  @IsArray()
  @Type(() => CreateCostDto)
  dataCosts: CreateCostDto[];

  @ApiProperty({
    type: [UpdateCostDto],
  })
  @IsArray()
  @Type(() => UpdateCostDto)
  dataUpdateCosts: UpdateCostDto[];
}
