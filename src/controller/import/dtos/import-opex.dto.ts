import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateBudgetOpexDto } from '../../budget/dtos/create-budget-opex.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportOpexDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateBudgetOpexDto],
  })
  @IsArray()
  @Type(() => CreateBudgetOpexDto)
  dataOpexes: CreateBudgetOpexDto[];
}
