import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateBusinessUnitDto } from '../../business-unit/dtos/create-business-unit.dto';
import { UpdateBusinessUnitDto } from '../../business-unit/dtos/update-business-unit.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportBusinessUnitDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateBusinessUnitDto],
  })
  @IsArray()
  @Type(() => CreateBusinessUnitDto)
  dataBusinessUnits: CreateBusinessUnitDto[];

  @ApiProperty({
    type: [UpdateBusinessUnitDto],
  })
  @IsArray()
  @Type(() => UpdateBusinessUnitDto)
  dataUpdateBusinessUnits: UpdateBusinessUnitDto[];
}
