import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, ValidateNested } from 'class-validator';
import { CreateActualSpendingDto } from '../../actual-spending/dtos/create-actual-spending.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportActualSpendingDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateActualSpendingDto],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateActualSpendingDto)
  createActualSpendingDtos: CreateActualSpendingDto[];
}
