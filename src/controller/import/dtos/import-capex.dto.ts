import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateBudgetCapexDto } from '../../budget/dtos/create-budget-capex.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportCapexDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateBudgetCapexDto],
  })
  @IsArray()
  @Type(() => CreateBudgetCapexDto)
  dataCapexes: CreateBudgetCapexDto[];
}
