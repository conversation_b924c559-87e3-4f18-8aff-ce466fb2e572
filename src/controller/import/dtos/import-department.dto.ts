import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateDepartmentDto } from '../../department/dtos/create-department.dto';
import { UpdateDepartmentDto } from '../../department/dtos/update-department.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportDepartmentDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateDepartmentDto],
  })
  @IsArray()
  @Type(() => CreateDepartmentDto)
  dataDepartments: CreateDepartmentDto[];

  @ApiProperty({
    type: [UpdateDepartmentDto],
  })
  @IsArray()
  @Type(() => UpdateDepartmentDto)
  dataUpdateDepartments: UpdateDepartmentDto[];
}
