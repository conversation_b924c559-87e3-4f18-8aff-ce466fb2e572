import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateMaterialGroupDto } from '../../material-group/dtos/create-material-group.dto';
import { UpdateMaterialGroupDto } from '../../material-group/dtos/update-material-group.model';
import { ImportBaseDto } from './import-base.dto';

export class ImportMaterialGroupDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateMaterialGroupDto],
  })
  @IsArray()
  @Type(() => CreateMaterialGroupDto)
  dataMaterialGroups: CreateMaterialGroupDto[];

  @ApiProperty({
    type: [UpdateMaterialGroupDto],
  })
  @IsArray()
  @Type(() => UpdateMaterialGroupDto)
  dataUpdateMaterialGroups: UpdateMaterialGroupDto[];
}
