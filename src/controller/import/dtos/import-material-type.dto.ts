import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateMaterialTypeDto } from '../../material-type/dtos/create-material-type.dto';
import { UpdateMaterialTypeDto } from '../../material-type/dtos/update-material-type.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportMaterialTypeDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateMaterialTypeDto],
  })
  @IsArray()
  @Type(() => CreateMaterialTypeDto)
  dataMaterialTypes: CreateMaterialTypeDto[];

  @ApiProperty({
    type: [UpdateMaterialTypeDto],
  })
  @IsArray()
  @Type(() => UpdateMaterialTypeDto)
  dataUpdateMaterialTypes: UpdateMaterialTypeDto[];
}
