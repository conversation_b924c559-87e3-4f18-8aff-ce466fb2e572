import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateMaterialDto } from '../../material/dtos/create-material.dto';
import { UpdateMaterialDto } from '../../material/dtos/update-material.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportMaterialDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateMaterialDto],
  })
  @IsArray()
  @Type(() => CreateMaterialDto)
  dataMaterials: CreateMaterialDto[];

  @ApiProperty({
    type: [UpdateMaterialDto],
  })
  @IsArray()
  @Type(() => UpdateMaterialDto)
  dataUpdateMaterials: UpdateMaterialDto[];
}
