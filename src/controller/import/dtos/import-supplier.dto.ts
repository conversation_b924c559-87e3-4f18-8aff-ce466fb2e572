import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateCostcenterSubaccountDto } from '../../costcenter-subaccount/dtos/create-costcenter-subaccount.dto';
import { ImportBaseDto } from './import-base.dto';
import { CreateSupplierDto } from '../../supplier/dtos/create-supplier.dto';
import { UpdateSupplierDto } from '../../supplier/dtos/update-supplier.dto';

export class ImportSupplierDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateSupplierDto],
  })
  @IsArray()
  @Type(() => CreateSupplierDto)
  dataSuppliers: CreateSupplierDto[];

  @ApiProperty({
    type: [UpdateSupplierDto],
  })
  @IsArray()
  @Type(() => UpdateSupplierDto)
  dataUpdateSuppliers: UpdateSupplierDto[];
}
