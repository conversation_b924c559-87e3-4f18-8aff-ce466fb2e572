import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { ImportBaseDto } from './import-base.dto';
import { CreateMeasureDto } from '../../measure/dtos/create-measure.dto';
import { UpdateMeasureDto } from '../../measure/dtos/update-measure.dto';

export class ImportMeasureDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateMeasureDto],
  })
  @IsArray()
  @Type(() => CreateMeasureDto)
  dataMeasures: CreateMeasureDto[];

  @ApiProperty({
    type: [UpdateMeasureDto],
  })
  @IsArray()
  @Type(() => UpdateMeasureDto)
  dataUpdateMeasures: UpdateMeasureDto[];
}
