import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateBudgetCapexDto } from '../../budget/dtos/create-budget-capex.dto';
import { ImportBaseDto } from './import-base.dto';
import { CreateBudgetCodeDto } from '../../budget-code/dtos/create-budget-code.dto';
import { UpdateBudgetCodeDto } from '../../budget-code/dtos/update-budget-code.dto';

export class ImportBudgetCodeDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateBudgetCodeDto],
  })
  @IsArray()
  @Type(() => CreateBudgetCodeDto)
  dataBudgetCodes: CreateBudgetCodeDto[];

  @ApiProperty({
    type: [UpdateBudgetCodeDto],
  })
  @IsArray()
  @Type(() => UpdateBudgetCodeDto)
  dataUpdateBudgetCodes: UpdateBudgetCodeDto[];
}
