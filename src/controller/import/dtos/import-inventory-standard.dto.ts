import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateInventoryStandardDto } from '../../inventory-standard/dtos/create-inventory-standard.dto';
import { UpdateInventoryStandardDto } from '../../inventory-standard/dtos/update-inventory-standard.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportInventoryStandardDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateInventoryStandardDto],
  })
  @IsArray()
  @Type(() => CreateInventoryStandardDto)
  dataInventoryStandards: CreateInventoryStandardDto[];

  @ApiProperty({
    type: [UpdateInventoryStandardDto],
  })
  @IsArray()
  @Type(() => UpdateInventoryStandardDto)
  dataUpdateInventoryStandards: UpdateInventoryStandardDto[];
}
