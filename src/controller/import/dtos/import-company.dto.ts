import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateCompanyDto } from '../../company/dtos/create-company.dto';
import { UpdateCompanyDto } from '../../company/dtos/update-company.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportCompanyDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateCompanyDto],
  })
  @IsArray()
  @Type(() => CreateCompanyDto)
  dataCompanys: CreateCompanyDto[];

  @ApiProperty({
    type: [UpdateCompanyDto],
  })
  @IsArray()
  @Type(() => UpdateCompanyDto)
  dataUpdateCompanys: UpdateCompanyDto[];
}
