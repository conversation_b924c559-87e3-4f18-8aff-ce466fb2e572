import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateCurrencyUnitExchangeDto } from '../../currency-unit-exchange/dtos/create-currency-unit-exchange.dto';
import { UpdateCurrencyUnitExchangeDto } from '../../currency-unit-exchange/dtos/update-currency-unit-exchange.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportCurrencyUnitExchangeDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateCurrencyUnitExchangeDto],
  })
  @IsArray()
  @Type(() => CreateCurrencyUnitExchangeDto)
  dataCurrencyUnitExchanges: CreateCurrencyUnitExchangeDto[];

  @ApiProperty({
    type: [UpdateCurrencyUnitExchangeDto],
  })
  @IsArray()
  @Type(() => UpdateCurrencyUnitExchangeDto)
  dataUpdateCurrencyUnitExchanges: UpdateCurrencyUnitExchangeDto[];
}
