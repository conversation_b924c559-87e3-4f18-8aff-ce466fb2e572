import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray } from 'class-validator';
import { CreateBusinessOwnerDto } from '../../business-owner/dtos/create-business-owner.dto';
import { UpdateBusinessOwnerDto } from '../../business-owner/dtos/update-business-owner.dto';
import { ImportBaseDto } from './import-base.dto';

export class ImportBusinessOwnerDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateBusinessOwnerDto],
  })
  @IsArray()
  @Type(() => CreateBusinessOwnerDto)
  dataBusinessOwners: CreateBusinessOwnerDto[];

  @ApiProperty({
    type: [UpdateBusinessOwnerDto],
  })
  @IsArray()
  @Type(() => UpdateBusinessOwnerDto)
  dataUpdateBusinessOwners: UpdateBusinessOwnerDto[];
}
