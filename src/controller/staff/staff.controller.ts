import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';

import { StaffUsecases } from '../../usecases/staff.usecase';
import {
  EPurchaseOrderPermission,
  EPurchaseRequestPermission,
  EStaffPermission,
} from '../../utils/constants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateStaffDto } from './dtos/create-staff.dto';
import { GetApproverByPositionDto } from './dtos/get-approver-by-position.dto';
import { GetDetailStaffDto } from './dtos/get-detail-staff.dto';
import { GetStaffByCodesDto } from './dtos/get-staff-by-codes.dto';
import { GetStaffByEmailsDto } from './dtos/get-staff-by-emails.dto';
import { GetStaffListDto } from './dtos/get-staff-list.dto';
import { UpdateStaffDto } from './dtos/update-staff.dto';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';

@Controller('/staff')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Staffs')
export class StaffController {
  constructor(private readonly staffUsecases: StaffUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EStaffPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(@Body() data: CreateStaffDto, @NewAuthUser() jwtPayload: any) {
    return await this.staffUsecases.createStaff(data, jwtPayload);
  }

  @Patch(':staffId')
  @UseGuards(NewPermissionGuard([EStaffPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param() param: GetDetailStaffDto,
    @Body() data: UpdateStaffDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.staffUsecases.updateStaff(
      param.staffId,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':staffId/detail')
  // @UseGuards(NewPermissionGuard([EStaffPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailStaffDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.staffUsecases.getDetailStaff(
      { staffId: param.staffId },
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EStaffPermission.VIEW]))
  async getList(
    @Query() conditions: GetStaffListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.staffUsecases.getStaffs(conditions, jwtPayload);
  }

  @Delete(':staffId')
  @UseGuards(NewPermissionGuard([EStaffPermission.DELETE]))
  async delete(
    @Param() param: GetDetailStaffDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.staffUsecases.deleteStaff(
      param.staffId,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/staffs-by-codes')
  @UseGuards(NewPermissionGuard([EStaffPermission.VIEW]))
  async getStaffByCodes(
    @Body() conditions: GetStaffByCodesDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.staffUsecases.getStaffByCodes(conditions, jwtPayload);
  }

  @Post('/staffs-by-emails')
  @UseGuards(
    NewPermissionGuard([
      EStaffPermission.VIEW,
      EPurchaseRequestPermission.CREATE,
      EPurchaseRequestPermission.EDIT,
      EPurchaseOrderPermission.CREATE,
      EPurchaseOrderPermission.EDIT,
    ]),
  )
  async getStaffByEmails(
    @Body() conditions: GetStaffByEmailsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.staffUsecases.getStaffByEmails(conditions, jwtPayload);
  }

  @Get('/approver-by-position')
  async approverByPosition(
    @Query() conditions: GetApproverByPositionDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.staffUsecases.approverByPosition(
      conditions,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
