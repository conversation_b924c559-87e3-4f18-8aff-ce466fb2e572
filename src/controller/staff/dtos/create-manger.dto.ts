import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsUUID } from 'class-validator';
import { EManagementLevel } from '../../../domain/config/enums/management-level.enum';

export class CreateManagerDto {
  @ApiProperty({
    type: EManagementLevel,
    enum: EManagementLevel,
    required: true,
    description: 'Level (1: Line Manager)',
  })
  @IsNotEmpty({ message: 'VALIDATE.LEVEL.IS_REQUIRED' })
  @IsEnum(EManagementLevel)
  level: EManagementLevel;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Manager Id',
  })
  @IsNotEmpty({ message: 'VALIDATE.MANAGER_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.MANAGER_ID.MUST_BE_UUID' })
  managerId: string;

  subordinateId?: string;
}
