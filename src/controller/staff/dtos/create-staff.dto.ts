import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON>Array,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  ValidateNested,
} from 'class-validator';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { CreateManagerDto } from './create-manger.dto';

export class CreateStaffDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'First Name',
  })
  @IsNotEmpty({ message: 'VALIDATE.FIRST_NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.FIRST_NAME.MUST_BE_STRING' })
  firstName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Last Name',
  })
  @IsNotEmpty({ message: 'VALIDATE.LAST_NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.LAST_NAME.MUST_BE_STRING' })
  lastName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Email',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Phone Number',
  })
  @Matches(/^[0-9]+$/, {
    message: 'VALIDATE.PHONE_NUMBER.MUST_BE_NUMBER',
  })
  phone: string;

  @ApiProperty({
    type: EStatus,
    enum: EStatus,
    required: false,
    description: 'Status',
    default: EStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EStatus)
  status: EStatus;

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Sector Ids',
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  sectorIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Company Ids',
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  companyIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Business Unit Ids',
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Department Ids',
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  departmentIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Function Unit Ids',
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  functionUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Business Owner Ids',
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessOwnerIds?: string[];

  @ApiProperty({
    type: String,
    required: true,
    description: 'Position Id',
  })
  @IsNotEmpty({ message: 'VALIDATE.POSITION_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.POSITION_ID.MUST_BE_UUID' })
  positionId: string;

  @ApiProperty({
    type: [CreateManagerDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateManagerDto)
  managers: CreateManagerDto[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'Người tạo PO',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PO_CREATOR_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PO_CREATOR_ID.MUST_BE_UUID' })
  poCreatorId: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'Người mua hàng',
  // })
  // @IsOptional()
  // @IsNotEmpty({ message: 'VALIDATE.PURCHASER_ID.IS_REQUIRED' })
  // @IsUUID('4', { message: 'VALIDATE.PURCHASER_ID.MUST_BE_UUID' })
  // purchaserId: string;

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Purchaser Ids',
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  purchaserIds?: string[];
}
