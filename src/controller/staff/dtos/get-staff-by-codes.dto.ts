import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional } from 'class-validator';

export class GetStaffByCodesDto {
  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  codes?: string[];

  businessOwnerCodes: string[];
  sectorCodes: string[];
  companyCodes: string[];
  businessUnitCodes: string[];
  departmentCodes: string[];
  functionUnitCodes: string[];
  positionCodes: string[];
}
