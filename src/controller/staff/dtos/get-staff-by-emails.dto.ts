import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional } from 'class-validator';

export class GetStaffByEmailsDto {
  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  emails?: string[];

  businessOwnerCodes?: string[];
  sectorCodes?: string[];
  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
  functionUnitCodes?: string[];
  positionCodes?: string[];
}
