import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { GetUuidDto } from '../../../domain/dtos/get-uuid.dto';

export class GetDetailStaffDto {
  @ApiProperty({ type: String, required: false, description: 'Id of Staff' })
  @IsNotEmpty({ message: 'VALIDATE.STAFF_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.STAFF_ID.MUST_BE_UUID' })
  staffId: string;

  businessOwnerCodes?: string[];
  sectorCodes?: string[];
  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
  functionUnitCodes?: string[];
  positionCodes?: string[];
}
