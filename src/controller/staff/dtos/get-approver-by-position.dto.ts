import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { EStatus } from '../../../domain/config/enums/status.enum';

export class GetApproverByPositionDto {
  @ApiProperty({
    type: [EStatus],
    required: true,
  })
  @IsArray()
  @IsEnum(EStatus, { each: true })
  statuses: EStatus[];

  @ApiProperty({
    type: [String],
    required: true,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: true,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  positionIds?: string[];

  @ApiProperty({
    type: [String],
    required: true,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  departmentIds?: string[];

  @ApiProperty({
    type: [String],
    required: true,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  functionUnitIds?: string[];

  staffIds?: string[];
}
