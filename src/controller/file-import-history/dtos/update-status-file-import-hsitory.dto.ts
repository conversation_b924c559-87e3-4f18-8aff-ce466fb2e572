import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { Any } from 'typeorm';
import { EFileImportStatus } from '../../../domain/config/enums/file-import.enum';

export class UpdateStatusFileImportHistoryDto {
  @ApiProperty({
    type: Any,
    required: false,
    description: 'Lỗi',
  })
  @IsOptional()
  errors?: string | object[];

  @ApiProperty({
    type: String,
    required: true,
    description: 'Trạng thái',
    enum: EFileImportStatus,
  })
  @IsNotEmpty({ message: 'VALIDATE.STATUS.IS_REQUIRED' })
  @IsEnum(EFileImportStatus, {
    message: 'VALIDATE.STATUS.INVALID_VALUE',
  })
  status: EFileImportStatus;
}
