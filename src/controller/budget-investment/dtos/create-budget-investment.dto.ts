import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';

export class CreateBudgetInvestmentDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Hạng mục đầu tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.INVESTMENT.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.INVESTMENT.MUST_BE_STRING' })
  investment: string;

  @ApiProperty({
    description: 'Số lượng',
    type: Number,
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.QUANTITY.IS_REQUIRED' })
  @IsInt({ message: 'VALIDATE.QUANTITY.MUST_BE_INT' })
  @Min(0)
  quantity: number;

  @ApiProperty({
    description: 'Đơn giá',
    type: Number,
    required: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'VALIDATE.PRICE.MUST_BE_NUMBER' })
  @Min(0)
  price?: number;

  @ApiProperty({
    description: 'Tổng chi phí vận chuyển',
    type: Number,
    required: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'VALIDATE.TRANSPORTATION_COST.MUST_BE_NUMBER' })
  @Min(0)
  transportationCosts?: number;

  row?: number;
}
