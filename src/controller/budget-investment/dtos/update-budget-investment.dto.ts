import { IsOptional, IsUUID } from 'class-validator';
import { CreateBudgetInvestmentDto } from './create-budget-investment.dto';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateBudgetInvestmentDto extends CreateBudgetInvestmentDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Id',
  })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.BUDGET_INVESTMENT_ID.MUST_BE_UUID' })
  id?: string;
}
