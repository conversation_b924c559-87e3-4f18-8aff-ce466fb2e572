import {
  Body,
  Controller,
  Delete,
  Param,
  Patch,
  Post,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { PublicUsecases } from '../../usecases/public.usecases';
import { AuthUser } from '../../utils/decorators/user.decorator';
import { PublicAuthGuard } from '../../utils/guard/public-auth.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateMaterialFromSapDto } from './dto/create-material-from-sap.dto';
import { CreateSupplierFromSapDto } from './dto/create-supplier-from-sap.dto';
import { UpdateMaterialFromSapDto } from './dto/update-material-from-sap.dto';
import { UpdateSupplierFromSapDto } from './dto/update-supplier-from-sap.dto';

@Controller('/public')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(PublicAuthGuard)
@ApiTags('Public')
export class PublicController {
  constructor(private readonly publicUsecases: PublicUsecases) {}

  @Post('material/create')
  async createMaterial(
    @Body() data: CreateMaterialFromSapDto,
    @AuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.publicUsecases.createMaterial(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch('material/:code/update')
  async updateMaterial(
    @Body() data: UpdateMaterialFromSapDto,
    @Param() param: GetUuidDto,
    @AuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.publicUsecases.updateMaterial(
      param.code,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Delete('material/:code')
  async deleteMaterial(
    @Param() param: GetUuidDto,
    @AuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.publicUsecases.deleteMaterial(
      param.code,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('supplier/create')
  async createSupplier(
    @Body() data: CreateSupplierFromSapDto,
    @AuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.publicUsecases.createSupplier(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch('supplier/:code/update')
  async updateSupplier(
    @Body() data: UpdateSupplierFromSapDto,
    @Param() param: GetUuidDto,
    @AuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.publicUsecases.updateSupplier(
      param.code,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Delete('supplier/:code')
  async deleteSupplier(
    @Param() param: GetUuidDto,
    @AuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.publicUsecases.deleteSupplier(
      param.code,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
