import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { EMaterialStatus } from '../../../domain/config/enums/material.enum';
import { CreateIndustryFromSapDto } from './create-supplier-from-sap.dto';

export class CreateMaterialFromSapDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã Vật tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên Vật tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON>ô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Loại vật tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_TYPE_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_TYPE_CODE.MUST_BE_STRING' })
  materialTypeCode: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Đơn vị tính',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.UNIT.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.UNIT.MUST_BE_STRING' })
  unit: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Nhóm vật tư',
  })
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_GROUP_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_GROUP_CODE.MUST_BE_STRING' })
  materialGroupCode: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Nhóm vật tư',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PURCHASING_DEPARTMENT_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.PURCHASING_DEPARTMENT_CODE.MUST_BE_STRING' })
  purchasingDepartmentCode?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Nhóm vật tư',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PURCHASING_GROUP.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.PURCHASING_GROUP.MUST_BE_STRING' })
  purchasingGroupCode?: string;

  // @ApiProperty({
  //   type: [CreateMaterialSectorDto],
  //   description: 'Ngành',
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray({ message: 'VALIDATE.INDUSTRIES.MUST_BE_ARRAY' })
  // @ArrayMinSize(1)
  // @ValidateNested({ each: true })
  // @Type(() => CreateMaterialSectorDto)
  // industries?: CreateMaterialSectorDto[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'Đơn vị kinh doanh ',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.INDUSTRIES.MUST_BE_ARRAY' })
  @ArrayMinSize(1)
  @IsString({
    message: 'VALIDATE.MATERIAL_GROUP_CODE.MUST_BE_STRING',
    each: true,
  })
  businessUnitCodes?: string[];

  @ApiProperty({
    type: [CreateIndustryFromSapDto],
    description: 'Ngành',
    required: true,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.INDUSTRIES.MUST_BE_ARRAY' })
  @ArrayMinSize(0)
  @ValidateNested({ each: true })
  @Type(() => CreateIndustryFromSapDto)
  industries: CreateIndustryFromSapDto[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EMaterialStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EMaterialStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EMaterialStatus;

  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}
