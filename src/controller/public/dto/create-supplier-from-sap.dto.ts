import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ESupplierSectorStatus } from '../../../domain/config/enums/supplier-sector.enum';
import { CreateSupplierDto } from '../../supplier/dtos/create-supplier.dto';
import { ESupplierType } from '../../../domain/config/enums/supplier.enum';

export class CreateIndustryFromSapDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã ngành',
  })
  @IsNotEmpty({ message: 'VALIDATE.SECTOR_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.SECTOR_CODE.MUST_BE_STRING' })
  sectorCode: string;
}

export class CreateSupplierFromSapDto extends OmitType(CreateSupplierDto, [
  'industries',
  'type',
]) {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: ESupplierSectorStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ESupplierSectorStatus, {
    message: 'VALIDATE.STATUS.INVALID_VALUE',
  })
  status: ESupplierSectorStatus;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Loại',
    default: ESupplierType.OFFICIAL,
  })
  @IsOptional()
  @IsEnum(ESupplierType, {
    message: 'VALIDATE.TYPE.INVALID_VALUE',
  })
  type: ESupplierType = ESupplierType.OFFICIAL;

  @ApiProperty({
    type: [CreateIndustryFromSapDto],
    description: 'Ngành',
    required: true,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.INDUSTRIES.MUST_BE_ARRAY' })
  @ArrayMinSize(0)
  @ValidateNested({ each: true })
  @Type(() => CreateIndustryFromSapDto)
  industries: CreateIndustryFromSapDto[];
}
