import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';
import { ENotificationFormType } from '../../../domain/config/enums/notification-form.enum';

export class CreateNotificationDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Notification form',
  })
  @IsEnum(ENotificationFormType, {
    message: 'VALIDATE.NOTIFICATION_FORM.INVALID_VALUE',
  })
  notificationForm?: ENotificationFormType;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người sở hữu',
  })
  @IsNotEmpty()
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  ownerId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Platform',
  })
  @IsEnum(EPlatform, { message: 'VALIDATE.PLATFORM.INVALID_VALUE' })
  platform: EPlatform;

  @ApiProperty({
    type: String,
    required: false,
    description: 'URL',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.URL.MUST_BE_STRING' })
  url?: string;

  @ApiProperty({
    type: Object,
    required: false,
    description: 'URL',
  })
  @IsOptional()
  @IsObject({ message: 'VALIDATE.META_DATA.MUST_BE_OBJECT' })
  metaData?: object;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Title',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.TITLE.MUST_BE_STRING' })
  title?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Body',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.BODY.MUST_BE_STRING' })
  body?: string;
}

///Dùng để gọi api queue notification
export class CreateMultipleNotificationDto {
  @ApiProperty({
    type: [CreateNotificationDto],
    required: true,
    description: 'Danh sách notification',
  })
  @ArrayMinSize(1)
  @IsArray()
  data: CreateNotificationDto[];

  ///Url để queue gọi khi chạy
  @ApiProperty({
    type: String,
    required: false,
    description: 'Call back Url',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.CALL_BACK_URL.MUST_BE_STRING' })
  callBackUrl?: string;
}
