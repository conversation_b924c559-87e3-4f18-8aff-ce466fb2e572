import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class GetDetailNotificationDto {
  @ApiProperty({ type: String, required: true, description: 'UUID' })
  @IsNotEmpty({ message: 'VALIDATE.ID.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.ID.MUST_BE_STRING' })
  id: string;

  onwerId?: string;
  platform?: EPlatform;
}
