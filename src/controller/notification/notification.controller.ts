import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { NotificationUsecases } from '../../usecases/notification.usecases';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CountUnReadNotificationDto } from './dtos/count-un-read-notification.dto';
import { GetDetailNotificationDto } from './dtos/get-detail-notification.dto';
import { GetNotificationListDto } from './dtos/get-notification-list.dto';
import { MarkAllAsReadDto } from './dtos/mark-all-as-read-notification.dto';
import { MarkAsReadDto } from './dtos/mark-as-read-notification.dto';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { CreateMultipleNotificationDto } from './dtos/create-notification.dto';

@Controller('/notification')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Notifications')
export class NotificationController {
  constructor(private readonly notificationUsecases: NotificationUsecases) {}

  // @Post('/create')
  // // @UseGuards(NewPermissionGuard([ENotificationPermission.CREATE]))
  // // @UseInterceptors(TransactionInterceptor)
  // async create(
  //   @Body() data: CreateNotificationDto,
  //   @NewAuthUser() jwtPayload: any,
  // ) {
  //   return await this.notificationUsecases.createNotification(data, jwtPayload);
  // }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([ENotificationPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailNotificationDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    param.onwerId = jwtPayload?.staffId || '';
    param.platform = jwtPayload?.platform || EPlatform.E_PURCHASE;
    return await this.notificationUsecases.getNotificationDetail(param);
  }

  @Get('/list')
  // @UseGuards(NewPermissionGuard([ENotificationPermission.VIEW]))
  async getList(
    @Query() param: GetNotificationListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    // param.onwerId = '123';
    // param.platform = EPlatform.E_PURCHASE;
    param.onwerId = jwtPayload?.staffId || '';
    param.platform = jwtPayload?.platform || EPlatform.E_PURCHASE;
    return await this.notificationUsecases.getNotifications(param);
  }

  @Put('/mark-as-read')
  async markAsRead(
    @Body() data: MarkAsReadDto,
    @NewAuthUser() jwtPayload: any,
  ): Promise<void> {
    data.onwerId = jwtPayload?.staffId || '';
    data.platform = jwtPayload?.platform || EPlatform.E_PURCHASE;
    await this.notificationUsecases.markAsRead(data);
  }

  @Put('/mark-all-as-read')
  async markAllAsRead(
    @Body() data: MarkAllAsReadDto,
    @NewAuthUser() jwtPayload: any,
  ): Promise<void> {
    data.onwerId = jwtPayload?.staffId || '';
    data.platform = jwtPayload?.platform || EPlatform.E_PURCHASE;
    await this.notificationUsecases.markAllAsRead(data);
  }

  @Get('/count-un-read')
  async countUnRead(
    @Query() data: CountUnReadNotificationDto,
    @NewAuthUser() jwtPayload: any,
  ): Promise<object> {
    // data.onwerId = '123';
    // data.platform = EPlatform.E_PURCHASE;
    data.onwerId = jwtPayload?.staffId || '';
    data.platform = jwtPayload?.platform || EPlatform.E_PURCHASE;
    return await this.notificationUsecases.countUnRead(data);
  }

  @Post('/push-notification')
  async pushNotification(@Body() data: CreateMultipleNotificationDto) {
    return await this.notificationUsecases.pushNotification(data);
  }

  @Post('/test-notification')
  async testNotification() {
    return await this.notificationUsecases.testNotification();
  }
}
