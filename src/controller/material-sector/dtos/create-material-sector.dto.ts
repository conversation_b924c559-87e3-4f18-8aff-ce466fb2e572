import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EMaterialStatus } from '../../../domain/config/enums/material.enum';

export class CreateMaterialSectorDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã SAP',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE_SAP.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE_SAP.MUST_BE_STRING' })
  codeSAP: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EMaterialStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EMaterialStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EMaterialStatus;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Mảng',
  })
  @IsNotEmpty({ message: 'VALIDATE.SECTOR_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.SECTOR_ID.MUST_BE_UUID' })
  sectorId: string;
}
