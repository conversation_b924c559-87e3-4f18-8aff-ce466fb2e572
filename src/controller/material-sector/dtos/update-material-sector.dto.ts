import { IsOptional, IsUUID } from 'class-validator';
import { CreateMaterialSectorDto } from './create-material-sector.dto';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateMaterialSectorDto extends CreateMaterialSectorDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Id',
  })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.SUPPLIER_SECTOR_ID.MUST_BE_UUID' })
  id?: string;
}
