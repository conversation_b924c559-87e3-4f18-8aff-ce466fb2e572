import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsOptional, IsUUID } from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetApprovalProcessDetailListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [String],
    required: false,
    description: 'Ngành',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.SECTOR_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.SECTOR_ID.MUST_BE_UUID',
    each: true,
  })
  sectorIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Department',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.DEPARTMENT_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.DEPARTMENT_ID.MUST_BE_UUID',
    each: true,
  })
  departmentIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Loại PR',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.PR_TYPE_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.PR_TYPE_ID.MUST_BE_UUID',
    each: true,
  })
  prTypeIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Khối chức năng',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.FUNCTION_UNIT_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.FUNCTION_UNIT_ID.MUST_BE_UUID',
    each: true,
  })
  functionUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Đơn vị kinh doanh',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.BUSINESS_UNIT_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.BUSINESS_UNIT_ID.MUST_BE_UUID',
    each: true,
  })
  businessUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Người yêu cầu',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.PR_REQUESTER_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.PR_REQUESTER_ID.MUST_BE_UUID',
    each: true,
  })
  prCreatedByIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Người yêu cầu',
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.PO_REQUESTER_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.PO_REQUESTER_ID.MUST_BE_UUID',
    each: true,
  })
  poCreatedByIds?: string[];

  ids?: string[];
}
