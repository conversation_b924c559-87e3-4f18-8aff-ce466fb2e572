import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  Is<PERSON>rray,
  IsNotEmpty,
  <PERSON>Optional,
  IsString,
  IsUUID,
  Min,
} from 'class-validator';

export class CreateApprovalProcessDetailDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Name',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Ngành',
  })
  @IsNotEmpty({ message: 'VALIDATE.SECTOR_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.SECTOR_ID.MUST_BE_UUID' })
  sectorId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: '<PERSON><PERSON><PERSON><PERSON> chức năng',
  })
  @IsNotEmpty({ message: 'VALIDATE.FUNCTION_UNIT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.FUNCTION_UNIT_ID.MUST_BE_UUID' })
  functionUnitId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Department',
  })
  @IsNotEmpty({ message: 'VALIDATE.DEPARTMENT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.DEPARTMENT_ID.MUST_BE_UUID' })
  departmentId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Đơn vị kinh doanh',
  })
  @IsNotEmpty({ message: 'VALIDATE.BUSINESS_UNIT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.BUSINESS_UNIT_ID.MUST_BE_UUID' })
  businessUnitId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người tạo PR',
  })
  @IsNotEmpty({ message: 'VALIDATE.PR_CREATED_BY_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_CREATED_BY_ID.MUST_BE_UUID' })
  prCreatedById: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người tạo PO',
  })
  @IsNotEmpty({ message: 'VALIDATE.PO_CREATED_BY_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PO_CREATED_BY_ID.MUST_BE_UUID' })
  poCreatedById: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PR 1',
  })
  @IsNotEmpty({ message: 'VALIDATE.PR_APPROVER_1_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_APPROVER_1_ID.MUST_BE_UUID' })
  prApprover1Id: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PR 2',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PR_APPROVER_2_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_APPROVER_2_ID.MUST_BE_UUID' })
  prApprover2Id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PR 3',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PR_APPROVER_3_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_APPROVER_3_ID.MUST_BE_UUID' })
  prApprover3Id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PR 4',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PR_APPROVER_4_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_APPROVER_4_ID.MUST_BE_UUID' })
  prApprover4Id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PR 5',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PR_APPROVER_5_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_APPROVER_5_ID.MUST_BE_UUID' })
  prApprover5Id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PR 6',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PR_APPROVER_6_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_APPROVER_6_ID.MUST_BE_UUID' })
  prApprover6Id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PR 7',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PR_APPROVER_7_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_APPROVER_7_ID.MUST_BE_UUID' })
  prApprover7Id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PO 1',
  })
  @IsNotEmpty({ message: 'VALIDATE.PO_APPROVER_1_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PO_APPROVER_1_ID.MUST_BE_UUID' })
  poApprover1Id: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PO 2',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PO_APPROVER_2_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PO_APPROVER_2_ID.MUST_BE_UUID' })
  poApprover2Id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Người duyệt PO 3',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.PO_APPROVER_3_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PO_APPROVER_3_ID.MUST_BE_UUID' })
  poApprover3Id?: string;

  @ApiProperty({
    type: [String],
    required: true,
    description: 'Loại PR',
  })
  @IsArray({ message: 'VALIDATE.PR_TYPE_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1)
  @IsUUID('4', {
    message: 'VALIDATE.PR_TYPE_ID.MUST_BE_UUID',
    each: true,
  })
  prTypeIds: string[];

  createdAt?: string;
  updatedAt?: string;
}
