import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ApprovalProcessDetailUsecases } from '../../usecases/approval-process-detail.usecases';
import { EApprovalProcessDetailPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateApprovalProcessDetailDto } from './dtos/create-approval-process-detail.dto';
import { DeleteApprovalProcessDetailDto } from './dtos/delete-approval-process-detail.dto';
import { GetApprovalProcessDetailListDto } from './dtos/get-approval-process-detail-list.dto';
import { GetDetailApprovalProcessDetailDto } from './dtos/get-detail-approval-process-detail.dto';
import { UpdateApprovalProcessDetailDto } from './dtos/update-approval-process-detail.dto';

@Controller('/approval-process-detail')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('ApprovalProcessDetail')
export class ApprovalProcessDetailController {
  constructor(
    private readonly approvalProcessDetailUsecases: ApprovalProcessDetailUsecases,
  ) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EApprovalProcessDetailPermission.CREATE]))
  async create(
    @Body() data: CreateApprovalProcessDetailDto,
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.approvalProcessDetailUsecases.createApprovalProcessDetail(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EApprovalProcessDetailPermission.EDIT]))
  async update(
    @Body() data: UpdateApprovalProcessDetailDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.approvalProcessDetailUsecases.updateApprovalProcessDetail(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  async getDetail(
    @Param() param: GetDetailApprovalProcessDetailDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.approvalProcessDetailUsecases.getDetailApprovalProcessDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EApprovalProcessDetailPermission.VIEW]))
  async getList(
    @Query() param: GetApprovalProcessDetailListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.approvalProcessDetailUsecases.getApprovalProcessDetails(
      param,
      jwtPayload,
    );
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EApprovalProcessDetailPermission.DELETE]))
  async delete(
    @Param() param: DeleteApprovalProcessDetailDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.approvalProcessDetailUsecases.deleteApprovalProcessDetail(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
