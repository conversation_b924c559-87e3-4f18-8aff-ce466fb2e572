import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ECostStatus } from '../../../domain/config/enums/cost.enum';

export class CreateCostDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Group cost',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.GROUP_COST.MUST_BE_STRING' })
  groupCost?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: ECostStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ECostStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: ECostStatus;

  createdAt?: string;
  updatedAt?: string;
}
