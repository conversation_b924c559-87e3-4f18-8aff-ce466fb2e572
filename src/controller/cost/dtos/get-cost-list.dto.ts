import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { ECostStatus } from '../../../domain/config/enums/cost.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetCostListDto extends OmitType(PaginationDto, ['from', 'to']) {
  @ApiProperty({
    type: [ECostStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ECostStatus, { each: true })
  statuses?: ECostStatus[];

  codes?: string[];
  ids: string[];
}
