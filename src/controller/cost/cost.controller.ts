import {
  Body,
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Param,
  Patch,
  Get,
  Query,
  Delete,
  Response,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpStatus,
} from '@nestjs/common';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { CostUsecases } from '../../usecases/cost.usecases';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { ECostPermission } from '../../utils/constants/permission.enum';
import { CreateCostDto } from './dtos/create-cost.dto';
import { UpdateCostDto } from './dtos/update-cost.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { GetDetailCostDto } from './dtos/get-detail-cost.dto';
import { GetCostListDto } from './dtos/get-cost-list.dto';
import { DeleteCostDto } from './dtos/delete-cost.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('/cost')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Cost')
export class CostController {
  constructor(private readonly costUsecases: CostUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([ECostPermission.CREATE]))
  async create(@Body() data: CreateCostDto, @Request() req) {
    return await this.costUsecases.createCost(
      data,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([ECostPermission.EDIT]))
  async update(
    @Body() data: UpdateCostDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.costUsecases.updateCost(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([ECostPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailCostDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.costUsecases.getDetailCost(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([ECostPermission.VIEW]))
  async getList(
    @Query() param: GetCostListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.costUsecases.getCosts(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([ECostPermission.DELETE]))
  async delete(
    @Param() param: DeleteCostDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.costUsecases.deleteCost(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-cost')
  @UseGuards(
    NewPermissionGuard([[ECostPermission.VIEW, ECostPermission.EXPORT]]),
  )
  async exportCost(
    @Query() param: GetCostListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.costUsecases.exportCost(param, jwtPayload);

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-cost')
  @UseGuards(NewPermissionGuard([ECostPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importCost(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.costUsecases.importCost(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
