import {
  Body,
  Controller,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Get,
  Query,
  Delete,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpStatus,
  Response,
} from '@nestjs/common';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { CreateSupplierDto } from './dtos/create-supplier.dto';
import { UpdateSupplierDto } from './dtos/update-supplier.dto';
import { GetDetailSupplierDto } from './dtos/get-detail-supplier.dto';
import { GetSupplierListDto } from './dtos/get-supplier-list.dto';
import { DeleteSupplierDto } from './dtos/delete-supplier.dto';
import { SupplierUsecases } from '../../usecases/supplier.usecases';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { ESupplierPermission } from '../../utils/constants/permission.enum';
import { FileInterceptor } from '@nestjs/platform-express';
import { ListByCodesDto } from '../../domain/dtos/base-dto-by-codes.dto';
import { GetSupplierListByIdsDto } from './dtos/get-supplier-list-by-ids.dto';

@Controller('/supplier')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Supplier')
export class SupplierController {
  constructor(private readonly supplierUsecases: SupplierUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([ESupplierPermission.CREATE]))
  async create(
    @Body() data: CreateSupplierDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.supplierUsecases.createSupplier(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([ESupplierPermission.EDIT]))
  async update(
    @Body() data: UpdateSupplierDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.supplierUsecases.updateSupplier(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([ESupplierPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailSupplierDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.supplierUsecases.getSupplierDetail(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([ESupplierPermission.VIEW]))
  async getList(
    @Query() param: GetSupplierListDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    const base64Payload = req.headers['authorization'].split('.')[1];
    const payloadBuffer = Buffer.from(base64Payload, 'base64');
    const updatedJwtPayload = JSON.parse(payloadBuffer.toString());

    return await this.supplierUsecases.getSuppliers(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([ESupplierPermission.DELETE]))
  async delete(
    @Param() param: DeleteSupplierDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.supplierUsecases.deleteSupplier(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-supplier')
  @UseGuards(NewPermissionGuard([ESupplierPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importSupplier(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.supplierUsecases.importSupplier(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/list-by-codes')
  async listByCodes(
    @Body() data: ListByCodesDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.supplierUsecases.listByCodes(
      data.codes || [],
      jwtPayload,
    );
  }

  @Get('/export-supplier')
  @UseGuards(
    NewPermissionGuard([
      [ESupplierPermission.VIEW, ESupplierPermission.EXPORT],
    ]),
  )
  async exportSupplier(
    @Query() param: GetSupplierListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.supplierUsecases.exportSupplier(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  ///For validate detail PR PO
  @Post('/list-by-ids')
  async getListByIds(
    @Body() param: GetSupplierListByIdsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.supplierUsecases.getListByIds(param, jwtPayload);
  }
}
