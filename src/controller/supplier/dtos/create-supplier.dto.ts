import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ESupplierType } from '../../../domain/config/enums/supplier.enum';
import { CreateSupplierSectorDto } from '../../supplier-sector/dtos/create-supplier-sector.dto';

export class CreateSupplierDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON>ô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Loại',
    default: ESupplierType.OFFICIAL,
  })
  @IsNotEmpty({ message: 'VALIDATE.TYPE.IS_REQUIRED' })
  @IsEnum(ESupplierType, {
    message: 'VALIDATE.TYPE.INVALID_VALUE',
  })
  type: ESupplierType = ESupplierType.OFFICIAL;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Địa chỉ',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.ADDRESS.MUST_BE_STRING' })
  address?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Số điện thoại',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.PHONE.MUST_BE_STRING' })
  phone?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Số fax/email',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.FAX_EMAIL.MUST_BE_STRING' })
  fax?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Số giấy phép đăng ký kinh doanh',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.BUSINESS_LICENSE_NUMBER.MUST_BE_STRING' })
  businessLicenseNumber?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Mã số thuế',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.TAX_CODE.MUST_BE_STRING' })
  taxCode?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Người liên hệ, người đại diện theo pháp luật',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.CONTACT_PERSON.MUST_BE_STRING' })
  contactPerson?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Đồng tiền giao dịch',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.TRANSACTION_CURRENCY.MUST_BE_STRING' })
  transactionCurrency?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Phương thức thanh toán',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.PAYMENT_METHOD.MUST_BE_STRING' })
  paymentMethod?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE.MUST_BE_STRING' })
  note?: string;

  @ApiProperty({
    type: [CreateSupplierSectorDto],
    description: 'Ngành',
    required: true,
  })
  @IsArray({ message: 'VALIDATE.INDUSTRIES.MUST_BE_ARRAY' })
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateSupplierSectorDto)
  industries: CreateSupplierSectorDto[];

  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}
