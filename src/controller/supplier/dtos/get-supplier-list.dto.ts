import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import {
  ESupplierStatus,
  ESupplierType,
} from '../../../domain/config/enums/supplier.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetSupplierListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [ESupplierStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ESupplierStatus, { each: true })
  statuses?: ESupplierStatus[];

  @ApiProperty({
    type: [ESupplierType],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ESupplierType, { each: true })
  types?: ESupplierType[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];

  codes?: string[];
}
