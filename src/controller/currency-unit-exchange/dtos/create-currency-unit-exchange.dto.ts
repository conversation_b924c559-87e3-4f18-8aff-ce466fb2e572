import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  Min,
} from 'class-validator';

export class CreateCurrencyUnitExchangeDto {
  @ApiProperty({
    type: Number,
    required: true,
    description: 'Quy đổi VND',
    default: 0,
  })
  @IsNotEmpty({ message: 'VALIDATE.EXCHANGE_RATE.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.EXCHANGE_RATE.MUST_BE_NUMBER' })
  @Min(0)
  exchangeRate: number;

  @ApiProperty({
    type: Date,
    description: 'Thời gian có hiệu lực',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_START_DATE.IS_REQUIRED' })
  // @Transform(({ value }) => new Date(value).toDateString())
  @IsDateString()
  effectiveStartDate: Date;

  @ApiProperty({
    type: Date,
    description: 'Thời gian hết hiệu lực',
    required: false,
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_END_DATE.IS_REQUIRED' })
  // @Transform(({ value }) => new Date(value).toDateString())
  @IsDateString()
  effectiveEndDate?: Date;

  @ApiProperty({
    description: 'Tỷ giá ngân sách',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  exchangeBudget: boolean;

  createdAt?: string;
  updatedAt?: string;
}
