import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsUUID } from 'class-validator';
import { CreateCurrencyUnitExchangeDto } from './create-currency-unit-exchange.dto';

export class UpdateCurrencyUnitExchangeDto extends CreateCurrencyUnitExchangeDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Id',
  })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.CURRENCY_UNIT_ID.MUST_BE_UUID' })
  id?: string;
}
