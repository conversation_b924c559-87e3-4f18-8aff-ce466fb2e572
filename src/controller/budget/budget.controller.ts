import {
  Body,
  Controller,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  Request,
  Response,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { EBudgetType } from '../../domain/config/enums/budget.enum';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { BudgetUsecases } from '../../usecases/budget.usecases';
import { EBudgetPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { filesValidate } from '../../utils/validate';
import { CreateBudgetCapexDto } from './dtos/create-budget-capex.dto';
import { CreateBudgetOpexDto } from './dtos/create-budget-opex.dto';
import { GetBudgetListDto } from './dtos/get-budget-list.dto';
import { GetDetailBudgetDto } from './dtos/get-detail-budget.dto';
import { HandleCopyExcelListDto } from './dtos/handle-copy-excel.dto';
import { LockUnlockBudgetDto } from './dtos/lock-unlock-budget.dto';
import { ReportBudgetDto } from './dtos/report-budget.dto';
import { UpdateBudgetCapexDto } from './dtos/update-budget-capex.dto';
import { UpdateBudgetOpexDto } from './dtos/update-budget-opex.dto';

@Controller('/budget')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Budget')
export class BudgetController {
  constructor(private readonly budgetUsecases: BudgetUsecases) {}

  @Get('/list')
  @UseGuards(NewPermissionGuard([EBudgetPermission.VIEW]))
  async getList(
    @Query() param: GetBudgetListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetUsecases.getBudgetList(param, jwtPayload);
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EBudgetPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailBudgetDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetUsecases.getBudgetDetail(param, jwtPayload);
  }

  @Post('/create-budget-opex')
  @UseGuards(NewPermissionGuard([EBudgetPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async createBudgetOpex(
    @Body() data: CreateBudgetOpexDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetUsecases.createBudgetOpex(data, jwtPayload);
  }

  @Post('/create-budget-capex')
  @UseGuards(NewPermissionGuard([EBudgetPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async createBudgetCapex(
    @Body() data: CreateBudgetCapexDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetUsecases.createBudgetCapex(data, jwtPayload);
  }

  @Put('/update-budget-opex/:id')
  @UseGuards(NewPermissionGuard([EBudgetPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async updateBudgetOpex(
    @Body() data: UpdateBudgetOpexDto,
    @Param('id') budgetOpexId: string,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetUsecases.updateBudgetOpex(
      budgetOpexId,
      data,
      jwtPayload,
    );
  }

  @Put('/update-budget-capex/:id')
  @UseGuards(NewPermissionGuard([EBudgetPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async updateBudgetCapex(
    @Body() data: UpdateBudgetCapexDto,
    @Param('id') budgetCapexId: string,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetUsecases.updateBudgetCapex(
      budgetCapexId,
      data,
      jwtPayload,
    );
  }

  @Post('/lock-unlock-budget')
  @UseGuards(NewPermissionGuard([EBudgetPermission.LOCK_UNLOCK]))
  @UseInterceptors(TransactionInterceptor)
  async lockBudget(
    @Body() body: LockUnlockBudgetDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    await this.budgetUsecases.lockUnLockBudget(
      body,
      jwtPayload,
      req.headers['authorization'],
    );
    return { message: 'Successfully!!!' };
  }

  @Post('/import-budget-opex')
  @UseGuards(NewPermissionGuard([EBudgetPermission.IMPORT]))
  @UseInterceptors(TransactionInterceptor)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importBudgetOpex(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.budgetUsecases.importBudgetOpex(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-budget-capex')
  @UseGuards(NewPermissionGuard([EBudgetPermission.IMPORT]))
  @UseInterceptors(TransactionInterceptor)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'importFile', maxCount: 1 },
      { name: 'filesAttach', maxCount: null },
    ]),
  )
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
        filesAttach: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
  })
  async importBudgetCapex(
    @UploadedFiles()
    files: {
      importFile: Express.Multer.File[];
      filesAttach: Express.Multer.File[];
    },
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    ///Validate import file
    await filesValidate(true, files.importFile, [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ]);

    return await this.budgetUsecases.importBudgetCapex(
      files.importFile[0],
      files.filesAttach || [],
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/import-adjust-budget-opex')
  @UseGuards(NewPermissionGuard([EBudgetPermission.IMPORT]))
  @UseInterceptors(TransactionInterceptor)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importAdjustBudgetOpex(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.budgetUsecases.importAdjustBudget(
      importFile,
      jwtPayload,
      EBudgetType.OPEX,
      req.headers['authorization'],
    );
  }

  @Post('/import-adjust-budget-capex')
  @UseGuards(NewPermissionGuard([EBudgetPermission.IMPORT]))
  @UseInterceptors(TransactionInterceptor)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importAdjustBudgetCapex(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.budgetUsecases.importAdjustBudget(
      importFile,
      jwtPayload,
      EBudgetType.CAPEX,
      req.headers['authorization'],
    );
  }

  @Get('/export-budget')
  @UseGuards(
    NewPermissionGuard([[EBudgetPermission.VIEW, EBudgetPermission.EXPORT]]),
  )
  async getExportBudget(
    @Query() param: GetBudgetListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.budgetUsecases.exportBudget(param, jwtPayload);

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/handle-copy-excel')
  async handleCopyExcel(
    @Body() data: HandleCopyExcelListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetUsecases.handleCopyExcel(data, jwtPayload);
  }

  @Get('/report-budget-detail')
  @UseGuards(NewPermissionGuard([EBudgetPermission.REPORT]))
  async reportBudgetDetail(
    @Query() param: ReportBudgetDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.budgetUsecases.reportBudgetDetail(
      param,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-report-budget-detail')
  @UseGuards(
    NewPermissionGuard([
      [
        EBudgetPermission.VIEW,
        EBudgetPermission.EXPORT,
        EBudgetPermission.REPORT,
      ],
    ]),
  )
  async exportReportBudgetDetail(
    @Query() param: ReportBudgetDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
    @Request() req,
  ) {
    const result = await this.budgetUsecases.exportReportBudgetDetail(
      param,
      jwtPayload,
      req.headers['authorization'],
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Get('/report-budget-overview')
  @UseGuards(NewPermissionGuard([EBudgetPermission.REPORT]))
  async reportBudgetOverview(
    @Query() param: ReportBudgetDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.budgetUsecases.reportBudgetOverview(
      param,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-report-budget-overview')
  @UseGuards(
    NewPermissionGuard([
      [
        EBudgetPermission.VIEW,
        EBudgetPermission.EXPORT,
        EBudgetPermission.REPORT,
      ],
    ]),
  )
  async exportReportBudgetOverview(
    @Query() param: ReportBudgetDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
    @Request() req,
  ) {
    const result = await this.budgetUsecases.exportReportBudgetOverview(
      param,
      jwtPayload,
      req.headers['authorization'],
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-budget-transfer-opex')
  @UseGuards(NewPermissionGuard([EBudgetPermission.IMPORT]))
  @UseInterceptors(TransactionInterceptor)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importBudgetTransferOpex(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.budgetUsecases.importBudgetTransfer(
      importFile,
      jwtPayload,
      EBudgetType.OPEX,
      req.headers['authorization'],
    );
  }

  @Post('/import-budget-transfer-capex')
  @UseGuards(NewPermissionGuard([EBudgetPermission.IMPORT]))
  @UseInterceptors(TransactionInterceptor)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importBudgetTransferCapex(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.budgetUsecases.importBudgetTransfer(
      importFile,
      jwtPayload,
      EBudgetType.CAPEX,
      req.headers['authorization'],
    );
  }
}
