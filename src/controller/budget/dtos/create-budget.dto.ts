import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
} from 'class-validator';
import {
  EBudgetCreateType,
  EBudgetType,
} from '../../../domain/config/enums/budget.enum';

export class CreateBudgetDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Loại khởi tạo',
    enum: EBudgetCreateType,
  })
  @IsNotEmpty({ message: 'VALIDATE.CREATE_TYPE.IS_REQUIRED' })
  @IsEnum(EBudgetCreateType, {
    message: 'VALIDATE.CREATE_TYPE.INVALID_VALUE',
  })
  createType: EBudgetCreateType;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ngân sách điều chỉnh',
  })
  @ValidateIf((data) => data.createType != EBudgetCreateType.NEW)
  @IsNotEmpty({ message: 'VALIDATE.ADJUST_BUDGET_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.ADJUST_BUDGET_ID.MUST_BE_UUID' })
  adjustBudgetId?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Loại ngân sách',
    enum: EBudgetType,
  })
  @IsNotEmpty({ message: 'VALIDATE.BUDGET_TYPE.IS_REQUIRED' })
  @IsEnum(EBudgetType, {
    message: 'VALIDATE.BUDGET_TYPE.INVALID_VALUE',
  })
  budgetType: EBudgetType;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Đơn vị tiền tệ',
  })
  @IsNotEmpty({ message: 'VALIDATE.CURRENCY_UNIT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.CURRENCY_UNIT_ID.MUST_BE_UUID' })
  currencyUnitId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã ngân sách',
  })
  @IsNotEmpty({ message: 'VALIDATE.BUDGET_CODE_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.BUDGET_CODE_ID.MUST_BE_UUID' })
  budgetCodeId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Costcenter/Subaccount',
  })
  @IsNotEmpty({ message: 'VALIDATE.COSTCENTER_SUBACCOUNT_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.COSTCENTER_SUBACCOUNT_ID.MUST_BE_UUID' })
  costcenterSubaccountId: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE.MUST_BE_STRING' })
  note?: string;

  @ApiProperty({
    type: Date,
    description: 'Thời gian có hiệu lực',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_START_DATE.IS_REQUIRED' })
  // @Transform(({ value }) => new Date(value).toDateString())
  @IsDateString()
  effectiveStartDate: Date;

  @ApiProperty({
    type: Date,
    description: 'Thời gian hết hiệu lực',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_END_DATE.IS_REQUIRED' })
  // @Transform(({ value }) => new Date(value).toDateString())
  @IsDateString()
  effectiveEndDate: Date;

  @ApiProperty({
    description: 'Tổng giá trị',
    type: Number,
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.TOTAL_VALUE.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.TOTAL_VALUE.MUST_BE_NUMBER' })
  @Min(0)
  totalValue: number;

  @ApiProperty({
    description: 'Trạng thái',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isLock: boolean;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú 2',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE_2.MUST_BE_STRING' })
  note2?: string;

  code?: string;
  createdAt?: string;
  updatedAt?: string;
  businessOwnerCodes?: string[];
  sectorCodes?: string[];
  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
  rowNumber?: number;
}
