import { ArrayMinSize, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, IsUUID, Min } from 'class-validator';
import { GetUuidDto } from '../../../domain/dtos/get-uuid.dto';
import { ApiProperty } from '@nestjs/swagger';

export class LockUnlockBudgetDto {
  @ApiProperty({
    type: [String],
    required: true,
  })
  @ArrayMinSize(1)
  @IsArray()
  @IsUUID(4, { each: true })
  budgetIds: string[];

  @ApiProperty({
    description: 'Trạng thái',
    type: Boolean,
    required: true,
    default: false,
  })
  @IsBoolean()
  isLock: boolean;

  businessOwnerCodes?: string[];
  sectorCodes?: string[];
  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
}
