import { ApiProperty, OmitType } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateBudgetDto } from './create-budget.dto';
import { Type } from 'class-transformer';

export class CreateBudgetOpexDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Biểu mẫu',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.FORM.MUST_BE_STRING' })
  form?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON><PERSON><PERSON> gi<PERSON>i chi phí/<PERSON>hi<PERSON><PERSON> vụ',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.OPERATIONS.MUST_BE_STRING' })
  operations?: string;

  @ApiProperty({
    type: CreateBudgetDto,
    description: 'Ngân sách',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.BUDGET.IS_REQUIRED' })
  @ValidateNested()
  @Type(() => CreateBudgetDto)
  budgetData: CreateBudgetDto;

  isNeedGen?: boolean;

  createdAt?: string;
  updatedAt?: string;
}
