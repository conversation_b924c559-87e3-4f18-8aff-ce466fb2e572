import { ApiProperty, OmitType } from '@nestjs/swagger';
import { CreateBudgetOpexDto } from './create-budget-opex.dto';
import { UpdateBudgetDto } from './update-budget.dto';
import { IsNotEmpty, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateBudgetOpexDto extends OmitType(CreateBudgetOpexDto, [
  'budgetData',
]) {
  @ApiProperty({
    type: UpdateBudgetDto,
    description: 'Ngân sách',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.BUDGET.IS_REQUIRED' })
  @ValidateNested()
  @Type(() => UpdateBudgetDto)
  budgetData: UpdateBudgetDto;
}
