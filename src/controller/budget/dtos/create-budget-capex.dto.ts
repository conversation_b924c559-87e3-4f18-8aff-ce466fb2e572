import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateBudgetInvestmentDto } from '../../budget-investment/dtos/create-budget-investment.dto';
import { CreateBudgetDto } from './create-budget.dto';

export class CreateBudgetCapexDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Thời gian sử dụng',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.USE_TIME.MUST_BE_STRING' })
  useTime?: string;

  @ApiProperty({
    type: Date,
    description: 'Thời điểm bắt đầu',
    required: true,
  })
  @IsOptional()
  // @Transform(({ value }) => new Date(value).toDateString())
  @IsDateString()
  startDate?: Date;

  @ApiProperty({
    type: Date,
    description: 'Thời điểm nghiệm thu dự kiến',
    required: false,
  })
  @IsOptional()
  // @Transform(({ value }) => new Date(value).toDateString())
  @IsDateString()
  expectedAcceptanceTime?: Date;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Phân loại',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.CLASSIFY.MUST_BE_STRING' })
  classify?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Mức độ ưu tiên',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.PRIORITY.MUST_BE_STRING' })
  priority?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Mục đích đầu tư',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.INVESTMENT_PURPOSE.MUST_BE_STRING' })
  investmentPurpose?: string;

  @ApiProperty({
    type: Array,
    description: 'Files đính kèm',
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.FILES.MUST_BE_ARRAY' })
  @IsString({ each: true, message: 'VALIDATE.FILES.MUST_BE_STRING' })
  files?: string[];

  @ApiProperty({
    type: CreateBudgetDto,
    description: 'Ngân sách',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.BUDGET.IS_REQUIRED' })
  @ValidateNested()
  @Type(() => CreateBudgetDto)
  budgetData: CreateBudgetDto;

  @ApiProperty({
    type: [CreateBudgetInvestmentDto],
    description: 'Hạng mục đầu tư',
    required: true,
  })
  @IsArray({ message: 'VALIDATE.INVESTMENTS.MUST_BE_ARRAY' })
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateBudgetInvestmentDto)
  investments: CreateBudgetInvestmentDto[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'Ghi chú 2',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.NOTE_2.MUST_BE_STRING' })
  note2?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Dự án trọng điểm',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.KEY_PROJECT.MUST_BE_STRING' })
  keyProject?: string;

  isNeedGen?: boolean;

  createdAt?: string;
  updatedAt?: string;
}
