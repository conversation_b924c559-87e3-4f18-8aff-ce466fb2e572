import { ApiProperty, OmitType } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
} from 'class-validator';
import {
  EBudgetType,
  EFilterCurrencyReportBudget,
} from '../../../domain/config/enums/budget.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class ReportBudgetDto extends OmitType(PaginationDto, ['from', 'to']) {
  @ApiProperty({
    type: EFilterCurrencyReportBudget,
    required: true,
  })
  @IsEnum(EFilterCurrencyReportBudget)
  eCurrency: EFilterCurrencyReportBudget;

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  sectorIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessOwnerIds?: string[];

  @ApiProperty({
    type: EBudgetType,
    required: true,
    default: EBudgetType.OPEX,
  })
  @IsEnum(EBudgetType)
  budgetType: EBudgetType;

  @ApiProperty({
    type: String,
    required: true,
    example: '2023-12',
    description:
      'Tháng và năm bắt đầu đối tượng cần tìm kiếm (định dạng YYYY-MM)',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{4}-(0[1-9]|1[0-2])$/, {
    message: 'VALIDATE.TO.INVALID_FORMAT',
  })
  period: string;

  from?: string;
  to?: string;

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  budgetCodeIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  costCenterIds?: string[];

  businessOwnerCodes?: string[];
  fucntionUnitCodes?: string[];
}
