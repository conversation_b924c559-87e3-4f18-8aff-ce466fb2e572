import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { BudgetCodeModel } from '../../../domain/model/budget-code.model';
import { CostcenterSubaccountModel } from '../../../domain/model/costcenter-subaccount.model';
import { MaterialModel } from '../../../domain/model/material.model';
import { MaterialGroupModel } from '../../../domain/model/material-group.model';
import { SupplierModel } from '../../../domain/model/supplier.model';
import { CurrencyUnitModel } from '../../../domain/model/currency-unit.model';
import { MeasureModel } from '../../../domain/model/measure.model';
import { TaxCodeModel } from '../../../domain/model/tax-code.model';
import { WarehouseModel } from '../../../domain/model/warehouse.model';

export class HandleCopyExcelDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Budget Code',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.BUDGET_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.BUDGET_CODE.MUST_BE_STRING' })
  budgetCode: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Cost Center',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.COST_CENTER.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.COST_CENTER.MUST_BE_STRING' })
  costCenter: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Material Code',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_CODE.MUST_BE_STRING' })
  materialCode: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Material Group',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_GROUP.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_GROUP.MUST_BE_STRING' })
  materialGroup: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Supplier',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.SUPPLIER.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.SUPPLIER.MUST_BE_STRING' })
  supplier?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Currency',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.CURRENCY.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CURRENCY.MUST_BE_STRING' })
  currency: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Measure Code',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.MEASURE_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MEASURE_CODE.MUST_BE_STRING' })
  measureCode?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Tax Code',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.TAX_CODE_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.TAX_CODE_CODE.MUST_BE_STRING' })
  taxCodeCode?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Warehouse Code',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.WAREHOUSE_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.WAREHOUSE_CODE.MUST_BE_STRING' })
  warehouseCode?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Key',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.KEY.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.KEY.MUST_BE_STRING' })
  key: string;
}

export class HandleCopyExcelListDto {
  @ApiProperty({
    type: [HandleCopyExcelDto],
    required: true,
  })
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => HandleCopyExcelDto)
  data: HandleCopyExcelDto[];
}

export interface IResponseHandleCopyExcel {
  budgetCode?: BudgetCodeModel;
  costCenter?: CostcenterSubaccountModel;
  materialCode?: MaterialModel;
  materialGroup?: MaterialGroupModel;
  supplier?: SupplierModel;
  currency?: CurrencyUnitModel;
  measure?: MeasureModel;
  taxCode?: TaxCodeModel;
  warehouse?: WarehouseModel;
  key?: string;
}
