import { ApiProperty, OmitType } from '@nestjs/swagger';
import { CreateBudgetCapexDto } from './create-budget-capex.dto';
import { UpdateBudgetDto } from './update-budget.dto';
import {
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';
import { UpdateBudgetInvestmentDto } from '../../budget-investment/dtos/update-budget-investment.dto';
import { Type } from 'class-transformer';

export class UpdateBudgetCapexDto extends OmitType(CreateBudgetCapexDto, [
  'budgetData',
  'investments',
]) {
  @ApiProperty({
    type: UpdateBudgetDto,
    description: '<PERSON>ân sách',
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.BUDGET.IS_REQUIRED' })
  @ValidateNested()
  @Type(() => UpdateBudgetDto)
  budgetData: UpdateBudgetDto;

  @ApiProperty({
    type: [UpdateBudgetInvestmentDto],
    description: 'Hạng mục đầu tư',
    required: true,
  })
  @IsArray({ message: 'VALIDATE.INVESTMENTS.MUST_BE_ARRAY' })
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => UpdateBudgetInvestmentDto)
  investments: UpdateBudgetInvestmentDto[];
}
