import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import {
  EBudgetCreateType,
  EBudgetType,
} from '../../../domain/config/enums/budget.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { removeUnicode } from '../../../utils/common';

export class GetBudgetListDto extends OmitType(PaginationDto, ['from', 'to']) {
  @ApiProperty({
    type: [EBudgetCreateType],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EBudgetCreateType, { each: true })
  createTypes?: EBudgetCreateType[];

  @ApiProperty({
    type: EBudgetType,
    required: true,
    default: EBudgetType.OPEX,
  })
  @IsEnum(EBudgetType)
  budgetType: EBudgetType;

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  adjustBudgetIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  currencyUnitIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  budgetCodeIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  costcenterSubaccountIds?: string[];

  @ApiProperty({
    description: 'Tổng chi phí vận chuyển',
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.MIN_VALUE.IS_REQUIRED' })
  @IsNumberString(
    {},
    { message: 'VALIDATE.TRANSPORTATION_COST.MUST_BE_NUMBER_STRING' },
  )
  minValue?: number;

  @ApiProperty({
    description: 'Tổng chi phí vận chuyển',
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.MAX_VALUE.IS_REQUIRED' })
  @IsNumberString(
    {},
    { message: 'VALIDATE.TRANSPORTATION_COST.MUST_BE_NUMBER_STRING' },
  )
  maxValue?: number;

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31T17:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_START.IS_REQUIRED' })
  effectiveStartDate?: string;

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31T17:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_END.IS_REQUIRED' })
  effectiveEndDate?: string;

  @ApiPropertyOptional({ type: String, description: 'Nội dung muốn tìm kiếm' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) =>
    value ? '%' + removeUnicode(value.trim()) + '%' : '',
  )
  searchID?: string;

  @ApiPropertyOptional({ type: String, description: 'Nội dung muốn tìm kiếm' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) =>
    value ? '%' + removeUnicode(value.trim()) + '%' : '',
  )
  searchClassify?: string;

  @ApiPropertyOptional({
    type: Number,
    example: 1,
    description: 'Trạng thái',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? +value == 1 : null))
  isLock?: boolean;

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31T17:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  @IsNotEmpty({ message: 'VALIDATE.CREATED_AT.IS_REQUIRED' })
  createdAt?: string;

  businessOwnerCodes?: string[];
  sectorCodes?: string[];
  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
  costCodes?: string[];
}
