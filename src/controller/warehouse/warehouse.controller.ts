import {
  Body,
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Patch,
  Param,
  Get,
  Delete,
  Query,
  Response,
} from '@nestjs/common';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { WarehouseUsecases } from '../../usecases/warehouse.usecases';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EWarehousePermission } from '../../utils/constants/permission.enum';
import { CreateWarehouseDto } from './dtos/create-warehouse.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { UpdateWarehouseDto } from './dtos/update-warehouse.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { GetDetailWarehouseDto } from './dtos/get-detail-warehouse.dto';
import { GetWarehouseListDto } from './dtos/get-warehouse-list.dto';
import { DeleteWarehouseDto } from './dtos/delete-warehouse.dto';

@Controller('/warehouse')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Warehouse')
export class WarehouseController {
  constructor(private readonly warehouseUsecases: WarehouseUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EWarehousePermission.CREATE]))
  async create(
    @Body() data: CreateWarehouseDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.warehouseUsecases.createWarehouse(data, jwtPayload);
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EWarehousePermission.EDIT]))
  async update(
    @Body() data: UpdateWarehouseDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.warehouseUsecases.updateWarehouse(
      param.id,
      data,
      jwtPayload,
    );
  }

  @Get(':id/detail')
  async getDetail(
    @Param() param: GetDetailWarehouseDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.warehouseUsecases.getDetailWarehouse(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EWarehousePermission.VIEW]))
  async getList(
    @Query() param: GetWarehouseListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.warehouseUsecases.getWarehouses(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EWarehousePermission.DELETE]))
  async delete(
    @Param() param: DeleteWarehouseDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.warehouseUsecases.deleteWarehouse(param.id, jwtPayload);
  }

  @Get('/export-warehouse')
  @UseGuards(NewPermissionGuard([EWarehousePermission.EXPORT]))
  async exportWarehouse(
    @Query() param: GetWarehouseListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.warehouseUsecases.exportWarehouse(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }
}
