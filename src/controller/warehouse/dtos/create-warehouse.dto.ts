import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON>yMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EWarehouseStatus } from '../../../domain/config/enums/warehouse.enum';
import { SectorModel } from '../../../domain/model/sector.model';

export class CreateWarehouseDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code of warehouse',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of warehouse',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of warehouse',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EWarehouseStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EWarehouseStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EWarehouseStatus;

  @ApiProperty({
    type: [String],
    required: true,
    description: 'Ngành',
  })
  @ArrayMinSize(1)
  @IsArray({ message: 'VALIDATE.SECTOR_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.SECTOR_ID.MUST_BE_UUID',
    each: true,
  })
  sectorIds: string[];

  sectors?: SectorModel[];
  createdBy?: object;
}
