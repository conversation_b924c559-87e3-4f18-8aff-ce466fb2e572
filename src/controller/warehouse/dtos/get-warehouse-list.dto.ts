import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { EWarehouseStatus } from '../../../domain/config/enums/warehouse.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetWarehouseListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EWarehouseStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EWarehouseStatus, { each: true })
  statuses?: EWarehouseStatus[];

  ids?: string[];
  sectorCodes?: string[];
}
