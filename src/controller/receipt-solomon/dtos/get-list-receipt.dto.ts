import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import {
  ArrayMinSize,
  IsArray,
  IsIn,
  IsInt,
  IsNumber,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';

export class GetListReceiptDto extends OmitType(PaginationDto, ['from', 'to']) {
  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  budgetCodeIds?: string[];

  @ApiProperty({ type: [Number], required: false })
  @IsOptional()
  @Type(() => Number)
  @IsArray()
  @IsNumber({}, { each: true })
  poIds?: number[];
}
