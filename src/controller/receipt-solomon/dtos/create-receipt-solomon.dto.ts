import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  Min,
  ValidateNested,
} from 'class-validator';

export class CreateReceiptSolomonDto {
  @ApiProperty({
    type: Number,
    required: true,
    description: 'Mã PO',
  })
  @IsNumber({}, { message: 'VALIDATE.PO_ID.MUST_BE_NUMBER' })
  @IsNotEmpty({ message: 'VALIDATE.PO_ID.IS_REQUIRED' })
  poId: number;

  @ApiProperty({
    type: Date,
    required: true,
    description: 'Thời gian phát sinh receipt',
    example: '2024-01-01',
  })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'VALIDATE.RECEIPT_CREATED_AT.MUST_BE_YYYY-MM-DD',
  })
  @IsDateString({}, { message: 'VALIDATE.RECEIPT_CREATED_AT.MUST_BE_DATE' })
  receiptCreatedAt: Date; //Thời gian bắt đầu

  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã số receipt',
  })
  @IsNotEmpty({ message: 'VALIDATE.RECEIPT_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.RECEIPT_CODE.MUST_BE_STRING' })
  receiptCode: string;

  @ApiProperty({
    type: Number,
    required: true,
    description: 'Số tiền',
  })
  @IsNotEmpty({ message: 'VALIDATE.TOTAL_AMOUNT.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.TOTAL_AMOUNT.MUST_BE_NUMBER' })
  @Min(0, { message: 'VALIDATE.TOTAL_AMOUNT.MUST_BE_GREATER_THAN_0' })
  totalAmount: number;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã ngân sách',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.BUDGET_CODE.MUST_BE_STRING' })
  budgetCode?: string;

  budgetCodeCode?: string;
  budgetCodeId?: string;

  errorStatus?: string;
  errorDetails?: string | object[];
}

export class CreateReceiptDto {
  @ApiProperty({
    type: [CreateReceiptSolomonDto],
  })
  @ArrayMinSize(1)
  @IsArray({ message: 'VALIDATE.DATA.MUST_BE_ARRAY' })
  @ValidateNested({ each: true })
  @Type(() => CreateReceiptSolomonDto)
  receipts: CreateReceiptSolomonDto[];
}
