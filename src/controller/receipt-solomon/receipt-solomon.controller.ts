import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
  Response,
  HttpStatus,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ReceiptSolomonUsecases } from '../../usecases/receipt-solomon.usecases';
import { AuthBasicGuard } from '../../utils/guard/auth-basic.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import {
  CreateReceiptDto,
  CreateReceiptSolomonDto,
} from './dtos/create-receipt-solomon.dto';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { GetListReceiptDto } from './dtos/get-list-receipt.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';

@Controller('/solomon')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiTags('Receipt Solomon')
export class ReceiptSolomonController {
  constructor(
    private readonly receiptSolomonUsecases: ReceiptSolomonUsecases,
  ) {}

  @ApiBasicAuth('BasicAuth')
  @UseGuards(AuthBasicGuard)
  @Post('/create-receipt')
  async createReceipt(@Body() conditions: CreateReceiptDto, @Response() res) {
    const now = Date.now();

    const results = await this.receiptSolomonUsecases.createReceipt(
      conditions.receipts,
    );

    const errors =
      results?.filter((item) => item.errorStatus === 'FAILED') || [];

    const success =
      results?.filter((item) => item.errorStatus === 'SUCCESSFUL') || [];

    return res
      .status(
        errors.length && success.length
          ? 207
          : !errors.length
            ? HttpStatus.OK
            : HttpStatus.BAD_REQUEST,
      )
      .send({
        data: results,
        message: null,
        duration: `${Date.now() - now}ms`,
      });
  }

  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard)
  @Get('/list')
  async getList(@Query() param: GetListReceiptDto) {
    return await this.receiptSolomonUsecases.getReceipts(param);
  }

  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard)
  @Get(':id/detail')
  async getDetailReceipt(@Query() param: GetUuidDto) {
    return await this.receiptSolomonUsecases.getDetailReceipt(param);
  }
}
