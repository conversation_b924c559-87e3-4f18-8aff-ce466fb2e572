import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Request,
  Response,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PriceInformationRecordModel } from '../../domain/model/price_information_record.model';
import { priceInformationRecordUsecases } from '../../usecases/price_information_record.usecases';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { GetPriceInformationRecordDto } from './dtos/get-all-price-information-record.dto';
import {
  PriceInformationRecordDto,
  UpdatePirStatusDto,
  UpdatePriceInformationRecordDto,
} from './dtos/price-information-record.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { GetPIRListByIdsDto } from './dtos/get-price-information-record-list-by-ids.dto';

@Controller('/price-information-records')
@UseInterceptors(TransformationInterceptor, TransactionInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('PriceInformationRecord')
export class PriceInformationRecordController {
  constructor(
    private readonly _priceInformationRecordUsecases: priceInformationRecordUsecases,
  ) {}

  @Get()
  async findAll(
    @Query() paginationDto: GetPriceInformationRecordDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ): Promise<ResponseDto<PriceInformationRecordModel>> {
    return this._priceInformationRecordUsecases.findAll(
      paginationDto,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('export')
  async exportPir(
    @Query() paginationDto: GetPriceInformationRecordDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
    @Response() res,
  ) {
    const result = await this._priceInformationRecordUsecases.exportPir(
      paginationDto,
      jwtPayload,
      req.headers['authorization'],
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Get(':id')
  async findPurchaseRequestById(
    @Param('id') id: number,
    @Request() req,
  ): Promise<any> {
    return this._priceInformationRecordUsecases.findOne(
      Number(id),
      req.headers['authorization'],
    );
  }

  @Post()
  async createPIR(
    @Body() price: PriceInformationRecordDto,
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ): Promise<PriceInformationRecordModel> {
    return await this._priceInformationRecordUsecases.create(
      price,
      req.headers['authorization'],
      jwtPayload,
    );
  }

  @Put(':id')
  async updatePIR(
    @Param('id') id: number,
    @Body() updatePrice: UpdatePriceInformationRecordDto,
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ): Promise<PriceInformationRecordModel> {
    return await this._priceInformationRecordUsecases.updatePriceInformationRecord(
      id,
      updatePrice,
      req.headers['authorization'],
      jwtPayload,
    );
  }

  @Patch(':id/status')
  async updateStatus(
    @Param('id') id: number,
    @Body() updateStatus: UpdatePirStatusDto,
  ): Promise<PriceInformationRecordModel> {
    return await this._priceInformationRecordUsecases.updateStatus(
      id,
      updateStatus,
    );
  }

  @Post('import')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  async importPriceInformationRecords(
    @UploadedFile() file: Express.Multer.File,
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ): Promise<string> {
    if (!file) {
      throw new BadRequestException('File is required');
    }
    return await this._priceInformationRecordUsecases.importPriceInformationRecords(
      file,
      req.headers['authorization'],
      jwtPayload,
    );
  }

  @Post('migration-relation-pir')
  async migrationRelationPIR(
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ): Promise<void> {
    return await this._priceInformationRecordUsecases.migrationRelationPIR(
      jwtPayload,
    );
  }

  ///For validate detail PR PO
  @Post('/list-by-ids')
  async getListByIds(
    @Body() param: GetPIRListByIdsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this._priceInformationRecordUsecases.getListByIds(
      param,
      jwtPayload,
    );
  }
}
