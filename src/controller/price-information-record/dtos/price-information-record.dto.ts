import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsString,
  IsDate,
  IsEnum,
  IsArray,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { BusinessUnitModel } from '../../../domain/model/business-unit.model';

export enum Status {
  Active = 'Active',
  Inactive = 'Inactive',
}

export class PriceInformationRecordDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng nhập Mã NCC' })
  vendorCodeId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng nhập Mã vật tư' })
  materialCodeId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng chọn tổ chức mua hàng' })
  purchaseOrganizationId: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  plantId?: string;

  @ApiProperty({ required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ message: 'Vui lòng chọn loại info' })
  infoType?: string[];

  @ApiProperty({ required: true })
  @IsString()
  @IsOptional()
  purchaseUnit?: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng nhập leadtime của NCC' })
  vendorLeadtime: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng chọn nhóm mua hàng' })
  purchaseGroupId: string;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty({ message: 'Vui lòng nhập số lượng thường mua hàng' })
  regularPurchaseQuantity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty({ message: 'Vui lòng nhập số lượng đặt hàng tối thiểu' })
  minimumOrderQuantity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty({ message: 'Vui lòng nhập dung sai trên theo %' })
  upperTolerance: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty({ message: 'Vui lòng nhập dung sai dưới theo %' })
  lowerTolerance: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty({ message: 'Vui lòng nhập giá tiền mua hàng' })
  purchasePrice: number;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng chọn đơn vị tiền tệ' })
  currencyId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng nhập trên bao nhiêu đơn vị mua' })
  overPurchaseUnit: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  unitOfMeasurement?: string;

  @ApiProperty({ required: true })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty({ message: 'Vui lòng nhập thời gian có hiệu lực' })
  effectiveDate: Date;

  @ApiProperty({ required: true })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty({ message: 'Vui lòng nhập thời gian hết hiệu lực' })
  expirationDate: Date;

  @ApiProperty({ enum: Status, required: true })
  @IsEnum(Status, { message: 'Vui lòng chọn trạng thái' })
  @IsNotEmpty({ message: 'Vui lòng chọn trạng thái' })
  status: Status;

  @ApiProperty({ required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ message: 'Vui lòng chọn loại Đơn vị kinh doanh' })
  businessUnitIds?: string[];

  businessUnits?: BusinessUnitModel[];
}

export class UpdatePriceInformationRecordDto extends PriceInformationRecordDto {}

export class UpdatePirStatusDto {
  @ApiProperty({ enum: Status })
  @IsEnum(Status)
  @IsNotEmpty()
  status: Status;
}
