import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { Status } from './price-information-record.dto';

export class GetPriceInformationRecordDto extends PaginationDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  vendorCodeIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  materialCodeIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  purchaseOrganizationIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  infoType?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  purchaseGroupIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  currencyIds?: string[];

  @ApiProperty({
    type: [Status],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(Status, { each: true })
  statuses?: Status[];

  sectorCodes?: string[];
  functionUnitCodes?: string[];
}
