import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { FunctionUnitUsecases } from '../../usecases/function-unit.usecases';
import { EFunctionUnitPermission } from '../../utils/constants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { CreateFunctionUnitDto } from './dtos/create-function-unit.dto';
import { DeleteFunctionUnitDto } from './dtos/delete-function-unit.dto';
import { GetDetailFunctionUnitDto } from './dtos/get-detail-function-unit.dto';
import { GetFunctionUnitListDto } from './dtos/get-function-unit-list.dto';
import { UpdateFunctionUnitDto } from './dtos/update-function-unit.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';

@Controller('/function-unit')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Function Unit')
export class FunctionUnitController {
  constructor(private readonly functionUnitUsecases: FunctionUnitUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EFunctionUnitPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(@Body() data: CreateFunctionUnitDto, @Request() req) {
    return await this.functionUnitUsecases.createFunctionUnit(
      data,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EFunctionUnitPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailFunctionUnitDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.functionUnitUsecases.getDetailFunctionUnit(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EFunctionUnitPermission.VIEW]))
  async getList(
    @Query() param: GetFunctionUnitListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.functionUnitUsecases.getFunctionUnits(param, jwtPayload);
  }

  @Delete(':id/delete')
  @UseGuards(NewPermissionGuard([EFunctionUnitPermission.DELETE]))
  async delete(
    @Param() param: DeleteFunctionUnitDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    await this.functionUnitUsecases.deleteFunctionUnit(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
    return { message: 'Successfully!!!' };
  }

  @Put(':id/update')
  @UseGuards(NewPermissionGuard([EFunctionUnitPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param() param: GetUuidDto,
    @Body() data: UpdateFunctionUnitDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.functionUnitUsecases.updateFunctionUnit(
      data,
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
