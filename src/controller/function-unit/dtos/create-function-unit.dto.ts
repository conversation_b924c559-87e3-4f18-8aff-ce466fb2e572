import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { EBusinessUnitStatus } from '../../../domain/config/enums/business-unit.enum';
import { EDepartmentStatus } from '../../../domain/config/enums/department.enum';
import { EFunctionUnitStatus } from '../../../domain/config/enums/function-unit.enum';

export class CreateFunctionUnitDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'ID',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên phòng ban',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Mô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EFunctionUnitStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EFunctionUnitStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EFunctionUnitStatus;
}
