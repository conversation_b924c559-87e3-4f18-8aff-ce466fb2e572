import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { EPositionStatus } from '../../../domain/config/enums/position.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetPositionListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EPositionStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPositionStatus, { each: true })
  statuses?: EPositionStatus[];

  codes?: string[];
}
