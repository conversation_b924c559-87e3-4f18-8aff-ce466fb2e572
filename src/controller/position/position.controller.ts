import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { PositionUsecases } from '../../usecases/position.usecases';
import { EPositionPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreatePositionDto } from './dtos/create-position.dto';
import { DeletePositionDto } from './dtos/delete-position.dto';
import { GetDetailPositionDto } from './dtos/get-detail-position.dto';
import { GetPositionListDto } from './dtos/get-position-list.dto';
import { UpdatePositionDto } from './dtos/update-position.dto';

@Controller('/position')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Position')
export class PositionController {
  constructor(private readonly positionUsecases: PositionUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EPositionPermission.CREATE]))
  async create(@Body() data: CreatePositionDto, @Request() req) {
    return await this.positionUsecases.createPosition(
      data,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EPositionPermission.EDIT]))
  async update(
    @Body() data: UpdatePositionDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.positionUsecases.updatePosition(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EPositionPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailPositionDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.positionUsecases.getDetailPosition(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EPositionPermission.VIEW]))
  async getList(
    @Query() param: GetPositionListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.positionUsecases.getPositions(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EPositionPermission.DELETE]))
  async delete(
    @Param() param: DeletePositionDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.positionUsecases.deletePosition(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
