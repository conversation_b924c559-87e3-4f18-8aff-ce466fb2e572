import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { PlantUsecases } from '../../usecases/plant.usecases';
import { EPlantPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreatePlantDto } from './dtos/create-plant.dto';
import { DeletePlantDto } from './dtos/delete-plant.dto';
import { GetDetailPlantDto } from './dtos/get-detail-plant.dto';
import { GetPlantListDto } from './dtos/get-plant-list.dto';
import { UpdatePlantDto } from './dtos/update-plant.dto';

@Controller('/plant')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Plant')
export class PlantController {
  constructor(private readonly plantUsecases: PlantUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EPlantPermission.CREATE]))
  async create(@Body() data: CreatePlantDto, @NewAuthUser() jwtPayload: any) {
    return await this.plantUsecases.createPlant(data, jwtPayload);
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EPlantPermission.EDIT]))
  async update(
    @Body() data: UpdatePlantDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.plantUsecases.updatePlant(param.id, data, jwtPayload);
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EPlantPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailPlantDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.plantUsecases.getPlantDetail(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EPlantPermission.VIEW]))
  async getList(
    @Query() param: GetPlantListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.plantUsecases.getPlants(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EPlantPermission.DELETE]))
  async delete(@Param() param: DeletePlantDto, @NewAuthUser() jwtPayload: any) {
    return await this.plantUsecases.deletePlant(param.id, jwtPayload);
  }
}
