import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { ESectorStatus } from '../../../domain/config/enums/sector.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import {
  EMaterialStatus,
  EMaterialTypeStatus,
} from '../../../domain/config/enums/material.enum';
import { EPurchasingDepartmentStatus } from '../../../domain/config/enums/purchasing.enum';
import { EPlantStatus } from '../../../domain/config/enums/plant.enum';

export class GetPlantListDto extends OmitType(PaginationDto, ['from', 'to']) {
  @ApiProperty({
    type: [EPlantStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPlantStatus, { each: true })
  statuses?: EPlantStatus[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  sectorIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];

  sectorCodes?: string[];

  codes?: string[];
}
