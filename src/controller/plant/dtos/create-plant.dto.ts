import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EPurchasingDepartmentStatus } from '../../../domain/config/enums/purchasing.enum';
import { EPlantStatus } from '../../../domain/config/enums/plant.enum';

export class CreatePlantDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON>ô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Mảng',
  })
  @IsNotEmpty({ message: 'VALIDATE.SECTOR_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.SECTOR_ID.MUST_BE_UUID' })
  sectorId: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EPlantStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EPlantStatus, {
    message: 'VALIDATE.STATUS.INVALID_VALUE',
  })
  status?: EPlantStatus;
}
