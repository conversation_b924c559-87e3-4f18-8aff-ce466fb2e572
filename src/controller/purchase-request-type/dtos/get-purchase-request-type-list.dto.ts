import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import {
  EPurchaseRequestStatus,
  EPurchaseRequestTypeForm,
} from '../../../domain/config/enums/purchasing.enum';

export class GetPurchaseRequestTypeListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EPurchaseRequestStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPurchaseRequestStatus, { each: true })
  statuses?: EPurchaseRequestStatus[];

  @ApiProperty({
    type: [EPurchaseRequestTypeForm],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPurchaseRequestTypeForm, { each: true })
  forms?: EPurchaseRequestTypeForm[];

  codes?: string[];
}
