import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import {
  EPurchaseRequestStatus,
  EPurchaseRequestTypeForm,
} from '../../../domain/config/enums/purchasing.enum';

export class CreatePurchaseRequestTypeDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Mã',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON><PERSON> tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EPurchaseRequestStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EPurchaseRequestStatus, {
    message: 'VALIDATE.STATUS.INVALID_VALUE',
  })
  status?: EPurchaseRequestStatus;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Hình thức',
    default: EPurchaseRequestTypeForm.NORMAL,
  })
  @IsOptional()
  @IsEnum(EPurchaseRequestTypeForm, {
    message: 'VALIDATE.FORM.INVALID_VALUE',
  })
  form?: EPurchaseRequestTypeForm;
}
