import {
  Body,
  Controller,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Get,
  Query,
  Delete,
} from '@nestjs/common';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { CreatePurchaseRequestTypeDto } from './dtos/create-purchase-request-type.dto';
import { UpdatePurchaseRequestTypeDto } from './dtos/update-purchase-request-type.dto';
import { GetPurchaseRequestTypeListDto } from './dtos/get-purchase-request-type-list.dto';
import { DeletePurchaseRequestTypeDto } from './dtos/delete-purchase-request-type.dto';
import { GetDetailPurchaseRequestTypeDto } from './dtos/get-detail-purchase-request-type.dto';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EPurchaseRequestTypePermission } from '../../utils/constants/permission.enum';
import { PurchaseRequestTypeUsecases } from '../../usecases/purchase-request-type.usecases';

@Controller('/purchase-request-type')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Purchase Request Type')
export class PrTypeController {
  constructor(
    private readonly _purchaseRequestTypeUsecases: PurchaseRequestTypeUsecases,
  ) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EPurchaseRequestTypePermission.CREATE]))
  async create(
    @Body() data: CreatePurchaseRequestTypeDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this._purchaseRequestTypeUsecases.createPrType(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EPurchaseRequestTypePermission.EDIT]))
  async update(
    @Body() data: UpdatePurchaseRequestTypeDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this._purchaseRequestTypeUsecases.updatePrType(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EPurchaseRequestTypePermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailPurchaseRequestTypeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this._purchaseRequestTypeUsecases.getPrTypeDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EPurchaseRequestTypePermission.VIEW]))
  async getList(
    @Query() param: GetPurchaseRequestTypeListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this._purchaseRequestTypeUsecases.getPrTypes(
      param,
      jwtPayload,
    );
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EPurchaseRequestTypePermission.DELETE]))
  async delete(
    @Param() param: DeletePurchaseRequestTypeDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this._purchaseRequestTypeUsecases.deletePrType(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
