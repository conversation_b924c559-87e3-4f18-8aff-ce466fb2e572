import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { EPurchasingDepartmentStatus } from '../../../domain/config/enums/purchasing.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetPurchasingDepartmentListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EPurchasingDepartmentStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPurchasingDepartmentStatus, { each: true })
  statuses?: EPurchasingDepartmentStatus[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  sectorIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];

  sectorCodes?: string[];
  codes?: string[];
}
