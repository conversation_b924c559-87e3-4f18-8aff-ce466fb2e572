import {
  Body,
  Controller,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Get,
  Query,
  Delete,
  Response,
} from '@nestjs/common';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { PurchasingDepartmentUsecases } from '../../usecases/purchasing-department.usecases';
import { CreatePurchasingDepartmentDto } from './dtos/create-purchasing-department.dto';
import { UpdatePurchasingDepartmentDto } from './dtos/update-purchasing-department.dto';
import { GetDetailPurchasingDepartmentDto } from './dtos/get-detail-purchasing-department.dto';
import { GetPurchasingDepartmentListDto } from './dtos/get-purchasing-department-list.dto';
import { DeletePurchasingDepartmentDto } from './dtos/delete-purchasing-department.dto';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EPurchasingDepartmentPermission } from '../../utils/constants/permission.enum';
import { ListByCodesDto } from '../../domain/dtos/base-dto-by-codes.dto';

@Controller('/purchasing-department')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Purchasing Department')
export class PurchasingDepartmentController {
  constructor(
    private readonly purchasingDepartmentUsecases: PurchasingDepartmentUsecases,
  ) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EPurchasingDepartmentPermission.CREATE]))
  async create(
    @Body() data: CreatePurchasingDepartmentDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.purchasingDepartmentUsecases.createPurchasingDepartment(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EPurchasingDepartmentPermission.EDIT]))
  async update(
    @Body() data: UpdatePurchasingDepartmentDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.purchasingDepartmentUsecases.updatePurchasingDepartment(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EPurchasingDepartmentPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailPurchasingDepartmentDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.purchasingDepartmentUsecases.getPurchasingDepartmentDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EPurchasingDepartmentPermission.VIEW]))
  async getList(
    @Query() param: GetPurchasingDepartmentListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.purchasingDepartmentUsecases.getPurchasingDepartments(
      param,
      jwtPayload,
    );
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EPurchasingDepartmentPermission.DELETE]))
  async delete(
    @Param() param: DeletePurchasingDepartmentDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.purchasingDepartmentUsecases.deletePurchasingDepartment(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/list-by-codes')
  async listByCodes(
    @Body() data: ListByCodesDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.purchasingDepartmentUsecases.listByCodes(
      data.codes || [],
      jwtPayload,
    );
  }

  @Get('/export-purchasing-department')
  @UseGuards(NewPermissionGuard([EPurchasingDepartmentPermission.EXPORT]))
  async exportPurchasingDepartment(
    @Query() param: GetPurchasingDepartmentListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result =
      await this.purchasingDepartmentUsecases.exportPurchasingDepartment(
        param,
        jwtPayload,
      );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }
}
