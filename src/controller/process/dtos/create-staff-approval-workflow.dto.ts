import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON>yMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateIf,
} from 'class-validator';
import { EApprover } from '../../../domain/config/enums/approver.enum';
import { EReturnRuleApprove } from '../../../domain/config/enums/return-rule-approve.enum';
import { StaffModel } from '../../../domain/model/staff.model';

export class CreateStaffApprovalWorkflowDto {
  @ApiProperty({
    type: Number,
    required: true,
    description: 'Level 1,2,.....',
  })
  @IsNotEmpty({ message: 'VALIDATE.LEVEL.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.LEVEL.MUST_BE_NUMBER' })
  level: number;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    description: 'Receive Email',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.RECEIVE_EMAIL.MUST_BE_BOOLEAN' })
  receiveEmail: boolean;

  @ApiProperty({
    type: EApprover,
    enum: EApprover,
    required: true,
    description: 'Approver',
  })
  @IsNotEmpty({ message: 'VALIDATE.APPROVER.IS_REQUIRED' })
  @IsEnum(EApprover, { message: 'VALIDATE.APPROVER.MUST_BE_ENUM' })
  approver: EApprover;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Staff Id',
  })
  @ValidateIf((o) => o.approver === EApprover.ASSIGN)
  @IsNotEmpty({ message: 'VALIDATE.STAFF_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.STAFF_ID.MUST_BE_UUID' })
  staffId?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Position Id',
  })
  @ValidateIf((o) => o.approver === EApprover.POSITION)
  @IsNotEmpty({ message: 'VALIDATE.POSITION_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.POSITION_ID.MUST_BE_UUID' })
  positionId?: string;

  @ApiProperty({
    type: EReturnRuleApprove,
    enum: EReturnRuleApprove,
    required: true,
    description: 'Return Approver',
  })
  @IsNotEmpty({ message: 'VALIDATE.RETURN_RULE.IS_REQUIRED' })
  @IsEnum(EReturnRuleApprove, { message: 'VALIDATE.RETURN_RULE.MUST_BE_ENUM' })
  returnRule: EReturnRuleApprove;

  @ApiProperty({
    description: 'Accountant Approved',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.ACCOUNTANT_APPROVED.MUST_BE_BOOLEAN' })
  accountantApproved: boolean;

  @ApiProperty({
    description: 'Accountant Approved',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.ACCOUNTANT_APPROVED.MUST_BE_BOOLEAN' })
  allowSelect: boolean;

  @ApiProperty({
    description: 'Allow Edit',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.ALLOW_EDIT.MUST_BE_BOOLEAN' })
  allowEdit: boolean;

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.allowSelect)
  @IsNotEmpty({ message: 'VALIDATE.PROCESS_IDS.IS_REQUIRED' })
  @IsArray()
  @ArrayMinSize(1, { message: 'VALIDATE.PROCESS_ID.IS_REQUIRED' })
  @IsUUID(4, { each: true })
  selectedStaffIds?: string[];
  selectedStaffs?: StaffModel[];
  // @ApiProperty({
  //   type: String,
  //   required: true,
  //   description: 'Approval Workflow Id',
  // })
  // @IsNotEmpty({ message: 'VALIDATE.APPROVAL_WORKFLOW_ID.IS_REQUIRED' })
  // @IsUUID('4', { message: 'VALIDATE.APPROVAL_WORKFLOW_ID.MUST_BE_UUID' })
  approvalWorkflowId?: string;
}
