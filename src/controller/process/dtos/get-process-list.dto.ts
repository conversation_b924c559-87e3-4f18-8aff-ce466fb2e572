import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { EProcessType } from '../../../domain/config/enums/process-type.enum';

export class GetProcessListDto extends PaginationDto {
  @ApiProperty({
    type: [EStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EStatus, { each: true })
  statuses: EStatus[];

  @ApiProperty({
    type: [EProcessType],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EProcessType, { each: true })
  types: EProcessType[];
}
