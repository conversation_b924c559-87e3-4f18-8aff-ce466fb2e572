import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { CreateConditionDetailDto } from './create-condition-detail.dto';

export class CreateConditionDto {
  @ApiProperty({
    type: [CreateConditionDetailDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateConditionDetailDto)
  createConditionDetailDtos?: CreateConditionDetailDto[];

  processConditionId?: string;
  createdBy?: object;
  updatedBy?: object;
}
