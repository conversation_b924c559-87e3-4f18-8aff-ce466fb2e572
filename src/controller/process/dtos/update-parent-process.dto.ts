import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { CreateApprovalWorkflowDto } from './create-approval-workflow.dto';

export class UpdateParentProcessDto {
  @ApiProperty({ type: String, required: true, description: 'Name of Process' })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of Process',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description: string;

  @ApiProperty({
    type: EStatus,
    enum: EStatus,
    required: true,
    example: EStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EStatus, { message: 'VALIDATE.STATUS.MUST_BE_ENUM' })
  status: EStatus;

  @ApiProperty({
    type: [CreateApprovalWorkflowDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateApprovalWorkflowDto)
  createApprovalWorkflowDtos: CreateApprovalWorkflowDto[];
}
