import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { ProcessModel } from '../../../domain/model/process.model';
import { CreateStaffApprovalWorkflowDto } from './create-staff-approval-workflow.dto';

export class CreateApprovalWorkflowDto {
  @ApiProperty({ type: String, required: true, description: 'Name' })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: [CreateStaffApprovalWorkflowDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateStaffApprovalWorkflowDto)
  createStaffApprovalWorkflowDtos?: CreateStaffApprovalWorkflowDto[];

  @ApiProperty({
    description: 'Send Email To Creator',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.SEND_EMAIL_TO_CREATOR.MUST_BE_BOOLEAN' })
  sendEmailToCreator: boolean;

  @ApiProperty({
    type: [String],
    required: true,
  })
  @IsNotEmpty({ message: 'VALIDATE.PROCESS_IDS.IS_REQUIRED' })
  @IsArray()
  @ArrayMinSize(1, { message: 'VALIDATE.PROCESS_ID.IS_REQUIRED' })
  @IsUUID(4, { each: true })
  processIds: string[];

  processes?: ProcessModel[];

  parentProcessId?: string;
}
