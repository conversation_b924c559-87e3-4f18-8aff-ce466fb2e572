import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { EProcessType } from '../../../domain/config/enums/process-type.enum';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { CreateConditionDto } from './create-condition.dto';

export class CreateProcessDto {
  @ApiProperty({ type: String, required: true, description: 'Name of Process' })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of Process',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description: string;

  @ApiProperty({ type: String, required: false, description: 'Parent ID' })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.PARENT_ID.MUST_BE_UUID' })
  parentId?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    description: 'Inherit',
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.ALLOW_INHERIT.MUST_BE_BOOLEAN' })
  allowInherit: boolean;

  @ApiProperty({
    type: [CreateConditionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateConditionDto)
  createConditionDtos: CreateConditionDto[];

  @ApiProperty({
    type: EStatus,
    enum: EStatus,
    required: true,
    description: 'Status',
  })
  @IsNotEmpty({ message: 'VALIDATE.STATUS.IS_REQUIRED' })
  @IsEnum(EStatus, { message: 'VALIDATE.STATUS.MUST_BE_ENUM' })
  status: EStatus;

  @ApiProperty({
    type: EProcessType,
    enum: EProcessType,
    required: true,
    description: 'Type PR or PO',
  })
  @IsNotEmpty({ message: 'VALIDATE.TYPE.IS_REQUIRED' })
  @IsEnum(EProcessType, { message: 'VALIDATE.TYPE.MUST_BE_ENUM' })
  type?: EProcessType;

  path?: string;

  createdBy?: object;
  updatedBy?: object;
}
