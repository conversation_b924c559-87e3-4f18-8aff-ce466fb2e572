import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
  Min,
  registerDecorator,
  ValidateIf,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { EComparisonType } from '../../../domain/config/enums/comparision-type.enum';
import { EConditionType } from '../../../domain/config/enums/condition-type.enum';
import { BudgetCodeModel } from '../../../domain/model/budget-code.model';
import { BusinessUnitModel } from '../../../domain/model/business-unit.model';
import { CompanyModel } from '../../../domain/model/company.model';
import { CostcenterSubaccountModel } from '../../../domain/model/costcenter-subaccount.model';
import { DepartmentModel } from '../../../domain/model/department.model';
import { FunctionUnitModel } from '../../../domain/model/function-unit.model';
import { PlantModel } from '../../../domain/model/plant.model';
import { ProcessTypeModel } from '../../../domain/model/process-type.model';
import { PurchaseOrderTypeModel } from '../../../domain/model/purchase-order-type.model';
import { PurchaseRequestTypeModel } from '../../../domain/model/purchase-request-type.model';
import { SectorModel } from '../../../domain/model/sector.model';

const conditionComparisonMap: Record<EConditionType, EComparisonType[]> = {
  [EConditionType.SECTOR]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.COMPANY]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.BUSINESS_UNIT]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.DEPARTMENT]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.PR_TYPE]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.PO_TYPE]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.COST_CENTER]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.BUDGET_CODE]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.VALUE_PR]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.LESS_THAN,
    EComparisonType.LESS_THAN_OR_EQUAL,
    EComparisonType.GREATER_THAN,
    EComparisonType.GREATER_THAN_OR_EQUAL,
  ],
  [EConditionType.VALUE_PO]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.LESS_THAN,
    EComparisonType.LESS_THAN_OR_EQUAL,
    EComparisonType.GREATER_THAN,
    EComparisonType.GREATER_THAN_OR_EQUAL,
  ],
  [EConditionType.VALUE_BUDGET]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.LESS_THAN,
    EComparisonType.LESS_THAN_OR_EQUAL,
    EComparisonType.GREATER_THAN,
    EComparisonType.GREATER_THAN_OR_EQUAL,
  ],
  [EConditionType.BUDGET_OVERRUN]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.LESS_THAN,
    EComparisonType.LESS_THAN_OR_EQUAL,
    EComparisonType.GREATER_THAN,
    EComparisonType.GREATER_THAN_OR_EQUAL,
  ],
  [EConditionType.CHECK_BUDGET]: [EComparisonType.YES, EComparisonType.NO],
  [EConditionType.DIFFERENCE_AMOUNT]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.LESS_THAN,
    EComparisonType.LESS_THAN_OR_EQUAL,
    EComparisonType.GREATER_THAN,
    EComparisonType.GREATER_THAN_OR_EQUAL,
  ],
  [EConditionType.BUDGET_OVERRUN_RATE]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.LESS_THAN,
    EComparisonType.LESS_THAN_OR_EQUAL,
    EComparisonType.GREATER_THAN,
    EComparisonType.GREATER_THAN_OR_EQUAL,
  ],
  [EConditionType.PROCESS_TYPE]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.PLANT]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.FUNCTION_UNIT]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.INCLUSION,
    EComparisonType.EXCLUSION,
  ],
  [EConditionType.DIFFERENCE_AMOUNT_ALL_ITEMS]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.LESS_THAN,
    EComparisonType.LESS_THAN_OR_EQUAL,
    EComparisonType.GREATER_THAN,
    EComparisonType.GREATER_THAN_OR_EQUAL,
  ],
  [EConditionType.FIRST_BUDGET]: [
    EComparisonType.EQUAL,
    EComparisonType.NOT_EQUAL,
    EComparisonType.LESS_THAN,
    EComparisonType.LESS_THAN_OR_EQUAL,
    EComparisonType.GREATER_THAN,
    EComparisonType.GREATER_THAN_OR_EQUAL,
  ],
};

function IsValidComparisonType(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'isValidComparisonType',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const { type } = args.object as any;

          // Kiểm tra nếu type tồn tại trong map, thì so sánh với các giá trị hợp lệ
          if (type && conditionComparisonMap[type]) {
            return conditionComparisonMap[type].includes(value);
          }

          // Nếu không có trong map thì mặc định là hợp lệ
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `ComparisonType "${args.value}" is not valid for ConditionType "${args.object['type']}"`;
        },
      },
    });
  };
}

export class CreateConditionDetailDto {
  @ApiProperty({
    type: EConditionType,
    enum: EConditionType,
    required: true,
    description: 'Type of condition',
  })
  @IsNotEmpty({ message: 'VALIDATE.TYPE.IS_REQUIRED' })
  @IsEnum(EConditionType, { message: 'VALIDATE.TYPE.MUST_BE_ENUM' })
  type: EConditionType;

  @ApiProperty({
    type: EComparisonType,
    enum: EComparisonType,
    required: true,
    description: 'Comparison type of condition',
  })
  @IsNotEmpty({ message: 'VALIDATE.COMPARISON_TYPE.IS_REQUIRED' })
  @IsEnum(EComparisonType, { message: 'VALIDATE.COMPARISON_TYPE.MUST_BE_ENUM' })
  @IsValidComparisonType({ message: 'VALIDATE.COMPARISON_TYPE.IS_NOT_VALID' })
  comparisonType: EComparisonType;

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.SECTOR)
  @IsNotEmpty({ message: 'VALIDATE.SECTOR_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.SECTOR_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.SECTOR_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.SECTOR_ID.MUST_BE_UUID', each: true })
  sectorIds?: string[];
  sectors?: SectorModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.COMPANY)
  @IsNotEmpty({ message: 'VALIDATE.COMPANY_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.COMPANY_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.COMPANY_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.COMPANY_ID.MUST_BE_UUID', each: true })
  companyIds?: string[];
  companies?: CompanyModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.BUSINESS_UNIT)
  @IsNotEmpty({ message: 'VALIDATE.BU_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.BU_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.BU_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.BU_ID.MUST_BE_UUID', each: true })
  businessUnitIds?: string[];
  businessUnits?: BusinessUnitModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.DEPARTMENT)
  @IsNotEmpty({ message: 'VALIDATE.DEPARTMENT_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.DEPARTMENT_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.DEPARTMENT_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.DEPARTMENT_ID.MUST_BE_UUID', each: true })
  departmentIds?: string[];
  departments?: DepartmentModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.COST_CENTER)
  @IsNotEmpty({ message: 'VALIDATE.COST_CENTER_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.COST_CENTER_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.COST_CENTER_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.COST_CENTER_ID.MUST_BE_UUID', each: true })
  costCenterIds?: string[];
  costCenters?: CostcenterSubaccountModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.BUDGET_CODE)
  @IsNotEmpty({ message: 'VALIDATE.BUDGET_CODE_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.BUDGET_CODE_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.BUDGET_CODE_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.BUDGET_CODE_ID.MUST_BE_UUID', each: true })
  budgetCodeIds?: string[];
  budgetCodes?: BudgetCodeModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.PR_TYPE)
  @IsNotEmpty({ message: 'VALIDATE.PR_TYPE_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.PR_TYPE_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.PR_TYPE_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PR_TYPE_ID.MUST_BE_UUID', each: true })
  prTypeIds?: string[];
  prTypes?: PurchaseRequestTypeModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.PO_TYPE)
  @IsNotEmpty({ message: 'VALIDATE.PO_TYPE_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.PO_TYPE_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.PO_TYPE_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PO_TYPE_ID.MUST_BE_UUID', each: true })
  poTypeIds?: string[];
  poTypes?: PurchaseOrderTypeModel[];

  @ApiProperty({
    description: 'valuePR',
    type: Number,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.VALUE_PR)
  @IsNotEmpty({ message: 'VALIDATE.VALUE_PR.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.VALUE_PR.MUST_BE_NUMBER' })
  @Min(0)
  valuePR?: number;

  @ApiProperty({
    description: 'valuePO',
    type: Number,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.VALUE_PO)
  @IsNotEmpty({ message: 'VALIDATE.VALUE_PO.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.VALUE_PO.MUST_BE_NUMBER' })
  @Min(0)
  valuePO?: number;

  @ApiProperty({
    description: 'valueBudget',
    type: Number,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.VALUE_BUDGET)
  @IsNotEmpty({ message: 'VALIDATE.VALUE_BUDGET.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.VALUE_BUDGET.MUST_BE_NUMBER' })
  @Min(0)
  valueBudget?: number;

  @ApiProperty({
    description: 'Budget Overrun',
    type: Number,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.BUDGET_OVERRUN)
  @IsNotEmpty({ message: 'VALIDATE.BUDGET_OVERRUN.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.BUDGET_OVERRUN.MUST_BE_NUMBER' })
  @Min(0)
  budgetOverrun?: number;

  @ApiProperty({
    description: 'Difference amount',
    type: Number,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.DIFFERENCE_AMOUNT)
  @IsNotEmpty({ message: 'VALIDATE.DIFFERENCE_AMOUNT.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.DIFFERENCE_AMOUNT.MUST_BE_NUMBER' })
  @Min(0)
  differenceAmount?: number;

  @ApiProperty({
    description: 'Budget Overrun Rate',
    type: Number,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.BUDGET_OVERRUN_RATE)
  @IsNotEmpty({ message: 'VALIDATE.BUDGET_OVERRUN_RATE.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.BUDGET_OVERRUN_RATE.MUST_BE_NUMBER' })
  @Min(0)
  budgetOverrunRate?: number;

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.PROCESS_TYPE)
  @IsNotEmpty({ message: 'VALIDATE.PROCESS_TYPE_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.PROCESS_TYPE_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.PROCESS_TYPE_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PROCESS_TYPE_ID.MUST_BE_UUID', each: true })
  processTypeIds?: string[];
  processTypes?: ProcessTypeModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.PLANT)
  @IsNotEmpty({ message: 'VALIDATE.PLANT_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.PLANT_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.PLANT_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.PLANT_ID.MUST_BE_UUID', each: true })
  plantIds?: string[];
  plants?: PlantModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.FUNCTION_UNIT)
  @IsNotEmpty({ message: 'VALIDATE.FUNCTION_UNIT_IDS.IS_REQUIRED' })
  @IsArray({ message: 'VALIDATE.FUNCTION_UNIT_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.FUNCTION_UNIT_IDS.IS_REQUIRED' })
  @IsUUID('4', {
    message: 'VALIDATE.FUNCTION_UNIT_ID.MUST_BE_UUID',
    each: true,
  })
  functionUnitIds?: string[];
  functionUnits?: FunctionUnitModel[];

  @ApiProperty({
    description: 'Difference amount all items',
    type: Number,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.DIFFERENCE_AMOUNT_ALL_ITEMS)
  @IsNotEmpty({ message: 'VALIDATE.DIFFERENCE_AMOUNT_ALL_ITEMS.IS_REQUIRED' })
  @IsNumber(
    {},
    { message: 'VALIDATE.DIFFERENCE_AMOUNT_ALL_ITEMS.MUST_BE_NUMBER' },
  )
  @Min(0)
  differenceAmountAllItems?: number;

  @ApiProperty({
    description: 'First Budget',
    type: Number,
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.type === EConditionType.FIRST_BUDGET)
  @IsNotEmpty({ message: 'VALIDATE.FIRST_BUDGET.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.FIRST_BUDGET.MUST_BE_NUMBER' })
  @Min(0)
  firstBudget?: number;

  conditionId?: string;
  createdBy?: object;
  updatedBy?: object;
}
