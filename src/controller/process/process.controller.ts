import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { IAuthUserPayload } from '../../domain/interface/auth-user-payload.interface';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ProcessUsecases } from '../../usecases/process.usecases';
import { StaffApprovalWorkflowUsecases } from '../../usecases/staff-approval-workflow.usecases';
import {
  EProcessPermission,
  EPurchaseOrderPermission,
  EPurchaseRequestPermission,
  EStaffPermission,
} from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateProcessDto } from './dtos/create-process.dto';
import { GetDetailProcessDto } from './dtos/get-detail-process.dto';
import { GetProcessListDto } from './dtos/get-process-list.dto';
import { UpdateParentProcessDto } from './dtos/update-parent-process.dto';
import { UpdateProcessDto } from './dtos/update-process.dto';

@Controller('/processes')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Processes')
export class ProcessController {
  constructor(
    private readonly processUsecases: ProcessUsecases,
    private readonly staffApprovalWorkflowUsecases: StaffApprovalWorkflowUsecases,
  ) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EProcessPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(@Body() data: CreateProcessDto, @NewAuthUser() jwtPayload: any) {
    return await this.processUsecases.createProcess(data, jwtPayload);
  }

  @Patch('/update/:id')
  @UseGuards(NewPermissionGuard([EProcessPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param('id') id: string,
    @Body() data: UpdateProcessDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    return await this.processUsecases.updateProcess(id, data, jwtPayload);
  }
  //Tree
  @Get(':id/detail-graph')
  //   @UseGuards(NewPermissionGuard([EProcessPermission.VIEW]))
  async getDetailGraph(
    @Param() param: GetDetailProcessDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.processUsecases.getDetailProcessGraph(param.id);
  }
  //List phẳng
  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EProcessPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailProcessDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.processUsecases.getDetailProcess(param.id);
  }

  @Get(':id/detail-node')
  // @UseGuards(NewPermissionGuard([EProcessPermission.VIEW]))
  async getDetailNode(
    @Param() param: GetDetailProcessDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.processUsecases.getDetailNodeProcess(param.id);
  }

  @Get('/list')
  //   @UseGuards(NewPermissionGuard([EProcessPermission.VIEW]))
  async getList(
    @Query() param: GetProcessListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.processUsecases.getProcessList(param);
  }

  @Delete(':id/delete')
  @UseGuards(NewPermissionGuard([EProcessPermission.DELETE]))
  async delete(@Param() param: GetDetailProcessDto) {
    return await this.processUsecases.deleteProcess(param.id);
  }

  @Get(':id/detail-parent-process')
  // @UseGuards(NewPermissionGuard([EProcessPermission.VIEW]))
  async getDetailParentProcess(
    @Param() param: GetDetailProcessDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.processUsecases.getDetailParentProcess(param.id);
  }

  @Patch('/update-parent-process/:id')
  @UseGuards(NewPermissionGuard([EProcessPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async updateParentProcess(
    @Param('id') id: string,
    @Body() data: UpdateParentProcessDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.processUsecases.updateParentProcess(id, data, jwtPayload);
  }

  @Get(':staffApprovalWorkflowId/selected-staffs')
  @UseGuards(
    NewPermissionGuard([
      EStaffPermission.VIEW,
      EPurchaseRequestPermission.CREATE,
      EPurchaseRequestPermission.EDIT,
      EPurchaseOrderPermission.CREATE,
      EPurchaseOrderPermission.EDIT,
    ]),
  )
  async getSelectedStaffs(
    @Param('staffApprovalWorkflowId') staffApprovalWorkflowId: string,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.staffApprovalWorkflowUsecases.getSelectedStaffs(
      staffApprovalWorkflowId,
    );
  }
}
