import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON><PERSON>y,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EMaterialGroupStatus } from '../../../domain/config/enums/material.enum';
import { BusinessOwnerModel } from '../../../domain/model/business-owner.model';
import { ProcessTypeModel } from '../../../domain/model/process-type.model';

export class CreateMaterialGroupDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code of material group',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of material group',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of material group',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EMaterialGroupStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EMaterialGroupStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EMaterialGroupStatus;

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.BUSINESS_OWNER_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.BUSINESS_OWNER_ID.MUST_BE_UUID',
    each: true,
  })
  businessOwnerIds?: string[];
  businessOwners?: BusinessOwnerModel[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.PROCESS_TYPE_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', { message: 'VALIDATE.PROCESS_TYPE_ID.MUST_BE_UUID', each: true })
  processTypeIds?: string[];
  processTypes?: ProcessTypeModel[];

  createdAt?: string;
}
