import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { EMaterialGroupStatus } from '../../../domain/config/enums/material.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetMaterialGroupListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EMaterialGroupStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EMaterialGroupStatus, { each: true })
  statuses?: EMaterialGroupStatus[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.BUSINESS_OWNER_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', {
    message: 'VALIDATE.BUSINESS_OWNER_ID.MUST_BE_UUID',
    each: true,
  })
  businessOwnerIds?: string[];

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.PROCESS_TYPE_IDS.MUST_BE_ARRAY' })
  @IsUUID('4', { message: 'VALIDATE.PROCESS_TYPE_ID.MUST_BE_UUID', each: true })
  processTypeIds?: string[];

  codes?: string[];
  businessOwnerCodes?: string[];
}
