import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  Request,
  Response,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { MaterialGroupUsecases } from '../../usecases/material-group.usecases';
import { EMaterialGroupPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateMaterialGroupDto } from './dtos/create-material-group.dto';
import { DeleteMaterialGroupDto } from './dtos/delete-material-group.dto';
import { GetDetailMaterialGroupDto } from './dtos/get-detail-material-group.dto';
import { GetMaterialGroupListDto } from './dtos/get-material-group-list.dto';
import { UpdateMaterialGroupDto } from './dtos/update-material-group.model';
import { GetMaterialGroupListByIdsDto } from './dtos/get-material-group-list-by-ids.dto';

@Controller('/material-group')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Material Group')
export class MaterialGroupController {
  constructor(private readonly materialGroupUsecases: MaterialGroupUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EMaterialGroupPermission.CREATE]))
  async create(
    @Body() data: CreateMaterialGroupDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialGroupUsecases.createMaterialGroup(
      data,
      jwtPayload,
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EMaterialGroupPermission.EDIT]))
  async update(
    @Body() data: UpdateMaterialGroupDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialGroupUsecases.updateMaterialGroup(
      param.id,
      data,
      jwtPayload,
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EMaterialGroupPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailMaterialGroupDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialGroupUsecases.getMaterialGroupDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EMaterialGroupPermission.VIEW]))
  async getList(
    @Query() param: GetMaterialGroupListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialGroupUsecases.getMaterialGroups(
      param,
      jwtPayload,
    );
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EMaterialGroupPermission.DELETE]))
  async delete(
    @Param() param: DeleteMaterialGroupDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialGroupUsecases.deleteMaterialGroup(
      param.id,
      jwtPayload,
    );
  }

  @Get('/export-material-group')
  @UseGuards(NewPermissionGuard([EMaterialGroupPermission.EXPORT]))
  async exportMaterialGroup(
    @Query() param: GetMaterialGroupListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.materialGroupUsecases.exportMaterialGroup(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-material-group')
  @UseGuards(NewPermissionGuard([EMaterialGroupPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importMaterialGroup(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.materialGroupUsecases.importMaterialGroup(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  ///For validate detail PR PO
  @Post('/list-by-ids')
  async getListByIds(
    @Body() param: GetMaterialGroupListByIdsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialGroupUsecases.getListByIds(param, jwtPayload);
  }
}
