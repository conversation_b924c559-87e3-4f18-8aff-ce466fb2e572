// import {
//   Controller,
//   Get,
//   Query,
//   Request,
//   UseGuards,
//   UseInterceptors,
// } from '@nestjs/common';
// import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
// import { ResponseDto } from '../../domain/dtos/response.dto';
// import { PurchaseOrderEntity } from '../../infrastructure/entities/purchase_order.entity';
// import { PurchaseRequestDetailEntity } from '../../infrastructure/entities/purchase_request_detail.entity';
// import { purchaseOrderUsecases } from '../../usecases/purchase_order.usecases';
// import { purchaseRequestUsecases } from '../../usecases/purchase_request.usecases';
// import { EPurchaseRequestPermission } from '../../utils/constants/permission.enum';
// import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
// import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
// import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
// import { GetPurchaseOrderDto } from '../purchase-order/dtos/get-all-purchase-order.dto';
// import { GetPurchaseRequestDto } from '../purchase-request/dtos/get-all-purchase-request.dto';
// import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
// import NewPermissionGuard from '../../utils/guard/new-permission.guard';
// import { PurchaseRequestDetailModel } from '../../domain/model/purchase_request_detail.model';

// @Controller('/material-purchase')
// @UseInterceptors(
//   TransformationInterceptor,
//   AspectLogger,
//   TransactionInterceptor,
// )
// @ApiBearerAuth('Authorization')
// @UseGuards(NewAuthGuard)
// @ApiTags('Material Purchase')
// export class MaterialPurchaseController {
//   constructor(
//     private readonly _purchaseRequestUsecases: purchaseRequestUsecases,
//     private readonly _purchaseOrderUsecases: purchaseOrderUsecases,
//   ) {}

//   @Get()
//   @UseGuards(NewPermissionGuard([EPurchaseRequestPermission.VIEW]))
//   async findAll(
//     @Query() paginationDto: GetPurchaseRequestDto,
//     @Request() req,
//   ): Promise<ResponseDto<PurchaseRequestDetailModel>> {
//     return await this._purchaseRequestUsecases.materialPurchase(
//       paginationDto,
//       req.headers['authorization'],
//     );
//   }

//   @Get('/purchase-order')
//   @UseGuards(NewPermissionGuard([EPurchaseRequestPermission.VIEW]))
//   async findPurchaseApproval(
//     @Query() paginationDto: GetPurchaseOrderDto,
//     @Request() req,
//   ): Promise<ResponseDto<PurchaseOrderEntity>> {
//     return this._purchaseOrderUsecases.materialPurchaseOrder(
//       paginationDto,
//       req.headers['authorization'],
//     );
//   }
// }
