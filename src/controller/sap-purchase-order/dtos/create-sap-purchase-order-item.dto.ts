import { EAccountAssignment } from '../../../domain/config/enums/account-assignment.enum';

export class CreateSapPurchaseOrderItemDto {
  eprId?: number;
  purchaseOrderDetailId: number;
  quantity: number;
  materialId: string; // Material id
  materialCode: string;
  materialName: string; //<PERSON><PERSON> tả sản phẩm cần mua đối với mua PO dich vụ không theo mã
  unit: string; //ĐVT
  deliveryDate: Date;
  price: number;
  accountAssignment: EAccountAssignment; //AAG - Loại đối tượng nhận hàng
  glAccount: string; //G/L Account Number
  costCenterId: string;
  costCenterCode: string;
  asset: string; //Tài sản
  internalOrder: string;
  wbs: string;
  functionalArea: string;
  materialGroupId: string;
  materialGroupName: string;
  materialGroupCode: string;
  currencyId: string;
  currencyCode: string;
  buId: string; //BU Id-Plant SAP
  buCode: string; //Plant SAP
  sapPurchaseOrderId?: number;
  taxCodeId: string;
  taxCodeCode: string;

  messageType?: string;
  message?: string;
}
