import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsNotEmpty, IsNumber, IsString, <PERSON><PERSON>ength, ValidateNested } from 'class-validator';
import { CreatePoItemSapDto } from './create-po-item-sap.dto';

export class CreatePoSapDto {
  @ApiProperty({ required: true, type: Number, description: 'Mã SAP PO ePurchase' })
  @IsNotEmpty({ message: 'VALIDATE.SEPID.IS_REQUIRED' })
  @IsNumber(null, { message: 'VALIDATE.SEPID.MUST_BE_NUMBER' })
  sepid: number;

  @ApiProperty({ required: true, type: String, description: 'Mã BU ePurchase' })
  @IsNotEmpty({ message: 'VALIDATE.BUKRS.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.BUKRS.MUST_BE_STRING' })
  bukrs: string;

  @ApiProperty({ required: true, type: String, format: 'date', description: '<PERSON><PERSON>y tích hợp', example: '2022-01-01' })
  crdat: string;

  @ApiProperty({ required: true, type: String, description: 'Người tích hợp' })
  @IsNotEmpty({ message: 'VALIDATE.CRNAM.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.CRNAM.MUST_BE_STRING' })
  @MaxLength(10, { message: 'VALIDATE.CRNAM.MAX_LENGTH' })
  crnam: string;

  @ApiProperty({ required: true, type: String, format: 'time', description: 'Giờ tích hợp', example: '00:00:00' })
  crtim: string;

  @ApiProperty({ required: true, type: String, description: 'Loại PO ePurchase' })
  @IsNotEmpty({ message: 'VALIDATE.BSART.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.BSART.MUST_BE_STRING' })
  @MaxLength(4, { message: 'VALIDATE.BSART.MAX_LENGTH' })
  bsart: string;

  @ApiProperty({ required: true, type: String, description: 'Tổ chức mua hàng' })
  @IsNotEmpty({ message: 'VALIDATE.EKORG.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.EKORG.MUST_BE_STRING' })
  @MaxLength(4, { message: 'VALIDATE.EKORG.MAX_LENGTH' })
  ekorg: string;

  @ApiProperty({ required: true, type: String, description: 'Nhóm mua hàng' })
  @IsNotEmpty({ message: 'VALIDATE.EKGRP.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.EKGRP.MUST_BE_STRING' })
  @MaxLength(3, { message: 'VALIDATE.EKGRP.MAX_LENGTH' })
  ekgrp: string;

  @ApiProperty({ required: true, type: String, description: 'Ngày tạo PO' })
  @IsNotEmpty({ message: 'VALIDATE.AEDAT.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.AEDAT.MUST_BE_STRING' })
  @MaxLength(3, { message: 'VALIDATE.AEDAT.MAX_LENGTH' })
  aedat: string;

  @ApiProperty({ required: true, type: String, description: 'Tỷ giá' })
  @IsNotEmpty({ message: 'VALIDATE.WKURS.IS_REQUIRED' })
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'VALIDATE.WKURS.MUST_BE_NUMBER' })
  wkurs: number;

  @ApiProperty({ required: true, type: String, description: 'Mã nhà cung cấp' })
  @IsNotEmpty({ message: 'VALIDATE.LIFNR.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.LIFNR.MUST_BE_STRING' })
  @MaxLength(10, { message: 'VALIDATE.LIFNR.MAX_LENGTH' })
  lifnr: string;

  @ApiProperty({ required: true, type: String, description: 'Tên nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.ONAME.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.ONAME.MUST_BE_STRING' })
  @MaxLength(300, { message: 'VALIDATE.ONAME.MAX_LENGTH' })
  oname: string;

  @ApiProperty({ required: true, type: String, description: 'Địa chỉ Thành phố nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.OCITY.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.OCITY.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.OCITY.MAX_LENGTH' })
  ocity: string;

  @ApiProperty({ required: true, type: String, description: 'Địa chỉ quận nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.ODISTRICT.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.ODISTRICT.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.ODISTRICT.MAX_LENGTH' })
  odistrict: string;

  @ApiProperty({ required: true, type: String, description: 'Địa chỉ quốc gia nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.OCOUNTRY.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.OCOUNTRY.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.OCOUNTRY.MAX_LENGTH' })
  ocountry: string;

  @ApiProperty({ required: true, type: String, description: 'Location nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.OLOCATION.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.OLOCATION.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.OLOCATION.MAX_LENGTH' })
  olocation: string;

  @ApiProperty({ required: true, type: String, description: 'Loại tiền' })
  @IsNotEmpty({ message: 'VALIDATE.WAERS.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.WAERS.MUST_BE_STRING' })
  @MaxLength(16, { message: 'VALIDATE.WAERS.MAX_LENGTH' })
  waers: string;

  @ApiProperty({ type: [CreatePoItemSapDto], required: true })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'VALIDATE.ITEMS.IS_REQUIRED' })
  @Type(() => CreatePoItemSapDto)
  items: CreatePoItemSapDto[];
}
