import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsDate,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateIf,
} from 'class-validator';
import { EAccountAssignment } from '../../../domain/config/enums/account-assignment.enum';

export class CreatePoItemSapDto {
  @ApiProperty({
    required: true,
    type: String,
    description: 'Số thứ tự dòng trên CT mua hàng E-purchase',
  })
  @IsNotEmpty({ message: 'VALIDATE.WKURS.IS_REQUIRED' })
  @IsInt({ message: 'VALIDATE.WKURS.MUST_BE_INTEGER' })
  eprid: number;

  @ApiProperty({ required: true, type: String, description: 'Mã vật tư' })
  @IsNotEmpty({ message: 'VALIDATE.MATNR.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATNR.MUST_BE_STRING' })
  @MaxLength(18, { message: 'VALIDATE.MATNR.MAX_LENGTH' })
  matnr: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'Mô tả sản phẩm cần mua đối với mua PO dich vụ không theo mã',
  })
  @IsOptional()
  @ValidateIf((o) => o.knttp)
  @IsNotEmpty({ message: 'VALIDATE.SHTEX.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.SHTEX.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.SHTEX.MAX_LENGTH' })
  shtex: string;

  @ApiProperty({ required: true, type: Number, description: 'Số lượng nhập' })
  @IsNotEmpty({ message: 'VALIDATE.MENGE.IS_REQUIRED' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'VALIDATE.MENGE.MUST_BE_NUMBER' },
  )
  menge: number;

  @ApiProperty({ required: true, type: String, description: 'ĐVT' })
  @IsNotEmpty({ message: 'VALIDATE.MEINS.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MEINS.MUST_BE_STRING' })
  @MaxLength(3, { message: 'VALIDATE.MEINS.MAX_LENGTH' })
  meins: string;

  @ApiProperty({
    required: true,
    type: String,
    format: 'date',
    description: 'Ngày giao hàng',
    example: '2022-01-01',
  })
  @IsNotEmpty({ message: 'VALIDATE.EINDT.IS_REQUIRED' })
  @Type(() => Date)
  @IsDate()
  eindt: string;

  @ApiProperty({ required: true, type: String, description: 'Giá' })
  @IsNotEmpty({ message: 'VALIDATE.NETPR.IS_REQUIRED' })
  @Transform((value) => +value)
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'VALIDATE.NETPR.MUST_BE_NUMBER' },
  )
  netpr: number;

  @ApiProperty({
    required: true,
    type: String,
    description: 'AAG - Loại đối tượng nhận hàng',
  })
  @IsNotEmpty({ message: 'VALIDATE.KNTTP.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.KNTTP.MUST_BE_STRING' })
  @MaxLength(1, { message: 'VALIDATE.KNTTP.MAX_LENGTH' })
  knttp: string;

  @ApiProperty({ required: true, type: String, description: 'G/L Account ' })
  @IsOptional()
  @ValidateIf((o) => o.knttp == EAccountAssignment.K)
  @IsNotEmpty({ message: 'VALIDATE.SAKNR.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.SAKNR.MUST_BE_STRING' })
  @MaxLength(10, { message: 'VALIDATE.SAKNR.MAX_LENGTH' })
  saknr: string;

  @ApiProperty({ required: true, type: String, description: 'Cost Center' })
  @IsOptional()
  @ValidateIf(
    (o) =>
      (o.account_assignment === EAccountAssignment.F ||
        o.account_assignment === EAccountAssignment.K ||
        o.account_assignment === EAccountAssignment.A) &&
      !o.material_code,
  )
  @IsNotEmpty({ message: 'VALIDATE.KOSTL.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.KOSTL.MUST_BE_STRING' })
  @MaxLength(10, { message: 'VALIDATE.KOSTL.MAX_LENGTH' })
  kostl: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'Main Asset Number',
  })
  @IsOptional()
  @ValidateIf((o) => o.account_assignment === EAccountAssignment.A)
  @IsNotEmpty({ message: 'VALIDATE.ANLN1.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.ANLN1.MUST_BE_STRING' })
  @MaxLength(12, { message: 'VALIDATE.ANLN1.MAX_LENGTH' })
  anln1: string;

  @ApiProperty({ required: true, type: String, description: 'IO Number' })
  @IsOptional()
  @ValidateIf((o) => o.account_assignment === EAccountAssignment.F)
  @IsNotEmpty({ message: 'VALIDATE.AFUNR.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.AFUNR.MUST_BE_STRING' })
  @MaxLength(12, { message: 'VALIDATE.AFUNR.MAX_LENGTH' })
  aufnr: string;

  @ApiProperty({ required: true, type: String, description: 'WBS' })
  @IsOptional()
  @ValidateIf((o) => o.account_assignment === EAccountAssignment.X)
  @IsNotEmpty({ message: 'VALIDATE.WBSEL.IS_REQUIRED' })
  @IsNumber(null, { message: 'VALIDATE.WBSEL.MUST_BE_STRING' })
  wbsel: string;

  @ApiProperty({ required: true, type: String, description: 'Functional Area' })
  @IsNotEmpty({ message: 'VALIDATE.FKBER.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.FKBER.MUST_BE_STRING' })
  @MaxLength(16, { message: 'VALIDATE.FKBER.MAX_LENGTH' })
  fkber: string;

  @ApiProperty({ required: true, type: String, description: 'Material Group' })
  @IsOptional()
  @ValidateIf((o) => o.knttp)
  @IsNotEmpty({ message: 'VALIDATE.MATKL.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATKL.MUST_BE_STRING' })
  @MaxLength(16, { message: 'VALIDATE.MATKL.MAX_LENGTH' })
  matkl: string;

  @ApiProperty({ required: true, type: String, description: 'Loại tiền' })
  @IsNotEmpty({ message: 'VALIDATE.WAERS.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.WAERS.MUST_BE_STRING' })
  @MaxLength(16, { message: 'VALIDATE.WAERS.MAX_LENGTH' })
  waers: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'Plant ( Nơi mua - Nhà Máy) (BU)',
  })
  @IsNotEmpty({ message: 'VALIDATE.WERKS.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.WERKS.MUST_BE_STRING' })
  @MaxLength(16, { message: 'VALIDATE.WERKS.MAX_LENGTH' })
  werks: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'Plant ( Nơi mua - Nhà Máy) (BU)',
  })
  @IsNotEmpty({ message: 'VALIDATE.MWSKZ.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MWSKZ.MUST_BE_STRING' })
  @MaxLength(2, { message: 'VALIDATE.MWSKZ.MAX_LENGTH' })
  mwskz: string;
}
