import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, ValidateNested } from 'class-validator';
import { CreateSapPurchaseOrderItemDto } from './create-sap-purchase-order-item.dto';
import { SapPurchaseOrderItemModel } from '../../../domain/model/sap_purchase_order_item.model';

export class CreateSapPurchaseOrderDto {
  poId: number; // PO EP id - chua can link po
  companyId: string; // Company id in BU Id
  companyCode: string;
  poTypeId: string; //PO Type
  poTypeCode: string;
  purchasingDepartmentId: string; //Purchasing organization
  purchasingDepartmentCode: string;
  purchasingGroupId: string; //Purchasing Group
  purchasingGroupCode: string;
  poCreatedAt: Date; //Ngày tạo PO
  exchangeRate: number; //Exchange rage
  currencyCode: string;
  supplierId: string;
  supplierCode: string;
  supplierInfo: string | object;

  messageType?: string;
  message?: string;

  @ApiProperty({ type: [CreateSapPurchaseOrderItemDto], required: true })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'VALIDATE.ITEMS.IS_REQUIRED' })
  @Type(() => CreateSapPurchaseOrderItemDto)
  createSapPurchaseOrderItemDtos?: CreateSapPurchaseOrderItemDto[];

  items?: SapPurchaseOrderItemModel[] | CreateSapPurchaseOrderItemDto[];
}
