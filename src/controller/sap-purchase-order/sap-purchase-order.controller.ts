import {
  Controller,
  Get,
  Param,
  Post,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { SapPurchaseOrderUsecases } from '../../usecases/sap-purchase-order.usecases';
import { EPurchaseOrderPermission } from '../../utils/constants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { GetListSapPurchaseOrderByIdsDto } from './dtos/get-list-sap-purchase-order-by-ids.dto';

@Controller('/sap-po')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('SAP PO')
export class SapPurchaseOrderController {
  constructor(
    private readonly sapPurchaseOrderUsecases: SapPurchaseOrderUsecases,
  ) {}

  @Post(':poId')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.CREATE]))
  async createSapPurchaseOrder(@Param('poId') poId: string, @Request() req) {
    return await this.sapPurchaseOrderUsecases.syncAndSaveSapPurchaseOrder(
      Number(poId),
      req.headers['authorization'],
      false,
    );
  }

  @Get('/list-by-ids')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.CREATE]))
  async getListSapPurchaseOrderByIds(
    @Query() getListSapPurchaseOrderByIdsDto: GetListSapPurchaseOrderByIdsDto,
  ) {
    return await this.sapPurchaseOrderUsecases.getListSapPurchaseOrderByIds(
      getListSapPurchaseOrderByIdsDto.ids,
    );
  }
}
