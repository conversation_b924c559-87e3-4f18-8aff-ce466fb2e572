import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { SectorUsecases } from '../../usecases/sector.usecases';
import { ESectorPermission } from '../../utils/constants/permission.enum';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateSectorDto } from './dtos/create-sector.dto';
import { DeleteSectorDto } from './dtos/delete-sector.dto';
import { GetDetailSectorDto } from './dtos/get-detail-sector.dto';
import { GetSectorListDto } from './dtos/get-sector-list.dto';
import { UpdateSectorDto } from './dtos/update-sector.dto';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';

@Controller('/sector')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Sector')
export class SectorController {
  constructor(private readonly sectorUsecases: SectorUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([ESectorPermission.CREATE]))
  async create(@Body() data: CreateSectorDto, @Request() req) {
    return await this.sectorUsecases.createSector(
      data,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([ESectorPermission.EDIT]))
  async update(
    @Body() data: UpdateSectorDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.sectorUsecases.updateSector(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([ESectorPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailSectorDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.sectorUsecases.getDetailSector(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([ESectorPermission.VIEW]))
  async getList(
    @Query() param: GetSectorListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.sectorUsecases.getSectors(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([ESectorPermission.DELETE]))
  async delete(
    @Param() param: DeleteSectorDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.sectorUsecases.deleteSector(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
