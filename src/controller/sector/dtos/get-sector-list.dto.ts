import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { ESectorStatus } from '../../../domain/config/enums/sector.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetSectorListDto extends OmitType(PaginationDto, ['from', 'to']) {
  @ApiProperty({
    type: [ESectorStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ESectorStatus, { each: true })
  statuses?: ESectorStatus[];

  codes?: string[];
  ids?: string[]; //For create/update staff
}
