import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { ESectorStatus } from '../../../domain/config/enums/sector.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { EMaterialTypeStatus } from '../../../domain/config/enums/material.enum';

export class GetMaterialTypeListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EMaterialTypeStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EMaterialTypeStatus, { each: true })
  statuses?: EMaterialTypeStatus[];

  codes?: string[];
}
