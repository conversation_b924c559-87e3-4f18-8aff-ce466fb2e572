import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { EMaterialTypeStatus } from '../../../domain/config/enums/material.enum';

export class CreateMaterialTypeDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code of material type',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of material type',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of material type',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EMaterialTypeStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EMaterialTypeStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EMaterialTypeStatus;

  createdAt?: string;
  updatedAt?: string;
}
