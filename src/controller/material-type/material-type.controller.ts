import {
  Body,
  Controller,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Get,
  Query,
  Delete,
  Response,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpStatus,
} from '@nestjs/common';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { MaterialTypeUsecases } from '../../usecases/material-type.usecases';
import { CreateMaterialTypeDto } from './dtos/create-material-type.dto';
import { UpdateMaterialTypeDto } from './dtos/update-material-type.dto';
import { GetMaterialTypeListDto } from './dtos/get-material-type-list.dto';
import { GetDetailMaterialTypeDto } from './dtos/get-detail-material-type.dto';
import { DeleteMaterialTypeDto } from './dtos/delete-material-type.dto';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EMaterialTypePermission } from '../../utils/constants/permission.enum';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('/material-type')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Material Type')
export class MaterialTypeController {
  constructor(private readonly materialTypeUsecases: MaterialTypeUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EMaterialTypePermission.CREATE]))
  async create(@Body() data: CreateMaterialTypeDto, @Request() req) {
    return await this.materialTypeUsecases.createMaterialType(
      data,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EMaterialTypePermission.EDIT]))
  async update(
    @Body() data: UpdateMaterialTypeDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.materialTypeUsecases.updateMaterialType(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EMaterialTypePermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailMaterialTypeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialTypeUsecases.getMaterialTypeDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EMaterialTypePermission.VIEW]))
  async getList(
    @Query() param: GetMaterialTypeListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.materialTypeUsecases.getMaterialTypes(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EMaterialTypePermission.DELETE]))
  async delete(
    @Param() param: DeleteMaterialTypeDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.materialTypeUsecases.deleteMaterialType(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-material-type')
  @UseGuards(NewPermissionGuard([EMaterialTypePermission.EXPORT]))
  async exportMaterialType(
    @Query() param: GetMaterialTypeListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.materialTypeUsecases.exportMaterialType(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-material-type')
  @UseGuards(NewPermissionGuard([EMaterialTypePermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importMaterialType(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.materialTypeUsecases.importMaterialType(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
