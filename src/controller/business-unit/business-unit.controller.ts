import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
  Response,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpStatus,
} from '@nestjs/common';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { BusinessUnitUsecases } from '../../usecases/business-unit.usecases';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { CreateBusinessUnitDto } from './dtos/create-business-unit.dto';
import { GetBusinessUnitListDto } from './dtos/get-business-unit-list.dto';
import { UpdateBusinessUnitDto } from './dtos/update-business-unit.dto';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EBusinessUnitPermission } from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { GetDetailBusinessUnitDto } from './dtos/get-detail-business-unit.dto';
import { DeleteBusinessUnitDto } from './dtos/delete-business-unit.dto';
import { ListByCodesDto } from '../../domain/dtos/base-dto-by-codes.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('/business-unit')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Business Unit')
export class BusinessUnitController {
  constructor(private readonly businessUnitUsecases: BusinessUnitUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EBusinessUnitPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(
    @Body() data: CreateBusinessUnitDto,
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.businessUnitUsecases.createBusinessUnit(
      data,
      req.headers['authorization'],
      jwtPayload,
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EBusinessUnitPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailBusinessUnitDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.businessUnitUsecases.getDetailBusinessUnit(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EBusinessUnitPermission.VIEW]))
  async getList(
    @Query() param: GetBusinessUnitListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.businessUnitUsecases.getBusinessUnits(param, jwtPayload);
  }

  @Delete(':id/delete')
  @UseGuards(NewPermissionGuard([EBusinessUnitPermission.DELETE]))
  async delete(
    @Param() param: DeleteBusinessUnitDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    await this.businessUnitUsecases.deleteBusinessUnit(
      param,
      jwtPayload,
      req.headers['authorization'],
    );
    return { message: 'Successfully!!!' };
  }

  @Put(':id/update')
  @UseGuards(NewPermissionGuard([EBusinessUnitPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param() param: GetUuidDto,
    @Body() data: UpdateBusinessUnitDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.businessUnitUsecases.updateBusinessUnit(
      data,
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/list-by-codes')
  async listByCodes(
    @Body() data: ListByCodesDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.businessUnitUsecases.listByCodes(
      data.codes || [],
      jwtPayload,
    );
  }

  @Get('/export-business-unit')
  @UseGuards(NewPermissionGuard([EBusinessUnitPermission.EXPORT]))
  async exportBusinessUnit(
    @Query() param: GetBusinessUnitListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.businessUnitUsecases.exportBusinessUnit(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-business-unit')
  @UseGuards(NewPermissionGuard([EBusinessUnitPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importBusinessUnit(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.businessUnitUsecases.importBusinessUnit(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
