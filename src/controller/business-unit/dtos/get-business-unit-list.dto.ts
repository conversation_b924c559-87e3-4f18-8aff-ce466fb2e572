import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { EBusinessUnitStatus } from '../../../domain/config/enums/business-unit.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetBusinessUnitListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EBusinessUnitStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EBusinessUnitStatus, { each: true })
  statuses?: EBusinessUnitStatus[];

  codes?: string[];
  companyCodes?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  companyIds?: string[];
}
