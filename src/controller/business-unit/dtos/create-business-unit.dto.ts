import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EBusinessUnitStatus } from '../../../domain/config/enums/business-unit.enum';

export class CreateBusinessUnitDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'ID',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên <PERSON>ơn vị kinh doanh',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON>ô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EBusinessUnitStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EBusinessUnitStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EBusinessUnitStatus;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Công ty',
  })
  @IsString()
  @IsNotEmpty({ message: 'VALIDATE.COMPANY_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.COMPANY_ID.MUST_BE_UUID' })
  companyId: string;

  createdAt?: string;
  updatedAt?: string;
}
