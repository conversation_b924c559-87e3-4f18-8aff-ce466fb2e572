import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, <PERSON><PERSON><PERSON><PERSON>, IsInt, IsString } from 'class-validator';

export class GetListSolomonPurchaseOrderByIdsDto {
  @ApiProperty({ type: [String], required: true })
  @IsArray()
  @ArrayMinSize(1, { message: 'VALIDATE.ITEMS.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.ITEMS.MUST_BE_STRING' })
  ids: string[];
}
