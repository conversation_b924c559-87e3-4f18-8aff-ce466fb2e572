export class CreateSolomonPurchaseOrderItemDto {
  solomonPurchaseOrderId?: string;

  materialId?: string; //Mã vật tư

  materialCode?: string;

  materialName?: string; //Mô tả sản phẩm cần mua đối với mua PO dich vụ không theo mã

  warehouseId?: string; //Mã kho

  warehouseCode?: string;

  description?: string; //Mô tả trong dòng item (note)

  quantity?: number; //Số lượng mua

  measureId?: string; //Mã đơn vị tính

  measureCode?: string; //Mã đơn vị tính (Lưu ý: Chuyển đổi sang đơn vị của hệ thống Solomon)

  unitPrice?: number; //Đơn giá

  totalAmountVat?: number; //Tổng tiền quy đổi * VAT

  unitWeight?: number = 0; //Mặc định = 0

  extWeight?: number = 0; //Mặc định = 0

  deliveryTime?: Date; //Ngày giao hàng

  taxCodeId?: string; //Mã thuế

  taxCodeCode?: string;

  cnvFact?: number = 1; //Mặc định = 1

  budgetCodeCode?: string; //Mã ngân sách

  budgetCodeId?: string; //Mã ngân sách

  purchaseOrderDetailId?: number;

  messageType?: string; //Response from SOLOMON

  message?: string;
}
