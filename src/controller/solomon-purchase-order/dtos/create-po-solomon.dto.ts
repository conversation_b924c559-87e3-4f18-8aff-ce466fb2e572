import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreatePoItemSolomonDto } from './create-po-item-solomon.dto';
import { Type } from 'class-transformer';

export class CreatePoSolomonDto {
  @ApiProperty({ required: true, type: String, description: 'Mã PO' })
  @IsNotEmpty({ message: 'VALIDATE.poNbr.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.poNbr.MUST_BE_STRING' })
  poNbr: string;

  @ApiProperty({ required: true, type: String, description: 'Mã nhà cung cấp' })
  @IsNotEmpty({ message: 'VALIDATE.vendID.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.vendID.MUST_BE_STRING' })
  vendID: string;

  @ApiProperty({
    required: true,
    type: Date,
    description: 'Ng<PERSON>y tạo PO',
    example: '2025-06-18T00:00:00Z"',
  })
  @IsNotEmpty({ message: 'VALIDATE.poDate.IS_REQUIRED' })
  @IsDateString()
  poDate: Date;

  @ApiProperty({ required: true, type: String, description: 'Mã ngân sách' })
  @IsNotEmpty({ message: 'VALIDATE.budgetCode.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.budgetCode.MUST_BE_STRING' })
  budgetCode: string;

  @ApiProperty({
    type: [CreatePoItemSolomonDto],
    required: true,
    description: 'Chi tiết đơn hàng',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'VALIDATE.purOrdDet.IS_REQUIRED' })
  @Type(() => CreatePoItemSolomonDto)
  purOrdDet: CreatePoItemSolomonDto[];
}
