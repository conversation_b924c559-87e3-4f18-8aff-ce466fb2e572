import { ApiProperty } from '@nestjs/swagger';
import { SolomonPurchaseOrderItemModel } from '../../../domain/model/solomon-purchase-order-item.model';
import { CreateSolomonPurchaseOrderItemDto } from './create-solomon-purchase-order-item.dto';
import { ArrayMinSize, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateSolomonPurchaseOrderDto {
  poId?: number; //Mã PO

  supplierId?: string; //Mã nhà cung cấp

  supplierCode?: string; //Mã nhà cung cấp

  supplierInfo?: string | object;

  poCreatedDate?: Date; //Ngày tạo PO

  budgetCodeCode?: string; //Mã ngân sách

  budgetCodeId?: string; //Mã ngân sách

  messageType?: string; //Response from SOLOMON

  message?: string;

  status?: string;

  @ApiProperty({ type: [CreateSolomonPurchaseOrderItemDto], required: true })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'VALIDATE.ITEMS.IS_REQUIRED' })
  @Type(() => CreateSolomonPurchaseOrderItemDto)
  createSolomonPurchaseOrderItemDtos?: CreateSolomonPurchaseOrderItemDto[];

  //Chi tiết đơn hàng
  items: SolomonPurchaseOrderItemModel[] | CreateSolomonPurchaseOrderItemDto[];
}
