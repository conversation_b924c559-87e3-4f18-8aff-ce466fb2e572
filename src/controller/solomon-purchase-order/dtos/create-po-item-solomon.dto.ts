import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsString,
} from 'class-validator';

export class CreatePoItemSolomonDto {
  @ApiProperty({ required: true, type: String, description: 'Mã PO' })
  @IsNotEmpty({ message: 'VALIDATE.poNbr.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.poNbr.MUST_BE_STRING' })
  poNbr: string;

  @ApiProperty({ required: true, type: String, description: 'Mã hàng hóa' })
  @IsNotEmpty({ message: 'VALIDATE.invtID.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.invtID.MUST_BE_STRING' })
  invtID: string;

  @ApiProperty({ required: true, type: String, description: 'Mã kho' })
  @IsNotEmpty({ message: 'VALIDATE.siteID.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.siteID.MUST_BE_STRING' })
  siteID: string;

  @ApiProperty({ required: true, type: String, description: 'Mô tả chi tiết' })
  @IsNotEmpty({ message: 'VALIDATE.tranDesc.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.tranDesc.MUST_BE_STRING' })
  tranDesc: string;

  @ApiProperty({
    required: true,
    type: Number,
    description: 'Số lượng đặt',
  })
  @IsNotEmpty({ message: 'VALIDATE.qtyOrd.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.qtyOrd.MUST_BE_NUMBER' })
  qtyOrd: number;

  @ApiProperty({ required: true, type: String, description: 'Đơn vị mua' })
  @IsNotEmpty({ message: 'VALIDATE.purchUnit.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.purchUnit.MUST_BE_STRING' })
  purchUnit: string;

  @ApiProperty({
    required: true,
    type: Number,
    description: 'Đơn giá',
  })
  @IsNotEmpty({ message: 'VALIDATE.curyUnitCost.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.curyUnitCost.MUST_BE_NUMBER' })
  curyUnitCost: number;

  @ApiProperty({
    required: true,
    type: Number,
    description: 'Thành tiền',
  })
  @IsNotEmpty({ message: 'VALIDATE.curyExtCost.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.curyExtCost.MUST_BE_NUMBER' })
  curyExtCost: number;

  @ApiProperty({
    required: true,
    type: Number,
    description: 'Cân nặng/đơn vị',
  })
  @IsNotEmpty({ message: 'VALIDATE.unitWeight.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.unitWeight.MUST_BE_NUMBER' })
  unitWeight: number = 0;

  @ApiProperty({
    required: true,
    type: Number,
    description: 'Tổng cân nặng',
  })
  @IsNotEmpty({ message: 'VALIDATE.extWeight.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.extWeight.MUST_BE_NUMBER' })
  extWeight: number = 0;

  @ApiProperty({
    required: true,
    type: Date,
    description: 'Ngày yêu cầu',
    example: '2025-06-18T00:00:00Z"',
  })
  @IsNotEmpty({ message: 'VALIDATE.reqdDate.IS_REQUIRED' })
  @IsDateString()
  reqdDate: Date;

  @ApiProperty({
    required: true,
    type: Date,
    description: 'Ngày hẹn giao',
    example: '2025-06-18T00:00:00Z"',
  })
  @IsNotEmpty({ message: 'VALIDATE.promDate.IS_REQUIRED' })
  @IsDateString()
  promDate: Date;

  @ApiProperty({ required: true, type: String, description: 'Mã thuế' })
  @IsNotEmpty({ message: 'VALIDATE.taxID00.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.taxID00.MUST_BE_STRING' })
  taxID00: string;

  @ApiProperty({
    required: true,
    type: Number,
    description: 'Hệ số chuyển đổi',
  })
  @IsNotEmpty({ message: 'VALIDATE.cnvFact.IS_REQUIRED' })
  @IsNumber({}, { message: 'VALIDATE.cnvFact.MUST_BE_NUMBER' })
  cnvFact: number = 1;

  @ApiProperty({ required: true, type: String, description: 'Mã ngân sách' })
  @IsNotEmpty({ message: 'VALIDATE.user1.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.user1.MUST_BE_STRING' })
  user1: string;

  @ApiProperty({
    required: true,
    type: Date,
    description: 'PO Date',
    example: '2025-06-18T00:00:00Z"',
  })
  @IsNotEmpty({ message: 'VALIDATE.user7.IS_REQUIRED' })
  @IsDateString()
  user7: Date;
}
