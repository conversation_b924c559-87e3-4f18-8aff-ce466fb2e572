import {
  Controller,
  Param,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Get,
  Query,
} from '@nestjs/common';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { SolomonPurchaseOrderUsecases } from '../../usecases/solomon-purchase-order.usecases';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EPurchaseOrderPermission } from '../../utils/constants/permission.enum';
import { GetListSolomonPurchaseOrderByIdsDto } from './dtos/get-list-solomon-purchase-order-by-ids.dto';

@Controller('/solomon-po')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('SOLOMON PO')
export class SolomonPurchaseOrderController {
  constructor(
    private readonly solomonPurchaseOrderUsecases: SolomonPurchaseOrderUsecases,
  ) {}

  @Post(':poId')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.CREATE]))
  async createSolomonPurchaseOrder(
    @Param('poId') poId: string,
    @Request() req,
  ) {
    return await this.solomonPurchaseOrderUsecases.syncAndSaveSolomonPurchaseOrder(
      Number(poId),
      req.headers['authorization'],
      false,
    );
  }

  @Get('/list-by-ids')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.CREATE]))
  async getListSolomonPurchaseOrderByIds(
    @Query()
    getListSolomonPurchaseOrderByIdsDto: GetListSolomonPurchaseOrderByIdsDto,
  ) {
    return await this.solomonPurchaseOrderUsecases.getListSolomonPurchaseOrderByIds(
      getListSolomonPurchaseOrderByIdsDto.ids,
    );
  }
}
