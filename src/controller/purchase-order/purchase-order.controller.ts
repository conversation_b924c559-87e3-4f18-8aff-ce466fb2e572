import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  Response,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PurchaseOrderModel } from '../../domain/model/purchase_order.model';
import { PurchaseOrderDetailModel } from '../../domain/model/purchase_order_detail.model';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { purchaseOrderUsecases } from '../../usecases/purchase_order.usecases';
import {
  EApprovedMaterialPermission,
  EPurchaseOrderPermission,
} from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { GetAdjustBudgetInPoDetailDto } from './dtos/get-adjust-budget-in-po.dto';
import { GetPurchaseOrderDto } from './dtos/get-all-purchase-order.dto';
import { GetPoDetailReportBudgetDetailDto } from './dtos/get-po-detail-report-budget.dto';
import { GetPOWithBudgetDto } from './dtos/get-po-with-budget.dto';
import { PurchaseOrderDetailDto } from './dtos/purchase-order-detail.dto';
import {
  PurchaseOrderDto,
  UpdatePurchaseOrderDto,
} from './dtos/purchase-order.dto';
import { ResendEmailPoDto } from './dtos/resend-email-po.dto';

@Controller('/purchase-order')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Purchase Order')
export class PurchaseOrderController {
  constructor(private readonly _purchaseOrderUsecases: purchaseOrderUsecases) {}

  @Get('/po-with-budget')
  async findPRWithBudget(
    @Query() paginationDto: GetPOWithBudgetDto,
  ): Promise<PurchaseOrderModel[]> {
    return await this._purchaseOrderUsecases.findPOWithBudget(paginationDto);
  }

  /// FOR REPORT BUDGET DETAIL
  @Get('/find-po-detail-for-report-budget')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.VIEW]))
  async findPoReportBudget(
    @Query() conditions: GetPoDetailReportBudgetDetailDto,
    @Request() req,
  ): Promise<PurchaseOrderDetailModel[]> {
    return await this._purchaseOrderUsecases.findPoReportBudget(
      conditions,
      req.headers['authorization'],
    );
  }

  @Get('/find-adjust-budget-in-po-detail')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.VIEW]))
  async findAdjustBudgetIdInPoDetail(
    @Query() conditions: GetAdjustBudgetInPoDetailDto,
  ): Promise<PurchaseOrderDetailModel[]> {
    return await this._purchaseOrderUsecases.findAdjustBudgetIdInPoDetail(
      conditions.adjustBudgetIds,
    );
  }

  @Get('/approved-material')
  @UseGuards(NewPermissionGuard([EApprovedMaterialPermission.VIEW]))
  async approvedMaterial(
    @Query() paginationDto: GetPurchaseOrderDto,
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ): Promise<ResponseDto<PurchaseOrderModel>> {
    return this._purchaseOrderUsecases.findAll(
      paginationDto,
      req.headers['authorization'],
      jwtPayload,
    );
  }

  @Get()
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.VIEW]))
  async findAll(
    @Query() paginationDto: GetPurchaseOrderDto,
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ): Promise<ResponseDto<PurchaseOrderModel>> {
    return this._purchaseOrderUsecases.findAll(
      paginationDto,
      req.headers['authorization'],
      jwtPayload,
    );
  }

  @Get('export-po')
  @UseGuards(
    NewPermissionGuard([
      [EPurchaseOrderPermission.VIEW, EPurchaseOrderPermission.EXPORT],
    ]),
  )
  async exportPo(
    @Query() paginationDto: GetPurchaseOrderDto,
    @Request() req,
    @Response() res,
    @NewAuthUser() jwtPayload: any,
  ) {
    const result = await this._purchaseOrderUsecases.exportPo(
      paginationDto,
      req.headers['authorization'],
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post()
  @UseGuards(
    NewPermissionGuard([
      EPurchaseOrderPermission.CREATE,
      EApprovedMaterialPermission.CREATE,
    ]),
  )
  async createPOFromPR(
    @Body() data: PurchaseOrderDto,
    @NewAuthUser() jwtPayload,
    @Request() req,
  ) {
    return await this._purchaseOrderUsecases.create(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.EDIT]))
  async update(
    @Param('id') id: number,
    @Body() data: UpdatePurchaseOrderDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return this._purchaseOrderUsecases.update(
      id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.VIEW]))
  async findPurchaseRequestById(
    @Param('id') id: string,
    @Request() req,
  ): Promise<PurchaseOrderModel> {
    return this._purchaseOrderUsecases.findOne(
      Number(id),
      req.headers['authorization'],
    );
  }

  @Post('calculate_remaining_budget')
  async calculateRemainingBudget(
    @Body() body: { details: PurchaseOrderDetailDto[]; createdAt?: string },
    @Request() req,
    @NewAuthUser() jwtPayload: any,
  ): Promise<any> {
    return await this._purchaseOrderUsecases.newCalculateRemainingBudget(
      body.details,
      req.headers['authorization'],
      body.createdAt ? new Date(body.createdAt) : new Date(),
      jwtPayload,
    );
  }

  @Get(':id')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.VIEW]))
  async findPurchaseOrderById(
    @Param('id') id: string,
    @Request() req,
  ): Promise<PurchaseOrderModel> {
    return this._purchaseOrderUsecases.findOne(
      Number(id),
      req.headers['authorization'],
    );
  }

  @Post('resend-email-po')
  async resendEmailPo(
    @Body() data: ResendEmailPoDto,
    @Request() req,
  ): Promise<any> {
    return this._purchaseOrderUsecases.resendEmailPo(
      data,
      req.headers['authorization'],
    );
  }
}
