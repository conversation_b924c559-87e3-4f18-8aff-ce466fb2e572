import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class SupplierInfoDto {
  @ApiProperty({ required: true, type: String, description: 'Địa chỉ Thành phố nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.SUPPLIER_NAME.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.SUPPLIER_NAME.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.SUPPLIER_NAME.MAX_LENGTH' })
  supplierName: string;

  @ApiProperty({ required: true, type: String, description: 'Địa chỉ Thành phố nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.SUPPLIER_CITY.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.SUPPLIER_CITY.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.SUPPLIER_CITY.MAX_LENGTH' })
  supplierCity: string;

  @ApiProperty({ required: true, type: String, description: 'Địa chỉ quận nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.SUPPLIER_DISTRICT.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.SUPPLIER_DISTRICT.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.SUPPLIER_DISTRICT.MAX_LENGTH' })
  supplierDistrict: string;

  @ApiProperty({ required: true, type: String, description: 'Địa chỉ quốc gia nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.SUPPLIER_COUNTRY.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.SUPPLIER_COUNTRY.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.SUPPLIER_COUNTRY.MAX_LENGTH' })
  supplierCountry: string;

  @ApiProperty({ required: true, type: String, description: 'Location nhà cung cấp vãng lai' })
  @IsNotEmpty({ message: 'VALIDATE.SUPPLIER_LOCATION.IS_REQUIRED' })
  @IsString({ each: true, message: 'VALIDATE.SUPPLIER_LOCATION.MUST_BE_STRING' })
  @MaxLength(100, { message: 'VALIDATE.SUPPLIER_LOCATION.MAX_LENGTH' })
  supplierLocation: string;
}
