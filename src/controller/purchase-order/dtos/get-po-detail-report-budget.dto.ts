import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBooleanString,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsUUID,
} from 'class-validator';
import { Status } from '../../../domain/config/enums/purchase-order.enum';

export class GetPoDetailReportBudgetDetailDto {
  @ApiProperty({ required: true })
  @IsDateString()
  @IsNotEmpty({ message: 'Vui lòng nhập thời gian có hiệu lực' })
  effectiveStart: Date;

  @ApiProperty({ required: true })
  @IsDateString()
  @IsNotEmpty({ message: 'Vui lòng nhập thời gian có hiệu lực' })
  effectiveEnd: Date;

  @ApiProperty({
    type: [Status],
    required: true,
  })
  @IsArray()
  @IsEnum(Status, { each: true })
  statusPo: Status[];

  // @ApiProperty({ required: true })
  // @IsBooleanString()
  // isAccountantApproved: string;

  @ApiProperty({ required: true })
  @IsArray()
  @IsUUID('4', { each: true })
  costCenterIds: string[];

  @ApiProperty({ required: true })
  @IsArray()
  @IsUUID('4', { each: true })
  budgetCodeIds: string[];
}
