import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { SupplierInfoDto } from './supplier-info.dto';
import { Transform, Type } from 'class-transformer';
import { EAccountAssignment } from '../../../domain/config/enums/account-assignment.enum';

export class PurchaseOrderDetailDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly budgetCodeId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.materialCodeId)
  @IsNotEmpty({ message: 'VALIDATE.COST_CENTER.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.COST_CENTER.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.COST_CENTER.MUST_BE_UUID' })
  readonly costCenterId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.materialCodeId)
  @IsNotEmpty({ message: 'VALIDATE.MEASURE_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MEASURE_CODE.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.MEASURE_CODE.MUST_BE_UUID' })
  readonly measureId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.WAREHOUSE_ID.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.WAREHOUSE_ID.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.WAREHOUSE_ID.MUST_BE_UUID' })
  warehouseId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.costCenterId)
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_CODE.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.MATERIAL_CODE.MUST_BE_UUID' })
  readonly materialCodeId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: 'MATERIAL_NAME_REQUIRED' })
  readonly materialName: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.materialCodeId)
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_GROUP.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_GROUP.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.MATERIAL_GROUP.MUST_BE_UUID' })
  readonly materialGroupId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.materialGroupId)
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_GROUP_NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_GROUP_NAME.MUST_BE_STRING' })
  materialGroupName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly unit?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly note?: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'DELIVERY_TIME_REQUIRED' })
  @IsDateString()
  deliveryTime: Date;

  @ApiProperty({
    required: false,
    description: 'Budget value, autofill based on logic',
  })
  @IsOptional()
  budget?: number;

  @ApiProperty({
    required: false,
    description: 'Remaining budget, autofill based on logic',
  })
  @IsOptional()
  remainingBudget?: number;

  @ApiProperty({
    required: false,
    description: 'Remaining actual budget, autofill based on logic',
  })
  @IsOptional()
  remainingActualBudget?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly unitPrice?: number;

  vat?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => +value)
  @IsNumber(
    { allowInfinity: false, allowNaN: false },
    { message: 'VALIDATE.TOTAL_AMOUNT.MUST_BE_NUMBER' },
  )
  readonly totalAmount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  totalAmountVat?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly prReference?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly pirId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly supplierId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => SupplierInfoDto)
  supplierInfo?: SupplierInfoDto;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly estimatedPrice?: number;

  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'PURCHASE_PRICE_REQUIRED' })
  readonly purchasePrice: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly standardQuantity?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly inventoryNumber?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly quantityOfferedPurchase?: number;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'QUANTITY_REQUIRED' })
  readonly quantity: number;

  @ApiProperty({ enum: EAccountAssignment, required: true })
  @IsEnum(EAccountAssignment)
  readonly accountAssignment: EAccountAssignment;

  @ApiProperty({ required: false })
  @ValidateIf(
    (o) =>
      (o.accountAssignment === EAccountAssignment.F ||
        o.accountAssignment === EAccountAssignment.K ||
        o.accountAssignment === EAccountAssignment.X) &&
      !o.material_code,
  )
  @IsString()
  @IsNotEmpty()
  readonly accountGl?: string;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.accountAssignment === EAccountAssignment.A)
  @IsString()
  @IsNotEmpty()
  readonly property?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  readonly internalOrder?: string;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.accountAssignment === EAccountAssignment.X)
  @IsString()
  @IsNotEmpty()
  readonly wbs?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly prDetailId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @IsUUID('4')
  budgetId?: string;

  matchingBudget?: any;
  adjustBudgetId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly id?: number;

  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'VALIDATE.CURRENCY.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.CURRENCY.MUST_BE_UUID' })
  currencyId?: string;

  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'VALIDATE.EXCHANGE_RATE.IS_REQUIRED' })
  @Transform(({ value }) => +value)
  @IsNumber(
    { allowInfinity: false, allowNaN: false },
    { message: 'VALIDATE.EXCHANGE_RATE.MUST_BE_NUMBER' },
  )
  exchangeRate?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.pr_detail_id && !o.pr_reference)
  @IsNotEmpty({ message: 'VALIDATE.REQUESTED_QUANTITY.IS_REQUIRED' })
  @Transform(({ value }) => +value)
  @IsNumber(
    { allowInfinity: false, allowNaN: false },
    { message: 'VALIDATE.REQUESTED_QUANTITY.MUST_BE_NUMBER' },
  )
  requestedQuantity?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => +value)
  @IsNumber(
    { allowInfinity: false, allowNaN: false },
    { message: 'VALIDATE.TOTAL_CONVERTED_AMOUNT.MUST_BE_NUMBER' },
  )
  totalConvertedAmount?: number;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'TAX_CODE_REQUIRED' })
  @IsUUID('4')
  readonly taxCodeId: string;
}
