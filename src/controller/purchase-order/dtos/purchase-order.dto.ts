import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { PurchaseOrderDetailDto } from './purchase-order-detail.dto';
import {
  EDisplayStatus,
  Priority,
  State,
  Status,
} from '../../../domain/config/enums/purchase-order.enum';
import { HistoryApproveDto } from '../../approve/dtos/history-approve.dto';
import { ApprovalLevelDto } from '../../approve/dtos/approve.dto';

export enum EPaymentMethod {
  OFFER = 'OFFER', //'Chào giá'
  COMPETITIVE_QUOTATION = 'COMPETITIVE_QUOTATION', //Báo giá cạnh tranh'
  OPEN_BIDDING = 'OPEN_BIDDING', //<PERSON><PERSON><PERSON> thầu mở rộng'
  SELECTIVE_BIDDING = 'SELECTIVE_BIDDING', //Đ<PERSON>u thầu có chọn lọc'
  DIRECT_APPOINTMENT = 'DIRECT_APPOINTMENT', //Chỉ định thầu'
}

export class PurchaseOrderDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SECTOR_REQUIRED' })
  @IsUUID('4')
  readonly sectorId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'BUSINESS_UNIT_REQUIRED' })
  @IsUUID('4')
  readonly businessUnitId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly isCheckBudget: boolean;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'REQUESTER_REQUIRED' })
  @IsUUID('4')
  requesterId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'TYPE_PO_REQUIRED' })
  @IsUUID('4')
  readonly typePoId: string;

  @ApiProperty({ enum: Status, required: false })
  @IsOptional()
  readonly statusPo: Status;

  displayStatusPo?: EDisplayStatus;

  @ApiProperty({ enum: State, required: false })
  @IsOptional()
  readonly statePo: State;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly costCenterId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly budgetCodeId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly purchaseOrgId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly purchaseGroupId?: string;

  @ApiProperty({ enum: Priority, required: true })
  @IsNotEmpty({ message: 'PRIORITY_REQUIRED' })
  readonly priority: Priority;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  readonly reason: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly total?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly totalPriceVat?: number;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // readonly purchaser?: string;

  @ApiProperty({
    type: Array,
    description: 'Các văn bản, tài liệu liên quan đến PO',
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.FILES.MUST_BE_ARRAY' })
  @IsString({ each: true, message: 'VALIDATE.FILES.MUST_BE_STRING' })
  attachments?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  refId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly accountGl?: string;

  @ApiProperty({ type: [PurchaseOrderDetailDto], required: true })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'DETAILS_REQUIRED' })
  @Type(() => PurchaseOrderDetailDto)
  details: PurchaseOrderDetailDto[];

  @ApiProperty({ type: [HistoryApproveDto], required: false })
  @IsOptional()
  @IsArray()
  @Type(() => HistoryApproveDto)
  history?: HistoryApproveDto[];

  @ApiProperty({ enum: EPaymentMethod, required: false })
  @IsOptional()
  @IsEnum(EPaymentMethod)
  readonly paymentMethod: EPaymentMethod;

  @ApiProperty({ type: [ApprovalLevelDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApprovalLevelDto)
  approvalLevelDtos?: ApprovalLevelDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.CURRENCY.MUST_BE_UUID' })
  currencyId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => +value)
  @IsNumber(
    { allowInfinity: false, allowNaN: false },
    { message: 'VALIDATE.EXCHANGE_RATE.MUST_BE_NUMBER' },
  )
  exchangeRate?: number;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'REQUESTER_PROCESS_TYPE' })
  @IsUUID('4')
  processTypeId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'PLANT_IS_REQUIRED' })
  @IsUUID('4')
  plantId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.FUNCTION_UNIT.MUST_BE_UUID' })
  @IsUUID('4')
  functionUnitId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.DEPARTMENT.MUST_BE_UUID' })
  @IsUUID('4')
  departmentId?: string;
}

export class UpdatePurchaseOrderDto extends PurchaseOrderDto {}
