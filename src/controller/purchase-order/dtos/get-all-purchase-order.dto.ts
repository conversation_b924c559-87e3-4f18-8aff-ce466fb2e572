import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import {
  EDisplayStatus,
  State,
} from '../../../domain/config/enums/purchase-order.enum';

export class GetPurchaseOrderDto extends PaginationDto {
  @ApiProperty({
    type: [EDisplayStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(EDisplayStatus, { each: true })
  displayStatusPos?: EDisplayStatus[];

  @ApiProperty({
    type: [State],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(State, { each: true })
  statePos?: State[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  typePoIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  costCenterIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  budgetCodeIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  materialCodeIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  materialNames?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  readonly idPr?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  readonly idPo?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsNotEmpty({ message: 'crudTemplateActiveTab_REQUIRED' })
  @IsString()
  crudTemplateActiveTab?: string; // ALL/REQUEST/WAITING/APPROVED

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  processTypeIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  companyIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  businessUnitIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  sectorIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  departmentIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  functionUnitIds?: string[];
}
