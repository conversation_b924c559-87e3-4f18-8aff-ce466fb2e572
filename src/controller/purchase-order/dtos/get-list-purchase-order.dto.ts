import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { EDisplayStatus } from '../../../domain/config/enums/purchase-order.enum';
import { State } from '../../../domain/config/enums/purchase-request.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetListPurchaseOrderDto extends PaginationDto {
  @ApiProperty({
    type: [EDisplayStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(EDisplayStatus, { each: true })
  display_status_po?: EDisplayStatus[];

  @ApiProperty({
    type: [State],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(State, { each: true })
  state_po?: State[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  type_po?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  cost_center?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  budget_code?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  material_code?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  material_name?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  readonly idPr?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  readonly idPo?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsNotEmpty({ message: 'crudTemplateActiveTab_REQUIRED' })
  @IsString()
  crudTemplateActiveTab?: string; // ALL/REQUEST/WAITING/APPROVED

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  process_type?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  company?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  business_unit?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  sector?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  department?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  function_unit?: string[];
}
