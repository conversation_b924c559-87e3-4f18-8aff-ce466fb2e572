import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Post,
  Request,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { ApprovePrDto, CancelPrPoDto, RejectPrDto } from './dtos/approve.dto';
//import * as jwt from 'jsonwebtoken';
import {
  errorMessage,
  getErrorMessage,
} from '../../domain/messages/error-message';
import { ApproveType } from '../../infrastructure/entities/approval-level.entity';
import {
  EPurchaseOrderPermission,
  EPurchaseRequestPermission,
} from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { approvalUsecases } from '../../usecases/approval.usecases';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { Status } from '../../domain/config/enums/purchase-request.enum';

@Controller('/approve-email')
@UseInterceptors(TransformationInterceptor, TransactionInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Approve Email')
export class approveEmailController {
  constructor(private readonly _approvalUsecases: approvalUsecases) {}

  @Post('approve-pr')
  async approvePr(
    @Body() data: ApprovePrDto,
    @Res() res: Response,
    @NewAuthUser() jwtPayload,
    @Request() req,
  ): Promise<any> {
    // try {
    const result = await this._approvalUsecases.approveLowestLevel(
      data,
      req.headers['authorization'],
      jwtPayload,
      req,
    );
    return result
      ? res.status(200).send({
          data: result,
          message: 'SUCCESS',
          duration: `${Date.now() - Date.now()}ms`,
        })
      : res.status(200).send({
          message: 'SUCCESS',
          duration: `${Date.now() - Date.now()}ms`,
        });
    // } catch (err) {
    //   console.log(err);
    //   // Token không hợp lệ hoặc đã hết hạn
    //   throw new HttpException(getErrorMessage(errorMessage.E_4033()), HttpStatus.FORBIDDEN);
    // }
  }

  @Post('approve-po')
  async approvePO(
    @Body() data: ApprovePrDto,
    @NewAuthUser() jwtPayload,
    @Res() res: Response,
    @Request() req,
  ): Promise<any> {
    // try {
    const result = await this._approvalUsecases.approvePOLowestLevel(
      data,
      ApproveType.PO,
      req.headers['authorization'],
      jwtPayload,
      req,
    );
    return result
      ? res.status(200).send({
          data: result,
          message: 'SUCCESS',
          duration: `${Date.now() - Date.now()}ms`,
        })
      : res.status(200).send({
          message: 'SUCCESS',
          duration: `${Date.now() - Date.now()}ms`,
        });
    // } catch (err) {
    //   // Token không hợp lệ hoặc đã hết hạn
    //   throw new ForbiddenException();
    // }
  }

  @Post('reject-pr')
  async rejectPR(
    @Body() data: RejectPrDto,
    @NewAuthUser() jwtPayload,
    @Res() res: Response,
    @Request() req,
  ): Promise<any> {
    // try {
    const verify = await this._approvalUsecases.verifyLevel(data.levelId);

    if (jwtPayload.email !== verify.email) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5333()),
        HttpStatus.FORBIDDEN,
      );
    }

    await this._approvalUsecases.declinePRLowestLevel(
      data,
      req.headers['authorization'],
      jwtPayload,
    );

    return res
      .status(200)
      .send({ message: 'SUCCESS', duration: `${Date.now() - Date.now()}ms` });
    // } catch (err) {
    //   // Token không hợp lệ hoặc đã hết hạn
    //   throw new ForbiddenException();
    // }
  }

  @Post('reject-po')
  async rejectPO(
    @Body() data: RejectPrDto,
    @NewAuthUser() jwtPayload,
    @Res() res: Response,
    @Request() req,
  ): Promise<any> {
    // try {
    const verify = await this._approvalUsecases.verifyLevel(data.levelId);

    if (jwtPayload.email !== verify.email) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5333()),
        HttpStatus.FORBIDDEN,
      );
    }

    await this._approvalUsecases.declinePOLowestLevel(
      data,
      req.headers['authorization'],
      jwtPayload,
    );

    return res
      .status(200)
      .send({ message: 'SUCCESS', duration: `${Date.now() - Date.now()}ms` });
    // } catch (err) {
    //   // Token không hợp lệ hoặc đã hết hạn
    //   throw new ForbiddenException();
    // }
  }

  @Post('cancel-pr')
  @UseGuards(NewPermissionGuard([EPurchaseRequestPermission.CANCEL]))
  async cancelPR(
    @Body() data: CancelPrPoDto,
    @Res() res: Response,
    @Request() req,
    @NewAuthUser() jwtPayload,
  ): Promise<any> {
    await this._approvalUsecases.cancelPRPo(
      data,
      req.headers['authorization'],
      true,
      jwtPayload,
      Status.Cancel,
    );

    return res
      .status(200)
      .send({ message: 'SUCCESS', duration: `${Date.now() - Date.now()}ms` });
  }

  @Post('cancel-po')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.CANCEL]))
  async cancelPO(
    @Body() data: CancelPrPoDto,
    @Res() res: Response,
    @Request() req,
    @NewAuthUser() jwtPayload,
  ): Promise<any> {
    await this._approvalUsecases.cancelPRPo(
      data,
      req.headers['authorization'],
      false,
      jwtPayload,
      Status.Cancel,
    );

    return res
      .status(200)
      .send({ message: 'SUCCESS', duration: `${Date.now() - Date.now()}ms` });
  }

  @Post('closed-po')
  @UseGuards(NewPermissionGuard([EPurchaseOrderPermission.CLOSED]))
  async closedPO(
    @Body() data: CancelPrPoDto,
    @Res() res: Response,
    @Request() req,
    @NewAuthUser() jwtPayload,
  ): Promise<any> {
    await this._approvalUsecases.cancelPRPo(
      data,
      req.headers['authorization'],
      false,
      jwtPayload,
      Status.Closed,
    );

    return res
      .status(200)
      .send({ message: 'SUCCESS', duration: `${Date.now() - Date.now()}ms` });
  }

  @Post('closed-pr')
  @UseGuards(NewPermissionGuard([EPurchaseRequestPermission.CLOSED]))
  async closedPR(
    @Body() data: CancelPrPoDto,
    @Res() res: Response,
    @Request() req,
    @NewAuthUser() jwtPayload,
  ): Promise<any> {
    await this._approvalUsecases.cancelPRPo(
      data,
      req.headers['authorization'],
      true,
      jwtPayload,
      Status.Closed,
    );

    return res
      .status(200)
      .send({ message: 'SUCCESS', duration: `${Date.now() - Date.now()}ms` });
  }
}
