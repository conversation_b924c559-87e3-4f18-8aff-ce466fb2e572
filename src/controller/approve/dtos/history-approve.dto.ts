import { ApiProperty } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class HistoryApproveDto {
  @ApiProperty({ required: false })
  @IsOptional()
  readonly level?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly role?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly user_id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly email?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly status?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly name?: string;
}