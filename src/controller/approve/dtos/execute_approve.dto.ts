import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
} from 'class-validator';

export class ExecuteApproveDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'APPROVE_MANAGER_REQUIRED' })
  readonly approve_manager: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'APPROVE_HEAD_DEPARTMENT_REQUIRED' })
  readonly approve_head_department: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'APPROVE_SPECIAL_REQUIRED' })
  readonly approve_special: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'APPROVE_SELF_SELECTION_REQUIRED' })
  readonly approve_self_selection: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'APPROVE_SUBMITTER_REQUIRED' })
  readonly approve_submitter: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'APPROVE_MULTIPLE_MANAGER_REQUIRED' })
  readonly approve_multiple_manager: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'APPROVE_MULTIPLE_HEAD_DEPARTMENT_REQUIRED' })
  readonly approve_multiple_head_department: string;
}