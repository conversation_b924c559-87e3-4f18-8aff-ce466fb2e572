import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsOptional, ValidateNested, ArrayMinSize } from 'class-validator';
import { Comparison, ListConditionDto } from './list-condition.dto';
import { Type } from 'class-transformer';

export enum ComparisonType {
  Equal = 'Equal',
  GreaterThan = 'GreaterThan',
  GreaterThanOrEqual = 'GreaterThanOrEqual',
  LessThan = 'LessThan',
  LessThanOrEqual = 'LessThanOrEqual',
  NotEqual = 'NotEqual',
  Includes = 'Includes',
  NotIncludes = 'NotIncludes',
}

export class CreateConditionDto {
  @ApiProperty({ type: [ListConditionDto], required: true })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'LIST_CONDITIONS_REQUIRED' })
  @Type(() => ListConditionDto)
  readonly type: ListConditionDto[];

  @ApiProperty({ required: true })
  @IsString()
  readonly field: string;

  @ApiProperty({ enum: Comparison, required: true })
  @IsString()
  readonly comparison: Comparison;

  @ApiProperty({ required: true })
  @IsString()
  readonly value: string;
}