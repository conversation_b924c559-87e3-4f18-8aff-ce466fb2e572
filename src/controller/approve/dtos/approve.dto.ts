import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { ApproveType } from '../../../infrastructure/entities/approval-level.entity';

export enum ComparisonType {
  Equal = 'Equal',
  GreaterThan = 'GreaterThan',
  GreaterThanOrEqual = 'GreaterThanOrEqual',
  LessThan = 'LessThan',
  LessThanOrEqual = 'LessThanOrEqual',
  NotEqual = 'NotEqual',
  Includes = 'Includes',
  NotIncludes = 'NotIncludes',
}

export enum StatusLevel {
  Temporary = 'Temporary', // Saved but not sent
  Pending = 'Pending', // Already sent for approval
  InProgress = 'In-Progress', // Approval levels are in progress
  Approved = 'Approved', // Data has been approved
  Rejected = 'Rejected', // Data has been rejected
  Rechecked = 'Rechecked', // Data has been returned to creator (for rechecking)
  Cancel = 'Cancel', // Data has been final approved but has been canceled
}

export class UpdateApprovedItemPoDto {
  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'VALIDATE.ID.IS_REQUIRED' })
  @IsInt({ message: 'VALIDATE.ID.MUST_BE_INTEGER' })
  id: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.INTERNAL_ORDER.IS_NOT_EMPTY' })
  @IsString({ message: 'VALIDATE.ID.MUST_BE_STRING' })
  internalOrder?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.BUDGET_CODE.IS_NOT_EMPTY' })
  @IsUUID('4', { message: 'VALIDATE.BUDGET_CODE.MUST_BE_UUID' })
  budgetCodeId?: string;
}

export class UpdateApprovedPoDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4', { message: 'VALIDATE.BUDGET_CODE.MUST_BE_UUID' })
  budgetCodeId?: string;

  @ApiProperty({ type: [UpdateApprovedItemPoDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateApprovedItemPoDto)
  updateApprovedItemPoDtos?: UpdateApprovedItemPoDto[];
}

export class ApprovalLevelDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'LEVEL_REQUIRED' })
  readonly level: number;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'ROLE_REQUIRED' })
  role: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly id?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly userId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly isSendMail?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  status?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  email?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  purchaseOrderId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  purchaseRequestId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  approveType: ApproveType;

  @ApiProperty({ required: false })
  @IsOptional()
  isSendMailCreator: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  isAccountantApproved: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  allowSelect: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  staffApprovalWorkflowId: string;
}

export class ApproveDto {
  @ApiProperty({ required: false })
  @IsOptional()
  readonly name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly submittedPersonName: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly notifiedPerson?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly isActive: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly isSendMail: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly comment: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly forward: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly rollback: string;

  @ApiProperty({ type: [ApprovalLevelDto], required: true })
  @ValidateNested({ each: true })
  @Type(() => ApprovalLevelDto)
  readonly levels: ApprovalLevelDto[];
}

export class ConditionDto {
  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'LEVEL_REQUIRED' })
  readonly level: number;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'FIELD_REQUIRED' })
  readonly field: string;

  @ApiProperty({ enum: ComparisonType, required: true })
  @IsNotEmpty({ message: 'COMPARISON_TYPE_REQUIRED' })
  readonly comparisonType: ComparisonType;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'VALUE_REQUIRED' })
  readonly value: any;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly parentId?: number;
}

export class CheckConditionsDto {
  @ApiProperty({ type: [ConditionDto], required: true })
  @ValidateNested({ each: true })
  @Type(() => ConditionDto)
  readonly conditions: ConditionDto[];

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'DATA_REQUIRED' })
  readonly prData: any;
}

export class ApprovePrDto {
  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'LEVEL_REQUIRED' })
  readonly levelId: number;

  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'LEVEL_REQUIRED' })
  readonly purchaseId: number;

  @ApiPropertyOptional({ required: false })
  @IsOptional({ message: '' })
  readonly reason: string;

  @ApiProperty({ type: [ApprovalLevelDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApprovalLevelDto)
  approvalLevelDtos?: ApprovalLevelDto[];

  @ApiProperty({ type: UpdateApprovedPoDto, required: false })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateApprovedPoDto)
  updateApprovedPoDto?: UpdateApprovedPoDto;
}

export class RejectPrDto {
  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'LEVEL_REQUIRED' })
  readonly levelId: number;

  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'PURCHASE_ID_REQUIRED' })
  readonly purchaseId: number;

  @ApiPropertyOptional({ required: false })
  @IsOptional({ message: '' })
  readonly reason: string;
}

export class CancelPrPoDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'PURCHASE_ID_REQUIRED' })
  readonly purchaseId: number;

  @ApiPropertyOptional({ required: false })
  @IsOptional({ message: '' })
  readonly reason?: string;
}
