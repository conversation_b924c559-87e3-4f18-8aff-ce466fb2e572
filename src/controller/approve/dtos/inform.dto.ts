import { ApiProperty } from '@nestjs/swagger';
import {
	IsNotEmpty,
} from 'class-validator';

export class InformDto {
	@ApiProperty({ required: true })
	@IsNotEmpty({ message: 'INFORM_MANAGER_REQUIRED' })
	readonly inform_manager: string;

	@ApiProperty({ required: true })
	@IsNotEmpty({ message: 'INFORM_HEAD_DEPARTMENT_REQUIRED' })
	readonly inform_head_department: string;

	@ApiProperty({ required: true })
	@IsNotEmpty({ message: 'INFORM_SPECIAL_REQUIRED' })
	readonly inform_special: string;

	@ApiProperty({ required: true })
	@IsNotEmpty({ message: 'INFORM_SELF_SELECTION_REQUIRED' })
	readonly inform_self_selection: string;

	@ApiProperty({ required: true })
	@IsNotEmpty({ message: 'INFORM_SUBMITTER_REQUIRED' })
	readonly inform_submitter: string;
}