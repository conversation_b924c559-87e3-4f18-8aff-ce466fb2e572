import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export enum Comparison {
	EQUAL = 'Bằng',
	GREATER_THAN = 'Lớn hơn',
	GREATER_THAN_OR_EQUAL = 'Lớn hơn hoặc bằng',
	LESSER_THAN = 'Nhỏ hơn',
	LESSER_THAN_OR_EQUAL = 'Nhỏ hơn hoặc bằng',
	OTHER = 'Khác',
	INCLUDE = 'Bao gồm',
	NOT_INCLUDE = 'Không bao gồm'
}

export class ListConditionDto {
	@ApiProperty({ required: true })
	@IsString()
	readonly segment?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly company?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly business_unit?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly department?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly cost_center?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly budget_code?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly pr_type?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly pr_value?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly po_type?: string;

	@ApiProperty({ required: true })
	@IsString()
	readonly po_value?: string;

	@ApiProperty({ enum: Comparison, required: true })
	@IsString()
	readonly comparison: Comparison;

	@ApiProperty({ required: true })
	@IsString()
	readonly values: string;
}