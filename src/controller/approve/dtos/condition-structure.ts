import { ConditionDto } from "./approve.dto";

export enum NodeType {
  condition_node = "condition_node",
  destination_node = "destination_node",
}

export class ApprovalNode {
  id: number;
  parent_id: number | null;
  type: NodeType;
  name: string;
  conditions: ConditionDto[] | null;
  children: ApprovalNode[] = [];

  constructor(
    id: number,
    type: NodeType,
    name: string,
    conditions: ConditionDto[] | null,
    parentId?: number
  ) {
    this.id = id;
    this.type = type;
    this.name = name;
    this.conditions = conditions;
    this.parent_id = parentId || null;
  }

  addChild(child: ApprovalNode): void {
    child.parent_id = this.id;
    this.children.push(child);
  }

  // Converts ApprovalNode to TreeNode type
  toTreeNode(): TreeNode {
    return {
      id: this.id,
      parent_id: this.parent_id,
      type: this.type,
      name: this.name,
      conditions: this.conditions,
    };
  }
}

type TreeNode = {
  id: string | number;
  parent_id: string | number | null;
  type: NodeType;
  name: string;
  conditions: ConditionDto[] | null; // Can return null for conditions when processing the list
};