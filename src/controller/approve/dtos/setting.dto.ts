import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class SettingDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SETTING_EMAIL_APPROVE_REQUIRED' })
  readonly setting_email_approve: boolean;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SETTING_ALLOW_COMMENT_REQUIRED' })
  readonly setting_allow_comment: boolean;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SETTING_ALLOW_TRANSFER_REQUIRED' })
  readonly setting_allow_transfer: boolean;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SETTING_ALLOW_SEND_BACK_REQUIRED' })
  readonly setting_allow_send_back: boolean;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SETTING_ALLOW_ADD_APPROVER_REQUIRED' })
  readonly setting_allow_add_approver: boolean;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SETTING_SAME_APPROVER_REQUIRED' })
  readonly setting_same_approver: boolean;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SETTING_APPROVER_BLANK_REQUIRED' })
  readonly setting_approver_blank: boolean;
}
