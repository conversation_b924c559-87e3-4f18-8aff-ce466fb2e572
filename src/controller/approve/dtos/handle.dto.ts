import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
} from 'class-validator';

export class HandleDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'HAND<PERSON>_MANGER_REQUIRED' })
  readonly handle_manager: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'HANDLE_HEAD_DEPARTMENT_REQUIRED' })
  readonly handle_head_department: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'HANDLE_SPECIAL_REQUIRED' })
  readonly handle_special: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'HANDLE_SELF_SELECTION_REQUIRED' })
  readonly handle_self_selection: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'HANDLE_SUBMITTER_REQUIRED' })
  readonly handle_submitter: string;
}