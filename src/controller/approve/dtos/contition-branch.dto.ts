import { IsString, Is<PERSON><PERSON>y, IsOptional, ValidateNested, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateConditionDto } from './create-condition.dto';
import { ApiProperty } from '@nestjs/swagger';

export class CreateConditionBranchDto {
  @ApiProperty({ required: true })
  @IsString()
  readonly name: string;

  @ApiProperty({ type: [CreateConditionDto], required: true })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'CONDITIONS_REQUIRED' })
  @Type(() => CreateConditionDto)
  readonly conditions: CreateConditionDto[];
}
