import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { EBusinessOwnerStatus } from '../../../domain/config/enums/business-owner.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetBusinessOwnerListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EBusinessOwnerStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EBusinessOwnerStatus, { each: true })
  statuses?: EBusinessOwnerStatus[];

  codes?: string[];
  ids: string[];
}
