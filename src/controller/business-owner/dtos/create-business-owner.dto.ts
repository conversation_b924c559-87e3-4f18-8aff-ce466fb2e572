import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { EBusinessOwnerStatus } from '../../../domain/config/enums/business-owner.enum';

export class CreateBusinessOwnerDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code of BO',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of BO',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of BO',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EBusinessOwnerStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EBusinessOwnerStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EBusinessOwnerStatus;

  createdAt?: string;
  updatedAt?: string;
}
