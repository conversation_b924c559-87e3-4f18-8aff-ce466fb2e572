import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
  Response,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpStatus,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { BusinessOwnerUsecases } from '../../usecases/business-owner.usecases';
import { EBusinessOwnerPermission } from '../../utils/constants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { CreateBusinessOwnerDto } from './dtos/create-business-owner.dto';
import { DeleteBusinessOwnerDto } from './dtos/delete-business-owner.dto';
import { GetBusinessOwnerListDto } from './dtos/get-business-owner-list.dto';
import { UpdateBusinessOwnerDto } from './dtos/update-business-owner.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('/business-owner')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Business Owner')
export class BusinessOwnerController {
  constructor(private readonly businessOwnerUsecases: BusinessOwnerUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EBusinessOwnerPermission.CREATE]))
  async create(@Body() data: CreateBusinessOwnerDto, @Request() req) {
    return await this.businessOwnerUsecases.createBusinessOwner(
      data,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EBusinessOwnerPermission.EDIT]))
  async update(
    @Body() data: UpdateBusinessOwnerDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.businessOwnerUsecases.updateBusinessOwner(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EBusinessOwnerPermission.VIEW]))
  async getDetail(
    @Param() param: DeleteBusinessOwnerDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.businessOwnerUsecases.getDetailBusinessOwner(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EBusinessOwnerPermission.VIEW]))
  async getList(
    @Query() param: GetBusinessOwnerListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.businessOwnerUsecases.getBusinessOwners(
      param,
      jwtPayload,
    );
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EBusinessOwnerPermission.DELETE]))
  async delete(
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.businessOwnerUsecases.deleteBusinessOwner(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-business-owner')
  @UseGuards(NewPermissionGuard([EBusinessOwnerPermission.EXPORT]))
  async exportBusinessOwner(
    @Query() param: GetBusinessOwnerListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.businessOwnerUsecases.exportBusinessOwner(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Post('/import-business-owner')
  @UseGuards(NewPermissionGuard([EBusinessOwnerPermission.IMPORT]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importBusinessOwner(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.businessOwnerUsecases.importBusinessOwner(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }
}
