import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Request,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Response,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { storage } from '../../domain/config/multer.config';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { BudgetCodeUsecases } from '../../usecases/budget-code.usecases';
import { EBudgetCodePermission } from '../../utils/constants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { CreateBudgetCodeDto } from './dtos/create-budget-code.dto';
import { DeleteBudgetCodeDto } from './dtos/delete-budget-code.dto';
import { GetBudgetCodeListDto } from './dtos/get-budget-code-list.dto';
import { GetDetailBudgetCodeDto } from './dtos/get-detail-budget-code.dto';
import { UpdateBudgetCodeDto } from './dtos/update-budget-code.dto';
import { GetBudgetCodeListByIdsDto } from './dtos/get-budget-code-list-by-ids.dto';

@Controller('/budget-code')
@UseInterceptors(TransformationInterceptor, AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Budget Code')
export class BudgetCodeController {
  constructor(private readonly budgetCodeUsecases: BudgetCodeUsecases) {}

  @Post('/create')
  @UseInterceptors(TransactionInterceptor)
  @UseGuards(NewPermissionGuard([EBudgetCodePermission.CREATE]))
  async create(
    @Body() data: CreateBudgetCodeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetCodeUsecases.createBudgetCode(data, jwtPayload);
  }

  @Get(':id/detail')
  @UseInterceptors(TransactionInterceptor)
  // @UseGuards(NewPermissionGuard([EBudgetCodePermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailBudgetCodeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetCodeUsecases.getBudgetCodeDetail(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EBudgetCodePermission.VIEW]))
  async getList(
    @Query() param: GetBudgetCodeListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetCodeUsecases.getBudgetCodes(param, jwtPayload);
  }

  @Delete(':id/delete')
  @UseInterceptors(TransactionInterceptor)
  @UseGuards(NewPermissionGuard([EBudgetCodePermission.DELETE]))
  async delete(
    @Param() param: DeleteBudgetCodeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetCodeUsecases.deleteBudgetCode(param, jwtPayload);
  }

  @Put(':id/update')
  @UseInterceptors(TransactionInterceptor)
  @UseGuards(NewPermissionGuard([EBudgetCodePermission.EDIT]))
  async update(
    @Param('id') budgetCodeId: string,
    @Body() data: UpdateBudgetCodeDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetCodeUsecases.updateBudgetCode(
      budgetCodeId,
      data,
      jwtPayload,
    );
  }

  @Post('/import-budget-code')
  @UseGuards(NewPermissionGuard([EBudgetCodePermission.IMPORT]))
  @UseInterceptors(TransactionInterceptor)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importBudgetCode(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.budgetCodeUsecases.importBudgetCode(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-budget-code')
  @UseGuards(
    NewPermissionGuard([
      [EBudgetCodePermission.VIEW, EBudgetCodePermission.EXPORT],
    ]),
  )
  async exportBudgetCodes(
    @Query() param: GetBudgetCodeListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.budgetCodeUsecases.exportBudgetCodes(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  ///For validate detail PR PO
  @Post('/list-by-ids')
  async getListByIds(
    @Body() param: GetBudgetCodeListByIdsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.budgetCodeUsecases.getListByIds(param, jwtPayload);
  }
}
