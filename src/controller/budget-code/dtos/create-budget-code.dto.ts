import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON>yMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateIf,
} from 'class-validator';
import { EBudgetCodeStatus } from '../../../domain/config/enums/budget-code.enum';
import { EBudgetType } from '../../../domain/config/enums/budget.enum';

export class CreateBudgetCodeDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of budget code',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'ID',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON>ô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EBudgetCodeStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EBudgetCodeStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EBudgetCodeStatus;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Business Owner',
  })
  @IsString()
  @IsNotEmpty({ message: 'VALIDATE.BUSINESS_OWNER_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.BUSINESS_OWNER_ID.MUST_BE_UUID' })
  businessOwnerId: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Loại ngân sách',
    enum: EBudgetType,
  })
  @IsNotEmpty({ message: 'VALIDATE.BUDGET_TYPE.IS_REQUIRED' })
  @IsEnum(EBudgetType, {
    message: 'VALIDATE.BUDGET_TYPE.INVALID_VALUE',
  })
  budgetType: EBudgetType;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Cost',
  })
  @ValidateIf((o) => o.budgetType === EBudgetType.OPEX)
  @IsNotEmpty({ message: 'VALIDATE.COST_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.COST_ID.MUST_BE_UUID' })
  costId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Internal Order',
  })
  @ValidateIf((o) => o.budgetType === EBudgetType.CAPEX)
  @IsNotEmpty({ message: 'VALIDATE.INTERNAL_ORDER.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.INTERNAL_ORDER.MUST_BE_STRING' })
  internalOrder?: string;

  createdAt?: string;
  updatedAt?: string;
}
