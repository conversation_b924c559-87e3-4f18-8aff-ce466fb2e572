import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { EBudgetCodeStatus } from '../../../domain/config/enums/budget-code.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { EBudgetType } from '../../../domain/config/enums/budget.enum';

export class GetBudgetCodeListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EBudgetCodeStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EBudgetCodeStatus, { each: true })
  statuses?: EBudgetCodeStatus[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  businessOwnerIds?: string[];

  @ApiProperty({
    type: [EBudgetType],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EBudgetType, { each: true })
  budgetTypes?: EBudgetType[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  costIds?: string[];

  // @ApiProperty({
  //   type: [String],
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray()
  // @IsUUID(4, { each: true })
  // costcenterSubaccountIds?: string[];
  //For filter budget code
  businessOwnerCodes?: string[];
  //For filter cost center / sub account
  sectorCodes?: string[];
  companyCodes?: string[];
  businessUnitCodes?: string[];
  departmentCodes?: string[];
  costCodes?: string[];
}
