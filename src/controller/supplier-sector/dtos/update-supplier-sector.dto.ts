import { IsOptional, IsUUID } from 'class-validator';
import { CreateSupplierSectorDto } from './create-supplier-sector.dto';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateSupplierSectorDto extends CreateSupplierSectorDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Id',
  })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.SUPPLIER_SECTOR_ID.MUST_BE_UUID' })
  id?: string;
}
