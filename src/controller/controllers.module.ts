import { Module } from '@nestjs/common';
import { emailTemplateService } from '../infrastructure/config/email-transport-config/email-template.service';
import { emailTransportService } from '../infrastructure/config/email-transport-config/email.service';
import { EnvironmentConfigModule } from '../infrastructure/config/environment-config/environment-config.module';
import { HttpServiceModule } from '../infrastructure/http/http-service.module';
import { RepositoriesModule } from '../infrastructure/repositories/repositories.module';
import { ActualSpendingUsecases } from '../usecases/actual-spending.usecases';
import { ApiLogUsecases } from '../usecases/api-log.usecase';
import { approvalEmailUsecases } from '../usecases/approval-email.usecases';
import { ApprovalWorkflowUsecases } from '../usecases/approval-workflow.usecases';
import { approvalUsecases } from '../usecases/approval.usecases';
import { BudgetCapexUsecases } from '../usecases/budget-capex.usecases';
import { BudgetCodeUsecases } from '../usecases/budget-code.usecases';
import { BudgetInvestmentUsecases } from '../usecases/budget-investment.usecases';
import { BudgetOpexUsecases } from '../usecases/budget-opex.usecases';
import { BudgetUsecases } from '../usecases/budget.usecases';
import { BusinessOwnerUsecases } from '../usecases/business-owner.usecases';
import { BusinessUnitUsecases } from '../usecases/business-unit.usecases';
import { CompanyUsecases } from '../usecases/company.usecases';
import { ConditionDetailUsecases } from '../usecases/condition-detail.usecase';
import { ConditionUsecases } from '../usecases/condition.usecase';
import { CostSubHistoryUsecases } from '../usecases/cost-sub-history.usecases';
import { CostUsecases } from '../usecases/cost.usecases';
import { CostcenterSubaccountUsecases } from '../usecases/costcenter-subaccount.usecases';
import { CronUsecases } from '../usecases/cron.usecases';
import { CurrencyUnitExchangeUsecases } from '../usecases/currency-unit-exchange.usecases';
import { CurrencyUnitUsecases } from '../usecases/currency-unit.usecases';
import { DepartmentUsecases } from '../usecases/department.usecases';
import { FileExportHistoryUsecases } from '../usecases/file-export-history.usecases';
import { FileImportHistoryUsecases } from '../usecases/file-import-history.usecases';
import { FileUsecases } from '../usecases/file.usecases';
import { FunctionUnitUsecases } from '../usecases/function-unit.usecases';
import { ImportUsecases } from '../usecases/import.usecases';
import { IncreasementCodeUsecases } from '../usecases/increasement-code.usecases';
import { InventoryStandardUsecases } from '../usecases/inventory-standard.usecases';
import { MaterialGroupUsecases } from '../usecases/material-group.usecases';
import { MaterialSectorUsecases } from '../usecases/material-sector.usecases';
import { MaterialTypeUsecases } from '../usecases/material-type.usecases';
import { MaterialUsecases } from '../usecases/material.usecases';
import { NotificationFormUsecases } from '../usecases/notification-form.usecases';
import { NotificationUsecases } from '../usecases/notification.usecases';
import { OwnerDeviceTokenUsecases } from '../usecases/owner-device-token.usecases';
import { PlantUsecases } from '../usecases/plant.usecases';
import { PositionUsecases } from '../usecases/position.usecases';
import { priceInformationRecordUsecases } from '../usecases/price_information_record.usecases';
import { ProcessConditionUsecases } from '../usecases/process-condition.usecases';
import { ProcessTypeUsecases } from '../usecases/process-type.usecases';
import { ProcessUsecases } from '../usecases/process.usecases';
import { ProfitCenterUsecases } from '../usecases/profit-center.usecases';
import { PublicUsecases } from '../usecases/public.usecases';
import { PurchaseOrderTypeUsecases } from '../usecases/purchase-order-type.usecases';
import { PurchaseRequestTypeUsecases } from '../usecases/purchase-request-type.usecases';
import { purchaseOrderUsecases } from '../usecases/purchase_order.usecases';
import { purchaseRequestUsecases } from '../usecases/purchase_request.usecases';
import { PurchasingDepartmentUsecases } from '../usecases/purchasing-department.usecases';
import { PurchasingGroupUsecases } from '../usecases/purchasing-group.usecases';
import { SapPurchaseOrderUsecases } from '../usecases/sap-purchase-order.usecases';
import { SectorUsecases } from '../usecases/sector.usecases';
import { StaffApprovalWorkflowUsecases } from '../usecases/staff-approval-workflow.usecases';
import { StaffHierarchyUsecases } from '../usecases/staff-hierarchy.usecases';
import { StaffUsecases } from '../usecases/staff.usecase';
import { SupplierSectorUsecases } from '../usecases/supplier-sector.usecases';
import { SupplierUsecases } from '../usecases/supplier.usecases';
import { ActualSpendingController } from './actual-spending/actual-spending.controller';
import { ApiLogController } from './api-log/api-log.controller';
import { approveEmailController } from './approve/approve-email.controller';
import { BudgetCodeController } from './budget-code/budget-code.controller';
import { BudgetController } from './budget/budget.controller';
import { BusinessOwnerController } from './business-owner/business-owner.controller';
import { BusinessUnitController } from './business-unit/business-unit.controller';
import { CompanyController } from './company/company.controller';
import { CostController } from './cost/cost.controller';
import { CostcenterSubaccountController } from './costcenter-subaccount/costcenter-subaccount.controller';
import { CurencyUnitController } from './currency-unit/currency-unit.controller';
import { DepartmentController } from './department/department.controller';
import { FileExportHistoryController } from './file-export-history/file-export-history.controller';
import { FileImportHistoryController } from './file-import-history/file-import-history.controller';
import { FileController } from './file/file.controller';
import { FunctionUnitController } from './function-unit/function-unit.controller';
import { ImportController } from './import/import.controller';
import { InventoryStandardController } from './inventory-standard/inventory-standard.controller';
import { MaterialGroupController } from './material-group/material-group.controller';
import { MaterialTypeController } from './material-type/material-type.controller';
import { MaterialController } from './material/material.controller';
import { NotificationFormController } from './notification-form/notification-form.controller';
import { NotificationController } from './notification/notification.controller';
import { OwnerDeviceTokenController } from './owner-device-token/owner-device-token.controller';
import { PlantController } from './plant/plant.controller';
import { PositionController } from './position/position.controller';
import { PriceInformationRecordController } from './price-information-record/price-information-record.controller';
import { ProcessTypeController } from './process-type/process-type.controller';
import { ProcessController } from './process/process.controller';
import { ProfitCenterController } from './profit-center/profit-center.controller';
import { PublicController } from './public/public.controller';
import { PurchaseOrderTypeController } from './purchase-order-type/purchase-order.controller';
import { PurchaseOrderController } from './purchase-order/purchase-order.controller';
import { PrTypeController } from './purchase-request-type/purchase-request-type.controller';
import { PurchaseRequestController } from './purchase-request/purchase-request.controller';
import { PurchasingDepartmentController } from './purchasing-department/purchasing-department.controller';
import { PurchasingGroupController } from './purchasing-group/purchasing-group.controller';
import { SapPurchaseOrderController } from './sap-purchase-order/sap-purchase-order.controller';
import { SectorController } from './sector/sector.controller';
import { StaffController } from './staff/staff.controller';
import { SupplierController } from './supplier/supplier.controller';
import { MeasureUsecases } from '../usecases/measure.usecases';
import { MeasureController } from './measure/measure.controller';
import { ApprovalProcessDetailController } from './approval-process-detail/approval-process-detail.controller';
import { ApprovalProcessDetailUsecases } from '../usecases/approval-process-detail.usecases';
import { TaxCodeController } from './tax-code/tax-code.controller';
import { TaxCodeUsecases } from '../usecases/tax-code.usecases';
import { ReceiptSolomonController } from './receipt-solomon/receipt-solomon.controller';
import { ReceiptSolomonUsecases } from '../usecases/receipt-solomon.usecases';
import { WarehouseController } from './warehouse/warehouse.controller';
import { WarehouseUsecases } from '../usecases/warehouse.usecases';
import { SolomonPurchaseOrderController } from './solomon-purchase-order/solomon-purchase-order.controller';
import { SolomonPurchaseOrderUsecases } from '../usecases/solomon-purchase-order.usecases';

@Module({
  imports: [RepositoriesModule, HttpServiceModule, EnvironmentConfigModule],
  controllers: [
    BusinessUnitController,
    DepartmentController,
    FunctionUnitController,
    SectorController,
    CompanyController,
    BusinessOwnerController,
    BudgetController,
    FileController,
    CurencyUnitController,
    BudgetCodeController,
    CostcenterSubaccountController,
    FileImportHistoryController,
    ImportController,
    MaterialTypeController,
    MaterialGroupController,
    MaterialController,
    PrTypeController,
    PurchaseOrderTypeController,
    PurchasingGroupController,
    PurchasingDepartmentController,
    PlantController,
    SupplierController,
    PositionController,
    StaffController,
    InventoryStandardController,
    FileExportHistoryController,
    NotificationFormController,
    NotificationController,
    OwnerDeviceTokenController,
    ProfitCenterController,
    ProcessController,
    PublicController,
    CostController,
    ActualSpendingController,
    ProcessTypeController,
    ApiLogController,
    approveEmailController,
    PurchaseRequestController,
    PriceInformationRecordController,
    SapPurchaseOrderController,
    PurchaseOrderController,
    MeasureController,
    ApprovalProcessDetailController,
    TaxCodeController,
    ReceiptSolomonController,
    WarehouseController,
    SolomonPurchaseOrderController,
  ],
  providers: [
    BusinessUnitUsecases,
    SectorUsecases,
    CompanyUsecases,
    BusinessOwnerUsecases,
    DepartmentUsecases,
    FunctionUnitUsecases,
    BudgetUsecases,
    FileUsecases,
    IncreasementCodeUsecases,
    CurrencyUnitUsecases,
    BudgetCodeUsecases,
    CostcenterSubaccountUsecases,
    BudgetOpexUsecases,
    BudgetCapexUsecases,
    BudgetInvestmentUsecases,
    CurrencyUnitExchangeUsecases,
    CostSubHistoryUsecases,
    CronUsecases,
    FileImportHistoryUsecases,
    ImportUsecases,
    MaterialTypeUsecases,
    MaterialGroupUsecases,
    MaterialUsecases,
    PurchaseRequestTypeUsecases,
    PurchaseOrderTypeUsecases,
    PurchasingGroupUsecases,
    PurchasingDepartmentUsecases,
    PlantUsecases,
    SupplierUsecases,
    PositionUsecases,
    StaffUsecases,
    StaffHierarchyUsecases,
    InventoryStandardUsecases,
    FileExportHistoryUsecases,
    NotificationFormUsecases,
    NotificationUsecases,
    OwnerDeviceTokenUsecases,
    ProfitCenterUsecases,
    SupplierSectorUsecases,
    MaterialSectorUsecases,
    ProcessUsecases,
    ProcessConditionUsecases,
    ConditionUsecases,
    ConditionDetailUsecases,
    ApprovalWorkflowUsecases,
    StaffApprovalWorkflowUsecases,
    PublicUsecases,
    CostUsecases,
    ActualSpendingUsecases,
    ProcessTypeUsecases,
    ApiLogUsecases,
    approvalEmailUsecases,
    emailTemplateService,
    emailTransportService,
    purchaseRequestUsecases,
    purchaseOrderUsecases,
    priceInformationRecordUsecases,
    SapPurchaseOrderUsecases,
    approvalUsecases,
    MeasureUsecases,
    ApprovalProcessDetailUsecases,
    TaxCodeUsecases,
    ReceiptSolomonUsecases,
    WarehouseUsecases,
    SolomonPurchaseOrderUsecases,
  ],
})
export class ControllersModule {}
