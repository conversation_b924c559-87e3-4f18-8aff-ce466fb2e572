import {
  Body,
  Controller,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Get,
  Query,
  Delete,
  Response,
} from '@nestjs/common';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { PurchasingGroupUsecases } from '../../usecases/purchasing-group.usecases';
import { CreatePurchasingGroupDto } from './dtos/create-purchasing-group.dto';
import { UpdatePurchasingGroupDto } from './dtos/update-purchasing-group.dto';
import { GetDetailPurchasingGroupDto } from './dtos/get-detail-purchasing-group.dto';
import { GetPurchasingGroupListDto } from './dtos/get-purchasing-group-list.dto';
import { DeletePurchasingGroupDto } from './dtos/delete-purchasing-group.dto';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EPurchasingGroupPermission } from '../../utils/constants/permission.enum';
import { ListByCodesDto } from '../../domain/dtos/base-dto-by-codes.dto';

@Controller('/purchasing-group')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Purchasing Group')
export class PurchasingGroupController {
  constructor(
    private readonly purchasingGroupUsecases: PurchasingGroupUsecases,
  ) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EPurchasingGroupPermission.CREATE]))
  async create(
    @Body() data: CreatePurchasingGroupDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.purchasingGroupUsecases.createPurchasingGroup(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EPurchasingGroupPermission.EDIT]))
  async update(
    @Body() data: UpdatePurchasingGroupDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.purchasingGroupUsecases.updatePurchasingGroup(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EPurchasingGroupPermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailPurchasingGroupDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.purchasingGroupUsecases.getPurchasingGroupDetail(
      param,
      jwtPayload,
    );
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EPurchasingGroupPermission.VIEW]))
  async getList(
    @Query() param: GetPurchasingGroupListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.purchasingGroupUsecases.getPurchasingGroups(
      param,
      jwtPayload,
    );
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EPurchasingGroupPermission.DELETE]))
  async delete(
    @Param() param: DeletePurchasingGroupDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.purchasingGroupUsecases.deletePurchasingGroup(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('/list-by-codes')
  async listByCodes(
    @Body() data: ListByCodesDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.purchasingGroupUsecases.listByCodes(
      data.codes || [],
      jwtPayload,
    );
  }

  @Get('/export-purchasing-group')
  @UseGuards(NewPermissionGuard([EPurchasingGroupPermission.EXPORT]))
  async exportPurchasingGroup(
    @Query() param: GetPurchasingGroupListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.purchasingGroupUsecases.exportPurchasingGroup(
      param,
      jwtPayload,
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }
}
