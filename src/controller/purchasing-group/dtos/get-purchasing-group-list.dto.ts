import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { ESectorStatus } from '../../../domain/config/enums/sector.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import {
  EMaterialStatus,
  EMaterialTypeStatus,
} from '../../../domain/config/enums/material.enum';
import { EPurchasingGroupStatus } from '../../../domain/config/enums/purchasing.enum';

export class GetPurchasingGroupListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EPurchasingGroupStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPurchasingGroupStatus, { each: true })
  statuses?: EPurchasingGroupStatus[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  sectorIds?: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ids?: string[];

  sectorCodes?: string[];
  codes?: string[];
}
