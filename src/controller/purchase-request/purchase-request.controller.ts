import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  Response,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { PurchaseRequestModel } from '../../domain/model/purchase_request.model';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { purchaseRequestUsecases } from '../../usecases/purchase_request.usecases';
import {
  EApprovedMaterialPermission,
  EPurchaseRequestPermission,
} from '../../utils/constants/permission.enum';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { GetPurchaseRequestDto } from './dtos/get-all-purchase-request.dto';
import { GetPRWithBudgetDto } from './dtos/get-pr-with-budget.dto';
import { PriceMaterialDto } from './dtos/price-material.dto';
import { PurchaseRequestDetailDto } from './dtos/purchase-request-detail.dto';
import {
  PurchaseRequestDto,
  UpdatePurchaseRequestDto,
} from './dtos/purchase-request.dto';
import { ResendEmailPrDto } from './dtos/resend-email-pr.dto';

@Controller('/purchase-request')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('PurchaseRequest')
export class PurchaseRequestController {
  constructor(
    private readonly _purchaseRequestUsecases: purchaseRequestUsecases,
  ) {}

  @Get('/pr-with-budget')
  async findPRWithBudget(
    @Query() paginationDto: GetPRWithBudgetDto,
  ): Promise<PurchaseRequestModel[]> {
    return this._purchaseRequestUsecases.findPRWithBudget(paginationDto);
  }

  @Get('count-pr-chua-dat-du')
  async countPr(): Promise<any> {
    return { total: (await this._purchaseRequestUsecases.countPr()) || 0 };
  }

  @Get('/approved-material')
  @UseGuards(NewPermissionGuard([EApprovedMaterialPermission.VIEW]))
  async approvedMaterial(
    @Query() paginationDto: GetPurchaseRequestDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ): Promise<ResponseDto<PurchaseRequestModel>> {
    return this._purchaseRequestUsecases.findAll(
      paginationDto,
      jwtPayload,
      req.headers['authorization'],
      true,
      true,
    );
  }

  @Get()
  @UseGuards(NewPermissionGuard([EPurchaseRequestPermission.VIEW]))
  async findAll(
    @Query() paginationDto: GetPurchaseRequestDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ): Promise<ResponseDto<PurchaseRequestModel>> {
    return this._purchaseRequestUsecases.findAll(
      paginationDto,
      jwtPayload,
      req.headers['authorization'],
      false,
      false,
    );
  }

  @Get('export-pr')
  @UseGuards(
    NewPermissionGuard([
      [EPurchaseRequestPermission.VIEW, EPurchaseRequestPermission.EXPORT],
    ]),
  )
  async exportPr(
    @Query() paginationDto: GetPurchaseRequestDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
    @Response() res,
  ) {
    const result = await this._purchaseRequestUsecases.exportPr(
      paginationDto,
      jwtPayload,
      req.headers['authorization'],
    );

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  @Get(':id')
  @UseGuards(NewPermissionGuard([EPurchaseRequestPermission.VIEW]))
  async findPurchaseRequestById(
    @Param('id') id: string,
    @Request() req,
  ): Promise<PurchaseRequestModel> {
    return this._purchaseRequestUsecases.findOne(
      Number(id),
      req.headers['authorization'],
    );
  }

  @Post()
  @UseGuards(NewPermissionGuard([EPurchaseRequestPermission.CREATE]))
  async createPurchaseRequest(
    @Body() data: PurchaseRequestDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this._purchaseRequestUsecases.create(
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Patch(':id')
  @UseGuards(NewPermissionGuard([EPurchaseRequestPermission.EDIT]))
  async update(
    @Param('id') id: number,
    @Body() data: UpdatePurchaseRequestDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return this._purchaseRequestUsecases.update(
      id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Post('calculate_remaining_budget')
  async calculateRemainingBudget(
    @Body() body: { details: PurchaseRequestDetailDto[]; createdAt?: string },
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ): Promise<any> {
    return this._purchaseRequestUsecases.newCalculateRemainingBudget(
      body.details,
      req.headers['authorization'],
      body.createdAt ? new Date(body.createdAt) : new Date(),
      jwtPayload,
    );
  }

  @Post('price-material')
  async priceMaterial(
    @Body() body: { details: PriceMaterialDto[] },
  ): Promise<any> {
    return await this._purchaseRequestUsecases.priceMaterial(body.details);
  }

  @Post('resend-email-pr')
  async resendEmailPr(
    @Body() data: ResendEmailPrDto,
    @Request() req,
  ): Promise<any> {
    return this._purchaseRequestUsecases.resendEmailPr(
      data,
      req.headers['authorization'],
    );
  }
}
