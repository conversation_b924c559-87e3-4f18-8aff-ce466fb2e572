import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class PriceMaterialDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_CODE.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.MATERIAL_CODE.MUST_BE_UUID' })
  readonly materialCodeId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'DELIVERY_TIME_REQUIRED' })
  readonly deliveryTime: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  key?: string;
}
