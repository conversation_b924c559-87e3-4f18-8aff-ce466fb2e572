import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsEnum, IsOptional } from 'class-validator';
import { Status } from '../../../domain/config/enums/purchase-request.enum';

export class GetPRWithBudgetDto {
  @ApiPropertyOptional()
  @IsArray()
  @ArrayMinSize(1)
  budget_codes: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  business_units?: string[];

  @ApiProperty({
    type: [Status],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(Status, { each: true })
  statuses?: Status[];
}
