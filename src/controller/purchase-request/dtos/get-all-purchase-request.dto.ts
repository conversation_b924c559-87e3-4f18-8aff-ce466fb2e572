import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsInt,
  IsISO8601,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { Transform } from 'class-transformer';
import {
  State,
  Status,
} from '../../../domain/config/enums/purchase-request.enum';

export class GetPurchaseRequestDto extends PaginationDto {
  @ApiProperty({
    type: [Status],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(Status, { each: true })
  statusPrs?: Status[];

  @ApiProperty({
    type: [State],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(State, { each: true })
  statePrs?: State[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  typePrIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  costCenterIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  budgetCodeIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  materialCodeIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  purchaseGroupIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  materialNames?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  sectorIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  businessUnitIds?: string[];

  ///Details
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  materialGroupIds?: string[];

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsISO8601()
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_START.IS_REQUIRED' })
  startDate?: string;

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsISO8601()
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_END.IS_REQUIRED' })
  endDate?: string;

  @ApiPropertyOptional({
    type: Number,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Min(1)
  @Transform(({ value }) => +value)
  @IsInt()
  isMaterial?: number;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsNotEmpty({ message: 'crudTemplateActiveTab_REQUIRED' })
  @IsString()
  crudTemplateActiveTab?: string; // ALL/REQUEST/WAITING/APPROVED

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  processTypeIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  companyIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  departmentIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  functionUnitIds?: string[];
}
