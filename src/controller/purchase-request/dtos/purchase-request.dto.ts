import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { PurchaseRequestDetailDto } from './purchase-request-detail.dto';
import { HistoryApproveDto } from '../../approve/dtos/history-approve.dto';
import { ApprovalLevelDto } from '../../approve/dtos/approve.dto';
import {
  Priority,
  State,
  Status,
} from '../../../domain/config/enums/purchase-request.enum';

export class PurchaseRequestDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'SECTOR_REQUIRED' })
  @IsUUID('4')
  readonly sectorId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'BUSINESS_UNIT_REQUIRED' })
  @IsUUID('4')
  readonly businessUnitId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'REQUESTER_REQUIRED' })
  @IsUUID('4')
  requesterId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'TYPE_PR_REQUIRED' })
  @IsUUID('4')
  readonly typePrId: string;

  @ApiProperty({ enum: Status, required: false })
  @IsOptional()
  readonly statusPr: Status;

  @ApiProperty({ enum: State, required: false })
  @IsOptional()
  readonly statePr: State;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly costCenterId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly purchaseOrgId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly purchaseGroupId?: string;

  @ApiProperty({ enum: Priority, required: true })
  @IsNotEmpty({ message: 'PRIORITY_REQUIRED' })
  readonly priority: Priority;

  @ApiProperty({ required: false })
  @IsString()
  @IsNotEmpty()
  readonly reason: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly isCheckBudget: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly budgetCodeId?: string;

  @ApiProperty({
    type: Array,
    description: 'Các văn bản, tài liệu liên quan đến PR',
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.FILES.MUST_BE_ARRAY' })
  @IsString({ each: true, message: 'VALIDATE.FILES.MUST_BE_STRING' })
  attachments?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  refId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly accountGl?: string;

  @ApiProperty({ type: [PurchaseRequestDetailDto], required: true })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'DETAILS_REQUIRED' })
  @Type(() => PurchaseRequestDetailDto)
  details: PurchaseRequestDetailDto[];

  @ApiProperty({ type: [HistoryApproveDto], required: false })
  @IsOptional()
  @IsArray()
  @Type(() => HistoryApproveDto)
  history?: HistoryApproveDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly purchaserId?: string;

  @ApiProperty({ type: [ApprovalLevelDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApprovalLevelDto)
  approvalLevelDtos?: ApprovalLevelDto[];

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'PROCESS_TYPE_IS_REQUIRED' })
  @IsUUID('4')
  processTypeId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'PLANT_IS_REQUIRED' })
  @IsUUID('4')
  plantId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.FUNCTION_UNIT.MUST_BE_UUID' })
  @IsUUID('4')
  functionUnitId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.DEPARTMENT.MUST_BE_UUID' })
  @IsUUID('4')
  departmentId?: string;
}

export class UpdatePurchaseRequestDto extends PurchaseRequestDto {}
