import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsInt,
  IsISO8601,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import {
  State,
  Status,
} from '../../../domain/config/enums/purchase-request.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetListPurchaseRequestDto extends PaginationDto {
  @ApiProperty({
    type: [Status],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(Status, { each: true })
  status_pr?: Status[];

  @ApiProperty({
    type: [State],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(State, { each: true })
  state_pr?: State[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  type_pr?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  cost_center?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  budget_code?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  material_code?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  purchase_group?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  material_name?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  sector?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  business_unit?: string[];

  ///Details
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  material_group?: string[];

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsISO8601()
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_START.IS_REQUIRED' })
  start_date?: string;

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsISO8601()
  @IsNotEmpty({ message: 'VALIDATE.EFFECTIVE_END.IS_REQUIRED' })
  end_date?: string;

  @ApiPropertyOptional({
    type: Number,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Min(1)
  @Transform(({ value }) => +value)
  @IsInt()
  isMaterial?: number;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsNotEmpty({ message: 'crudTemplateActiveTab_REQUIRED' })
  @IsString()
  crudTemplateActiveTab?: string; // ALL/REQUEST/WAITING/APPROVED

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  process_type?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  company?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  department?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  function_unit?: string[];
}
