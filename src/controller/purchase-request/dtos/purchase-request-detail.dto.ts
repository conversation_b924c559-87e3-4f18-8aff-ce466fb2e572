import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateIf,
} from 'class-validator';

export class PurchaseRequestDetailDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty()
  @IsUUID('4')
  readonly budgetCodeId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.materialCodeId)
  @IsNotEmpty({ message: 'VALIDATE.COST_CENTER.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.COST_CENTER.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.COST_CENTER.MUST_BE_UUID' })
  readonly costCenterId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.materialCodeId)
  @IsNotEmpty({ message: 'VALIDATE.MEASURE_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MEASURE_CODE.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.MEASURE_CODE.MUST_BE_UUID' })
  readonly measureId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.WAREHOUSE_ID.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.WAREHOUSE_ID.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.WAREHOUSE_ID.MUST_BE_UUID' })
  readonly warehouseId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.costCenterId)
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_CODE.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.MATERIAL_CODE.MUST_BE_UUID' })
  readonly materialCodeId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: 'MATERIAL_NAME_REQUIRED' })
  readonly materialName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.materialCodeId)
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_GROUP.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_GROUP.MUST_BE_STRING' })
  @IsUUID('4', { message: 'VALIDATE.MATERIAL_GROUP.MUST_BE_UUID' })
  readonly materialGroupId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => !o.materialGroupId)
  @IsNotEmpty({ message: 'VALIDATE.MATERIAL_GROUP_NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.MATERIAL_GROUP_NAME.MUST_BE_STRING' })
  materialGroupName?: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'QUANTITY_REQUIRED' })
  readonly quantity: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly unit?: string;

  @ApiProperty({ required: false })
  @IsNotEmpty({ message: 'UNIT_PRICE_REQUIRED' })
  readonly unitPrice: number;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly note?: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'DELIVERY_TIME_REQUIRED' })
  @IsDateString()
  deliveryTime: Date;

  // New fields based on new requirements

  @ApiProperty({
    required: false,
    description: 'Total amount, autofill based on logic',
  })
  @IsOptional()
  readonly totalAmount?: number;

  @ApiProperty({
    required: false,
    description: 'Budget value, autofill based on logic',
  })
  @IsOptional()
  budget?: number;

  @ApiProperty({
    required: false,
    description: 'Remaining budget, autofill based on logic',
  })
  @IsOptional()
  remainingBudget?: number;

  @ApiProperty({
    required: false,
    description: 'Remaining actual budget, autofill based on logic',
  })
  @IsOptional()
  remainingActualBudget?: number;

  @ApiProperty({
    required: true,
    description: 'Estimated price, autofill or enter a positive integer',
  })
  @IsNotEmpty({ message: 'ESTIMATED_PRICE_REQUIRED' })
  @IsNumber()
  readonly estimatedPrice: number;

  @ApiProperty({
    required: false,
    description: 'Standard quantity, autofill based on logic',
  })
  @IsOptional()
  readonly standardQuantity?: number;

  @ApiProperty({
    required: false,
    description: 'Inventory number, autofill based on logic',
  })
  @IsOptional()
  readonly inventoryNumber?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @IsUUID('4')
  budgetId?: string;

  id?: number;
  matchingBudget?: any;
  adjustBudgetId?: string;
}
