import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NotificationFormUsecases } from '../../usecases/notification-form.usecases';
import { ENotificationFormPermission } from '../../utils/constants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { GetDetailNotificationFormDto } from './dtos/get-detail-notification-form.dto';
import { GetNotificationFormListDto } from './dtos/get-notification-form-list.dto';
import { UpdateNotificationFormDto } from './dtos/update-notification-form.dto';
import { CreateNotificationFormDto } from './dtos/create-notification-form.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { EPlatform } from '../../domain/config/enums/platform.enum';

@Controller('/notification-form')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Notification Form')
export class NotificationFormController {
  constructor(
    private readonly notificationFormUsecases: NotificationFormUsecases,
  ) {}

  // @Post('/create')
  // @UseGuards(NewPermissionGuard([ENotificationFormPermission.CREATE]))
  // async create(@Body() data: CreateNotificationFormDto) {
  //   return await this.notificationFormUsecases.createNotificationForm(data);
  // }

  @Get(':id/detail')
  @UseGuards(NewPermissionGuard([ENotificationFormPermission.VIEW]))
  async getDetail(@Param() param: GetDetailNotificationFormDto) {
    return await this.notificationFormUsecases.getNotificationFormDetail(param);
  }

  @Get('/list')
  // @UseGuards(NewPermissionGuard([ENotificationFormPermission.VIEW]))
  async getList(
    @Query() param: GetNotificationFormListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    param.platform = jwtPayload?.platform || EPlatform.E_PURCHASE;
    return await this.notificationFormUsecases.getNotificationForms(param);
  }

  // @Delete(':id/delete')
  // @UseGuards(NewPermissionGuard([ENotificationFormPermission.DELETE]))
  // async delete(@Param() param: DeleteNotificationFormDto) {
  //   return await this.notificationFormUsecases.deleteNotificationForm(param.id);
  // }
  @Put(':id/update')
  @UseGuards(NewPermissionGuard([ENotificationFormPermission.EDIT]))
  async update(
    @Param() param: GetUuidDto,
    @Body() data: UpdateNotificationFormDto,
  ) {
    return await this.notificationFormUsecases.updateNotificationForm(
      param.id,
      data,
    );
  }

  @Post('/init-notification-form')
  // @UseGuards(NewPermissionGuard([ENotificationFormPermission.EDIT]))
  async initNotificationForm() {
    return await this.notificationFormUsecases.initMasterDataNotificationForm();
  }
}
