import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import {
  ENotificationFormStatus,
  ENotificationFormType,
} from '../../../domain/config/enums/notification-form.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class GetNotificationFormListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [ENotificationFormStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ENotificationFormStatus, { each: true })
  statuses?: ENotificationFormStatus[];

  @ApiProperty({
    type: [ENotificationFormType],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ENotificationFormType, { each: true })
  types?: ENotificationFormType[];

  platform?: EPlatform;
}
