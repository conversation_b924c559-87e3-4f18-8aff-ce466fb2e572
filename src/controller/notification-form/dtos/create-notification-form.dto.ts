import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import {
  ENotificationFormStatus,
  ENotificationFormType,
} from '../../../domain/config/enums/notification-form.enum';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class CreateNotificationFormDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Loại thông báo',
  })
  @IsEnum(ENotificationFormType, { message: 'VALIDATE.TYPE.INVALID_VALUE' })
  type: ENotificationFormType;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Tên loại thông báo',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: '<PERSON>ô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: ENotificationFormStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ENotificationFormStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: ENotificationFormStatus;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Nội dung',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.CONTENT.MUST_BE_STRING' })
  content?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Platform',
  })
  @IsEnum(EPlatform, { message: 'VALIDATE.PLATFORM.INVALID_VALUE' })
  platform: EPlatform;
}
