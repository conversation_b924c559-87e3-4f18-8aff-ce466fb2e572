import {
  Body,
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
  Request,
  Param,
  Patch,
  Get,
  Query,
  Delete,
  Response,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpStatus,
} from '@nestjs/common';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { NewAuthUser } from '../../utils/decorators/new-user.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import { CreateMeasureDto } from './dtos/create-measure.dto';
import { UpdateMeasureDto } from './dtos/update-measure.dto';
import { GetDetailMeasureDto } from './dtos/get-detail-cost.dto';
import { GetMeasureListDto } from './dtos/get-measure-list.dto';
import { DeleteMeasureDto } from './dtos/delete-measure.dto';
import { MeasureUsecases } from '../../usecases/measure.usecases';
import { GetMeasureListByIdsDto } from './dtos/get-measure-list-by-ids.dto';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { EMeasurePermission } from '../../utils/constants/permission.enum';

@Controller('/measure')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Measure')
export class MeasureController {
  constructor(private readonly measureUsecases: MeasureUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EMeasurePermission.CREATE]))
  async create(@Body() data: CreateMeasureDto, @Request() req) {
    return await this.measureUsecases.createMeasure(
      data,
      req.headers['authorization'],
    );
  }

  @Patch(':id/update')
  @UseGuards(NewPermissionGuard([EMeasurePermission.EDIT]))
  async update(
    @Body() data: UpdateMeasureDto,
    @Param() param: GetUuidDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.measureUsecases.updateMeasure(
      param.id,
      data,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get(':id/detail')
  //
  async getDetail(
    @Param() param: GetDetailMeasureDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.measureUsecases.getDetailMeasure(param, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EMeasurePermission.VIEW]))
  async getList(
    @Query() param: GetMeasureListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.measureUsecases.getMeasures(param, jwtPayload);
  }

  @Delete(':id')
  @UseGuards(NewPermissionGuard([EMeasurePermission.DELETE]))
  async delete(
    @Param() param: DeleteMeasureDto,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.measureUsecases.deleteMeasure(
      param.id,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/export-measure')
  @UseGuards(NewPermissionGuard([EMeasurePermission.EXPORT]))
  async exportMeasure(
    @Query() param: GetMeasureListDto,
    @NewAuthUser() jwtPayload: any,
    @Response() res,
  ) {
    const result = await this.measureUsecases.exportMeasure(param, jwtPayload);

    if (Buffer.isBuffer(result)) {
      // const newFileName = encodeURIComponent(sanitizeFileName(importFile.originalname.toLowerCase().replace(/\.xlsx$/, '_errors')));
      // Thiết lập header để trả về file Excel
      // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // res.setHeader(`Content-Disposition', 'attachment; filename=${newFileName}.xlsx`);
      return res.send({ result: result.toString('base64') });
    } else {
      return res.send(result);
    }
  }

  // @Post('/import-measure')
  // @ApiConsumes('multipart/form-data')
  // @UseInterceptors(FileInterceptor('importFile'))
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       importFile: {
  //         type: 'string',
  //         format: 'binary',
  //       },
  //     },
  //   },
  // })
  // async importMeasure(
  //   @UploadedFile(
  //     new ParseFilePipe({
  //       validators: [
  //         new FileTypeValidator({
  //           fileType:
  //             'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //         }),
  //       ],
  //       fileIsRequired: true,
  //       errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
  //     }),
  //   )
  //   importFile,
  //   @NewAuthUser() jwtPayload: any,
  //   @Request() req,
  // ) {
  //   return await this.measureUsecases.importMeasure(
  //     importFile,
  //     jwtPayload,
  //     req.headers['authorization'],
  //   );
  // }

  ///For validate detail PR PO
  @Post('/list-by-ids')
  async getListByIds(
    @Body() param: GetMeasureListByIdsDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.measureUsecases.getListByIds(param, jwtPayload);
  }
}
