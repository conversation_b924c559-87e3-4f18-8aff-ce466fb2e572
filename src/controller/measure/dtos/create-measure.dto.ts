import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import {
  EMeasureStatus,
  EMeasureSystem,
} from '../../../domain/config/enums/measure.enum';

export class CreateCodeConversionDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Hệ thống',
  })
  @IsEnum(EMeasureSystem, { message: 'VALIDATE.SYSTEM.INVALID_VALUE' })
  system: EMeasureSystem;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Code',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;
}
export class CreateMeasureDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Code',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name',
  })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_STRING' })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EMeasureStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EMeasureStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EMeasureStatus;

  @ApiProperty({
    type: [CreateCodeConversionDto],
  })
  @ArrayMinSize(1)
  @IsArray({ message: 'VALIDATE.CODE_CONVERSIONS.MUST_BE_ARRAY' })
  @ValidateNested({ each: true })
  @Type(() => CreateCodeConversionDto)
  codeConversions: CreateCodeConversionDto[];

  createdAt?: string;
  updatedAt?: string;
}
