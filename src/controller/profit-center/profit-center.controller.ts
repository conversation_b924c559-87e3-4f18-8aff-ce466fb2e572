import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ProfitCenterUsecases } from '../../usecases/profit-center.usecases';
import { EProfitCenterPermission } from '../../utils/constants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateProfitCenterDto } from './dtos/create-profit-center.dto';
import { DeleteProfitCenterDto } from './dtos/delete-profit-center.dto';
import { GetDetailProfitCenterDto } from './dtos/get-detail-profit-center.dto';
import { GetProfitCenterListDto } from './dtos/get-profit-center-list.dto';
import { UpdateProfitCenterDto } from './dtos/update-profit-center.dto';

@Controller('/profit-center')
@UseInterceptors(
  TransformationInterceptor,
  AspectLogger,
  TransactionInterceptor,
)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Profit Center')
export class ProfitCenterController {
  constructor(private readonly profitCenterUsecases: ProfitCenterUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([EProfitCenterPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(@Body() data: CreateProfitCenterDto) {
    return await this.profitCenterUsecases.createProfitCenter(data);
  }

  @Get(':id/detail')
  // @UseGuards(NewPermissionGuard([EProfitCenterPermission.VIEW]))
  async getDetail(@Param() param: GetDetailProfitCenterDto) {
    return await this.profitCenterUsecases.getDetailProfitCenter(param);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([EProfitCenterPermission.VIEW]))
  async getList(@Query() param: GetProfitCenterListDto) {
    return await this.profitCenterUsecases.getProfitCenters(param);
  }

  @Delete(':id/delete')
  @UseGuards(NewPermissionGuard([EProfitCenterPermission.DELETE]))
  async delete(@Param() param: DeleteProfitCenterDto) {
    await this.profitCenterUsecases.deleteProfitCenter(param.id);
    return { message: 'Successfully!!!' };
  }

  @Put(':id/update')
  @UseGuards(NewPermissionGuard([EProfitCenterPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param() param: GetUuidDto,
    @Body() data: UpdateProfitCenterDto,
  ) {
    return await this.profitCenterUsecases.updateProfitCenter(data, param.id);
  }
}
