import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { EDepartmentStatus } from '../../../domain/config/enums/department.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { EFunctionUnitStatus } from '../../../domain/config/enums/function-unit.enum';

export class GetProfitCenterListDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EFunctionUnitStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EFunctionUnitStatus, { each: true })
  statuses?: EFunctionUnitStatus[];
}
