import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { EProfitCenterStatus } from '../../../domain/config/enums/profit-center.enum';

export class CreateProfitCenterDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'ID',
  })
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Mô tả',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Trạng thái',
    default: EProfitCenterStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EProfitCenterStatus, { message: 'VALIDATE.STATUS.INVALID_VALUE' })
  status?: EProfitCenterStatus;
}
