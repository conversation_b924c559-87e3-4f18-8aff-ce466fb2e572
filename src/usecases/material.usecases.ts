import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { GetDetailCompanyDto } from '../controller/company/dtos/get-detail-company.dto';
import { GetDetailDepartmentDto } from '../controller/department/dtos/get-detail-department.dto';
import { ImportMaterialDto } from '../controller/import/dtos/import-material.dto';
import { GetDetailMaterialGroupDto } from '../controller/material-group/dtos/get-detail-material-group.dto';
import { CreateMaterialSectorDto } from '../controller/material-sector/dtos/create-material-sector.dto';
import { GetDetailMaterialTypeDto } from '../controller/material-type/dtos/get-detail-material-type.dto';
import { CreateMaterialDto } from '../controller/material/dtos/create-material.dto';
import { GetDetailMaterialDto } from '../controller/material/dtos/get-detail-material.dto';
import { GetMaterialListDto } from '../controller/material/dtos/get-material-list.dto';
import { UpdateMaterialDto } from '../controller/material/dtos/update-material.dto';
import { GetDetailMeasureDto } from '../controller/measure/dtos/get-detail-cost.dto';
import { GetDetailProfitCenterDto } from '../controller/profit-center/dtos/get-detail-profit-center.dto';
import { GetDetailPurchasingDepartmentDto } from '../controller/purchasing-department/dtos/get-detail-purchasing-department.dto';
import { GetDetailPurchasingGroupDto } from '../controller/purchasing-group/dtos/get-detail-purchasing-group.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import { EBusinessUnitStatus } from '../domain/config/enums/business-unit.enum';
import { ECompanyStatus } from '../domain/config/enums/company.enum';
import { EDepartmentStatus } from '../domain/config/enums/department.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import {
  EColumnImportMaterial,
  EMaterialGroupStatus,
  EMaterialStatus,
  EMaterialTypeStatus,
} from '../domain/config/enums/material.enum';
import {
  EPurchasingDepartmentStatus,
  EPurchasingGroupStatus,
} from '../domain/config/enums/purchasing.enum';
import { ESectorStatus } from '../domain/config/enums/sector.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { importErrorDetails } from '../domain/messages/error-details/import';
import { materialErrorDetails } from '../domain/messages/error-details/material';
import { materialGroupErrorDetails } from '../domain/messages/error-details/material-group';
import { materialTypeErrorDetails } from '../domain/messages/error-details/material-type';
import { purchasingDepartmentErrorDetails } from '../domain/messages/error-details/purchasing-department';
import { purchasingGroupErrorDetails } from '../domain/messages/error-details/purchasing-group';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { CompanyModel } from '../domain/model/company.model';
import { DepartmentModel } from '../domain/model/department.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { MaterialSectorModel } from '../domain/model/material-sector.model';
import { MaterialModel } from '../domain/model/material.model';
import { ProfitCenterModel } from '../domain/model/profit-center.model';
import { SupplierSectorModel } from '../domain/model/supplier-sector.model';
import { IMaterialRepository } from '../domain/repositories/material.repository';
import {
  codeToIdMap,
  getFlattenColumnValues,
  getStatus,
  getStatusMaterial,
  getValueOrResult,
} from '../utils/common';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CompanyUsecases } from './company.usecases';
import { DepartmentUsecases } from './department.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { MaterialGroupUsecases } from './material-group.usecases';
import { MaterialSectorUsecases } from './material-sector.usecases';
import { MaterialTypeUsecases } from './material-type.usecases';
import { MeasureUsecases } from './measure.usecases';
import { ProfitCenterUsecases } from './profit-center.usecases';
import { PurchasingDepartmentUsecases } from './purchasing-department.usecases';
import { PurchasingGroupUsecases } from './purchasing-group.usecases';
import { SectorUsecases } from './sector.usecases';
import { measureErrorDetails } from '../domain/messages/error-details/measure';
import { GetMaterialListByIdsDto } from '../controller/material/dtos/get-material-list-by-ids.dto';
import { EFunctionUnitStatus } from '../domain/config/enums/function-unit.enum';
import { FunctionUnitUsecases } from './function-unit.usecases';

@Injectable()
export class MaterialUsecases {
  constructor(
    @Inject(IMaterialRepository)
    private readonly materialRepository: IMaterialRepository,
    private readonly materialTypeUsecases: MaterialTypeUsecases,
    private readonly materialGroupUsecases: MaterialGroupUsecases,
    private readonly profitCenterUsecases: ProfitCenterUsecases,
    private readonly materialSectorUsecases: MaterialSectorUsecases,
    private readonly sectorUsecases: SectorUsecases,
    private readonly companyUsecases: CompanyUsecases,
    private readonly businessUnitUsecases: BusinessUnitUsecases,
    private readonly departmentUsecases: DepartmentUsecases,
    private readonly fileUsecases: FileUsecases,
    private readonly purchasingGroupUsecases: PurchasingGroupUsecases,
    private readonly purchasingDepartmentUsecases: PurchasingDepartmentUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private readonly measureUsecases: MeasureUsecases,
    private readonly functionUnitUsecases: FunctionUnitUsecases,
  ) {}

  async createMaterial(
    data: CreateMaterialDto,
    jwtPayload: IAuthUserPayload,
    authorization: string,
    isImport: boolean = false,
    isSap: boolean = false,
  ): Promise<MaterialModel> {
    const materialData = isSap
      ? { ...data, industries: undefined }
      : await this.verifyDataDto(data, jwtPayload);

    const materialModel = new MaterialModel({
      ...materialData,
      createdBy: {
        id: jwtPayload?.userId,
        firstName: jwtPayload?.firstName,
        lastName: jwtPayload?.lastName,
        email: jwtPayload?.email,
        phone: jwtPayload?.phone,
        staffId: jwtPayload?.staffId,
        staffCode: jwtPayload?.staffCode,
      },
    });

    // Create the material in the repository
    const material =
      await this.materialRepository.createMaterial(materialModel);

    // Create material sector (can run in parallel with API calls below)
    await this.createMaterialSector(
      data.industries,
      material.id,
      jwtPayload,
      isImport,
    );

    // Handle data role creation or deletion
    // const apiCallPromise = data.deletedAt
    //   ? sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(material.id), {
    //       authorization,
    //       'x-api-key': process.env.API_KEY,
    //     })
    //   : sendPost(
    //       IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
    //       {
    //         description: material.name,
    //         refId: material.id,
    //         refCode: material.code,
    //         type: EDataRoleType.MATERIAL,
    //         isEnabled: true,
    //         platform: EPlatform.E_PURCHASE,
    //       },
    //       { authorization, 'x-api-key': process.env.API_KEY },
    //     );

    // // Wait for both async operations to complete
    // await Promise.all([createSectorPromise, apiCallPromise]);

    // Return detailed material info
    return this.materialRepository.getMaterialDetail(
      plainToInstance(GetDetailMaterialDto, { id: material.id }),
      jwtPayload,
    );
  }

  async updateMaterial(
    id: string,
    updateMaterialDto: UpdateMaterialDto,
    jwtPayload: any,
    authorization: string,
    isImport: boolean = false,
    isSap: boolean = false,
  ): Promise<MaterialModel> {
    const materialData = isSap
      ? { ...updateMaterialDto, industries: undefined }
      : await this.verifyDataDto(updateMaterialDto, jwtPayload, id);

    const materialModel = new MaterialModel({
      ...materialData,
      updatedBy: {
        id: jwtPayload?.userId,
        firstName: jwtPayload?.firstName,
        lastName: jwtPayload?.lastName,
        email: jwtPayload?.email,
        phone: jwtPayload?.phone,
        staffId: jwtPayload?.staffId,
        staffCode: jwtPayload?.staffCode,
      },
    });

    const material = await this.materialRepository.updateMaterial(
      id,
      materialModel,
    );

    await this.createMaterialSector(
      updateMaterialDto.industries,
      material.id,
      jwtPayload,
      isImport,
    );

    // try {
    //   if (updateMaterialDto.deletedAt) {
    //     await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
    //       authorization,
    //       'x-api-key': process.env.API_KEY,
    //     });
    //   } else {
    //     await sendPatch(
    //       IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
    //       {
    //         description: material.name,
    //         refCode: material.code,
    //         isEnabled: true,
    //       },
    //       {
    //         authorization,
    //         'x-api-key': process.env.API_KEY,
    //       },
    //     );
    //   }
    // } catch (e) {}

    // Return detailed material info
    return this.materialRepository.getMaterialDetail(
      plainToInstance(GetDetailMaterialDto, { id: material.id }),
      jwtPayload,
    );
  }

  // async getMaterialById(id: string): Promise<MaterialModel> {
  //   const material = await this.materialRepository.getMaterialById(id);

  //   if (!material) {
  //     throw new HttpException(
  //       materialErrorDetails.E_2500(),
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   return material;
  // }

  async getMaterials(
    conditions: GetMaterialListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<MaterialModel>> {
    return await this.materialRepository.getMaterials(conditions, jwtPayload);
  }

  async deleteMaterial(
    id: string,
    jwtPayload: any,
    authorization: string,
    isSap: boolean = false,
  ): Promise<void> {
    if (!isSap) {
      const material = await this.materialRepository.getMaterialDetail(
        plainToInstance(GetDetailMaterialDto, {
          id,
        }),
        jwtPayload,
      );

      if (!material) {
        throw new HttpException(
          materialErrorDetails.E_2500(),
          HttpStatus.NOT_FOUND,
        );
      }
    }
    await this.materialSectorUsecases.deleteMaterialSectors(id);
    await this.materialRepository.deleteMaterial(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
      'x-api-key': process.env.API_KEY,
    });
  }

  async checkExistMaterialByCode(code: string, id?: string) {
    const material = await this.materialRepository.getMaterialByCode(code, id);
    if (material) {
      throw new HttpException(
        materialErrorDetails.E_2501(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getMaterialByCode(code: string) {
    return await this.materialRepository.getMaterialByCode(code);
  }

  async verifyDataDto(
    conditions: CreateMaterialDto | UpdateMaterialDto,
    jwtPayload: any,
    id?: string,
  ) {
    const data: MaterialModel = new MaterialModel({
      ...conditions,
      industries: undefined,
    });

    let detail: MaterialModel;
    if (id) {
      detail = await this.materialRepository.getMaterialDetail(
        plainToInstance(GetDetailMaterialDto, {
          id,
        }),
        jwtPayload,
      );

      if (!detail) {
        throw new HttpException(
          materialErrorDetails.E_2500(),
          HttpStatus.NOT_FOUND,
        );
      }
    }
    await this.checkExistMaterialByCode(conditions.code, id);

    // const checkDuplicateSectorIds = [...new Set(conditions.sectorIds)];
    // if (checkDuplicateSectorIds?.length != conditions.sectorIds?.length) {
    //   throw new HttpException(
    //     getErrorMessage(errorMessage.E_1078()),
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    // const sectors = await this.sectorUsecases.getSectorByIds(
    //   conditions.sectorIds,
    //   jwtPayload,
    // );

    // const sectorIds = (sectors ?? []).map((item) => item.id);

    // const diffSectorIds = _.difference(conditions.sectorIds, sectorIds);

    // if (diffSectorIds.length) {
    //   throw new HttpException(
    //     getErrorMessage(
    //       errorMessage.E_1003(),
    //       `SECTOR_IDS_NOT_FOUND_${diffSectorIds.join(', ')}`,
    //     ),
    //     HttpStatus.NOT_FOUND,
    //   );
    // }

    // let sectorsInactive = [];
    // if (detail) {
    //   const currentSectorIds = (detail.sectors ?? []).map((item) => item.id);

    //   sectorsInactive = sectors.filter(
    //     (item) =>
    //       item.status == ESectorStatus.IN_ACTIVE &&
    //       !currentSectorIds.includes(item.id),
    //   );
    // } else {
    //   sectorsInactive = sectors.filter(
    //     (item) => item.status == ESectorStatus.IN_ACTIVE,
    //   );
    // }

    // if (sectorsInactive?.length) {
    //   throw new HttpException(
    //     getErrorMessage(
    //       errorMessage.E_1027(),
    //       `SECTOR_IDS_IN_ACTIVE_${sectorsInactive.map((item) => item.id).join(', ')}`,
    //     ),
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    if (detail?.materialTypeId != conditions.materialTypeId) {
      const materialType =
        await this.materialTypeUsecases.getMaterialTypeDetail(
          plainToInstance(GetDetailMaterialTypeDto, {
            id: conditions.materialTypeId,
          }),
          jwtPayload,
        );

      if (materialType.status == EMaterialTypeStatus.IN_ACTIVE) {
        throw new HttpException(
          materialTypeErrorDetails.E_2002(),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (detail?.materialGroupId != conditions.materialGroupId) {
      const materialGroup =
        await this.materialGroupUsecases.getMaterialGroupDetail(
          plainToInstance(GetDetailMaterialGroupDto, {
            id: conditions.materialGroupId,
          }),
          jwtPayload,
        );

      if (materialGroup.status == EMaterialGroupStatus.IN_ACTIVE) {
        throw new HttpException(
          materialGroupErrorDetails.E_2202(),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (conditions.profitCenterId) {
      await this.profitCenterUsecases.getDetailProfitCenter(
        plainToInstance(GetDetailProfitCenterDto, {
          id: conditions.profitCenterId,
        }),
      );

      data.profitCenter = new ProfitCenterModel({
        id: conditions.profitCenterId,
      });
    }

    if (conditions.companyId && detail?.company?.id != conditions.companyId) {
      const company = await this.companyUsecases.getDetailCompany(
        plainToInstance(GetDetailCompanyDto, {
          id: conditions.companyId,
        }),
        jwtPayload,
      );
      if (company.status == ECompanyStatus.IN_ACTIVE) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1028()),
          HttpStatus.BAD_REQUEST,
        );
      }
      data.company = new CompanyModel({
        id: conditions.companyId,
      });
    }

    const oldBusinessUnitIds =
      detail?.businessUnits?.map((item) => item.id) || [];

    if (
      conditions.businessUnitIds?.length &&
      _.difference(conditions.businessUnitIds, oldBusinessUnitIds).length
    ) {
      const businessUnits = await this.businessUnitUsecases.getBuByIds(
        conditions.businessUnitIds,
        jwtPayload,
      );

      const inactiveBusinessUnits = businessUnits.filter(
        (item) => item.status == EBusinessUnitStatus.IN_ACTIVE,
      );
      if (inactiveBusinessUnits?.length) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1029()),
          HttpStatus.BAD_REQUEST,
        );
      }
      data.businessUnits = businessUnits;
    }

    const oldFunctionUnitIds =
      detail?.functionUnits?.map((item) => item.id) || [];

    if (
      conditions.functionUnitIds?.length &&
      _.difference(conditions.functionUnitIds, oldFunctionUnitIds).length
    ) {
      const functionUnits =
        await this.functionUnitUsecases.getFunctionUnitByIds(
          conditions.functionUnitIds,
          jwtPayload,
        );

      data.functionUnits = functionUnits;
    }

    if (
      conditions.departmentId &&
      detail?.department?.id != conditions.departmentId
    ) {
      const department = await this.departmentUsecases.getDetailDepartment(
        plainToInstance(GetDetailDepartmentDto, {
          id: conditions.departmentId,
        }),
        jwtPayload,
      );
      if (department.status == EDepartmentStatus.IN_ACTIVE) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1030()),
          HttpStatus.BAD_REQUEST,
        );
      }
      data.department = new DepartmentModel({
        id: conditions.departmentId,
      });
    }

    if (
      conditions.purchasingGroupId &&
      detail?.purchasingGroupId != conditions.purchasingGroupId
    ) {
      const purchasingGroup =
        await this.purchasingGroupUsecases.getPurchasingGroupDetail(
          plainToInstance(GetDetailPurchasingGroupDto, {
            id: conditions.purchasingGroupId,
          }),
          jwtPayload,
        );

      if (purchasingGroup.status == EPurchasingGroupStatus.IN_ACTIVE) {
        throw new HttpException(
          purchasingGroupErrorDetails.E_2602(),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (
      conditions.purchasingDepartmentId &&
      detail?.purchasingDepartmentId != conditions.purchasingDepartmentId
    ) {
      const purchasingDepartment =
        await this.purchasingDepartmentUsecases.getPurchasingDepartmentDetail(
          plainToInstance(GetDetailPurchasingDepartmentDto, {
            id: conditions.purchasingDepartmentId,
          }),
          jwtPayload,
        );

      if (
        purchasingDepartment.status == EPurchasingDepartmentStatus.IN_ACTIVE
      ) {
        throw new HttpException(
          purchasingDepartmentErrorDetails.E_2802(),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (detail?.measureId != conditions.measureId) {
      await this.measureUsecases.getDetailMeasure(
        plainToInstance(GetDetailMeasureDto, {
          id: conditions.measureId,
        }),
        jwtPayload,
      );
    }

    return {
      ...data,
      id: detail?.id || undefined, // @TODO: bùa cực mạnh
    };
  }

  async getMaterialDetail(conditions: GetDetailMaterialDto, jwtPayload) {
    const material = await this.materialRepository.getMaterialDetail(
      conditions,
      jwtPayload,
    );

    if (!material) {
      throw new HttpException(
        materialErrorDetails.E_2500(),
        HttpStatus.NOT_FOUND,
      );
    }

    return material;
  }

  async createMaterialSector(
    data: CreateMaterialSectorDto[],
    materialId: string,
    jwtPayload: any,
    isImport: boolean = false,
  ) {
    const sectorIds = [
      ...new Set((data || []).map((item) => item.sectorId).filter(Boolean)),
    ];
    const sectors = await this.sectorUsecases.getSectorByIds(
      sectorIds,
      jwtPayload,
    );

    const material = await this.getMaterialDetail(
      plainToInstance(GetDetailMaterialDto, {
        id: materialId,
      }),
      jwtPayload,
    );

    const industries: MaterialSectorModel[] = [];

    for (const materialSector of data) {
      const sector = sectors.find((item) => item.id == materialSector.sectorId);

      if (!sector) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1036()),
          HttpStatus.NOT_FOUND,
        );
      }
      const checkMaterialSector = (material.industries || []).find(
        (item) => item.sector?.id == sector.id,
      );

      if (
        !checkMaterialSector &&
        sector.status == ESectorStatus.IN_ACTIVE &&
        !isImport
      ) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1027()),
          HttpStatus.BAD_REQUEST,
        );
      }

      industries.push(
        new MaterialSectorModel({
          ...materialSector,
          sector: sector,
          material: material,
        }),
      );
    }

    await this.materialSectorUsecases.deleteMaterialSectors(materialId);

    await this.materialSectorUsecases.createManyMaterialSector(industries);
  }

  async importMaterial(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.MATERIAL,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        // const templateWorkbook = new Excel.Workbook();
        // await templateWorkbook.xlsx.readFile(
        //   resolve(
        //     __dirname,
        //     '../domain/template/import/template-material.xlsx',
        //   ),
        // );

        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));
        //Check template
        // const templateHeaderRow =
        //   templateWorkbook.worksheets[0]?.getRow(1)?.values;
        // const templateHeader = Array.isArray(templateHeaderRow)
        //   ? templateHeaderRow.slice(1).toString()
        //   : '';

        // const workbookHeaderRow = workbook.worksheets[0]?.getRow(1)?.values;
        // const workbookHeader = Array.isArray(workbookHeaderRow)
        //   ? workbookHeaderRow.slice(1).toString()
        //   : '';

        // if (templateHeader != workbookHeader) {
        //   throw new HttpException(
        //     importErrorDetails.E_5001(),
        //     HttpStatus.BAD_REQUEST,
        //   );
        // }

        const rows =
          workbook.worksheets[0]?.getRows(
            2,
            workbook.worksheets[0].actualRowCount - 1,
          ) || [];

        // Extract column values using helper function
        const getColumnValues = (column) =>
          rows
            .map((row) => getValueOrResult(row, column)?.toString())
            .filter(Boolean);

        const [
          codes,
          sectorCodes,
          companyCodes,
          businessUnitCodes,
          materialTypeCodes,
          materialGroupCodes,
          purchasingGroupCodes,
          purchasingDepartmentCodes,
          measureCodes,
        ] = [
          EColumnImportMaterial.CODE,
          EColumnImportMaterial.SECTOR_CODE,
          EColumnImportMaterial.COMPANY_CODE,
          EColumnImportMaterial.BUSINESS_UNIT_CODE,
          EColumnImportMaterial.MATERIAL_TYPE_CODE,
          EColumnImportMaterial.MATERIAL_GROUP_CODE,
          EColumnImportMaterial.PURCHASING_GROUP_CODE,
          EColumnImportMaterial.PURCHASING_DEPARTMENT_CODE,
          EColumnImportMaterial.UNIT,
        ].map(getColumnValues?.length ? getColumnValues : () => []);

        const functionUnitCodes = getFlattenColumnValues(
          rows,
          EColumnImportMaterial.FUNCTION_UNIT_CODE,
        ) as string[];

        const [
          materials,
          sectors,
          companies,
          businessUnits,
          materialTypes,
          materialGroups,
          purchasingGroups,
          purchasingDepartments,
          measures,
          functionUnits,
        ] = await Promise.all([
          this.listByCodes(codes, jwtPayload, false),
          this.sectorUsecases.listByCodes(sectorCodes, jwtPayload),
          this.companyUsecases.listByCodes(companyCodes, jwtPayload),
          this.businessUnitUsecases.listByCodes(businessUnitCodes, jwtPayload),
          this.materialTypeUsecases.listByCodes(materialTypeCodes, jwtPayload),
          this.materialGroupUsecases.listByCodes(
            materialGroupCodes,
            jwtPayload,
          ),
          this.purchasingGroupUsecases.listByCodes(
            purchasingGroupCodes,
            jwtPayload,
          ),
          this.purchasingDepartmentUsecases.listByCodes(
            purchasingDepartmentCodes,
            jwtPayload,
          ),
          this.measureUsecases.listByCodes(measureCodes, jwtPayload),
          this.functionUnitUsecases.listByCodes(functionUnitCodes, jwtPayload),
        ]);

        const errors: TErrorMessageImport[] = [];

        // Map data for quick access
        const [
          dataMaterials,
          dataSectors,
          dataCompanies,
          dataBusinessUnits,
          dataMaterialTypes,
          dataMaterialGroups,
          dataPurchasingGroups,
          dataPurchasingDepartments,
          dataMeasures,
          dataFunctionUnits,
        ] = [
          materials,
          sectors,
          companies,
          businessUnits,
          materialTypes,
          materialGroups,
          purchasingGroups,
          purchasingDepartments,
          measures,
          functionUnits,
        ].map((items: any) => codeToIdMap(items, 'code', ['id']));

        const dataCreate = [];
        const dataUpdate = [];

        for (let i = 0; i < rows.length; i++) {
          const row = rows[i];
          const rowNumber = row.number;

          const code = getValueOrResult(
            row,
            EColumnImportMaterial.CODE,
          )?.toString();

          const codeSAP = getValueOrResult(
            row,
            EColumnImportMaterial.CODE_SAP,
          )?.toString();

          const name = getValueOrResult(
            row,
            EColumnImportMaterial.NAME,
          )?.toString();

          const sectorCode = getValueOrResult(
            row,
            EColumnImportMaterial.SECTOR_CODE,
          )?.toString();

          const companyCode = getValueOrResult(
            row,
            EColumnImportMaterial.COMPANY_CODE,
          )?.toString();

          const businessUnitCode = getValueOrResult(
            row,
            EColumnImportMaterial.BUSINESS_UNIT_CODE,
          )?.toString();
          const functionUnitCodes = [
            ...new Set(
              getValueOrResult(row, EColumnImportMaterial.FUNCTION_UNIT_CODE)
                ?.toString()
                ?.split(',')
                ?.map((item) => item.trim())
                ?.filter(Boolean) || [],
            ),
          ] as string[];
          const materialTypeCode = getValueOrResult(
            row,
            EColumnImportMaterial.MATERIAL_TYPE_CODE,
          )?.toString();

          const materialGroupCode = getValueOrResult(
            row,
            EColumnImportMaterial.MATERIAL_GROUP_CODE,
          )?.toString();

          const purchasingGroupCode = getValueOrResult(
            row,
            EColumnImportMaterial.PURCHASING_GROUP_CODE,
          )?.toString();

          const purchasingDepartmentCode = getValueOrResult(
            row,
            EColumnImportMaterial.PURCHASING_DEPARTMENT_CODE,
          )?.toString();

          const unit = getValueOrResult(
            row,
            EColumnImportMaterial.UNIT,
          )?.toString();

          const description = getValueOrResult(
            row,
            EColumnImportMaterial.DESCRIPTION,
          )?.toString();

          const status = getStatus(
            SupplierSectorModel,
            getValueOrResult(row, EColumnImportMaterial.STATUS)?.toString(),
          );

          const deleted =
            getValueOrResult(row, EColumnImportMaterial.DELETED)
              ?.toString()
              ?.toLowerCase() == 'x';

          const materialObject = {
            id: code ? dataMaterials[code] : undefined,
            code,
            name,
            companyId: companyCode ? dataCompanies[companyCode] : null,
            businessUnitId: businessUnitCode
              ? dataBusinessUnits[businessUnitCode]
              : null,
            materialTypeId: materialTypeCode
              ? dataMaterialTypes[materialTypeCode]
              : null,
            materialGroupId: materialGroupCode
              ? dataMaterialGroups[materialGroupCode]
              : null,
            purchasingGroupId: purchasingGroupCode
              ? dataPurchasingGroups[purchasingGroupCode]
              : null,
            purchasingDepartmentId: purchasingDepartmentCode
              ? dataPurchasingDepartments[purchasingDepartmentCode]
              : null,
            measureId: unit ? dataMeasures[unit] : null,
            description,
            industries: [],
            functionUnitIds: [],
            status,
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            deletedAt: deleted
              ? moment()
                  .add(i * 500, 'milliseconds')
                  .toISOString()
              : undefined,
          };

          if (!code) {
            errors.push({
              error: materialErrorDetails.E_2503(),
              row: rowNumber,
            });
          }

          if (!materialTypeCode) {
            errors.push({
              error: materialErrorDetails.E_2504(),
              row: rowNumber,
            });
          } else if (!materialObject.materialTypeId) {
            errors.push({
              error: materialTypeErrorDetails.E_2000(),
              row: rowNumber,
            });
          }

          if (!materialGroupCode) {
            errors.push({
              error: materialErrorDetails.E_2505(),
              row: rowNumber,
            });
          } else if (!materialObject.materialGroupId) {
            errors.push({
              error: materialGroupErrorDetails.E_2200(),
              row: rowNumber,
            });
          }

          if (!unit) {
            errors.push({
              error: measureErrorDetails.E_6052(),
              row: rowNumber,
            });
          } else if (!materialObject.measureId) {
            errors.push({
              error: measureErrorDetails.E_6050(),
              row: rowNumber,
            });
          }

          if (!codeSAP) {
            errors.push({
              error: materialErrorDetails.E_2506(),
              row: rowNumber,
            });
          }

          if (!sectorCode) {
            errors.push({
              error: materialErrorDetails.E_2507(),
              row: rowNumber,
            });
          } else {
            const checkSectorId = dataSectors[sectorCode];

            if (!checkSectorId) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1036()),
                row: rowNumber,
              });
            } else {
              materialObject.industries.push({
                codeSAP,
                status,
                sectorId: checkSectorId,
              });
            }
          }

          if (companyCode && !materialObject.companyId) {
            errors.push({
              error: getErrorMessage(errorMessage.E_1024()),
              row: rowNumber,
            });
          }

          if (businessUnitCode && !materialObject.businessUnitId) {
            errors.push({
              error: getErrorMessage(errorMessage.E_1022()),
              row: rowNumber,
            });
          }

          if (purchasingGroupCode && !materialObject.purchasingGroupId) {
            errors.push({
              error: purchasingGroupErrorDetails.E_2600(),
              row: rowNumber,
            });
          }

          if (
            purchasingDepartmentCode &&
            !materialObject.purchasingDepartmentId
          ) {
            errors.push({
              error: purchasingDepartmentErrorDetails.E_2800(),
              row: rowNumber,
            });
          }

          if (functionUnitCodes && functionUnitCodes?.length) {
            for (const functionUnitCode of functionUnitCodes) {
              const functionUnitId = dataFunctionUnits[functionUnitCode];

              if (!functionUnitId) {
                errors.push({
                  error: getErrorMessage(errorMessage.E_1034()),
                  row: rowNumber,
                });
              } else {
                materialObject.functionUnitIds.push(functionUnitId);
              }
            }
          }

          if (materialObject.id) {
            const materialDto = plainToInstance(
              UpdateMaterialDto,
              materialObject,
            );

            const checkDuplicate = dataUpdate.find(
              (item) =>
                item.code == code &&
                (item.industries || [])
                  .map((data) => data.codeSAP)
                  .includes(codeSAP),
            );
            if (checkDuplicate) {
              errors.push({
                error: getErrorMessage(errorMessage.E_6060()),
                row: rowNumber,
              });
            }

            dataUpdate.push(materialDto);
          } else {
            const materialDto = plainToInstance(CreateMaterialDto, {
              ...materialObject,
              createdAt: moment()
                .add(i * 500, 'milliseconds')
                .toISOString(),
            });

            const dataCreate = dataUpdate.find(
              (item) =>
                item.code == code &&
                (item.industries || [])
                  .map((data) => data.codeSAP)
                  .includes(codeSAP),
            );
            if (dataCreate) {
              errors.push({
                error: getErrorMessage(errorMessage.E_6060()),
                row: rowNumber,
              });
            }

            dataCreate.push(materialDto);
          }
        }

        const createMaterialDtos: CreateMaterialDto[] = [
          ...this.groupDuplicateObjectsData(dataCreate),
        ];
        const updateMaterialDtos: UpdateMaterialDto[] = [
          ...this.groupDuplicateObjectsData(dataUpdate),
        ];

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportMaterialDto = {
          dataMaterials: createMaterialDtos,
          dataUpdateMaterials: updateMaterialDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_MATERIAL(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  public groupDuplicateObjectsData(objects): any {
    const groups = [];

    // Sử dụng mảng để lưu trữ các nhóm object trùng nhau
    for (let i = 0; i < objects.length; i++) {
      let foundGroup = false;
      for (const group of groups) {
        // Kiểm tra xem object hiện tại có thuộc nhóm nào không

        if (this.areObjectsEqualCode(objects[i], group[0])) {
          group.push(objects[i]);
          foundGroup = true;
          break;
        }
      }
      // Nếu không tìm thấy nhóm nào phù hợp, tạo nhóm mới
      if (!foundGroup) {
        groups.push([objects[i]]);
      }
    }

    // Lọc ra những nhóm có nhiều hơn một object (tức là các nhóm trùng nhau)
    const dataGroups = [];
    for (let i = 0; i < groups.length; i++) {
      let data = { ...groups[i][0], industries: [] };
      for (let j = 0; j < groups[i].length; j++) {
        data = {
          ...groups[i][j],
          industries: data.industries,
        };

        data.industries.push(...(groups[i][j].industries ?? []));
      }

      dataGroups.push(data);
    }
    return dataGroups;
  }

  private areObjectsEqualCode(obj1, obj2): any {
    // Tạo bản sao của các object mà không có trường investments
    const obj1WithoutInvestments = {
      code: obj1.code,
      industries: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };
    const obj2WithoutInvestments = {
      code: obj2.code,
      industries: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };

    // So sánh các thuộc tính khác của hai object
    return _.isEqual(obj1WithoutInvestments, obj2WithoutInvestments);
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.materialRepository.getMaterialByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }

  async exportMaterials(conditions: GetMaterialListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.materialRepository.getMaterials(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-material.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toMaterialModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-materials.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toMaterialModel(materials: MaterialModel[]) {
    const items = [];

    for (let i = 0; i < materials.length; i++) {
      const sectors = (materials[i].industries || [])
        .filter((item) => item.status == EMaterialStatus.ACTIVE)
        .map((data) => data.sector?.code || '')
        .filter(Boolean);
      items.push({
        code: materials[i].code || '',
        name: materials[i].name || '',
        sectors: sectors.join(', '),
        company: materials[i].company?.code || '',
        businessUnit: (materials[i].businessUnits || [])
          .map((item) => item.code)
          .filter(Boolean)
          .join(', '),
        materialTypeCode: materials[i].materialType?.code || '',
        materialTypeName: materials[i].materialType?.name || '',
        materialGroupCode: materials[i].materialGroup?.code || '',
        materialGroupName: materials[i].materialGroup?.name || '',
        purchasingGroup: materials[i].purchasingGroup?.code || '',
        purchasingDepartment: materials[i].purchasingDepartment?.code || '',
        measure: materials[i]?.unit || materials[i]?.measure?.code || '',
        description: materials[i].description || '',
        status: getStatusMaterial(
          sectors.length ? EMaterialStatus.ACTIVE : EMaterialStatus.IN_ACTIVE,
        ),
      });
    }

    return items;
  }

  async getListByIds(
    conditions: GetMaterialListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<MaterialModel>> {
    return await this.materialRepository.getListByIds(conditions, jwtPayload);
  }
}
