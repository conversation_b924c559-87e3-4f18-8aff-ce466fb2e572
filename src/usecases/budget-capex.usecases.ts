import { Inject, Injectable } from '@nestjs/common';
import { IBudgetCapexRepository } from '../domain/repositories/budget-capex.repository';
import { CreateBudgetCapexDto } from '../controller/budget/dtos/create-budget-capex.dto';
import { BudgetCapexModel } from '../domain/model/budget-capex.model';
import { UpdateBudgetCapexDto } from '../controller/budget/dtos/update-budget-capex.dto';

@Injectable()
export class BudgetCapexUsecases {
  constructor(
    @Inject(IBudgetCapexRepository)
    private readonly budgetCapexRepository: IBudgetCapexRepository,
  ) {}

  async createBudgetCapex(
    data: CreateBudgetCapexDto,
  ): Promise<BudgetCapexModel> {
    const createBudgetCapex = new BudgetCapexModel({
      ...data,
    });

    return await this.budgetCapexRepository.createBudgetCapex(
      createBudgetCapex,
    );
  }

  async updateBudgetCapex(
    id: string,
    data: UpdateBudgetCapexDto,
  ): Promise<BudgetCapexModel> {
    const updateBudgetCapex = new BudgetCapexModel({
      id: id,
      ...data,
    });

    return await this.budgetCapexRepository.updateBudgetCapex(
      updateBudgetCapex,
    );
  }

  async getBudgetCapexById(id: string): Promise<BudgetCapexModel> {
    return await this.budgetCapexRepository.getBudgetCapexById(id);
  }
}
