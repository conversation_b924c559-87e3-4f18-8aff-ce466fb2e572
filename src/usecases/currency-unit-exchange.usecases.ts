import { Inject, Injectable } from '@nestjs/common';
import { ICurrencyUnitExchangeRepository } from '../domain/repositories/currency-unit-exchange.repository';
import { CreateCurrencyUnitExchangeDto } from '../controller/currency-unit-exchange/dtos/create-currency-unit-exchange.dto';
import { CurrencyUnitExchangeModel } from '../domain/model/currency-unit-exchange.model';

@Injectable()
export class CurrencyUnitExchangeUsecases {
  constructor(
    @Inject(ICurrencyUnitExchangeRepository)
    private readonly currencyUnitExchangeRepository: ICurrencyUnitExchangeRepository,
  ) {}

  async createManyCurrencyExchange(
    data: CurrencyUnitExchangeModel[],
  ): Promise<CurrencyUnitExchangeModel[]> {
    const createManyCurrencyUnitExchange: CurrencyUnitExchangeModel[] = [];
    for (const currencyUnitExchange of data) {
      createManyCurrencyUnitExchange.push(
        new CurrencyUnitExchangeModel(currencyUnitExchange),
      );
    }

    return await this.currencyUnitExchangeRepository.createManyCurrencyUnitExchange(
      createManyCurrencyUnitExchange,
    );
  }

  async updateManyCurrencyUnitExchange(
    data: CurrencyUnitExchangeModel[],
  ): Promise<CurrencyUnitExchangeModel[]> {
    const updateManyCurrencyUnitExchange: CurrencyUnitExchangeModel[] = [];
    for (const currencyUnitExchange of data) {
      updateManyCurrencyUnitExchange.push(
        new CurrencyUnitExchangeModel(currencyUnitExchange),
      );
    }

    return await this.currencyUnitExchangeRepository.updateManyCurrencyUnitExchange(
      updateManyCurrencyUnitExchange,
    );
  }

  async deleteCurrencyUnitExchangesNotIn(
    ids: string[],
    currencyId: string,
  ): Promise<void> {
    return await this.currencyUnitExchangeRepository.deleteCurrencyUnitExchangesNotIn(
      ids,
      currencyId,
    );
  }
}
