import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import { resolve } from 'path';
import { GetDetailBusinessUnitDto } from '../controller/business-unit/dtos/get-detail-business-unit.dto';
import { GetDetailCompanyDto } from '../controller/company/dtos/get-detail-company.dto';
import { GetDetailDepartmentDto } from '../controller/department/dtos/get-detail-department.dto';
import { CreateInventoryStandardDto } from '../controller/inventory-standard/dtos/create-inventory-standard.dto';
import { GetDetailInventoryStandardDto } from '../controller/inventory-standard/dtos/get-detail-inventory-standard.dto';
import { GetInventoryStandardListDto } from '../controller/inventory-standard/dtos/get-inventory-standard-list.dto';
import { UpdateInventoryStandardDto } from '../controller/inventory-standard/dtos/update-inventory-standard.dto';
import { GetDetailMaterialDto } from '../controller/material/dtos/get-detail-material.dto';
import { GetDetailSectorDto } from '../controller/sector/dtos/get-detail-sector.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import { EBusinessUnitStatus } from '../domain/config/enums/business-unit.enum';
import { ECompanyStatus } from '../domain/config/enums/company.enum';
import { EDepartmentStatus } from '../domain/config/enums/department.enum';
import { ESectorStatus } from '../domain/config/enums/sector.enum';
import { inventoryStandardErrorDetails } from '../domain/messages/error-details/inventory-standard';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { InventoryStandardModel } from '../domain/model/inventory-standard.model';
import { IInventoryStandardRepository } from '../domain/repositories/inventory-standard.repository';
import {
  checkValuesEmptyRowExcel,
  getPathInventoryStandard,
  getStatus,
  getStatusInventoryStandard,
  getValueOrResult,
  uidToPath,
} from '../utils/common';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CompanyUsecases } from './company.usecases';
import { DepartmentUsecases } from './department.usecases';
import { FileUsecases } from './file.usecases';
import { MaterialUsecases } from './material.usecases';
import { SectorUsecases } from './sector.usecases';
import { importErrorDetails } from '../domain/messages/error-details/import';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import * as moment from 'moment';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import {
  EColumnImportInventoryStandard,
  EInventoryStandardStatus,
} from '../domain/config/enums/inventory-standard.enum';
import { IMaterialRepository } from '../domain/repositories/material.repository';
import { ISectorRepository } from '../domain/repositories/sector.repository';
import { ICompanyRepository } from '../domain/repositories/company.repository';
import { IBusinessUnitRepository } from '../domain/repositories/business-unit.repository';
import { IDepartmentRepository } from '../domain/repositories/department.repository';
import { ImportInventoryStandardDto } from '../controller/import/dtos/import-inventory-standard.dto';
import { sendPost } from '../utils/http';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { materialErrorDetails } from '../domain/messages/error-details/material';

@Injectable()
export class InventoryStandardUsecases {
  constructor(
    @Inject(IInventoryStandardRepository)
    private readonly inventoryStandardRepository: IInventoryStandardRepository,
    @Inject(IMaterialRepository)
    private readonly materialRepository: IMaterialRepository,
    @Inject(ISectorRepository)
    private readonly sectorRepository: ISectorRepository,
    @Inject(ICompanyRepository)
    private readonly companyRepository: ICompanyRepository,
    @Inject(IBusinessUnitRepository)
    private readonly businessUnitRepository: IBusinessUnitRepository,
    @Inject(IDepartmentRepository)
    private readonly departmentRepository: IDepartmentRepository,
    private readonly sectorUsecases: SectorUsecases,
    private readonly companyUsecases: CompanyUsecases,
    private readonly businessUnitUsecases: BusinessUnitUsecases,
    private readonly departmentUsecases: DepartmentUsecases,
    private readonly materialUsecases: MaterialUsecases,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
  ) {}

  async createInventoryStandard(
    conditions: CreateInventoryStandardDto,
    jwtPayload: any,
    isImport: boolean = false,
  ): Promise<InventoryStandardModel> {
    await this.verifyData(conditions, jwtPayload, isImport);

    const inventoryStandardModel = new InventoryStandardModel(conditions);
    return await this.inventoryStandardRepository.createInventoryStandard(
      inventoryStandardModel,
    );
  }

  async updateInventoryStandard(
    id: string,
    conditions: UpdateInventoryStandardDto,
    jwtPayload: any,
    isImport: boolean = false,
  ): Promise<InventoryStandardModel> {
    await this.verifyData(conditions, jwtPayload, isImport, id);

    const inventoryStandardModel = new InventoryStandardModel({
      id,
      ...conditions,
    });
    return await this.inventoryStandardRepository.updateInventoryStandard(
      inventoryStandardModel,
    );
  }

  async getInventoryStandards(
    conditions: GetInventoryStandardListDto,
    jwtPayload: any,
  ) {
    return await this.inventoryStandardRepository.getInventoryStandards(
      conditions,
      jwtPayload,
    );
  }

  async deleteInventoryStandard(id: string, jwtPayload: any) {
    await this.getInventoryStandardDetail(
      plainToInstance(GetDetailInventoryStandardDto, {
        id,
      }),
      jwtPayload,
    );

    return await this.inventoryStandardRepository.deleteInventoryStandard(id);
  }

  async getInventoryStandardDetail(
    conditions: GetDetailInventoryStandardDto,
    jwtPayload: any,
  ) {
    const detail =
      await this.inventoryStandardRepository.getInventoryStandardDetail(
        conditions,
        jwtPayload,
      );

    if (!detail) {
      throw new HttpException(
        inventoryStandardErrorDetails.E_6500(),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async verifyData(
    conditions: CreateInventoryStandardDto | UpdateInventoryStandardDto,
    jwtPayload: any,
    isImport: boolean = false,
    id?: string,
  ) {
    let detail: InventoryStandardModel;
    if (id) {
      detail = await this.getInventoryStandardDetail(
        plainToInstance(GetDetailInventoryStandardDto, {
          id,
        }),
        jwtPayload,
      );
    }

    if (!isImport) {
      if (detail?.materialId != conditions.materialId) {
        await this.materialUsecases.getMaterialDetail(
          plainToInstance(GetDetailMaterialDto, {
            id: conditions.materialId,
          }),
          jwtPayload,
        );
      }

      if (detail?.sectorId != conditions.sectorId) {
        const sector = await this.sectorUsecases.getDetailSector(
          plainToInstance(GetDetailSectorDto, {
            id: conditions.sectorId,
          }),
          jwtPayload,
        );
        if (sector.status == ESectorStatus.IN_ACTIVE) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1027()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      if (conditions.companyId && detail?.companyId != conditions.companyId) {
        const company = await this.companyUsecases.getDetailCompany(
          plainToInstance(GetDetailCompanyDto, {
            id: conditions.companyId,
          }),
          jwtPayload,
        );
        if (company.status == ECompanyStatus.IN_ACTIVE) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1028()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      if (
        conditions.businessUnitId &&
        detail?.businessUnitId != conditions.businessUnitId
      ) {
        const businessUnit =
          await this.businessUnitUsecases.getDetailBusinessUnit(
            plainToInstance(GetDetailBusinessUnitDto, {
              id: conditions.businessUnitId,
            }),
            jwtPayload,
          );
        if (businessUnit.status == EBusinessUnitStatus.IN_ACTIVE) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1029()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      if (
        conditions.departmentId &&
        detail?.departmentId != conditions.departmentId
      ) {
        const department = await this.departmentUsecases.getDetailDepartment(
          plainToInstance(GetDetailDepartmentDto, {
            id: conditions.departmentId,
          }),
          jwtPayload,
        );
        if (department.status == EDepartmentStatus.IN_ACTIVE) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1030()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    }

    const duplicates =
      await this.inventoryStandardRepository.checkDuplicatePathInventoryStandard(
        conditions,
        id,
      );

    if (duplicates && duplicates.length) {
      throw new HttpException(
        inventoryStandardErrorDetails.E_6501(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async exportExcelInventoryStandard(
    conditions: GetInventoryStandardListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data = await this.inventoryStandardRepository.getInventoryStandards(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-inventory-standard.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toInventoryStandardModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-inventory-standard.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toInventoryStandardModel(
    inventoryStandards: InventoryStandardModel[],
  ) {
    const items = [];

    for (let i = 0; i < inventoryStandards.length; i++) {
      items.push({
        materialCode: inventoryStandards[i].material?.code || '',
        materialName: inventoryStandards[i].material?.name || '',
        materialGroup:
          inventoryStandards[i].material?.materialGroup?.name || '',
        unit: inventoryStandards[i].unit || '',
        sectorCode: inventoryStandards[i].sector?.code || '',
        sectorName: inventoryStandards[i].sector?.name || '',
        companyCode: inventoryStandards[i].company?.code || '',
        companyName: inventoryStandards[i].company?.name || '',
        businessUnitCode: inventoryStandards[i].businessUnit?.code || '',
        businessUnitName: inventoryStandards[i].businessUnit?.name || '',
        departmentCode: inventoryStandards[i].department?.code || '',
        departmentName: inventoryStandards[i].department?.name || '',
        standardQuantity: inventoryStandards[i].standardQuantity || 0,
        inventoryQuantity: inventoryStandards[i].inventoryQuantity || 0,
        status: getStatusInventoryStandard(inventoryStandards[i].status),
      });
    }

    return items;
  }

  async importInventoryStandard(
    file: Express.Multer.File,
    jwtPayload: any,
    authorization: string,
  ) {
    ///Upload file import

    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.INVENTORY_STANDARD,
      });

      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createInventoryStandardDtos: CreateInventoryStandardDto[] = [];
        const updateInventoryStandardDtos: UpdateInventoryStandardDto[] = [];
        const totalData: any = [];
        const errors: TErrorMessageImport[] = [];

        let totalRowHasValue = 0;

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const materialCodes = rows
          .map((item) =>
            getValueOrResult(
              item,
              EColumnImportInventoryStandard.MATERIAL_CODE,
            )?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const sectorCodes = rows
          .map((item) =>
            getValueOrResult(
              item,
              EColumnImportInventoryStandard.SECTOR_CODE,
            )?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const companyCodes = rows
          .map((item) =>
            getValueOrResult(
              item,
              EColumnImportInventoryStandard.COMPANY_CODE,
            )?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const businessUnitCodes = rows
          .map((item) =>
            getValueOrResult(
              item,
              EColumnImportInventoryStandard.BUSINESS_UNIT_CODE,
            )?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const departmentCodes = rows
          .map((item) =>
            getValueOrResult(
              item,
              EColumnImportInventoryStandard.DEPARTMENT_CODE,
            )?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const [materials, sectors, companies, businessUnits, departments] =
          await Promise.all([
            this.materialRepository.getMaterialByCodesWithRole(
              [...new Set(materialCodes)],
              jwtPayload,
            ),
            this.sectorRepository.getSectorsByCodesWithRole(
              [...new Set(sectorCodes)],
              jwtPayload,
            ),
            this.companyRepository.getCompaniesByCodesWithRole(
              [...new Set(companyCodes)],
              jwtPayload,
            ),
            this.businessUnitRepository.getBusinessUnitsByCodesWithRole(
              [...new Set(businessUnitCodes)],
              jwtPayload,
            ),
            this.departmentRepository.getDepartmentsByCodesWithRole(
              [...new Set(departmentCodes)],
              jwtPayload,
            ),
          ]);

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportInventoryStandard.MATERIAL_CODE, // First Cell
            EColumnImportInventoryStandard.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          const materialCode = getValueOrResult(
            row,
            EColumnImportInventoryStandard.MATERIAL_CODE,
          )?.toString();

          const sectorCode = getValueOrResult(
            row,
            EColumnImportInventoryStandard.SECTOR_CODE,
          )?.toString();

          const companyCode = getValueOrResult(
            row,
            EColumnImportInventoryStandard.COMPANY_CODE,
          )?.toString();

          const businessUnitCode = getValueOrResult(
            row,
            EColumnImportInventoryStandard.BUSINESS_UNIT_CODE,
          )?.toString();

          const departmentCode = getValueOrResult(
            row,
            EColumnImportInventoryStandard.DEPARTMENT_CODE,
          )?.toString();

          const standardQuantity = getValueOrResult(
            row,
            EColumnImportInventoryStandard.STANDARD_QUANTITY,
            true,
          );

          const inventoryQuantity = getValueOrResult(
            row,
            EColumnImportInventoryStandard.INVENTORY_QUANTITY,
            true,
          );

          const status = getStatus(
            InventoryStandardModel,
            getValueOrResult(
              row,
              EColumnImportInventoryStandard.STATUS,
            )?.toString(),
          );

          const inventoryStandardObject = {
            id: undefined,
            materialId: null,
            sectorId: null,
            companyId: null,
            businessUnitId: null,
            departmentId: null,
            standardQuantity: standardQuantity || 0,
            inventoryQuantity: inventoryQuantity || 0,
            status,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            row: i + 1,
          };

          if (!materialCode) {
            errors.push({
              error: getErrorMessage(
                inventoryStandardErrorDetails.E_6504(),
                'Mã vật tư không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const material = materials.find(
              (item) => item.code === materialCode,
            );
            if (!material) {
              errors.push({
                error: getErrorMessage(
                  materialErrorDetails.E_2500(),
                  'Mã vật tư chưa đúng',
                ),
                row: i + 1,
              });
            } else {
              inventoryStandardObject.materialId = material.id;
            }
          }

          if (!sectorCode) {
            errors.push({
              error: getErrorMessage(
                inventoryStandardErrorDetails.E_6505(),
                'Mã ngành không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const sector = sectors.find((item) => item.code === sectorCode);
            if (!sector) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1036(),
                  'Mã ngành chưa đúng',
                ),
                row: i + 1,
              });
            } else {
              inventoryStandardObject.sectorId = sector.id;
            }
          }

          if (companyCode) {
            const company = companies.find((item) => item.code === companyCode);
            if (!company) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1024(),
                  'Mã công ty chưa đúng',
                ),
                row: i + 1,
              });
            } else {
              inventoryStandardObject.companyId = company.id;
            }
          }

          if (businessUnitCode) {
            const businessUnit = businessUnits.find(
              (item) => item.code === businessUnitCode,
            );
            if (!businessUnit) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1022(),
                  'Mã plant chưa đúng',
                ),
                row: i + 1,
              });
            } else {
              inventoryStandardObject.businessUnitId = businessUnit.id;
            }
          }

          if (departmentCode) {
            const department = departments.find(
              (item) => item.code === departmentCode,
            );
            if (!department) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1050(),
                  'Mã phòng ban chưa đúng',
                ),
                row: i + 1,
              });
            } else {
              inventoryStandardObject.departmentId = department.id;
            }
          }

          if (
            inventoryStandardObject.materialId &&
            inventoryStandardObject.sectorId
          ) {
            const path = getPathInventoryStandard(
              inventoryStandardObject.materialId,
              inventoryStandardObject.sectorId,
              inventoryStandardObject.companyId,
              inventoryStandardObject.businessUnitId,
              inventoryStandardObject.departmentId,
            );

            const checkPath =
              await this.inventoryStandardRepository.getInventoryStandardByPath(
                path,
              );

            if (checkPath) {
              inventoryStandardObject.id = checkPath.id;
            } else {
              const dataCreate: CreateInventoryStandardDto = {
                materialId: inventoryStandardObject.materialId,
                sectorId: inventoryStandardObject.sectorId,
                companyId: inventoryStandardObject.companyId,
                businessUnitId: inventoryStandardObject.businessUnitId,
                departmentId: inventoryStandardObject.departmentId,
                standardQuantity: 0,
                inventoryQuantity: 0,
                status: EInventoryStandardStatus.ACTIVE,
              };
              const duplicates =
                await this.inventoryStandardRepository.checkDuplicatePathInventoryStandard(
                  dataCreate,
                );

              if (duplicates && duplicates.length) {
                errors.push({
                  error: getErrorMessage(
                    inventoryStandardErrorDetails.E_6501(),
                    'Định mức tồn kho bị trùng trong database',
                  ),
                  row: i + 1,
                });
              }
            }
          }

          totalData.push({
            ...inventoryStandardObject,
            path: getPathInventoryStandard(
              inventoryStandardObject.materialId,
              inventoryStandardObject.sectorId,
              inventoryStandardObject.companyId,
              inventoryStandardObject.businessUnitId,
              inventoryStandardObject.departmentId,
            ),
          });

          if (inventoryStandardObject.id) {
            const inventoryStandardDto = plainToInstance(
              UpdateInventoryStandardDto,
              inventoryStandardObject,
            );
            updateInventoryStandardDtos.push({
              ...inventoryStandardDto,
              createdAt: undefined,
            });
          } else {
            const inventoryStandardDto = plainToInstance(
              CreateInventoryStandardDto,
              inventoryStandardObject,
            );
            createInventoryStandardDtos.push(inventoryStandardDto);
          }
        }

        for (let i = 0; i < totalData?.length; i++) {
          const data = totalData[i];

          if (data.materialId && data.sectorId) {
            const listPath =
              await this.inventoryStandardRepository.getParamsCheckkDuplicatesInventoryStandard(
                data.materialId,
                data.sectorId,
                data.companyId,
                data.businessUnitId,
                data.departmentId,
              );

            if (!data.companyId || !data.businessUnitId || !data.departmentId) {
              const checkDuplicates = totalData.filter(
                (item) =>
                  listPath.includes(item.path) ||
                  item.path.includes(
                    `${uidToPath(data.materialId)}.${uidToPath(data.sectorId)}`,
                  ),
              );

              if (checkDuplicates && checkDuplicates.length > 1) {
                errors.push({
                  error: getErrorMessage(
                    inventoryStandardErrorDetails.E_6501(),
                    'Định mức tồn kho bị trùng trong file',
                  ),
                  row: data.row || 0,
                });
              }
            } else {
              const checkDuplicates = totalData.filter((item) =>
                listPath.includes(item.path),
              );

              if (checkDuplicates && checkDuplicates.length > 1) {
                errors.push({
                  error: getErrorMessage(
                    inventoryStandardErrorDetails.E_6501(),
                    'Định mức tồn kho bị trùng trong file',
                  ),
                  row: data.row || 0,
                });
              }
            }
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportInventoryStandardDto = {
          dataInventoryStandards: createInventoryStandardDtos,
          dataUpdateInventoryStandards: updateInventoryStandardDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_INVENTORY_STANDARD(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
