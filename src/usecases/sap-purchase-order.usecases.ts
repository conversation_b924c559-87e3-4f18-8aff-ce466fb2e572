import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import * as moment from 'moment';
import { CancelPoSapDto } from '../controller/sap-purchase-order/dtos/cancel-po-sap.dto';
import { CreatePoItemSapDto } from '../controller/sap-purchase-order/dtos/create-po-item-sap.dto';
import { CreatePoSapDto } from '../controller/sap-purchase-order/dtos/create-po-sap.dto';
import { CreateSapPurchaseOrderItemDto } from '../controller/sap-purchase-order/dtos/create-sap-purchase-order-item.dto';
import { CreateSapPurchaseOrderDto } from '../controller/sap-purchase-order/dtos/create-sap-purchase-order.dto';
import { EAccountAssignment } from '../domain/config/enums/account-assignment.enum';
import { EBudgetType } from '../domain/config/enums/budget.enum';
import { State, Status } from '../domain/config/enums/purchase-order.enum';
import { ESapPoStatus } from '../domain/config/enums/sap-po-status.enum';
import { PurchaseOrderModel } from '../domain/model/purchase_order.model';
import { PurchaseOrderDetailModel } from '../domain/model/purchase_order_detail.model';
import { SapPurchaseOrderModel } from '../domain/model/sap_purchase_order.model';
import { SapPurchaseOrderItemModel } from '../domain/model/sap_purchase_order_item.model';
import { ISapPurchaseOrderItemRepository } from '../domain/repositories/sap-purchase-order-item.repository';
import { ISapPurchaseOrderRepository } from '../domain/repositories/sap-purchase-order.repository';
import { EnvironmentConfigService } from '../infrastructure/config/environment-config/environment-config.service';
import { PurchaseOrderEntity } from '../infrastructure/entities/purchase_order.entity';
import { groupByMultipleFields } from '../utils/common';
import {
  SapApiUrlsConst,
  SapApiUrlsFoodConst,
} from '../utils/constants/sap-api-url.const';
import { sendPost } from '../utils/http';
import { purchaseOrderUsecases } from './purchase_order.usecases';
import { DataSource } from 'typeorm';
import { errorMessage } from '../domain/messages/error-message';
import { ApiLogUsecases } from './api-log.usecase';

const config: EnvironmentConfigService = new EnvironmentConfigService();

@Injectable()
export class SapPurchaseOrderUsecases {
  constructor(
    private readonly dataSource: DataSource,
    private _purchaseOrderUsecases: purchaseOrderUsecases,
    private apiLogUsecases: ApiLogUsecases,
    @Inject('ISapPurchaseOrderRepository')
    private readonly sapPurchaseOrderRepository: ISapPurchaseOrderRepository,
    @Inject('ISapPurchaseOrderItemRepository')
    private readonly sapPurchaseOrderItemRepository: ISapPurchaseOrderItemRepository,
  ) {}

  async syncAndSaveSapPurchaseOrder(
    poId: number,
    authorization: string,
    isApprove: boolean = true,
    po?: PurchaseOrderModel,
  ): Promise<SapPurchaseOrderModel[]> {
    po ??= await this._purchaseOrderUsecases.findOne(poId, authorization);
    if (po.statePo === State.Completed) {
      throw new HttpException(errorMessage.E_6056(), HttpStatus.BAD_REQUEST);
    }

    if (po.statusPo !== Status.Approved && !isApprove) {
      throw new HttpException(errorMessage.E_6055(), HttpStatus.BAD_REQUEST);
    }

    if (!po?.details?.length) return;

    if (!isApprove && po?.sapPos?.length) {
      const sapPoIds = po.sapPos?.map((sapPo) => {
        if (sapPo.messageType == 'E') return sapPo.id;
      });

      if (sapPoIds?.length)
        await Promise.all([
          this.sapPurchaseOrderRepository.deleteSapPurchaseOrdersById(sapPoIds),
          this.sapPurchaseOrderItemRepository.deleteSapPOItemsBySapPurchaseOrderId(
            sapPoIds,
          ),
        ]);
    }

    const sapPurchaseOrders =
      (await this.createAndPrepareSapPurchaseOrders(po)) || [];

    const responseSapPo = await this.syncSapPurchaseOrder(
      poId,
      sapPurchaseOrders,
      po,
    );

    const errors = this.processSapPurchaseOrders(
      po,
      sapPurchaseOrders,
      responseSapPo,
    );

    const newSapPurchaseOrders =
      await this.sapPurchaseOrderRepository.updateSapPurchaseOrders(
        sapPurchaseOrders,
      );

    if (errors.length && !isApprove) {
      throw new HttpException(errors, HttpStatus.BAD_REQUEST);
    }

    if (!errors.length && !isApprove) {
      const queryRunner = this.dataSource.createQueryRunner();

      try {
        await queryRunner.connect();
        await queryRunner.startTransaction();

        await queryRunner.manager.save(PurchaseOrderEntity, {
          id: poId,
          isCreatedSap: true,
          statePo: State.Completed,
        });

        await queryRunner.commitTransaction();
      } catch (error) {
        await queryRunner.rollbackTransaction();
      } finally {
        await queryRunner.release();
      }
    }

    return newSapPurchaseOrders;
  }

  private async createAndPrepareSapPurchaseOrders(
    po: PurchaseOrderModel,
  ): Promise<SapPurchaseOrderModel[]> {
    const groupedDetails = groupByMultipleFields<PurchaseOrderDetailModel>(
      po.details,
      ['supplier', 'supplierInfo', 'currency', 'exchangeRate'],
    );

    const createSapPurchaseOrderDtos = groupedDetails?.map((details) =>
      this.createSapPurchaseOrderDto(po, details),
    );

    return await this.createSapPurchaseOrders(createSapPurchaseOrderDtos);
  }

  private processSapPurchaseOrders(
    po: PurchaseOrderModel,
    sapPurchaseOrders: SapPurchaseOrderModel[],
    responseSapPo: any,
    isDelete: boolean = false,
  ) {
    const errors: any[] = [];

    for (let i = 0; i < sapPurchaseOrders.length; i++) {
      const sapPo = responseSapPo?.listpo?.find(
        (po) => po.sepid === sapPurchaseOrders[i].id,
      );

      sapPurchaseOrders[i].messageType =
        sapPo?.mestyp || responseSapPo?.mestyp || 'E';
      sapPurchaseOrders[i].message =
        sapPo?.mestyp === 'E' || responseSapPo?.mestyp === 'E'
          ? sapPo?.message ||
            responseSapPo?.message ||
            'EP synchronization failed'
          : '';
      sapPurchaseOrders[i].ebeln = sapPo?.ebeln || responseSapPo?.ebeln;

      if (sapPo?.mestyp === 'S') {
        sapPurchaseOrders[i].status = !isDelete
          ? ESapPoStatus.COMPLETED
          : sapPo?.status;
      } else {
        if (isDelete) {
          sapPurchaseOrders[i].status =
            po.statusPo === Status.Cancel ? 'DELETE_FAILED' : 'CLOSE_FAILED';
        } else {
          sapPurchaseOrders[i].status = 'SYNC_FAILED';
        }
      }

      const error = this.processSapPoItems(po, sapPurchaseOrders[i], sapPo);

      if (error.messageType === 'E') {
        errors.push(error);
      }
    }

    return errors;
  }

  private processSapPoItems(
    po: PurchaseOrderModel,
    sapPurchaseOrder: SapPurchaseOrderModel,
    sapPo: any,
    isDelete: boolean = false,
  ): any {
    const error = {
      poId: po.id,
      messageType: sapPurchaseOrder.messageType,
      message: sapPurchaseOrder.message,
      items: [],
    };

    if (!isDelete) {
      sapPo?.items?.forEach((sapPoItem: any) => {
        const item = sapPurchaseOrder.items?.find(
          (item) => item.eprId == sapPoItem.eprid,
        );

        for (let i = 0; i < sapPurchaseOrder.items.length; i++) {
          if (sapPurchaseOrder.items[i].eprId == sapPoItem.eprid) {
            sapPurchaseOrder.items[i].messageType = sapPoItem?.mestyp || 'E';
            sapPurchaseOrder.items[i].message =
              sapPoItem?.mestyp === 'E'
                ? sapPoItem?.message || 'EP synchronization failed'
                : '';

            if (sapPoItem?.mestyp === 'E') {
              error.items.push({
                poDetailId: item.id,
                messageType: item.messageType,
                message: item.message,
              });
            }
          }
        }

        // if (item) {
        //   item.messageType = sapPoItem?.mestyp || 'E';
        //   item.message = sapPoItem?.mestyp === 'E' ? sapPoItem?.message || 'EP synchronization failed' : '';

        //   if (sapPoItem?.mestyp === 'E') {
        //     error.items.push({
        //       poDetailId: item.id,
        //       messageType: item.messageType,
        //       message: item.message,
        //     });
        //   }
        // }
      });

      if (error.items.some((item) => item.messageType === 'E')) {
        error.messageType = 'E';
      }
    }

    return error;
  }

  private createSapPurchaseOrderDto(
    po: PurchaseOrderModel,
    details: PurchaseOrderDetailModel[],
  ): CreateSapPurchaseOrderDto {
    const [firstDetail] = details;
    const {
      businessUnit,
      typePo,
      purchaseOrg,
      purchaseGroup,
      currency,
      exchangeRate,
    } = po;

    return {
      poId: po.id,
      companyId: businessUnit?.company?.id,
      companyCode: businessUnit?.company?.code,
      poTypeId: typePo?.id,
      poTypeCode: typePo?.code,
      purchasingDepartmentId: purchaseOrg?.id,
      purchasingDepartmentCode: purchaseOrg?.code,
      purchasingGroupId: purchaseGroup?.id,
      purchasingGroupCode: purchaseGroup?.code,
      poCreatedAt: po.createdAt,
      exchangeRate: Number(exchangeRate || firstDetail.exchangeRate),
      currencyCode:
        currency?.currencyCode || firstDetail?.currency?.currencyCode,
      supplierId: firstDetail?.supplier?.id,
      supplierCode: firstDetail?.supplier?.code,
      supplierInfo: firstDetail?.supplierInfo,
      items: details
        ?.map((detail, index) =>
          this.createSapPurchaseOrderItemDto(po, detail, index),
        )
        .filter((item) => Object.keys(item).length > 0),
    };
  }

  private createSapPurchaseOrderItemDto(
    po: PurchaseOrderModel,
    detail: PurchaseOrderDetailModel,
    index: number,
  ): CreateSapPurchaseOrderItemDto {
    if (!detail?.quantity || Number(detail.quantity) <= 0)
      return {} as CreateSapPurchaseOrderItemDto;

    return {
      eprId: (index + 1) * 10,
      materialId: detail.material?.id,
      materialCode: detail.material?.code,
      materialName: detail.materialName,
      materialGroupId: detail.materialGroup?.id,
      materialGroupCode: detail.materialGroup?.code,
      materialGroupName: detail.materialGroupName,
      quantity: Number(detail.quantity),
      unit: detail.unit,
      deliveryDate: detail.deliveryTime,
      price: Number(detail.purchasePrice),
      accountAssignment: detail.accountAssignment,
      glAccount: detail.accountGl,
      costCenterId: detail.costCenter?.id,
      costCenterCode: detail.costCenter?.code,
      asset: detail.property,
      internalOrder: detail.internalOrder,
      wbs: detail.wbs,
      functionalArea: !detail.budgetCode
        ? '*********'
        : detail.budgetCode.budgetType === EBudgetType.CAPEX
          ? null
          : detail.budgetCode.code,
      currencyId: detail.currency.id,
      currencyCode: detail.currency.currencyCode,
      buId: po.businessUnit?.id,
      buCode: po.businessUnit?.code,
      purchaseOrderDetailId: detail.id,
      taxCodeId: detail.taxCode?.id,
      taxCodeCode: detail.taxCode?.code,
    };
  }

  async createSapPurchaseOrders(
    createSapPurchaseOrderDtos: CreateSapPurchaseOrderDto[],
  ): Promise<SapPurchaseOrderModel[]> {
    await this.sapPurchaseOrderRepository.createSapPurchaseOrders(
      createSapPurchaseOrderDtos,
    );

    return createSapPurchaseOrderDtos?.map(
      (dto) =>
        new SapPurchaseOrderModel({
          ...dto,
          items: dto.items?.map((item) => new SapPurchaseOrderItemModel(item)),
        }),
    );
  }

  async createSapPurchaseOrderItem(
    createSapPurchaseOrderItemDtos: CreateSapPurchaseOrderItemDto[],
  ): Promise<SapPurchaseOrderItemModel[]> {
    return await this.sapPurchaseOrderItemRepository.createSapPurchaseOrderItem(
      createSapPurchaseOrderItemDtos,
    );
  }

  async syncSapPurchaseOrder(
    poId: number,
    sapPurchaseOrders: SapPurchaseOrderModel[],
    po: PurchaseOrderModel,
  ): Promise<any> {
    try {
      const syncPoList = (sapPurchaseOrders || []).map((sapPurchaseOrder) => {
        const createPoSapDto: CreatePoSapDto = {
          sepid: sapPurchaseOrder.id,
          bukrs: sapPurchaseOrder.companyCode,
          crdat: moment().format('YYYYMMDD'),
          crnam:
            `${(sapPurchaseOrder.createdBy as any)?.lastName || ''} ${(sapPurchaseOrder.createdBy as any)?.firstName || ''}`.trim(),
          crtim: moment().format('HH:mm:ss'),
          bsart: sapPurchaseOrder.poTypeCode,
          ekorg: sapPurchaseOrder.purchasingDepartmentCode,
          ekgrp: sapPurchaseOrder.purchasingGroupCode,
          aedat: moment(sapPurchaseOrder.poCreatedAt).format('YYYYMMDD'),
          wkurs: Number(sapPurchaseOrder.exchangeRate),
          lifnr: sapPurchaseOrder.supplierCode,
          oname: (sapPurchaseOrder.supplierInfo as any)?.supplierName || '',
          ocity: (sapPurchaseOrder.supplierInfo as any)?.supplierCity || '',
          odistrict:
            (sapPurchaseOrder.supplierInfo as any)?.supplierDistrict || '',
          ocountry:
            (sapPurchaseOrder.supplierInfo as any)?.supplierCountry || '',
          olocation:
            (sapPurchaseOrder.supplierInfo as any)?.supplierLocation || '',
          waers: sapPurchaseOrder.currencyCode,
          items:
            sapPurchaseOrder?.items
              ?.map((item) => this.createPoItemSapDto(item))
              .filter((item) => Object.keys(item).length > 0) || [],
        };

        return createPoSapDto;
      });

      const dataSendPost = {
        eprno: poId,
        listpo: syncPoList?.filter((po) => po.items.length > 0),
      };

      let data = { ...dataSendPost };

      try {
        if (dataSendPost.listpo?.length) {
          let response;
          if (
            po.sector?.code == 'FEED' &&
            po.businessUnit?.company?.code == '1000'
          ) {
            response = await sendPost(
              SapApiUrlsConst.CREATE_PO(),
              dataSendPost,
              {},
              {
                username: config.getSapBasicUsername(),
                password: config.getSapBasicPassword(),
              },
            );
          }

          if (
            po.sector?.code == 'FOOD' &&
            (po.businessUnit?.company?.code == '3000' ||
              po.businessUnit?.company?.code == '3100')
          ) {
            response = await sendPost(
              SapApiUrlsFoodConst.CREATE_PO(),
              dataSendPost,
              {},
              {
                username: config.getSapBasicUsernameFood(),
                password: config.getSapBasicPasswordFood(),
              },
            );
          }

          data = { ...dataSendPost, ...response?.data };

          await this.apiLogUsecases.create({
            controller: 'SapPurchaseOrderController', // Tên controller
            method: 'POST', // Method: GET, POST...
            route: 'sap', // Endpoint API
            statusCode: 500, // HTTP Status Code
            isSuccess: false, // Thành công hay thất bại
            body: dataSendPost,
            errorMessage:
              response?.data?.listpo
                ?.flatMap((po) => [
                  ...(po.mestyp === 'E' ? [po.message] : []),
                  ...(po.items
                    ?.filter((item) => item.mestyp === 'E')
                    ?.map((item) => item.message) || []),
                ])
                .join(', ') ||
              response?.data ||
              'EP synchronization failed',
          });
        }
      } catch (error) {
        await this.apiLogUsecases.create({
          controller: 'SapPurchaseOrderController', // Tên controller
          method: 'POST', // Method: GET, POST...
          route: 'sap', // Endpoint API
          statusCode: 500, // HTTP Status Code
          isSuccess: false, // Thành công hay thất bại
          body: dataSendPost,
          errorMessage: error,
        });
      }

      return data;
    } catch (error) {
      return;
    }
  }

  private createPoItemSapDto(
    item: SapPurchaseOrderItemModel,
  ): CreatePoItemSapDto {
    if (!item?.quantity || Number(item.quantity) <= 0)
      return {} as CreatePoItemSapDto;
    return {
      eprid: item.eprId,
      matnr: item.materialCode,
      shtex: item.materialName,
      menge: item.quantity,
      meins: item.unit,
      eindt: moment(item.deliveryDate).format('YYYYMMDD'),
      netpr: item.price,
      knttp:
        item.accountAssignment !== EAccountAssignment.NULL
          ? item.accountAssignment
          : null,
      saknr: item.glAccount,
      kostl: item.costCenterCode,
      anln1: item.asset,
      aufnr: item.internalOrder,
      wbsel: item.wbs,
      fkber: item.functionalArea || '*********',
      matkl: item.materialGroupCode,
      waers: item.currencyCode,
      mwskz: item.taxCodeCode,
      werks: item.buCode,
    };
  }
  // For Actual Spending
  async getListSapPurchaseOrderByIds(
    ids: number[],
  ): Promise<SapPurchaseOrderModel[]> {
    return (
      (await this.sapPurchaseOrderRepository.getListSapPurchaseOrderByIds(
        ids,
      )) || []
    );
  }

  async cancelOrDeletePurchaseOrderSap(
    poId: number,
    sapPurchaseOrders: SapPurchaseOrderModel[],
    jwtPayload: any,
  ): Promise<any> {
    try {
      const po = await this._purchaseOrderUsecases.findOne(poId);

      const syncPoList: CancelPoSapDto[] = (sapPurchaseOrders || []).map(
        (sapPurchaseOrder) => {
          return { sepid: sapPurchaseOrder.id };
        },
      );

      if (
        po.sector?.code == 'FEED' &&
        po.businessUnit?.company?.code == '1000'
      ) {
        const response = await sendPost(
          SapApiUrlsConst.DELETE_PO(),
          {
            eprno: poId,
            crnam:
              `${(jwtPayload as any)?.lastName || ''} ${(jwtPayload as any)?.firstName || ''}`.trim(),
            crdat: moment().format('YYYY-MM-DD'),
            crtim: moment().format('HH:mm:ss'),
            listpo: syncPoList,
          },
          {},
          {
            username: config.getSapBasicUsername(),
            password: config.getSapBasicPassword(),
          },
        );

        return response?.data;
      }

      if (
        po.sector?.code == 'FOOD' &&
        (po.businessUnit?.company?.code == '3000' ||
          po.businessUnit?.company?.code == '3100')
      ) {
        const response = await sendPost(
          SapApiUrlsFoodConst.DELETE_PO(),
          {
            eprno: poId,
            crnam:
              `${(jwtPayload as any)?.lastName || ''} ${(jwtPayload as any)?.firstName || ''}`.trim(),
            crdat: moment().format('YYYY-MM-DD'),
            crtim: moment().format('HH:mm:ss'),
            listpo: syncPoList,
          },
          {},
          {
            username: config.getSapBasicUsernameFood(),
            password: config.getSapBasicPasswordFood(),
          },
        );

        return response?.data;
      }
    } catch (error) {
      return;
    }
  }

  async cancelSapPurchaseOrder(
    po: PurchaseOrderEntity,
    jwtPayload: any,
  ): Promise<SapPurchaseOrderModel[] | any[]> {
    if (!po || !po.sapPos?.length) {
      return;
    }

    const sapPurchaseOrders =
      po.sapPos?.filter(
        (sapPo) =>
          sapPo.messageType !== 'E' &&
          !['DELETE', 'CLOSED', 'SYNC_FAILED'].includes(sapPo.status),
      ) || [];

    const responseSapPo = await this.cancelOrDeletePurchaseOrderSap(
      po.id,
      sapPurchaseOrders,
      jwtPayload,
    );

    const errors = this.processSapPurchaseOrders(
      po,
      sapPurchaseOrders,
      responseSapPo,
      true,
    );

    if (errors.length) {
      throw new HttpException(errors, HttpStatus.BAD_REQUEST);
    }

    return await this.sapPurchaseOrderRepository.updateSapPurchaseOrders(
      sapPurchaseOrders,
    );
  }
}
