import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { GetActualSpendingListDto } from '../controller/actual-spending/dtos/get-list-actual-spending.dto';
import { GetDetailBudgetCodeDto } from '../controller/budget-code/dtos/get-detail-budget-code.dto';
import { CreateBudgetCapexDto } from '../controller/budget/dtos/create-budget-capex.dto';
import { CreateBudgetOpexDto } from '../controller/budget/dtos/create-budget-opex.dto';
import { CreateBudgetDto } from '../controller/budget/dtos/create-budget.dto';
import { GetBudgetListDto } from '../controller/budget/dtos/get-budget-list.dto';
import { GetDetailBudgetDto } from '../controller/budget/dtos/get-detail-budget.dto';
import {
  HandleCopyExcelListDto,
  IResponseHandleCopyExcel,
} from '../controller/budget/dtos/handle-copy-excel.dto';
import { LockUnlockBudgetDto } from '../controller/budget/dtos/lock-unlock-budget.dto';
import { ReportBudgetDto } from '../controller/budget/dtos/report-budget.dto';
import { UpdateBudgetCapexDto } from '../controller/budget/dtos/update-budget-capex.dto';
import { UpdateBudgetOpexDto } from '../controller/budget/dtos/update-budget-opex.dto';
import { UpdateBudgetDto } from '../controller/budget/dtos/update-budget.dto';
import { GetDetailCostcenterSubaccountDto } from '../controller/costcenter-subaccount/dtos/get-detail-costcenter-subaccount.dto';
import { GetDetailCurrencyUnitDto } from '../controller/currency-unit/dtos/get-detail-currency-unit.dto';
import { ImportCapexDto } from '../controller/import/dtos/import-capex.dto';
import { ImportOpexDto } from '../controller/import/dtos/import-opex.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import { EBudgetCodeStatus } from '../domain/config/enums/budget-code.enum';
import {
  EBudgetCreateType,
  EBudgetStatus,
  EBudgetType,
  EColumnImportAdjustBudget,
  EColumnImportBudgetCapex,
  EColumnImportBudgetOpex,
  EColumnImportBudgetTransfer,
  EFilterCurrencyReportBudget,
} from '../domain/config/enums/budget.enum';
import { ECodeType } from '../domain/config/enums/code-type.enum';
import { ECostcenterSubaccountStatus } from '../domain/config/enums/costcenter-subaccount.enum';
import { ECurrencyUnitStatus } from '../domain/config/enums/currency-unit.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import {
  IImportBudget,
  IReponseReportBudgetDetail,
  IReponseReportBudgetOverview,
} from '../domain/interface/budget.interface';
import { budgetErrorDetails } from '../domain/messages/error-details/budget';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  TErrorMessageImport,
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { BudgetCodeModel } from '../domain/model/budget-code.model';
import { BudgetInvestmentModel } from '../domain/model/budget-investment.model';
import { BudgetModel } from '../domain/model/budget.model';
import { CostcenterSubaccountModel } from '../domain/model/costcenter-subaccount.model';
import { CurrencyUnitModel } from '../domain/model/currency-unit.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { IActualSpendingRepository } from '../domain/repositories/actual-spending.repository';
import { IBudgetCodeRepository } from '../domain/repositories/budget-code.repository';
import { IBudgetRepository } from '../domain/repositories/budget.repository';
import { ICostcenterSubaccountRepository } from '../domain/repositories/costcenter-subaccount.repository';
import { ICurrencyUnitRepository } from '../domain/repositories/currency-unit.repository';
import { IMaterialGroupRepository } from '../domain/repositories/material-group.repository';
import { IMaterialRepository } from '../domain/repositories/material.repository';
import { ISupplierRepository } from '../domain/repositories/supplier.repository';
import {
  checkDuplicateBudget,
  checkEffectiveTimeOverlaps,
  excelSerialToDate,
  getBudgetCreateType,
  getFirstAndLastDate,
  getIsLockBudget,
  getValueOrResult,
  hasDuplicateObjects,
  mergedErrors,
  removeUnicode,
} from '../utils/common';

import { GetCurrencyUnitListDto } from '../controller/currency-unit/dtos/get-currency-unit-list.dto';
import { EStatusActualSpending } from '../domain/config/enums/actual-spending.enum';
import { Status } from '../domain/config/enums/purchase-order.enum';
import { IMeasureRepository } from '../domain/repositories/measure.repository';
import { BudgetCapexUsecases } from './budget-capex.usecases';
import { BudgetCodeUsecases } from './budget-code.usecases';
import { BudgetInvestmentUsecases } from './budget-investment.usecases';
import { BudgetOpexUsecases } from './budget-opex.usecases';
import { CostcenterSubaccountUsecases } from './costcenter-subaccount.usecases';
import { CurrencyUnitUsecases } from './currency-unit.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { IncreasementCodeUsecases } from './increasement-code.usecases';
import { IPurchaseRequestRepository } from '../domain/repositories/purchaseRequestRepository.repository';
import { IPurchaseOrderRepository } from '../domain/repositories/purchaseOrderRepository.repository';
import { ITaxCodeRepository } from '../domain/repositories/tax-code.repository';
import { IWarehouseRepository } from '../domain/repositories/warehouse.repository';

@Injectable()
export class BudgetUsecases {
  constructor(
    @Inject(IBudgetRepository)
    private readonly budgetRepository: IBudgetRepository,
    @Inject(ICurrencyUnitRepository)
    private readonly currencyUnitRepository: ICurrencyUnitRepository,
    @Inject(IBudgetCodeRepository)
    private readonly budgetCodeRepository: IBudgetCodeRepository,
    @Inject(ICostcenterSubaccountRepository)
    private readonly costcenterSubaccountRepository: ICostcenterSubaccountRepository,
    @Inject(IMaterialRepository)
    private readonly materialRepository: IMaterialRepository,
    @Inject(IMaterialGroupRepository)
    private readonly materialGroupRepository: IMaterialGroupRepository,
    @Inject(ISupplierRepository)
    private readonly supplierRepository: ISupplierRepository,
    @Inject(IActualSpendingRepository)
    private readonly actualSpendingRepository: IActualSpendingRepository,
    @Inject(IMeasureRepository)
    private readonly measureRepository: IMeasureRepository,
    @Inject(ITaxCodeRepository)
    private readonly taxCodeRepository: ITaxCodeRepository,
    @Inject(IWarehouseRepository)
    private readonly warehouseRepository: IWarehouseRepository,
    private readonly increasementCodeUsecases: IncreasementCodeUsecases,
    private readonly budgetOpexUsecases: BudgetOpexUsecases,
    private readonly budgetCapexUsecases: BudgetCapexUsecases,
    private readonly currencyUnitUsecases: CurrencyUnitUsecases,
    private readonly budgetCodeUsecases: BudgetCodeUsecases,
    private readonly costcenterSubaccountUsecases: CostcenterSubaccountUsecases,
    private readonly budgetInvestmentUsecases: BudgetInvestmentUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private readonly fileUsecases: FileUsecases,
    @Inject('IPurchaseRequestRepository')
    private readonly purchaseRequestRepository: IPurchaseRequestRepository,
    @Inject('IPurchaseOrderRepository')
    private readonly purchaseOrderRepository: IPurchaseOrderRepository,
  ) {}

  async getBudgetList(
    conditions: GetBudgetListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<BudgetModel>> {
    return await this.budgetRepository.getBudgetList(conditions, jwtPayload);
  }

  async getBudgetById(id: string): Promise<BudgetModel> {
    const detail = await this.budgetRepository.getBudgetById(id);
    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1006()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async createBudgetOpex(
    data: CreateBudgetOpexDto,
    jwtPayload: any,
    isImport: boolean = false,
  ): Promise<BudgetModel> {
    data.budgetData.budgetType = EBudgetType.OPEX;

    const budgetData = await this.verifyBudgetDto(
      data.budgetData,
      false,
      jwtPayload,
      isImport,
    );
    if (data.budgetData.createType == EBudgetCreateType.NEW) {
      const checkDuplicateBudgets =
        await this.budgetRepository.checkDuplicateBudget(budgetData);

      const findDup = checkDuplicateBudgets.find(
        (item) =>
          (item.budgetOpex?.operations || null) == (data.operations || null),
      );

      if (findDup) {
        throw new HttpException(
          getErrorMessage(budgetErrorDetails.E_6002()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const budgetOpex = await this.budgetOpexUsecases.createBudgetOpex(data);

    budgetData.budgetOpexId = budgetOpex.id;
    budgetData.searchValue = removeUnicode(`${budgetData.code}`);

    const budget = await this.budgetRepository.createBudget(budgetData);

    const detail = await this.getBudgetById(budget.id);

    return detail;
  }

  async createBudgetCapex(
    data: CreateBudgetCapexDto,
    jwtPayload: any,
    isImport: boolean = false,
  ): Promise<BudgetModel> {
    data.budgetData.budgetType = EBudgetType.CAPEX;
    data.startDate = data.startDate ? new Date(data.startDate) : null;

    const budgetData = await this.verifyBudgetDto(
      data.budgetData,
      false,
      jwtPayload,
      isImport,
    );

    if (data.budgetData.createType == EBudgetCreateType.NEW) {
      const checkDuplicateBudgets =
        await this.budgetRepository.checkDuplicateBudget(budgetData);

      const dupBudgets: BudgetModel[] = [];

      for (let i = 0; i < (data.investments || []).length; i++) {
        for (let y = 0; y < checkDuplicateBudgets.length; y++) {
          const investments =
            checkDuplicateBudgets[y]?.budgetCapex?.budgetInvestments || [];

          const dupInvestments = investments.find(
            (item) =>
              item.investment == data.investments[i].investment &&
              item.quantity == data.investments[i].quantity &&
              (item.price || null) == (data.investments[i].price || null) &&
              (item.transportationCosts || null) ==
                (data.investments[i].transportationCosts || null),
          );

          if (dupInvestments) {
            dupBudgets.push(checkDuplicateBudgets[y]);
            continue;
          }
        }
      }

      if (dupBudgets?.length) {
        throw new HttpException(
          getErrorMessage(budgetErrorDetails.E_6002()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const budgetCapex = await this.budgetCapexUsecases.createBudgetCapex(data);

    const investments: BudgetInvestmentModel[] = [];

    for (const investmentData of data.investments) {
      investments.push(
        new BudgetInvestmentModel({
          ...investmentData,
          budgetCapexId: budgetCapex.id,
        }),
      );
    }

    if (hasDuplicateObjects(investments)) {
      throw new HttpException(
        budgetErrorDetails.E_6000(),
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.budgetInvestmentUsecases.createManyBudgetInvestment(investments);

    budgetData.budgetCapexId = budgetCapex.id;
    budgetData.searchValue = (
      removeUnicode(`${data.classify || ''}`) +
      ' ' +
      removeUnicode(`${budgetData.code}`)
    ).trim();

    const budget = await this.budgetRepository.createBudget(budgetData);

    const detail = await this.getBudgetById(budget.id);

    return detail;
  }

  async updateBudgetOpex(
    id: string,
    data: UpdateBudgetOpexDto,
    jwtPayload: any,
  ): Promise<BudgetModel> {
    const budgetData = await this.verifyBudgetDto(
      data.budgetData,
      true,
      jwtPayload,
      false,
      id,
    );

    if (data.budgetData.createType == EBudgetCreateType.NEW) {
      const checkDuplicateBudgets =
        await this.budgetRepository.checkDuplicateBudget(budgetData);

      const findDup = checkDuplicateBudgets.find(
        (item) =>
          (item.budgetOpex?.operations || null) == (data.operations || null) &&
          item.id !== id,
      );

      if (findDup && findDup.id !== id) {
        throw new HttpException(
          getErrorMessage(budgetErrorDetails.E_6002()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    await this.budgetOpexUsecases.updateBudgetOpex(id, {
      ...data,
      operations: data.operations || null,
    });

    budgetData.budgetOpexId = id;
    budgetData.searchValue = removeUnicode(`${budgetData.code}`);
    budgetData.note = budgetData.note || null;

    const budget = await this.budgetRepository.updateBudget(id, budgetData);

    const detail = await this.getBudgetById(budget.id);
    return detail;
  }

  async updateBudgetCapex(
    id: string,
    data: UpdateBudgetCapexDto,
    jwtPayload: any,
  ): Promise<BudgetModel> {
    data.startDate = data.startDate ? new Date(data.startDate) : null;

    const budgetData = await this.verifyBudgetDto(
      data.budgetData,
      true,
      jwtPayload,
      false,
      id,
    );

    if (data.budgetData.createType == EBudgetCreateType.NEW) {
      const checkDuplicateBudgets =
        await this.budgetRepository.checkDuplicateBudget(budgetData);

      const dupBudgets: BudgetModel[] = [];

      for (let i = 0; i < (data.investments || []).length; i++) {
        for (let y = 0; y < checkDuplicateBudgets.length; y++) {
          const investments =
            checkDuplicateBudgets[y]?.budgetCapex?.budgetInvestments || [];

          const dupInvestments = investments.find(
            (item) =>
              item.investment == data.investments[i].investment &&
              item.quantity == data.investments[i].quantity &&
              item.price == data.investments[i].price &&
              (item.transportationCosts || null) ==
                (data.investments[i].transportationCosts || null) &&
              checkDuplicateBudgets[y].id !== id,
          );

          if (dupInvestments) {
            dupBudgets.push(checkDuplicateBudgets[y]);
            continue;
          }
        }
      }

      if (dupBudgets.length) {
        throw new HttpException(
          getErrorMessage(budgetErrorDetails.E_6002()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    await this.budgetCapexUsecases.updateBudgetCapex(id, {
      ...data,
      useTime: data.useTime || null,
      expectedAcceptanceTime: data.expectedAcceptanceTime || null,
      priority: data.priority || null,
      investmentPurpose: data.investmentPurpose || null,
    });

    const createInvestments: BudgetInvestmentModel[] = [];
    const updateInvestments: BudgetInvestmentModel[] = [];
    for (const investmentData of data.investments) {
      if (investmentData.id) {
        updateInvestments.push(
          new BudgetInvestmentModel({
            id: investmentData.id,
            ...investmentData,
            budgetCapexId: id,
          }),
        );
      } else {
        createInvestments.push(
          new BudgetInvestmentModel({
            ...investmentData,
            budgetCapexId: id,
          }),
        );
      }
    }

    if (
      hasDuplicateObjects(
        [...createInvestments, ...updateInvestments].map((item) => {
          return { ...item, id: undefined };
        }),
      )
    ) {
      throw new HttpException(
        budgetErrorDetails.E_6000(),
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.budgetInvestmentUsecases.deleteBudgetInvestmentsNotIn(
      updateInvestments.map((item) => item.id),
      id,
    );
    await this.budgetInvestmentUsecases.createManyBudgetInvestment(
      createInvestments,
    );
    await this.budgetInvestmentUsecases.updateManyBudgetInvestment(
      updateInvestments,
    );

    budgetData.budgetCapexId = id;
    budgetData.searchValue =
      removeUnicode(`${data.classify}`) +
      ' ' +
      removeUnicode(`${budgetData.code}`);
    budgetData.note = budgetData.note || null;

    const budget = await this.budgetRepository.updateBudget(id, budgetData);

    const detail = await this.getBudgetById(budget.id);
    return detail;
  }

  async lockUnLockBudget(
    conditions: LockUnlockBudgetDto,
    jwtPayload: any,
    authorization: string,
  ) {
    const setBudgetIds = [...new Set(conditions.budgetIds)];

    const budgets = await this.budgetRepository.getBudgetByIds(
      setBudgetIds,
      jwtPayload,
    );

    const budgetIds = (budgets ?? []).map((item) => item.id);

    const diffBusinessOwnerIds = _.difference(setBudgetIds, budgetIds);

    if (diffBusinessOwnerIds.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1006()),
        HttpStatus.NOT_FOUND,
      );
    }

    const bugdetOpexes = budgets.filter(
      (item) => item.budgetType == EBudgetType.OPEX,
    );
    const bugdetCapexes = budgets.filter(
      (item) => item.budgetType == EBudgetType.CAPEX,
    );

    if (bugdetOpexes.length && bugdetCapexes.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1008()),
        HttpStatus.NOT_FOUND,
      );
    }

    if (conditions.isLock) {
      const budgetLocked = budgets.filter((item) => item.isLock == true);
      if (budgetLocked.length) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1007()),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.budgetRepository.lockBudget(setBudgetIds);
    } else {
      const budgetUnLocked = budgets.filter((item) => item.isLock == false);
      if (budgetUnLocked.length) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_10081()),
          HttpStatus.BAD_REQUEST,
        );
      }

      const [prs, pos] = await Promise.all([
        this.purchaseRequestRepository.findPRWithBudget({
          budget_codes: (budgets ?? []).map((item) => item.budgetCodeId),
        }),
        this.purchaseOrderRepository.findPOWithBudget({
          budgetCodeIds: (budgets ?? []).map((item) => item.budgetCodeId),
        }),
      ]);

      if (prs?.length || pos?.length) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1082()),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.budgetRepository.unlockBudget(setBudgetIds);
    }
  }

  private async verifyBudgetDto(
    dto: CreateBudgetDto | UpdateBudgetDto,
    isUpdate: boolean,
    jwtPayload: any,
    isImport: boolean = false,
    id?: string,
  ): Promise<BudgetModel> {
    //Chỉ sử dụng adjustBudgetId khi loại khởi tạo != NEW
    if (dto.createType == EBudgetCreateType.NEW) {
      delete dto.adjustBudgetId;
    }

    //Kiểm tra effectiveStartDate < effectiveEndDate
    const effectiveStartDate = new Date(dto.effectiveStartDate);
    const effectiveEndDate = new Date(dto.effectiveEndDate);

    const currentDate = new Date();
    const currentSubDays = new Date(currentDate);
    currentSubDays.setDate(currentDate.getDate() - 1);

    if (!isImport) {
      let budgetCheck: BudgetModel;
      if (id) {
        budgetCheck = await this.budgetRepository.getBudgetDetail(
          plainToInstance(GetDetailBudgetDto, {
            id: id,
          }),
          jwtPayload,
        );

        if (!budgetCheck) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1006()),
            HttpStatus.NOT_FOUND,
          );
        }

        if (budgetCheck.budgetType != dto.budgetType) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1008()),
            HttpStatus.NOT_FOUND,
          );
        }

        // if (budgetCheck.isLock) {
        //   throw new HttpException(
        //     getErrorMessage(errorMessage.E_1007()),
        //     HttpStatus.BAD_REQUEST,
        //   );
        // }

        dto.createType = budgetCheck.createType;
        dto.budgetType = budgetCheck.budgetType;
        dto.code = budgetCheck.code;
      }

      //Kiểm tra addjustBudgetId có hợp lệ
      if (
        dto.adjustBudgetId &&
        budgetCheck?.adjustBudgetId !== dto.adjustBudgetId
      ) {
        const adjustBudget = await this.budgetRepository.getBudgetDetail(
          plainToInstance(GetDetailBudgetDto, {
            ...dto,
            id: dto.adjustBudgetId,
          }),
          jwtPayload,
        );

        if (!adjustBudget) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1009()),
            HttpStatus.NOT_FOUND,
          );
        }

        if (adjustBudget.createType != EBudgetCreateType.NEW) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1010()),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (adjustBudget.budgetType != dto.budgetType) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1011()),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!adjustBudget.isLock) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1079()),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (
          currentDate < new Date(adjustBudget.effectiveStartDate) ||
          currentSubDays > new Date(adjustBudget.effectiveEndDate)
        ) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1012()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      if (budgetCheck?.currencyUnitId != dto.currencyUnitId) {
        //Kiểm tra curencyUnitId có hợp lệ
        const currencyUnit =
          await this.currencyUnitUsecases.getCurrencyUnitDetail(
            plainToInstance(GetDetailCurrencyUnitDto, {
              id: dto.currencyUnitId,
            }),
            jwtPayload,
          );

        if (currencyUnit.status == ECurrencyUnitStatus.IN_ACTIVE) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1014()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      if (budgetCheck?.budgetCodeId != dto.budgetCodeId) {
        const budgetCode = await this.budgetCodeUsecases.getBudgetCodeDetail(
          plainToInstance(GetDetailBudgetCodeDto, {
            ...dto,
            id: dto.budgetCodeId,
          }),
          jwtPayload,
        );

        if (!budgetCode) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1015()),
            HttpStatus.NOT_FOUND,
          );
        }

        if (budgetCode.status == EBudgetCodeStatus.IN_ACTIVE) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1016()),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (budgetCode.budgetType != dto.budgetType) {
          throw new HttpException(
            getErrorMessage(
              budgetErrorDetails.E_6003(
                `Type of budget code is ${budgetCode.budgetType} and not ${dto.budgetType}`,
              ),
            ),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      if (budgetCheck?.costcenterSubaccountId != dto.costcenterSubaccountId) {
        //Kiểm tra costcenterSubaccountId có hợp lệ
        const costcenterSubaccount =
          await this.costcenterSubaccountUsecases.getCostcenterSubaccountDetail(
            plainToInstance(GetDetailCostcenterSubaccountDto, {
              ...dto,
              id: dto.costcenterSubaccountId,
            }),
            jwtPayload,
          );

        if (!costcenterSubaccount) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1005()),
            HttpStatus.NOT_FOUND,
          );
        }

        if (
          costcenterSubaccount.status == ECostcenterSubaccountStatus.IN_ACTIVE
        ) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1018()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      if (effectiveStartDate > effectiveEndDate) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1019()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    dto.effectiveStartDate = effectiveStartDate;
    dto.effectiveEndDate = effectiveEndDate;

    if (!isUpdate) {
      //Generate mã code cho ngân sách
      let code = '';
      while (true) {
        code = await this.increasementCodeUsecases.generateIncreasementCode(
          (dto as CreateBudgetDto).budgetType == EBudgetType.OPEX
            ? ECodeType.BUDGET_OPEX
            : ECodeType.BUDGET_CAPEX,
        );

        //Kiểm tra xem code đã tồn tại hay chưa
        const budget = await this.budgetRepository.getBudgetByCode(code);
        if (!budget) {
          break;
        }
      }

      return new BudgetModel({
        ...dto,
        code: code,
      });
    } else {
      return new BudgetModel({
        ...dto,
      });
    }
  }

  async importBudgetOpex(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.OPEX,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createBudgetOpexDtos: CreateBudgetOpexDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0].actualRowCount,
          ) ?? [];

        // const adjustBudgetCodes = [
        //   ...new Set(
        //     rows
        //       .map((item) =>
        //         getValueOrResult(
        //           item,
        //           EColumnImportBudgetOpex.ADJUST_BUDGET_CODE,
        //         )?.toString(),
        //       )
        //       ?.filter(Boolean),
        //   ),
        // ];

        const currencyUnitCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetOpex.CURRENCY_UNIT_CODE,
                )?.toString(),
              )
              ?.filter(Boolean),
          ),
        ];
        const budgetCodeCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetOpex.BUDGET_CODE_CODE,
                )?.toString(),
              )
              ?.filter(Boolean),
          ),
        ];
        const costcenterSubaccountCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetOpex.COSTCENTER_SUBACCOUNT_CODE,
                )?.toString(),
              )
              ?.filter(Boolean),
          ),
        ];

        const [
          // adjustBudgets,
          currencyUnits,
          budgetCodes,
          costcenterSubaccounts,
        ] = await Promise.all([
          // this.budgetRepository.getBudgetsByCodesWithRole(
          //   adjustBudgetCodes,
          //   jwtPayload,
          //   EBudgetType.OPEX,
          // ),
          this.currencyUnitRepository.getCurrencyUnitsByCodesWithRole(
            currencyUnitCodes,
            jwtPayload,
          ),
          this.budgetCodeRepository.getBudgetCodesByCodesWithRole(
            budgetCodeCodes,
            jwtPayload,
          ),
          this.costcenterSubaccountRepository.getCostcenterSubaccountsByCodesWithRole(
            costcenterSubaccountCodes,
            jwtPayload,
          ),
        ]);

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          //Thông tin chung của ngân sách
          // const createType = getValueOrResult(
          //   row,
          //   EColumnImportBudgetOpex.CREATE_TYPE,
          // )?.toString(); //Loại khởi tạo
          // const adjustBudgetCode = getValueOrResult(
          //   row,
          //   EColumnImportBudgetOpex.ADJUST_BUDGET_CODE,
          // )?.toString(); //Ngân sách điều chỉnh
          const budgetType = EBudgetType.OPEX; //Loại ngân sách
          const currencyUnitCode = getValueOrResult(
            row,
            EColumnImportBudgetOpex.CURRENCY_UNIT_CODE,
          )?.toString(); //Đơn vị tiền tệ
          const budgetCodeCode = getValueOrResult(
            row,
            EColumnImportBudgetOpex.BUDGET_CODE_CODE,
          )?.toString(); //Code ngân sách
          const costcenterSubaccountCode = getValueOrResult(
            row,
            EColumnImportBudgetOpex.COSTCENTER_SUBACCOUNT_CODE,
          )?.toString(); //Costcenter/Subaccount
          const note = getValueOrResult(
            row,
            EColumnImportBudgetOpex.NOTE,
          )?.toString(); //Ghi chú
          const effectiveStartDate = row.getCell(
            EColumnImportBudgetOpex.EFFECTIVE_START_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportBudgetOpex.EFFECTIVE_START_DATE)
                    .value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian bắt đầu
          const effectiveEndDate = row.getCell(
            EColumnImportBudgetOpex.EFFECTIVE_END_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportBudgetOpex.EFFECTIVE_END_DATE)
                    .value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian kết thúc
          const totalValue = getValueOrResult(
            row,
            EColumnImportBudgetOpex.TOTAL_VALUE,
            true,
          ); //Số tiền (nguyên tệ)

          //Opex
          // const level = getValueOrResult(
          //   row,
          //   EColumnImportBudgetOpex.LEVEL,
          // )?.toString(); //Mức
          const form = getValueOrResult(
            row,
            EColumnImportBudgetOpex.FORM,
          )?.toString(); //Biểu mẫu
          const operations = getValueOrResult(
            row,
            EColumnImportBudgetOpex.OPERATIONS,
          )?.toString(); //Diễn giải chi phí/nghiệp vụ
          const isLock = getIsLockBudget(
            getValueOrResult(row, EColumnImportBudgetOpex.IS_LOCK)?.toString(),
          ); //Chưa công bố / Đã công bố

          ///Kiểm tra format ngày
          if (effectiveStartDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(errorMessage.E_1075()),
              row: i + 1,
            });
          }

          if (effectiveEndDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(errorMessage.E_1076()),
              row: i + 1,
            });
          }

          ///Verify opex data
          // if (!level) {
          //   errors.push({
          //     error: getErrorMessage(errorMessage.E_1063()),
          //     row: i + 1,
          //   });
          // }

          const verifyDataData = await this.verifyBudgetDataImport(
            {
              createType: EBudgetCreateType.NEW,
              budgetType,
              currencyUnitCode,
              budgetCodeCode,
              costcenterSubaccountCode,
              // adjustBudgetCode:
              //   createType != EBudgetCreateType.NEW ? adjustBudgetCode : null,
              note,
              effectiveStartDate,
              effectiveEndDate,
              status: EBudgetStatus.ACTIVE,
              totalValue,
            },
            i + 1,
            // adjustBudgets,
            currencyUnits,
            budgetCodes,
            costcenterSubaccounts,
          );

          errors.push(...verifyDataData.errors);

          const budgetOpexObject = {
            // level,
            form,
            operations,
            budgetData: {
              // createType,
              budgetType,
              ...verifyDataData.budgetDataVerify,
              note,
              effectiveStartDate,
              effectiveEndDate,
              status: EBudgetStatus.ACTIVE,
              totalValue,
              isLock,
              createdAt: moment()
                .add(i * 500, 'milliseconds')
                .toISOString(),
              updatedAt: moment()
                .add(i * 500, 'milliseconds')
                .toISOString(),
              rowNumber: i + 1,
            },
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };

          const budgetOpexDto = plainToInstance(
            CreateBudgetOpexDto,
            budgetOpexObject,
          );

          const checkDuplicateBudgets =
            await this.budgetRepository.checkDuplicateBudget(
              new BudgetModel(budgetOpexDto.budgetData),
            );

          const findDup = checkDuplicateBudgets.find(
            (item) =>
              (item.budgetOpex?.operations || null) ==
              (budgetOpexDto.operations || null),
          );

          if (findDup) {
            errors.push({
              error: budgetErrorDetails.E_6002(),
              row: i + 1,
            });
          }

          createBudgetOpexDtos.push(budgetOpexDto);
        }

        const findDuplicateBudgets: CreateBudgetOpexDto[] =
          checkDuplicateBudget(createBudgetOpexDtos, EBudgetType.OPEX);

        if (findDuplicateBudgets?.length) {
          for (let i = 0; i < findDuplicateBudgets.length || 0; i++) {
            errors.push({
              error: budgetErrorDetails.E_6002(),
              row: findDuplicateBudgets[i]?.budgetData?.rowNumber || 0,
            });
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportOpexDto = {
          dataOpexes: createBudgetOpexDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        // await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
        //   importBody: importBody,
        //   importHeader: {
        //     authorization,
        //     'x-api-key': process.env.API_KEY,
        //   },
        //   importUrl: PurchaseServiceApiUrlsConst.IMPORT_OPEX(),
        //   updateStatusFileUrl:
        //     PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
        //       fileImportHistory.id,
        //     ),
        // });
        await this.import(importBody, EFileImportType.OPEX);

        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.SUCCESS,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async importBudgetCapex(
    file: Express.Multer.File,
    files: Express.Multer.File[],
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    // ///Upload files attachment
    // let filesUploaded = [];
    // if (files?.length) {
    //   filesUploaded = await this.fileUsecases.uploadFiles(files, null);
    //
    // }

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.CAPEX,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createBudgetCapexDtos: CreateBudgetCapexDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0].actualRowCount,
          ) ?? [];

        // const filesAttachment = [
        //   ...new Set(
        //     _.flatten(
        //       rows
        //         .map((item) =>
        //           getValueOrResult(item, EColumnImportBudgetCapex.FILE_ATTACH)
        //             ?.toString()
        //             ?.split(',')
        //             ?.map((item) => item.trim()),
        //         )
        //         ?.slice(1)
        //         ?.filter(Boolean),
        //     ),
        //   ),
        // ];
        //

        // const adjustBudgetCodes = [
        //   ...new Set(
        //     rows
        //       .map((item) =>
        //         getValueOrResult(
        //           item,
        //           EColumnImportBudgetCapex.ADJUST_BUDGET_CODE,
        //         )?.toString(),
        //       )
        //       ?.filter(Boolean),
        //   ),
        // ];
        const currencyUnitCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetCapex.CURRENCY_UNIT_CODE,
                )?.toString(),
              )
              ?.filter(Boolean),
          ),
        ];
        const budgetCodeCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetCapex.BUDGET_CODE_CODE,
                )?.toString(),
              )
              ?.filter(Boolean),
          ),
        ];
        const costcenterSubaccountCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetCapex.COSTCENTER_SUBACCOUNT_CODE,
                )?.toString(),
              )
              ?.filter(Boolean),
          ),
        ];

        const [currencyUnits, budgetCodes, costcenterSubaccounts] =
          await Promise.all([
            this.currencyUnitRepository.getCurrencyUnitsByCodesWithRole(
              currencyUnitCodes,
              jwtPayload,
            ),
            this.budgetCodeRepository.getBudgetCodesByCodesWithRole(
              budgetCodeCodes,
              jwtPayload,
            ),
            this.costcenterSubaccountRepository.getCostcenterSubaccountsByCodesWithRole(
              costcenterSubaccountCodes,
              jwtPayload,
            ),
          ]);

        for (const worksheet of workbook.worksheets) {
          const dataCapex = [];
          const rows = worksheet.getRows(1, worksheet.actualRowCount);

          for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            //Thông tin chung của ngân sách
            const budgetType = EBudgetType.CAPEX; //Loại ngân sách
            const currencyUnitCode = getValueOrResult(
              row,
              EColumnImportBudgetCapex.CURRENCY_UNIT_CODE,
            )?.toString(); //Đơn vị tiền tệ
            const costcenterSubaccountCode = getValueOrResult(
              row,
              EColumnImportBudgetCapex.COSTCENTER_SUBACCOUNT_CODE,
            )?.toString(); //Costcenter/Subaccount
            const totalValue = getValueOrResult(
              row,
              EColumnImportBudgetCapex.TOTAL_VALUE,
              true,
            ); //Số tiền (nguyên tệ)

            const startDate = row.getCell(EColumnImportBudgetCapex.START_DATE)
              .value
              ? moment(
                  excelSerialToDate(
                    row.getCell(EColumnImportBudgetCapex.START_DATE).value as
                      | string
                      | number,
                  ),
                ).format('YYYY-MM-DD')
              : null; //Thời gian bắt đầu
            const expectedAcceptanceTime = row.getCell(
              EColumnImportBudgetCapex.EXPECTED_ACCEPTANCE_TIME,
            ).value
              ? moment(
                  excelSerialToDate(
                    row.getCell(
                      EColumnImportBudgetCapex.EXPECTED_ACCEPTANCE_TIME,
                    ).value as string | number,
                  ),
                ).format('YYYY-MM-DD')
              : null; //Thời điểm nghiệm thu dự kiến
            const note = getValueOrResult(
              row,
              EColumnImportBudgetCapex.NOTE,
            )?.toString(); //Ghi chú
            const budgetCodeCode = getValueOrResult(
              row,
              EColumnImportBudgetCapex.BUDGET_CODE_CODE,
            )?.toString(); //Code ngân sách

            //Capex
            const classify = getValueOrResult(
              row,
              EColumnImportBudgetCapex.CLASSIFY,
            )?.toString(); //Phân loại (Type)
            //Investment
            const investment = getValueOrResult(
              row,
              EColumnImportBudgetCapex.INVESTMENT,
            )?.toString(); //Hạng mục đầu tư (Items)
            const quantity = getValueOrResult(
              row,
              EColumnImportBudgetCapex.QUANTITY,
              true,
            ); //Số lượng
            const price = getValueOrResult(
              row,
              EColumnImportBudgetCapex.PRICE,
              true,
            ); //Giá
            const transportationCosts = getValueOrResult(
              row,
              EColumnImportBudgetCapex.TRANSPORTATION_COSTS,
              true,
            ); //Tổng CP vận chuyển
            const useTime = getValueOrResult(
              row,
              EColumnImportBudgetCapex.USE_TIME,
            )?.toString(); //Thời gian sử dụng
            const priority = getValueOrResult(
              row,
              EColumnImportBudgetCapex.PRIORITY,
            )?.toString(); //Mức độ ưu tiên;
            const investmentPurpose = getValueOrResult(
              row,
              EColumnImportBudgetCapex.INVESTMENT_PURPOSE,
            )?.toString(); //Mục đích đầu tư
            const effectiveStartDate = row.getCell(
              EColumnImportBudgetCapex.EFFECTIVE_START_DATE,
            ).value
              ? moment(
                  excelSerialToDate(
                    row.getCell(EColumnImportBudgetCapex.EFFECTIVE_START_DATE)
                      .value as string | number,
                  ),
                ).format('YYYY-MM-DD')
              : null; //Thời gian bắt đầu
            const effectiveEndDate = row.getCell(
              EColumnImportBudgetCapex.EFFECTIVE_END_DATE,
            ).value
              ? moment(
                  excelSerialToDate(
                    row.getCell(EColumnImportBudgetCapex.EFFECTIVE_END_DATE)
                      .value as string | number,
                  ),
                ).format('YYYY-MM-DD')
              : null; //Thời gian kết thúc

            // const filesAttach = [
            //   ...new Set(
            //     getValueOrResult(row, EColumnImportBudgetCapex.FILE_ATTACH)
            //       ?.toString()
            //       ?.split(',')
            //       ?.map((item) => item.trim()),
            //   ),
            // ];
            const note2 = getValueOrResult(
              row,
              EColumnImportBudgetCapex.NOTE2,
            )?.toString(); //Mục đích đầu tư
            const keyProject = getValueOrResult(
              row,
              EColumnImportBudgetCapex.KEY_PROJECT,
            )?.toString(); //Mục đích đầu tư
            const isLock = getIsLockBudget(
              getValueOrResult(
                row,
                EColumnImportBudgetCapex.IS_LOCK,
              )?.toString(),
            ); //Chưa công bố / Đã công bố

            ///Kiểm tra format ngày
            if (startDate == 'Invalid date') {
              errors.push({
                error: getErrorMessage(errorMessage.E_1073()),
                row: i + 1,
              });
            }

            if (expectedAcceptanceTime == 'Invalid date') {
              errors.push({
                error: getErrorMessage(errorMessage.E_1074()),
                row: i + 1,
              });
            }

            if (effectiveStartDate == 'Invalid date') {
              errors.push({
                error: getErrorMessage(errorMessage.E_1075()),
                row: i + 1,
              });
            }

            if (effectiveEndDate == 'Invalid date') {
              errors.push({
                error: getErrorMessage(errorMessage.E_1076()),
                row: i + 1,
              });
            }

            if (
              getValueOrResult(
                row,
                EColumnImportBudgetCapex.START_DATE,
              )?.toString() &&
              (!startDate || startDate == 'Invalid date')
            ) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1065()),
                row: i + 1,
              });
            }

            if (!investment) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1067()),
                row: i + 1,
              });
            }

            if (!quantity) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1068()),
                row: i + 1,
              });
            }

            if (
              getValueOrResult(
                row,
                EColumnImportBudgetCapex.PRICE,
              )?.toString() &&
              !price &&
              price != 0
            ) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1069()),
                row: i + 1,
              });
            }

            if (
              getValueOrResult(
                row,
                EColumnImportBudgetCapex.TRANSPORTATION_COSTS,
              )?.toString() &&
              !transportationCosts &&
              transportationCosts != 0
            ) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1070()),
                row: i + 1,
              });
            }

            const verifyData = await this.verifyBudgetDataImport(
              {
                createType: EBudgetCreateType.NEW,
                budgetType,
                currencyUnitCode,
                budgetCodeCode,
                costcenterSubaccountCode,
                note,
                effectiveStartDate,
                effectiveEndDate,
                status: EBudgetStatus.ACTIVE,
                totalValue,
              },
              i + 1,
              currencyUnits,
              budgetCodes,
              costcenterSubaccounts,
            );

            errors.push(...verifyData.errors);

            const budgetCapexObject = {
              useTime,
              startDate,
              expectedAcceptanceTime,
              classify,
              priority,
              investmentPurpose,
              note2,
              keyProject,
              investments: [
                {
                  row: i + 1,
                  investment,
                  quantity,
                  price,
                  transportationCosts,
                },
              ],
              budgetData: {
                budgetType,
                ...verifyData.budgetDataVerify,
                note,
                effectiveStartDate,
                effectiveEndDate,
                status: EBudgetStatus.ACTIVE,
                totalValue,
                isLock,
                createdAt: moment()
                  .add(i * 500, 'milliseconds')
                  .toISOString(),
                updatedAt: moment()
                  .add(i * 500, 'milliseconds')
                  .toISOString(),
                rowNumber: i + 1,
              },
              createdAt: moment()
                .add(i * 500, 'milliseconds')
                .toISOString(),
              updatedAt: moment()
                .add(i * 500, 'milliseconds')
                .toISOString(),
            };

            // if (filesAttach?.length) {
            //   const checkFilesAttach = filesAttach.filter(
            //     (item) =>
            //       !filesUploaded
            //         .map((data) => data.originalname)
            //         .includes(item),
            //   );
            //   if (checkFilesAttach?.length) {
            //     errors.push({
            //       error: getErrorMessage(errorMessage.E_1077()),
            //       row: i + 1,
            //     });
            //   } else {
            //     budgetCapexObject.files = filesUploaded
            //       .filter((item) => filesAttach.includes(item.originalname))
            //       .map((data) => data.path);
            //   }
            // }

            dataCapex.push(budgetCapexObject);
          }
          const dataCapexGroup = this.groupDuplicateObjectsData(dataCapex);
          createBudgetCapexDtos.push(...dataCapexGroup);

          for (const dataCapex of createBudgetCapexDtos) {
            const checkDuplicateBudgets =
              await this.budgetRepository.checkDuplicateBudget(
                new BudgetModel(dataCapex.budgetData),
              );

            const dupBudgets: BudgetModel[] = [];

            for (let i = 0; i < (dataCapex.investments || []).length; i++) {
              for (let y = 0; y < checkDuplicateBudgets.length; y++) {
                const investments =
                  checkDuplicateBudgets[y]?.budgetCapex?.budgetInvestments ||
                  [];

                const dupInvestments = investments.find(
                  (item) =>
                    item.investment == dataCapex.investments[i].investment &&
                    item.quantity == dataCapex.investments[i].quantity &&
                    (item.price || null) ==
                      (dataCapex.investments[i].price || null) &&
                    (item.transportationCosts || null) ==
                      (dataCapex.investments[i].transportationCosts || null),
                );

                if (dupInvestments) {
                  dupBudgets.push(checkDuplicateBudgets[y]);
                  continue;
                }
              }
            }

            if (dupBudgets?.length) {
              errors.push({
                error: budgetErrorDetails.E_6002(),
                row: 0,
              });
            }

            if (
              hasDuplicateObjects(
                dataCapex.investments.map((item) => {
                  return { ...item, row: undefined };
                }),
              )
            ) {
              for (const investment of dataCapex.investments) {
                errors.push({
                  error: budgetErrorDetails.E_6000(),
                  row: investment.row,
                });
              }
            }
          }
        }

        const findDuplicateBudgets = checkDuplicateBudget(
          createBudgetCapexDtos,
          EBudgetType.CAPEX,
        );

        if (findDuplicateBudgets?.length) {
          for (let i = 0; i < findDuplicateBudgets?.length || 0; i++) {
            errors.push({
              error: budgetErrorDetails.E_6002(),
              row: findDuplicateBudgets[i]?.budgetData?.rowNumber || 0,
            });
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          // await this.fileUsecases.deleteFiles(
          //   filesUploaded.map((item) => item.path),
          // );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        // if (!filesAttachment || filesAttachment?.length == 0) {
        //   await this.fileUsecases.deleteFiles(
        //     filesUploaded.map((item) => item.path),
        //   );
        // }

        const importBody: ImportCapexDto = {
          dataCapexes: createBudgetCapexDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        // await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
        //   importBody: importBody,
        //   importHeader: {
        //     authorization,
        //     'x-api-key': process.env.API_KEY,
        //   },
        //   importUrl: PurchaseServiceApiUrlsConst.IMPORT_CAPEX(),
        //   updateStatusFileUrl:
        //     PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
        //       fileImportHistory.id,
        //     ),
        // });

        await this.import(importBody, EFileImportType.CAPEX);

        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.SUCCESS,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        // await this.fileUsecases.deleteFiles(
        //   filesUploaded.map((item) => item.path),
        // );

        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  public groupDuplicateObjectsData(objects): any {
    const groups = [];

    // Sử dụng mảng để lưu trữ các nhóm object trùng nhau
    for (let i = 0; i < objects.length; i++) {
      let foundGroup = false;
      for (const group of groups) {
        // Kiểm tra xem object hiện tại có thuộc nhóm nào không

        if (this.areObjectsEqualExceptInvestments(objects[i], group[0])) {
          group.push(objects[i]);
          foundGroup = true;
          break;
        }
      }
      // Nếu không tìm thấy nhóm nào phù hợp, tạo nhóm mới
      if (!foundGroup) {
        groups.push([objects[i]]);
      }
    }

    // Lọc ra những nhóm có nhiều hơn một object (tức là các nhóm trùng nhau)
    const dataGroups = [];
    for (let i = 0; i < groups.length; i++) {
      const data = { ...groups[i][0], investments: [] };
      for (let j = 0; j < groups[i].length; j++) {
        data.investments.push(...(groups[i][j].investments ?? []));
      }
      dataGroups.push(data);
    }
    return dataGroups;
  }

  private areObjectsEqualExceptInvestments(obj1, obj2): any {
    // Tạo bản sao của các object mà không có trường investments
    const obj1WithoutInvestments = {
      ...obj1,
      budgetData: {
        ...obj1.budgetData,
        createdAt: undefined,
        updatedAt: undefined,
      },
      investments: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };
    const obj2WithoutInvestments = {
      ...obj2,
      budgetData: {
        ...obj2.budgetData,
        createdAt: undefined,
        updatedAt: undefined,
      },
      investments: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };

    // So sánh các thuộc tính khác của hai object
    return _.isEqual(obj1WithoutInvestments, obj2WithoutInvestments);
  }

  public async verifyBudgetDataImport(
    budgetData: IImportBudget,
    row: number,
    // adjustBudgets: BudgetModel[],
    currencyUnits: CurrencyUnitModel[],
    budgetCodes: BudgetCodeModel[],
    costcenterSubaccounts: CostcenterSubaccountModel[],
  ) {
    budgetData.createType = getBudgetCreateType(budgetData.createType);

    const errors: TErrorMessageImport[] = [];

    const budgetDataVerify = {
      createType: EBudgetCreateType.NEW,
      budgetType: budgetData.budgetType,
      currencyUnitId: null,
      budgetCodeId: null,
      costcenterSubaccountId: null,
      adjustBudgetId: null,
    };

    ///Verify budget data
    if (!budgetData.createType) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1053()),
        row: row,
      });
    } else {
      if (!(budgetData.createType in EBudgetCreateType)) {
        errors.push({
          error: getErrorMessage(errorMessage.E_1062()),
          row: row,
        });
      }
    }

    if (!budgetData.currencyUnitCode) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1055()),
        row: row,
      });
    } else {
      const currencyUnit = currencyUnits.find(
        (item) => item.currencyCode == budgetData.currencyUnitCode,
      );
      if (currencyUnit) {
        if (currencyUnit.status == ECurrencyUnitStatus.IN_ACTIVE) {
          errors.push({
            error: getErrorMessage(errorMessage.E_1014()),
            row: row,
          });
        }

        budgetDataVerify.currencyUnitId = currencyUnit.id;
      } else {
        errors.push({
          error: getErrorMessage(errorMessage.E_1013()),
          row: row,
        });
      }
    }

    if (!budgetData.budgetCodeCode) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1056()),
        row: row,
      });
    } else {
      const budgetCode = budgetCodes.find(
        (item) => item.code == budgetData.budgetCodeCode,
      );
      if (budgetCode) {
        if (budgetCode.status == EBudgetCodeStatus.IN_ACTIVE) {
          errors.push({
            error: getErrorMessage(errorMessage.E_1016()),
            row: row,
          });
        }
        budgetDataVerify.budgetCodeId = budgetCode.id;
      } else {
        errors.push({
          error: getErrorMessage(errorMessage.E_1015()),
          row: row,
        });
      }
    }

    if (!budgetData.costcenterSubaccountCode) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1057()),
        row: row,
      });
    } else {
      const costcenterSubaccount = costcenterSubaccounts.find(
        (item) => item.code == budgetData.costcenterSubaccountCode,
      );
      if (costcenterSubaccount) {
        if (
          costcenterSubaccount.status == ECostcenterSubaccountStatus.IN_ACTIVE
        ) {
          errors.push({
            error: getErrorMessage(errorMessage.E_1018()),
            row: row,
          });
        }

        budgetDataVerify.costcenterSubaccountId = costcenterSubaccount.id;
      } else {
        errors.push({
          error: getErrorMessage(errorMessage.E_1005()),
          row: row,
        });
      }
    }

    if (!budgetData.effectiveStartDate) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1058()),
        row: row,
      });
    }

    if (!budgetData.effectiveEndDate) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1059()),
        row: row,
      });
    }

    if (!budgetData.totalValue) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1060()),
        row: row,
      });
    }

    if (
      budgetData.effectiveStartDate &&
      budgetData.effectiveEndDate &&
      budgetData.effectiveStartDate > budgetData.effectiveEndDate
    ) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1019()),
        row: row,
      });
    }

    return {
      errors: errors,
      budgetDataVerify: budgetDataVerify,
    };
  }

  async getBudgetDetail(
    conditions: GetDetailBudgetDto,
    jwtPayload: any,
  ): Promise<BudgetModel> {
    const detail = await this.budgetRepository.getBudgetDetail(
      conditions,
      jwtPayload,
    );
    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1006()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async importAdjustBudget(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    budgetType: EBudgetType,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType:
          budgetType == EBudgetType.OPEX
            ? EFileImportType.OPEX
            : EFileImportType.CAPEX,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createBudgetOpexDtos: CreateBudgetOpexDto[] = [];
        const createBudgetCapexDtos: CreateBudgetCapexDto[] = [];

        let errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0].actualRowCount,
          ) ?? [];

        const budgetCodeCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportAdjustBudget.BUDGET_CODE_CODE,
                )?.toString(),
              )
              ?.slice(1)
              ?.filter(Boolean),
          ),
        ];

        const currencyCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportAdjustBudget.CURRENCY_CODE,
                )?.toString(),
              )
              ?.slice(1)
              ?.filter(Boolean),
          ),
        ];

        const costcenterSubaccountCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportAdjustBudget.COSTCENTER_SUBACCOUNT_CODE,
                )?.toString(),
              )
              ?.slice(1)
              ?.filter(Boolean),
          ),
        ];

        const effectiveStartDates = [
          ...new Set(
            rows
              .map((item) =>
                item.getCell(EColumnImportAdjustBudget.EFFECTIVE_START_DATE)
                  .value
                  ? moment(
                      excelSerialToDate(
                        item.getCell(
                          EColumnImportAdjustBudget.EFFECTIVE_START_DATE,
                        ).value as string | number,
                      ),
                    ).format('YYYY-MM-DD')
                  : null,
              )
              ?.slice(1)
              ?.filter((item) => item != 'Invalid date'),
          ),
        ];

        const [budgetCodes, adjustBudgets, costcenterSubaccounts, currencys] =
          await Promise.all([
            this.budgetCodeRepository.getBudgetCodesByCodesWithRole(
              budgetCodeCodes,
              jwtPayload,
            ),
            this.budgetRepository.getBudgetsForImportAdjustBudgetWithRole(
              budgetCodeCodes,
              costcenterSubaccountCodes,
              effectiveStartDates,
              jwtPayload,
              budgetType,
            ),
            this.costcenterSubaccountRepository.getCostcenterSubaccountsByCodesWithRole(
              costcenterSubaccountCodes,
              jwtPayload,
            ),
            this.currencyUnitRepository.getCurrencyUnitsByCodesWithRole(
              currencyCodes,
              jwtPayload,
            ),
          ]);

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          //Thông tin chung của ngân sách
          const createType = getValueOrResult(
            row,
            EColumnImportAdjustBudget.CREATE_TYPE,
          )?.toString(); //Loại khởi tạo
          const budgetCodeCode = getValueOrResult(
            row,
            EColumnImportAdjustBudget.BUDGET_CODE_CODE,
          )?.toString(); //Code ngân sách
          const costcenterSubaccountCode = getValueOrResult(
            row,
            EColumnImportAdjustBudget.COSTCENTER_SUBACCOUNT_CODE,
          )?.toString();
          const effectiveStartDate = row.getCell(
            EColumnImportAdjustBudget.EFFECTIVE_START_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportAdjustBudget.EFFECTIVE_START_DATE)
                    .value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian bắt đầu
          const effectiveEndDate = row.getCell(
            EColumnImportAdjustBudget.EFFECTIVE_END_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportAdjustBudget.EFFECTIVE_END_DATE)
                    .value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian bắt đầu
          const operationInvestment = getValueOrResult(
            row,
            EColumnImportAdjustBudget.OPERATION_INVESTMENT,
          )?.toString(); //Diễn giải/Nghiệp vụ
          const totalValue = getValueOrResult(
            row,
            EColumnImportAdjustBudget.TOTAL_VALUE,
            true,
          ); //Số tiền (nguyên tệ)
          const currencyCode = getValueOrResult(
            row,
            EColumnImportAdjustBudget.CURRENCY_CODE,
          )?.toString(); //Đơn vị tiền tệ
          const note = getValueOrResult(
            row,
            EColumnImportAdjustBudget.NOTE,
          )?.toString(); //Ghi chú
          const isLock = getIsLockBudget(
            getValueOrResult(
              row,
              EColumnImportAdjustBudget.IS_LOCK,
            )?.toString(),
          ); //Chưa công bố / Đã công bố

          ///Kiểm tra format ngày
          if (!effectiveStartDate || effectiveStartDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(errorMessage.E_1075()),
              row: i + 1,
            });
          }

          ///Kiểm tra format ngày
          if (!effectiveEndDate || effectiveEndDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(errorMessage.E_1076()),
              row: i + 1,
            });
          }

          const verifyDataData = await this.verifyAdjustBudgetDataImport(
            {
              createType,
              budgetType,
              budgetCodeCode,
              note,
              costcenterSubaccountCode,
              effectiveStartDate,
              effectiveEndDate,
              status: EBudgetStatus.ACTIVE,
              totalValue,
              operationInvestment: operationInvestment,
              currencyUnitCode: currencyCode,
            },
            i + 1,
            budgetCodes,
            adjustBudgets,
            costcenterSubaccounts,
            currencys,
          );

          errors.push(...verifyDataData.errors);

          const timeObject = {
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };
          const generalBudgetData = {
            effectiveStartDate,
            effectiveEndDate,
            note,
            status: EBudgetStatus.ACTIVE,
            totalValue,
            isLock,
            ...timeObject,
          };

          if (budgetType == EBudgetType.OPEX) {
            const budgetOpexObject = {
              ...(verifyDataData?.budgetDataVerify?.budgetOpex || {}),
              id: undefined,
              budgetData: {
                createType,
                ...(verifyDataData?.budgetDataVerify || {}),
                id: undefined,
                budgetOpexId: undefined,
                budgetOpex: undefined,
                ...generalBudgetData,
              },
              ...timeObject,
            };

            const budgetOpexDto = plainToInstance(CreateBudgetOpexDto, {
              ...budgetOpexObject,
              isNeedGen: verifyDataData?.budgetDataVerify?.isNeedGen || false,
            });

            createBudgetOpexDtos.push(budgetOpexDto);
          } else {
            const budgetCapexObject = {
              ...(verifyDataData?.budgetDataVerify?.budgetCapex || {}),
              id: undefined,
              investments: (
                verifyDataData?.budgetDataVerify?.budgetCapex
                  ?.budgetInvestments || []
              ).map((item) => {
                return {
                  ...item,
                  id: undefined,
                  budgetCapexId: undefined,
                };
              }),
              budgetInvestments: undefined,
              budgetData: {
                budgetType,
                ...(verifyDataData.budgetDataVerify || {}),
                id: undefined,
                budgetCapex: undefined,
                budgetCapexId: undefined,
                ...generalBudgetData,
              },
              ...timeObject,
            };
            const budgetCapexDto = plainToInstance(CreateBudgetCapexDto, {
              ...budgetCapexObject,
              isNeedGen: verifyDataData?.budgetDataVerify?.isNeedGen || false,
            });

            createBudgetCapexDtos.push(budgetCapexDto);
          }
        }

        let findDuplicateBudgets:
          | CreateBudgetOpexDto[]
          | CreateBudgetCapexDto[];
        if (budgetType == EBudgetType.OPEX) {
          findDuplicateBudgets = checkDuplicateBudget(
            (createBudgetOpexDtos as CreateBudgetOpexDto[])
              .filter((item) => item.isNeedGen)
              .filter(Boolean),
            EBudgetType.OPEX,
          );
        } else if (budgetType == EBudgetType.CAPEX) {
          findDuplicateBudgets = checkDuplicateBudget(
            (createBudgetCapexDtos as CreateBudgetCapexDto[])
              .filter((item) => item.isNeedGen)
              .filter(Boolean),
            EBudgetType.CAPEX,
          );
        }

        if (findDuplicateBudgets?.length) {
          for (let i = 0; i < findDuplicateBudgets.length || 0; i++) {
            errors.push({
              error: budgetErrorDetails.E_6002(),
              row: findDuplicateBudgets[i]?.budgetData?.rowNumber || 0,
            });
          }
        }

        errors = mergedErrors(errors);

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (budgetType == EBudgetType.OPEX) {
          const importBody: ImportOpexDto = {
            dataOpexes: createBudgetOpexDtos,
            fileImportHistoryId: fileImportHistory.id,
          };

          await this.import(importBody, EFileImportType.OPEX);
        } else {
          const importBody: ImportCapexDto = {
            dataCapexes: createBudgetCapexDtos,
            fileImportHistoryId: fileImportHistory.id,
          };

          await this.import(importBody, EFileImportType.CAPEX);
        }

        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.SUCCESS,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  public async verifyAdjustBudgetDataImport(
    budgetData: IImportBudget,
    row: number,
    budgetCodes: BudgetCodeModel[],
    adjustBudgets: BudgetModel[],
    costcenterSubaccounts: CostcenterSubaccountModel[],
    currencys: CurrencyUnitModel[],
  ) {
    budgetData.createType = getBudgetCreateType(budgetData.createType);

    const errors: TErrorMessageImport[] = [];

    const budgetDataVerify = {
      createType: budgetData.createType,
      budgetType: budgetData.budgetType,
      budgetCodeId: null,
      costcenterSubaccountId: null,
      adjustBudgetId: null,
      isNeedGen: false,
      effectiveStartDate: budgetData.effectiveStartDate,
      effectiveEndDate: budgetData.effectiveEndDate,
      currencyUnitId: null,
    };

    ///Verify budget data
    if (!budgetData.createType) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1053()),
        row: row,
      });
    } else {
      if (
        !(budgetData.createType in EBudgetCreateType) ||
        budgetData.createType == EBudgetCreateType.NEW
      ) {
        errors.push({
          error: getErrorMessage(errorMessage.E_1062()),
          row: row,
        });
      }
    }

    if (!budgetData.budgetCodeCode) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1056()),
        row: row,
      });
    } else {
      const budgetCode = budgetCodes.find(
        (item) => item.code == budgetData.budgetCodeCode,
      );
      if (budgetCode) {
        if (budgetCode.status == EBudgetCodeStatus.IN_ACTIVE) {
          errors.push({
            error: getErrorMessage(errorMessage.E_1016()),
            row: row,
          });
        }
        budgetDataVerify.budgetCodeId = budgetCode.id;
      } else {
        errors.push({
          error: getErrorMessage(errorMessage.E_1015()),
          row: row,
        });
      }
    }

    if (!budgetData.costcenterSubaccountCode) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1057()),
        row: row,
      });
    } else {
      const costcenterSubaccount = costcenterSubaccounts.find(
        (item) => item.code == budgetData.costcenterSubaccountCode,
      );
      if (costcenterSubaccount) {
        if (
          costcenterSubaccount.status == ECostcenterSubaccountStatus.IN_ACTIVE
        ) {
          errors.push({
            error: getErrorMessage(errorMessage.E_1018()),
            row: row,
          });
        }

        budgetDataVerify.costcenterSubaccountId = costcenterSubaccount.id;
      } else {
        errors.push({
          error: getErrorMessage(errorMessage.E_1005()),
          row: row,
        });
      }
    }

    if (!budgetData.currencyUnitCode) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1055()),
        row: row,
      });
    } else {
      const currencyUnit = currencys.find(
        (item) => item.currencyCode == budgetData.currencyUnitCode,
      );
      if (currencyUnit) {
        budgetDataVerify.currencyUnitId = currencyUnit.id;
      } else {
        errors.push({
          error: getErrorMessage(errorMessage.E_1013()),
          row: row,
        });
      }
    }

    if (!budgetData.effectiveStartDate) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1058()),
        row: row,
      });
    }

    if (!budgetData.totalValue) {
      errors.push({
        error: getErrorMessage(errorMessage.E_1060()),
        row: row,
      });
    }

    let adjustBudget: BudgetModel;
    const budgets = adjustBudgets.filter(
      (item) =>
        item.budgetCodeId == budgetDataVerify.budgetCodeId &&
        item.costcenterSubaccountId ==
          budgetDataVerify.costcenterSubaccountId &&
        new Date(item.effectiveStartDate).getTime() <=
          new Date(budgetData.effectiveEndDate).getTime() &&
        new Date(item.effectiveEndDate).getTime() >=
          new Date(budgetData.effectiveStartDate).getTime(),
    );

    if (budgetData.budgetType == EBudgetType.OPEX) {
      adjustBudget = budgets.find(
        (item) =>
          (item.budgetOpex?.operations || null) ==
          (budgetData.operationInvestment || null),
      );
    } else {
      for (let y = 0; y < budgets.length; y++) {
        const investments = budgets[y]?.budgetCapex?.budgetInvestments || [];

        const dupInvestments = investments.find(
          (item) =>
            item.investment == (budgetData.operationInvestment || null) &&
            item.quantity == 1 &&
            (item.price || null) == (budgetData.totalValue || null) &&
            (item.transportationCosts || 0) == 0,
        );

        if (dupInvestments) {
          adjustBudget = budgets[y];
          break;
        }
      }
    }
    let dataBudget = { ...adjustBudget, ...budgetDataVerify };

    if (adjustBudget) {
      if (adjustBudget.budgetType != budgetData.budgetType) {
        errors.push({
          error: getErrorMessage(errorMessage.E_1011()),
          row: row,
        });
      }

      dataBudget.adjustBudgetId = adjustBudget.id;
    } else {
      if (budgetData.createType == EBudgetCreateType.DECREASE) {
        errors.push({
          error: getErrorMessage(errorMessage.E_1009()),
          row: row,
        });
      } else {
        dataBudget.isNeedGen = true;
      }
    }

    if (budgetData.budgetType == EBudgetType.OPEX) {
      dataBudget.budgetOpex = {
        ...dataBudget.budgetOpex,
        operations: budgetData.operationInvestment,
      };
    } else {
      dataBudget.budgetCapex = {
        ...dataBudget.budgetCapex,
        budgetInvestments: [
          {
            investment: budgetData.operationInvestment,
            quantity: 1,
            transportationCosts: null,
            price: budgetData.totalValue,
            budgetCapexId: undefined,
            id: undefined,
          },
        ],
      };
    }

    return {
      errors: errors,
      budgetDataVerify: dataBudget,
    };
  }

  async exportBudget(conditions: GetBudgetListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.budgetRepository.getBudgetList(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const items = await this.toBudgetOverviewModel(
        data?.results || [],
        conditions.budgetType,
      );

      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          conditions.budgetType == EBudgetType.OPEX
            ? '../domain/template/export/template-opex-export.xlsx'
            : '../domain/template/export/template-capex-export.xlsx',
        ),
      );

      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);

      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Copy value
        targetCell.style = { ...cell.style }; // Copy full style
        targetCell.border = cell.border; // Copy border
        targetCell.font = cell.font; // Copy font
        targetCell.alignment = cell.alignment; // Copy alignment
        targetCell.numFmt = cell.numFmt; // Copy number format
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      for (let i = 0; i < items.length; i++) {
        //Bắt đầu ghi từ dòng 2
        const targetRow = targetWorksheet.getRow(i + 2);
        Object.values(items[i]).forEach((value: any, colIndex) => {
          const targetCell = targetRow.getCell(colIndex + 1);
          const sourceCell = sourceWorksheet.getRow(2).getCell(colIndex + 1); // Lấy format từ dòng 2

          let finalValue = value; // Giá trị thực tế
          let numFmt = sourceCell.numFmt; // Format Excel

          // Nếu giá trị rỗng, giữ nguyên ''
          if (value === '' || value === null || value === undefined) {
            finalValue = '';
          } else if (sourceCell.numFmt) {
            if (
              sourceCell.numFmt.includes('yyyy') ||
              sourceCell.numFmt.includes('mm') ||
              sourceCell.numFmt.includes('dd')
            ) {
              // Nếu là ngày, giữ đúng kiểu Date
              if (value instanceof Date) {
                finalValue = value;
              } else {
                const parsedDate = new Date(value);
                finalValue = isNaN(parsedDate.getTime()) ? '' : parsedDate;
              }
              numFmt = 'dd/mm/yyyy'; // Format ngày
            } else {
              // Nếu là số hoặc tiền tệ, ép kiểu về number nếu có thể
              const numericValue = Number(value);
              if (!isNaN(numericValue)) {
                finalValue = numericValue;
              }
            }
          }

          targetCell.value = finalValue; // Gán giá trị vào ô

          if (numFmt) {
            targetCell.numFmt = numFmt; // Áp dụng định dạng Excel
          }
        });

        targetRow.commit();
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        conditions.budgetType == EBudgetType.OPEX
          ? 'opex-export.xlsx'
          : 'capex-export.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }

    return data;
  }

  private async toBudgetOverviewModel(items: BudgetModel[], type: EBudgetType) {
    const datas = [];

    for (const data of items) {
      if (type == EBudgetType.OPEX) {
        datas.push({
          code: data?.code || '',
          currencyUnit: data.currencyUnit?.currencyCode || '',
          budgetCode: data.budgetCode?.code || '',
          costcenter: data.costcenterSubaccount?.code || '',
          operations: data.budgetOpex?.operations || '',
          totalValue: data.totalValue || 0,
          form: data.budgetOpex?.form || '',
          effectiveStartDate: data.effectiveStartDate
            ? new Date(data.effectiveStartDate)
            : '',
          effectiveEndDate: data.effectiveEndDate
            ? new Date(data.effectiveEndDate)
            : '',
          note: data.note || '',
          status: data.isLock || false ? 'Đã công bố' : 'Chưa công bố',
          createType: data.createType || '',
          adjustBudget: data.parent?.code || '',
        });
      }

      if (type == EBudgetType.CAPEX) {
        if (data.budgetCapex?.budgetInvestments?.length) {
          for (
            let i = 0;
            i < data.budgetCapex?.budgetInvestments?.length;
            i++
          ) {
            datas.push({
              code: data?.code || '',
              createType: data.createType || '',
              adjustBudget: data.parent?.code || '',
              currencyUnit: data.currencyUnit?.currencyCode || '',
              costcenter: data.costcenterSubaccount?.code || '',
              classify: data.budgetCapex?.classify || '',
              investment:
                data.budgetCapex?.budgetInvestments[i]?.investment || '',
              quantity: data.budgetCapex?.budgetInvestments[i]?.quantity || 0,
              price: data.budgetCapex?.budgetInvestments[i]?.price || '',
              transportationCosts:
                data.budgetCapex?.budgetInvestments[i]?.transportationCosts ||
                '',
              totalValue: data.totalValue || 0,
              startDate: data.budgetCapex?.startDate
                ? new Date(data.budgetCapex?.startDate)
                : '',
              expectedAcceptanceTime: data.budgetCapex?.expectedAcceptanceTime
                ? new Date(data.budgetCapex?.expectedAcceptanceTime)
                : '',
              useTime: data.budgetCapex?.useTime || '',
              priority: data.budgetCapex?.priority || '',
              investmentPurpose: data.budgetCapex?.investmentPurpose || '',
              note: data.note || '',
              budgetCode: data.budgetCode?.code || '',
              effectiveEndDate: data.effectiveEndDate
                ? new Date(data.effectiveEndDate)
                : '',
              files: data.budgetCapex?.files?.join(', ') || '',
              status: data.isLock || false ? 'Đã công bố' : 'Chưa công bố',
            });
          }
        } else {
          datas.push({
            code: data?.code || '',
            createType: data.createType || '',
            adjustBudget: data.parent?.code || '',
            currencyUnit: data.currencyUnit?.currencyCode || '',
            costcenter: data.costcenterSubaccount?.code || '',
            classify: data.budgetCapex?.classify || '',
            investment: '',
            quantity: '',
            price: '',
            transportationCosts: '',
            totalValue: data.totalValue || 0,
            startDate: data.budgetCapex?.startDate
              ? new Date(data.budgetCapex?.startDate)
              : '',
            expectedAcceptanceTime: data.budgetCapex?.expectedAcceptanceTime
              ? new Date(data.budgetCapex?.expectedAcceptanceTime)
              : '',
            useTime: data.budgetCapex?.useTime || '',
            priority: data.budgetCapex?.priority || '',
            investmentPurpose: data.budgetCapex?.investmentPurpose || '',
            note: data.note || '',
            budgetCode: data.budgetCode?.code || '',
            effectiveEndDate: data.effectiveEndDate
              ? new Date(data.effectiveEndDate)
              : '',
            files: data.budgetCapex?.files?.join(', ') || '',
            status: data.isLock || false ? 'Đã công bố' : 'Chưa công bố',
          });
        }
      }
    }

    return datas;
  }

  async handleCopyExcel(conditions: HandleCopyExcelListDto, jwtPayload: any) {
    const budgetCodeCodes = [
      ...new Set(
        conditions.data.map((item) => item.budgetCode).filter(Boolean),
      ),
    ];
    const costCenterCodes = [
      ...new Set(
        conditions.data.map((item) => item.costCenter).filter(Boolean),
      ),
    ];
    const materialCodes = [
      ...new Set(
        conditions.data.map((item) => item.materialCode).filter(Boolean),
      ),
    ];
    const materialGroupCodes = [
      ...new Set(
        conditions.data.map((item) => item.materialGroup).filter(Boolean),
      ),
    ];
    const supplierCodes = [
      ...new Set(conditions.data.map((item) => item.supplier).filter(Boolean)),
    ];
    const currencyCodes = [
      ...new Set(conditions.data.map((item) => item.currency).filter(Boolean)),
    ];
    const measureCodes = [
      ...new Set(
        conditions.data.map((item) => item.measureCode).filter(Boolean),
      ),
    ];
    const taxCodeCodes = [
      ...new Set(
        conditions.data.map((item) => item.taxCodeCode).filter(Boolean),
      ),
    ];
    const warehouseCodes = [
      ...new Set(
        conditions.data.map((item) => item.warehouseCode).filter(Boolean),
      ),
    ];

    const [
      budgetCodes,
      costCenters,
      materials,
      materialGroups,
      suppliers,
      currencyUnits,
      measures,
      taxCodes,
      warehouses,
    ] = await Promise.all([
      this.budgetCodeRepository.getBudgetCodesByCodesWithRole(
        budgetCodeCodes,
        jwtPayload,
      ),
      this.costcenterSubaccountRepository.getCostcenterSubaccountsByCodesWithRole(
        costCenterCodes,
        jwtPayload,
      ),
      this.materialRepository.getMaterialByCodesWithRole(
        materialCodes,
        jwtPayload,
      ),
      this.materialGroupRepository.getMaterialGroupByCodesWithRole(
        materialGroupCodes,
        jwtPayload,
      ),
      this.supplierRepository.getSupplierByCodesWithRole(
        supplierCodes,
        jwtPayload,
      ),
      this.currencyUnitRepository.getCurrencyUnitsByCodesWithRole(
        currencyCodes,
        jwtPayload,
      ),
      this.measureRepository.getMeasuresByCodesWithRole(
        measureCodes,
        jwtPayload,
      ),
      this.taxCodeRepository.getTaxCodesByCodesWithRole(
        taxCodeCodes,
        jwtPayload,
      ),
      this.warehouseRepository.getWarehousesByCodesWithRole(
        warehouseCodes,
        jwtPayload,
      ),
    ]);

    const dataResponse: IResponseHandleCopyExcel[] = [];

    for (const item of conditions.data) {
      dataResponse.push({
        budgetCode:
          budgetCodes.find((data) => data.code == item.budgetCode) || null,
        costCenter:
          costCenters.find((data) => data.code == item.costCenter) || null,
        materialCode:
          materials.find((data) => data.code == item.materialCode) || null,
        materialGroup:
          materialGroups.find((data) => data.code == item.materialGroup) ||
          null,
        supplier: suppliers.find((data) => data.code == item.supplier) || null,
        currency:
          currencyUnits.find((data) => data.currencyCode == item.currency) ||
          null,
        measure: measures.find((data) => data.code == item.measureCode) || null,
        taxCode: taxCodes.find((data) => data.code == item.taxCodeCode) || null,
        warehouse:
          warehouses.find((data) => data.code == item.warehouseCode) || null,
        key: item.key || null,
      });
    }

    return dataResponse;
  }

  async reportBudgetDetail(
    conditions: ReportBudgetDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<ResponseDto<IReponseReportBudgetDetail>> {
    const from = moment(conditions.period, 'YYYY-MM')
      .startOf('year')
      .format('YYYY-MM-DD');
    const to = moment(conditions.period, 'YYYY-MM')
      .endOf('month')
      .format('YYYY-MM-DD');

    conditions.from = from;
    conditions.to = to;

    const reponseData: IReponseReportBudgetDetail[] = [];

    const reportBudgetDetails = await this.budgetRepository.reportBudgetDetail(
      conditions,
      jwtPayload,
    );
    console.log(2925, reportBudgetDetails);
    const budgetCodeIds = [
      ...new Set(reportBudgetDetails.results.map((item) => item.budgetCodeId)),
    ];
    const costCenterIds = [
      ...new Set(
        reportBudgetDetails.results.map((item) => item.costcenterSubaccountId),
      ),
    ];

    const conditionsForActualSpending: GetActualSpendingListDto = {
      period: conditions.period,
      page: 1,
      limit: 10,
      searchString: '',
      getAll: 1,
      costCenterIds,
      budgetCodeIds,
      budgetCodeType: conditions.budgetType,
    };
    const actualSpendings =
      costCenterIds?.length && budgetCodeIds?.length
        ? await this.actualSpendingRepository.getListActualSpending(
            conditionsForActualSpending,
            jwtPayload,
          )
        : null;

    const adjustBudgets: BudgetModel[] = [];

    for (let i = 0; i < reportBudgetDetails.results.length; i++) {
      for (let y = 0; y < reportBudgetDetails.results[i].budgets.length; y++) {
        if (
          reportBudgetDetails.results[i].budgets[y].createType ==
            EBudgetCreateType.INCREASE ||
          reportBudgetDetails.results[i].budgets[y].createType ==
            EBudgetCreateType.DECREASE
        ) {
          adjustBudgets.push(reportBudgetDetails.results[i].budgets[y]);
        }
      }
    }

    const adjustBudgetIds = [...new Set(adjustBudgets.map((item) => item.id))];

    const [poDetailsReponse, poDetailsAdjustBudgetReponse] = await Promise.all([
      costCenterIds?.length && budgetCodeIds?.length
        ? await this.purchaseOrderRepository.findPoReportBudget(
            {
              effectiveStart: new Date(conditions.from),
              effectiveEnd: new Date(conditions.to),
              statusPo: [Status.Approved],
              // isAccountantApproved: 'true',
              costCenterIds: costCenterIds,
              budgetCodeIds: budgetCodeIds,
            },
            authorization,
          )
        : null,
      adjustBudgetIds?.length
        ? await this.purchaseOrderRepository.findAdjustBudgetIdInPoDetail(
            adjustBudgetIds,
          )
        : null,
    ]);

    const poDetails = poDetailsReponse || [];
    const poDetailsAdjustBudgetIds = (poDetailsAdjustBudgetReponse || []).map(
      (item) => item.adjustBudgetId,
    );

    ///  Curency từ PO và Filter
    const currencyUnitIds = [];
    const poDetailCurrencyUnitIds = poDetails
      .map((item) => item?.purchaseOrder?.currency)
      .filter(Boolean);
    const budgetCurrencyUnitIds = (reportBudgetDetails.results || [])
      .flatMap((data) => data.budgets)
      .map((item) => item.currencyUnitId);
    currencyUnitIds.push(...poDetailCurrencyUnitIds, ...budgetCurrencyUnitIds);

    const conditionsCurrency: GetCurrencyUnitListDto = {
      ids: [...new Set(currencyUnitIds)],
      getAll: 1,
      page: 1,
      limit: 10,
      searchString: '',
    };
    const currencyUnits = await this.currencyUnitUsecases.getCurrencyUnits(
      conditionsCurrency,
      jwtPayload,
    );

    let currency = await this.currencyUnitUsecases.getCurrencyUnitVND();

    for (let i = 0; i < reportBudgetDetails.results.length; i++) {
      const firstBudget = reportBudgetDetails.results[i]?.budgets[0]; // Lấy Mã ngân sách và Cost center

      if (conditions.eCurrency == EFilterCurrencyReportBudget.ORIGINAL) {
        currency = firstBudget?.currencyUnit;
      }

      if (!currency) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1013()),
          HttpStatus.NOT_FOUND,
        );
      }

      const matchPoDetails = poDetails.filter(
        (item) =>
          item.budgetCodeId == reportBudgetDetails.results[i].budgetCodeId &&
          item.costCenterId ==
            reportBudgetDetails.results[i].costcenterSubaccountId,
      );

      const budgetCodeCodes = reportBudgetDetails.results[i]?.budgets
        .map((item) => item.budgetCode?.code)
        .filter(Boolean);

      const matchActualSpendings =
        actualSpendings?.results?.filter(
          (item) =>
            budgetCodeCodes.includes(item.budgetCode?.code) &&
            item.costCenter?.id ==
              reportBudgetDetails.results[i].costcenterSubaccountId,
        ) || [];

      let beginBudget = 0; //Logic lấy tỷ giá theo ngân sách
      let addedBudget = 0; //Logic lấy tỷ giá theo ngân sách
      let adjustBudget = 0; //Logic lấy tỷ giá theo ngân sách
      for (const budget of reportBudgetDetails.results[i].budgets) {
        const matchCurrencyExchangeBudget = (
          (currencyUnits.results || []).find(
            (item) => item.id == budget.currencyUnitId,
          )?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              budget.effectiveStartDate,
              item.effectiveStartDate,
              budget.effectiveEndDate,
              item.effectiveEndDate,
            ) && item.exchangeBudget,
        );

        const matchCurrencyExchangeFilter = (
          currency?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              budget.effectiveStartDate,
              item.effectiveStartDate,
              budget.effectiveEndDate,
              item.effectiveEndDate,
            ) && item.exchangeBudget,
        );

        if (!matchCurrencyExchangeBudget || !matchCurrencyExchangeFilter) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1103()),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (budget.createType == EBudgetCreateType.NEW) {
          beginBudget +=
            (Number(budget.totalValue) *
              matchCurrencyExchangeBudget.exchangeRate) /
            matchCurrencyExchangeFilter.exchangeRate;
        }

        if (
          (budget.createType == EBudgetCreateType.INCREASE ||
            budget.createType == EBudgetCreateType.DECREASE) &&
          poDetailsAdjustBudgetIds.includes(budget.id)
        ) {
          if (budget.createType == EBudgetCreateType.INCREASE) {
            addedBudget +=
              ((budget.totalValue || 0) *
                matchCurrencyExchangeBudget.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }
          if (budget.createType == EBudgetCreateType.DECREASE) {
            addedBudget -=
              ((budget.totalValue || 0) *
                matchCurrencyExchangeBudget.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }
        }

        if (
          (budget.createType == EBudgetCreateType.INCREASE ||
            budget.createType == EBudgetCreateType.DECREASE) &&
          !poDetailsAdjustBudgetIds.includes(budget.id)
        ) {
          if (budget.createType == EBudgetCreateType.INCREASE) {
            adjustBudget +=
              ((budget.totalValue || 0) *
                matchCurrencyExchangeBudget.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }
          if (budget.createType == EBudgetCreateType.DECREASE) {
            adjustBudget -=
              ((budget.totalValue || 0) *
                matchCurrencyExchangeBudget.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }
        }
      }

      let totalBudget = beginBudget + addedBudget + adjustBudget;

      let approvedBudget = 0; //Logic lấy tỷ giá theo ngày duyệt PO
      let poAmount = 0; //Logic lấy tỷ giá theo ngày duyệt PO
      for (const item of matchPoDetails) {
        const lastLevelHistory = (item?.purchaseOrder?.history || []).sort(
          (a, b) => b.level - a.level,
        )[0];

        const matchCurrencyExchangeFilter = (
          currency?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              new Date(lastLevelHistory?.updatedAt),
              item.effectiveStartDate,
              null,
              item.effectiveEndDate,
            ) && item.exchangeBudget == false,
        );

        if (!matchCurrencyExchangeFilter) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1103()),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (item?.purchaseOrder?.statusPo == 'Approved') {
          approvedBudget +=
            Number(item.totalAmountVat || 0) /
            matchCurrencyExchangeFilter.exchangeRate;
        }

        poAmount +=
          conditions.budgetType == EBudgetType.CAPEX
            ? Number(item.totalAmountVat || 0) /
              matchCurrencyExchangeFilter.exchangeRate
            : 0;
      }

      let availableBudget = totalBudget - approvedBudget;

      let totalPayment = 0; //Báo cáo dữ liệu thanh toán thực tế
      let actualPaymentPrPoFromE = 0; //Báo cáo dữ liệu thanh toán thực tế
      let actualPaymentPrPoExcludingE = 0; //Báo cáo dữ liệu thanh toán thực tế
      let actual = 0;
      let advance = 0;

      for (const actualSpending of matchActualSpendings) {
        const matchCurrencyExchangeFilter = (
          currency?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              actualSpending.postingDate,
              item.effectiveStartDate,
              null,
              item.effectiveEndDate,
            ) && item.exchangeBudget == false,
        );

        if (!matchCurrencyExchangeFilter) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1103()),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (actualSpending.status == EStatusActualSpending.CONFIRMED) {
          let docAmount = 0;
          if (conditions.budgetType == EBudgetType.CAPEX) {
            docAmount = Number(actualSpending.docAmount || 0);
          } else {
            const taxCode = (actualSpending.taxCode || '')
              .split('')[0]
              ?.toLowerCase();
            docAmount = ['a', 'c', 'e', 'g'].includes(taxCode)
              ? Number(actualSpending.docAmount || 0)
              : Number(actualSpending.docAmount || 0) *
                (1 +
                  (isNaN(Number(actualSpending.taxRate || 0))
                    ? 0
                    : Number(actualSpending.taxRate || 0) / 100));
          }

          if (
            checkEffectiveTimeOverlaps(
              actualSpending.paymentDate,
              new Date(conditions.from),
              null,
              new Date(conditions.to),
            )
          ) {
            totalPayment +=
              conditions.budgetType == EBudgetType.CAPEX
                ? (actualSpending.docPaymentAmount *
                    actualSpending.exchangeRate) /
                  matchCurrencyExchangeFilter.exchangeRate
                : 0;
          }

          if (
            checkEffectiveTimeOverlaps(
              actualSpending.postingDate,
              new Date(conditions.from),
              null,
              new Date(conditions.to),
            )
          ) {
            if (actualSpending.poSapId) {
              actualPaymentPrPoExcludingE +=
                conditions.budgetType == EBudgetType.OPEX
                  ? (docAmount * actualSpending.exchangeRate) /
                    matchCurrencyExchangeFilter.exchangeRate
                  : 0;
            } else {
              actualPaymentPrPoFromE +=
                conditions.budgetType == EBudgetType.OPEX
                  ? (docAmount * actualSpending.exchangeRate) /
                    matchCurrencyExchangeFilter.exchangeRate
                  : 0;
            }

            if (
              actualSpending.status == EStatusActualSpending.CONFIRMED &&
              actualSpending.note?.toLowerCase() == 'act'
            ) {
              actual +=
                conditions.budgetType == EBudgetType.OPEX
                  ? (docAmount * actualSpending.exchangeRate) /
                    matchCurrencyExchangeFilter.exchangeRate
                  : 0;
            } else if (
              actualSpending.status == EStatusActualSpending.CONFIRMED &&
              actualSpending.note?.toLowerCase() == 'bud'
            ) {
              advance +=
                conditions.budgetType == EBudgetType.OPEX
                  ? (docAmount * actualSpending.exchangeRate) /
                    matchCurrencyExchangeFilter.exchangeRate
                  : 0;
            }
          }
        }
      }

      let usedBudget =
        conditions.budgetType == EBudgetType.CAPEX
          ? (approvedBudget / totalBudget) * 100
          : 0;
      let payment =
        conditions.budgetType == EBudgetType.CAPEX
          ? (totalPayment / approvedBudget) * 100
          : 0;
      let commitment =
        conditions.budgetType == EBudgetType.CAPEX
          ? approvedBudget - totalPayment
          : 0;
      let reclassifyAmount = 0;

      reponseData.push({
        budgetCode: firstBudget?.budgetCode,
        costcenterSubaccount: firstBudget?.costcenterSubaccount,
        budgets: reportBudgetDetails.results[i].budgets,
        beginBudget,
        addedBudget,
        adjustBudget,
        totalBudget,
        approvedBudget,
        availableBudget,
        /// CAPEX
        poAmount,
        usedBudget,
        totalPayment,
        payment,
        commitment,
        /// OPEX
        actualPaymentPrPoFromE,
        actualPaymentPrPoExcludingE,
        reclassifyAmount,
        actual,
        advance,
      });
    }
    console.log(3321, reponseData);
    return new ResponseDto<IReponseReportBudgetDetail>(
      reponseData,
      conditions.page,
      conditions.limit,
      reportBudgetDetails.total,
    );
  }

  async exportReportBudgetDetail(
    conditions: ReportBudgetDto,
    jwtPayload: any,
    authorization: string,
  ) {
    conditions.getAll = 1;
    const data = await this.reportBudgetDetail(
      conditions,
      jwtPayload,
      authorization,
    );

    const currency = await this.currencyUnitUsecases.getCurrencyUnitVND();

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          conditions.budgetType == EBudgetType.OPEX
            ? '../domain/template/export/template-export-report-opex-detail.xlsx'
            : '../domain/template/export/template-export-report-capex-detail.xlsx',
        ),
      );

      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      for (
        let rowNumber = 1;
        rowNumber <= (conditions.budgetType == EBudgetType.OPEX ? 2 : 1);
        rowNumber++
      ) {
        const sourceRow = sourceWorksheet.getRow(rowNumber);
        const targetRow = targetWorksheet.getRow(rowNumber);

        sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const targetCell = targetRow.getCell(colNumber);
          targetCell.value = cell.value; // Copy value
          targetCell.style = { ...cell.style }; // Copy full style
          targetCell.border = cell.border; // Copy border
          targetCell.font = cell.font; // Copy font
          targetCell.alignment = cell.alignment; // Copy alignment
          targetCell.numFmt = cell.numFmt; // Copy number format
        });

        targetRow.commit(); // Ghi các thay đổi của row vào sheet
      }

      const items = await this.toReportBudgetDetailModel(
        data?.results || [],
        conditions.budgetType,
        currency,
        conditions.eCurrency,
      );
      for (let i = 0; i < items.length; i++) {
        const targetRow = targetWorksheet.getRow(
          i + (conditions.budgetType == EBudgetType.OPEX ? 3 : 2),
        );

        Object.values(items[i]).forEach((value: any, colIndex) => {
          const targetCell = targetRow.getCell(colIndex + 1);
          const sourceCell = sourceWorksheet.getRow(2).getCell(colIndex + 1); // Lấy format từ dòng 2

          let finalValue = value; // Giá trị thực tế
          let numFmt = sourceCell.numFmt; // Format Excel

          // Nếu giá trị rỗng, giữ nguyên ''
          if (value === '' || value === null || value === undefined) {
            finalValue = '';
          } else if (sourceCell.numFmt) {
            if (
              sourceCell.numFmt.includes('yyyy') ||
              sourceCell.numFmt.includes('mm') ||
              sourceCell.numFmt.includes('dd')
            ) {
              // Nếu là ngày, giữ đúng kiểu Date
              if (value instanceof Date) {
                finalValue = value;
              } else {
                const parsedDate = new Date(value);
                finalValue = isNaN(parsedDate.getTime()) ? '' : parsedDate;
              }
              numFmt = 'dd/mm/yyyy'; // Format ngày
            } else {
              // Nếu là số hoặc tiền tệ, ép kiểu về number nếu có thể
              const numericValue = Number(value);
              if (!isNaN(numericValue)) {
                finalValue = numericValue;
              }
            }
          }

          targetCell.value = finalValue; // Gán giá trị vào ô

          if (numFmt) {
            targetCell.numFmt = numFmt; // Áp dụng định dạng Excel
          }
        });

        targetRow.commit();
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        conditions.budgetType == EBudgetType.OPEX
          ? 'report-opex-detail.xlsx'
          : 'report-capex-detail.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }

    return data;
  }

  private async toReportBudgetDetailModel(
    items: IReponseReportBudgetDetail[],
    type: EBudgetType,
    currency: CurrencyUnitModel,
    eCurrency: EFilterCurrencyReportBudget,
  ) {
    const datas = [];
    for (const data of items) {
      const firstBudget = data.budgets[0];
      if (type == EBudgetType.CAPEX) {
        datas.push({
          currencyCode:
            eCurrency == EFilterCurrencyReportBudget.VND
              ? currency?.currencyCode || ''
              : firstBudget.currencyUnit?.currencyCode || '',
          businessOwner: data.budgetCode?.businessOwner?.name || '',
          division: data.costcenterSubaccount?.note2 || '',
          industry: data.costcenterSubaccount?.note3 || '',
          bu: data.costcenterSubaccount?.note4 || '',
          costCenter: data.costcenterSubaccount?.code || '',
          budgetCode: data.budgetCode?.code || '',
          items:
            firstBudget?.budgetCapex.budgetInvestments[0]?.investment || '',
          type: firstBudget?.budgetCapex?.classify || '',
          beginBudget: data.beginBudget || 0,
          addedBudget: data.addedBudget || 0,
          adjustBudget: data.adjustBudget || 0,
          totalBudget: data.totalBudget || 0,
          poAmount: data.poAmount || 0,
          approvedBudget: data.approvedBudget || 0,
          usedBudget: data.usedBudget || 0,
          totalPayment: data.totalPayment || 0,
          payment: data.payment || 0,
          commitment: data.commitment || 0,
          availableBudget: data.availableBudget || 0,
          note: firstBudget?.note || '',
          keyProject: firstBudget?.note2 || '',
          acutal: data.actual || 0,
          advance: data.advance || 0,
        });
      }

      if (type == EBudgetType.OPEX) {
        datas.push({
          currencyCode: currency?.currencyCode || '',
          businessOwner: data.budgetCode?.businessOwner?.name || '',
          budgetOwner: data.budgetCode?.businessOwner?.description || '',
          division: data.costcenterSubaccount?.note2 || '',
          costCenter: data.costcenterSubaccount?.code || '',
          descriptionCostCenter: data.costcenterSubaccount?.description || '',
          budgetCode: data.budgetCode?.code || '',
          expenseGroup: data.budgetCode?.cost?.code || '',
          description: firstBudget?.budgetOpex?.operations || '',
          beginBudget: data.beginBudget || 0,
          addedBudget: data.addedBudget || 0,
          adjustBudget: data.adjustBudget || 0,
          totalBudget: data.totalBudget || 0,
          approvedBudget: data.approvedBudget || 0,
          actualPaymentPrPoFromE: data.actualPaymentPrPoFromE || 0,
          actualPaymentPrPoExcludingE: data.actualPaymentPrPoExcludingE || 0,
          reclassifyAmount: data.reclassifyAmount || 0,
          availableBudget: data.availableBudget || 0,
          acutal: data.actual || 0,
          advance: data.advance || 0,
        });
      }
    }

    return datas;
  }

  async reportBudgetOverview(
    conditions: ReportBudgetDto,
    jwtPayload: any,
    authorization: string,
  ) {
    const from = moment(conditions.period, 'YYYY-MM')
      .startOf('year')
      .format('YYYY-MM-DD');
    const to = moment(conditions.period, 'YYYY-MM')
      .endOf('month')
      .format('YYYY-MM-DD');

    conditions.from = from;
    conditions.to = to;

    const reponseData: IReponseReportBudgetOverview[] = [];
    const reportBudgetOverview =
      await this.budgetRepository.reportBudgetOverview(conditions, jwtPayload);

    const budgetCodeIds = [
      ...new Set(
        reportBudgetOverview.results.flatMap((item) =>
          item.budgets.map((subItem) => subItem.budgetCodeId),
        ),
      ),
    ];
    const costCenterIds = [
      ...new Set(
        reportBudgetOverview.results.flatMap((item) =>
          item.budgets.map((subItem) => subItem.costcenterSubaccountId),
        ),
      ),
    ];

    const conditionsForActualSpending: GetActualSpendingListDto = {
      period: conditions.period,
      page: 1,
      limit: 10,
      searchString: '',
      getAll: 1,
      costCenterIds,
      budgetCodeIds,
      budgetCodeType: conditions.budgetType,
    };
    const actualSpendings =
      costCenterIds?.length && budgetCodeIds?.length
        ? await this.actualSpendingRepository.getListActualSpending(
            conditionsForActualSpending,
            jwtPayload,
          )
        : null;

    const adjustBudgets: BudgetModel[] = [];
    for (let i = 0; i < reportBudgetOverview.results.length; i++) {
      for (let y = 0; y < reportBudgetOverview.results[i].budgets.length; y++) {
        if (
          reportBudgetOverview.results[i].budgets[y].createType ==
            EBudgetCreateType.INCREASE ||
          reportBudgetOverview.results[i].budgets[y].createType ==
            EBudgetCreateType.DECREASE
        ) {
          adjustBudgets.push(reportBudgetOverview.results[i].budgets[y]);
        }
      }
    }
    const adjustBudgetIds = [...new Set(adjustBudgets.map((item) => item.id))];

    const [poDetailsReponse, poDetailsAdjustBudgetReponse] = await Promise.all([
      costCenterIds?.length && budgetCodeIds?.length
        ? await this.purchaseOrderRepository.findPoReportBudget(
            {
              effectiveStart: new Date(conditions.from),
              effectiveEnd: new Date(conditions.to),
              statusPo: [Status.Approved],
              // isAccountantApproved: 'true',
              costCenterIds: costCenterIds,
              budgetCodeIds: budgetCodeIds,
            },
            authorization,
          )
        : null,
      adjustBudgetIds?.length
        ? await this.purchaseOrderRepository.findAdjustBudgetIdInPoDetail(
            adjustBudgetIds,
          )
        : null,
    ]);

    const poDetails = poDetailsReponse || [];
    const poDetailsAdjustBudgetIds = (poDetailsAdjustBudgetReponse || []).map(
      (item) => item.adjustBudgetId,
    );

    ///  Curency từ PO và Filter
    ///  Curency từ PO và Filter
    const currencyUnitIds = [];
    const poDetailCurrencyUnitIds = poDetails
      .map((item) => item?.purchaseOrder?.currency)
      .filter(Boolean);
    const budgetCurrencyUnitIds = (reportBudgetOverview.results || [])
      .flatMap((data) => data.budgets)
      .map((item) => item.currencyUnitId);
    currencyUnitIds.push(...poDetailCurrencyUnitIds, ...budgetCurrencyUnitIds);

    const conditionsCurrency: GetCurrencyUnitListDto = {
      ids: [...new Set(currencyUnitIds)],
      getAll: 1,
      page: 1,
      limit: 10,
      searchString: '',
    };
    const currencyUnits = await this.currencyUnitUsecases.getCurrencyUnits(
      conditionsCurrency,
      jwtPayload,
    );

    let currency = await this.currencyUnitUsecases.getCurrencyUnitVND();

    for (let i = 0; i < reportBudgetOverview.results.length; i++) {
      const firstBudget = reportBudgetOverview.results[i]?.budgets[0];
      if (conditions.eCurrency == EFilterCurrencyReportBudget.ORIGINAL) {
        currency = firstBudget?.currencyUnit;
      }

      if (!currency) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1013()),
          HttpStatus.NOT_FOUND,
        );
      }

      const budgetCodeIds: string[] = reportBudgetOverview.results[
        i
      ]?.budgets.map((item) => item.budgetCodeId);

      const budgetCodeCodes: string[] = reportBudgetOverview.results[i]?.budgets
        .map((item) => item.budgetCode?.code)
        .filter(Boolean);

      const costCenterIds: string[] = reportBudgetOverview.results[
        i
      ]?.budgets.map((item) => item.costcenterSubaccountId);

      const matchPoDetails = poDetails.filter(
        (item) =>
          budgetCodeIds.includes(item.budgetCodeId) &&
          costCenterIds.includes(item.costCenterId),
      );

      const matchActualSpendings =
        actualSpendings?.results?.filter(
          (item) =>
            budgetCodeCodes.includes(item.budgetCode?.code) &&
            costCenterIds.includes(item.costCenter?.id),
        ) || [];

      // Logic lấy tỷ giá theo ngân sách)
      let beginBudget = 0;
      let addedBudget = 0;
      let adjustBudget = 0;
      let budget = 0;
      for (const budgetItem of reportBudgetOverview.results[i].budgets) {
        const matchCurrencyExchangeBudget = (
          (currencyUnits.results || []).find(
            (item) => item.id == budgetItem.currencyUnitId,
          )?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              budgetItem.effectiveStartDate,
              item.effectiveStartDate,
              budgetItem.effectiveEndDate,
              item.effectiveEndDate,
            ) && item.exchangeBudget,
        );

        const matchCurrencyExchangeFilter = (
          currency?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              budgetItem.effectiveStartDate,
              item.effectiveStartDate,
              budgetItem.effectiveEndDate,
              item.effectiveEndDate,
            ) && item.exchangeBudget,
        );

        if (!matchCurrencyExchangeBudget || !matchCurrencyExchangeFilter) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1103()),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (budgetItem.createType == EBudgetCreateType.NEW) {
          beginBudget +=
            conditions.budgetType == EBudgetType.OPEX
              ? 0
              : (Number(budgetItem.totalValue) *
                  matchCurrencyExchangeBudget.exchangeRate) /
                matchCurrencyExchangeFilter.exchangeRate;
        }

        if (
          (budgetItem.createType == EBudgetCreateType.INCREASE ||
            budgetItem.createType == EBudgetCreateType.DECREASE) &&
          poDetailsAdjustBudgetIds.includes(budgetItem.id) &&
          conditions.budgetType != EBudgetType.OPEX
        ) {
          if (budgetItem.createType == EBudgetCreateType.INCREASE) {
            addedBudget +=
              ((budgetItem.totalValue || 0) *
                matchCurrencyExchangeBudget.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }
          if (budgetItem.createType == EBudgetCreateType.DECREASE) {
            addedBudget -=
              ((budgetItem.totalValue || 0) *
                matchCurrencyExchangeBudget.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }
        }

        if (
          (budgetItem.createType == EBudgetCreateType.INCREASE ||
            budgetItem.createType == EBudgetCreateType.DECREASE) &&
          !poDetailsAdjustBudgetIds.includes(budgetItem.id) &&
          conditions.budgetType != EBudgetType.OPEX
        ) {
          if (budgetItem.createType == EBudgetCreateType.INCREASE) {
            adjustBudget +=
              ((budgetItem.totalValue || 0) *
                matchCurrencyExchangeBudget.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }
          if (budgetItem.createType == EBudgetCreateType.DECREASE) {
            adjustBudget -=
              ((budgetItem.totalValue || 0) *
                matchCurrencyExchangeBudget.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }
        }

        if (conditions.budgetType == EBudgetType.OPEX) {
          budget +=
            ((budgetItem.totalValue || 0) *
              matchCurrencyExchangeBudget.exchangeRate) /
            matchCurrencyExchangeFilter.exchangeRate;
        }
      }

      // Logic lấy tỷ giá theo ngày duyệt PO
      let approvedBudget = 0;
      for (const poDetail of matchPoDetails) {
        const lastLevelHistory = (poDetail?.purchaseOrder?.history || []).sort(
          (a, b) => b.level - a.level,
        )[0];

        const matchCurrencyExchangeFilter = (
          currency?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              new Date(lastLevelHistory?.updatedAt),
              item.effectiveStartDate,
              null,
              item.effectiveEndDate,
            ) && item.exchangeBudget == false,
        );

        if (!matchCurrencyExchangeFilter) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1103()),
            HttpStatus.BAD_REQUEST,
          );
        }
        if (
          poDetail?.purchaseOrder?.statusPo == 'Approved' &&
          conditions.budgetType != EBudgetType.OPEX
        ) {
          approvedBudget +=
            Number(poDetail.totalAmountVat || 0) /
            matchCurrencyExchangeFilter.exchangeRate;
        }
      }

      //  Báo cáo dữ liệu thanh toán thực tế
      let totalPayment = 0;
      let actual = 0;
      let advance = 0;
      for (const actualSpending of matchActualSpendings) {
        const matchCurrencyExchangeFilter = (
          currency?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              actualSpending.postingDate,
              item.effectiveStartDate,
              null,
              item.effectiveEndDate,
            ) && item.exchangeBudget == false,
        );

        if (!matchCurrencyExchangeFilter) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1103()),
            HttpStatus.BAD_REQUEST,
          );
        }

        let docAmount = 0;
        if (conditions.budgetType == EBudgetType.CAPEX) {
          docAmount = Number(actualSpending.docAmount || 0);
        } else {
          const taxCode = (actualSpending.taxCode || '')
            .split('')[0]
            ?.toLowerCase();
          docAmount = ['a', 'c', 'e', 'g'].includes(taxCode)
            ? Number(actualSpending.docAmount || 0)
            : Number(actualSpending.docAmount || 0) *
              (1 +
                (isNaN(Number(actualSpending.taxRate || 0))
                  ? 0
                  : Number(actualSpending.taxRate || 0) / 100));
        }

        if (actualSpending.status == EStatusActualSpending.CONFIRMED) {
          if (
            checkEffectiveTimeOverlaps(
              actualSpending.paymentDate,
              new Date(conditions.from),
              null,
              new Date(conditions.to),
            ) &&
            conditions.budgetType != EBudgetType.OPEX
          ) {
            totalPayment +=
              (actualSpending.docPaymentAmount * actualSpending.exchangeRate) /
              matchCurrencyExchangeFilter.exchangeRate;
          }

          if (
            checkEffectiveTimeOverlaps(
              actualSpending.postingDate,
              new Date(conditions.from),
              null,
              new Date(conditions.to),
            ) &&
            conditions.budgetType == EBudgetType.OPEX
          ) {
            if (actualSpending.note?.toLowerCase() == 'act') {
              actual +=
                (docAmount * actualSpending.exchangeRate) /
                matchCurrencyExchangeFilter.exchangeRate;
            } else if (actualSpending.note?.toLowerCase() == 'bud') {
              advance +=
                (docAmount * actualSpending.exchangeRate) /
                matchCurrencyExchangeFilter.exchangeRate;
            }
          }
        }
      }

      // ===== //
      let totalBudget =
        conditions.budgetType == EBudgetType.OPEX
          ? 0
          : beginBudget + addedBudget + adjustBudget;
      let commitment =
        conditions.budgetType == EBudgetType.OPEX
          ? 0
          : approvedBudget - totalPayment;
      let endBalance =
        conditions.budgetType == EBudgetType.OPEX
          ? 0
          : totalBudget - totalPayment;
      let availableBudget =
        conditions.budgetType == EBudgetType.OPEX
          ? 0
          : totalBudget - approvedBudget;

      reponseData.push({
        businessOwner: firstBudget?.budgetCode?.businessOwner,
        division: reportBudgetOverview.results[i].division,
        budgets: reportBudgetOverview.results[i].budgets,
        /// CAPEX
        beginBudget,
        addedBudget,
        adjustBudget,
        totalBudget,
        approvedBudget,
        totalPayment,
        commitment,
        endBalance,
        availableBudget,
        note: '',
        /// OPEX
        cost:
          conditions.budgetType == EBudgetType.OPEX
            ? firstBudget?.budgetCode?.cost
            : null,
        budget,
        actual,
        advance,
      });
    }

    return new ResponseDto<IReponseReportBudgetOverview>(
      reponseData,
      conditions.page,
      conditions.limit,
      reportBudgetOverview.total,
    );
  }

  async exportReportBudgetOverview(
    conditions: ReportBudgetDto,
    jwtPayload: any,
    authorization: string,
  ) {
    conditions.getAll = 1;
    const data = await this.reportBudgetOverview(
      conditions,
      jwtPayload,
      authorization,
    );

    const currency = await this.currencyUnitUsecases.getCurrencyUnitVND();

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          conditions.budgetType == EBudgetType.OPEX
            ? '../domain/template/export/template-export-report-opex-overview.xlsx'
            : '../domain/template/export/template-export-report-capex-overview.xlsx',
        ),
      );

      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      for (let rowNumber = 1; rowNumber <= 1; rowNumber++) {
        const sourceRow = sourceWorksheet.getRow(rowNumber);
        const targetRow = targetWorksheet.getRow(rowNumber);

        sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const targetCell = targetRow.getCell(colNumber);
          targetCell.value = cell.value; // Copy value
          targetCell.style = { ...cell.style }; // Copy full style
          targetCell.border = cell.border; // Copy border
          targetCell.font = cell.font; // Copy font
          targetCell.alignment = cell.alignment; // Copy alignment
          targetCell.numFmt = cell.numFmt; // Copy number format
        });

        targetRow.commit(); // Ghi các thay đổi của row vào sheet
      }

      const items = await this.toReportBudgetOverviewModel(
        data?.results || [],
        conditions.budgetType,
        currency,
        conditions.eCurrency,
      );
      for (let i = 0; i < items.length; i++) {
        const targetRow = targetWorksheet.getRow(i + 2);
        Object.values(items[i]).forEach((value: any, colIndex) => {
          const targetCell = targetRow.getCell(colIndex + 1);
          const sourceCell = sourceWorksheet.getRow(2).getCell(colIndex + 1); // Lấy format từ dòng 2

          let finalValue = value; // Giá trị thực tế
          let numFmt = sourceCell.numFmt; // Format Excel

          // Nếu giá trị rỗng, giữ nguyên ''
          if (value === '' || value === null || value === undefined) {
            finalValue = '';
          } else if (sourceCell.numFmt) {
            if (
              sourceCell.numFmt.includes('yyyy') ||
              sourceCell.numFmt.includes('mm') ||
              sourceCell.numFmt.includes('dd')
            ) {
              // Nếu là ngày, giữ đúng kiểu Date
              if (value instanceof Date) {
                finalValue = value;
              } else {
                const parsedDate = new Date(value);
                finalValue = isNaN(parsedDate.getTime()) ? '' : parsedDate;
              }
              numFmt = 'dd/mm/yyyy'; // Format ngày
            } else {
              // Nếu là số hoặc tiền tệ, ép kiểu về number nếu có thể
              const numericValue = Number(value);
              if (!isNaN(numericValue)) {
                finalValue = numericValue;
              }
            }
          }

          targetCell.value = finalValue; // Gán giá trị vào ô

          if (numFmt) {
            targetCell.numFmt = numFmt; // Áp dụng định dạng Excel
          }
        });

        targetRow.commit();
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        conditions.budgetType == EBudgetType.OPEX
          ? 'report-opex-overview.xlsx'
          : 'report-capex-overview.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }

    return data;
  }

  private async toReportBudgetOverviewModel(
    items: IReponseReportBudgetOverview[],
    type: EBudgetType,
    currency: CurrencyUnitModel,
    eCurrency: EFilterCurrencyReportBudget,
  ) {
    const datas = [];

    for (const data of items) {
      const firstBudget = data.budgets[0];

      if (type == EBudgetType.CAPEX) {
        datas.push({
          currencyCode:
            eCurrency == EFilterCurrencyReportBudget.VND
              ? currency?.currencyCode || ''
              : firstBudget.currencyUnit?.currencyCode || '',
          businessOwner: data.businessOwner?.name || '',
          division: data.division || '',
          beginBudget: data.beginBudget || 0,
          addedBudget: data.addedBudget || 0,
          adjustBudget: data.adjustBudget || 0,
          totalBudget: data.totalBudget || 0,
          approvedBudget: data.approvedBudget || 0,
          totalPayment: data.totalPayment || 0,
          commitment: data.commitment || 0,
          endBalance: data.endBalance || 0,
          availableBudget: data.availableBudget || 0,
          note: data.note || '',
          acutal: data.actual || 0,
          advance: data.advance || 0,
        });
      }

      if (type == EBudgetType.OPEX) {
        datas.push({
          currencyCode: currency?.currencyCode || '',
          businessOwner: data.businessOwner?.name || '',
          bu: data.businessOwner?.description || '',
          division: data.division || '',
          expenseGroup: data.cost?.groupCost || '',
          budget: data.budget || 0,
          actual: data.actual || 0,
          advance: data.advance || 0,
        });
      }
    }

    return datas;
  }

  async import(
    body: ImportOpexDto | ImportCapexDto,
    type: EFileImportType,
    jwtPayload?: IAuthUserPayload,
    authorization?: string,
  ) {
    switch (type) {
      case EFileImportType.OPEX:
        const dataOpex = body as ImportOpexDto;
        for (let i = 0; i < (dataOpex.dataOpexes?.length ?? 0); i++) {
          if (dataOpex.dataOpexes[i].isNeedGen) {
            const budget = await this.createBudgetOpex(
              {
                ...dataOpex.dataOpexes[i],
                budgetData: {
                  ...dataOpex.dataOpexes[i].budgetData,
                  totalValue: 0,
                  createType: EBudgetCreateType.NEW,
                },
              },
              jwtPayload,
              true,
            );
            dataOpex.dataOpexes[i].budgetData.adjustBudgetId = budget?.id;
          }
          await this.createBudgetOpex(dataOpex.dataOpexes[i], jwtPayload, true);
        }
        break;
      case EFileImportType.CAPEX:
        const dataCapex = body as ImportCapexDto;
        for (let i = 0; i < (dataCapex.dataCapexes?.length ?? 0); i++) {
          if (dataCapex.dataCapexes[i].isNeedGen) {
            const budget = await this.createBudgetCapex(
              {
                ...dataCapex.dataCapexes[i],
                budgetData: {
                  ...dataCapex.dataCapexes[i].budgetData,
                  totalValue: 0,
                  createType: EBudgetCreateType.NEW,
                },
              },
              jwtPayload,
              true,
            );
            dataCapex.dataCapexes[i].budgetData.adjustBudgetId = budget?.id;
          }
          await this.createBudgetCapex(
            dataCapex.dataCapexes[i],
            jwtPayload,
            true,
          );
        }
        break;
    }
  }

  async importBudgetTransfer(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    budgetType: EBudgetType,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType:
          budgetType == EBudgetType.OPEX
            ? EFileImportType.OPEX
            : EFileImportType.CAPEX,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createBudgetOpexDtos: CreateBudgetOpexDto[] = [];
        const createBudgetCapexDtos: CreateBudgetCapexDto[] = [];

        let errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0].actualRowCount,
          ) ?? [];

        const budgetCodeCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetTransfer.BUDGET_CODE_CODE_1,
                )?.toString(),
              )
              ?.slice(1)
              ?.filter(Boolean),
          ),
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetTransfer.BUDGET_CODE_CODE_2,
                )?.toString(),
              )
              ?.slice(1)
              ?.filter(Boolean),
          ),
        ];

        const currencyCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetTransfer.CURRENCY_CODE,
                )?.toString(),
              )
              ?.slice(1)
              ?.filter(Boolean),
          ),
        ];

        const costcenterSubaccountCodes = [
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetTransfer.COST_CENTER_CODE_1,
                )?.toString(),
              )
              ?.slice(1)
              ?.filter(Boolean),
          ),
          ...new Set(
            rows
              .map((item) =>
                getValueOrResult(
                  item,
                  EColumnImportBudgetTransfer.COST_CENTER_CODE_2,
                )?.toString(),
              )
              ?.slice(1)
              ?.filter(Boolean),
          ),
        ];

        const effectiveStartDates = [
          ...new Set(
            rows
              .map((item) =>
                item.getCell(EColumnImportBudgetTransfer.EFFECTIVE_START_DATE)
                  .value
                  ? moment(
                      excelSerialToDate(
                        item.getCell(
                          EColumnImportBudgetTransfer.EFFECTIVE_START_DATE,
                        ).value as string | number,
                      ),
                    ).format('YYYY-MM-DD')
                  : null,
              )
              ?.slice(1)
              ?.filter((item) => item != 'Invalid date'),
          ),
        ];

        const [budgetCodes, adjustBudgets, costcenterSubaccounts, currencys] =
          await Promise.all([
            this.budgetCodeRepository.getBudgetCodesByCodesWithRole(
              budgetCodeCodes,
              jwtPayload,
            ),
            this.budgetRepository.getBudgetsForImportAdjustBudgetWithRole(
              budgetCodeCodes,
              costcenterSubaccountCodes,
              effectiveStartDates,
              jwtPayload,
              budgetType,
            ),
            this.costcenterSubaccountRepository.getCostcenterSubaccountsByCodesWithRole(
              costcenterSubaccountCodes,
              jwtPayload,
            ),
            this.currencyUnitRepository.getCurrencyUnitsByCodesWithRole(
              currencyCodes,
              jwtPayload,
            ),
          ]);

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          //Thông tin chung của ngân sách
          const budgetCodeCode1 = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.BUDGET_CODE_CODE_1,
          )?.toString(); //Code ngân sách
          const budgetCodeCode2 = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.BUDGET_CODE_CODE_2,
          )?.toString(); //Code ngân sách
          const costcenterSubaccountCode1 = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.COST_CENTER_CODE_1,
          )?.toString();
          const costcenterSubaccountCode2 = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.COST_CENTER_CODE_2,
          )?.toString();
          const effectiveStartDate = row.getCell(
            EColumnImportBudgetTransfer.EFFECTIVE_START_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportBudgetTransfer.EFFECTIVE_START_DATE)
                    .value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian bắt đầu
          const effectiveEndDate = row.getCell(
            EColumnImportBudgetTransfer.EFFECTIVE_END_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportBudgetTransfer.EFFECTIVE_END_DATE)
                    .value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian bắt đầu
          const operationInvestment1 = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.OPERATION_INVESTMENT_1,
          )?.toString(); //Diễn giải/Nghiệp vụ
          const operationInvestment2 = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.OPERATION_INVESTMENT_2,
          )?.toString(); //Diễn giải/Nghiệp vụ
          const totalValue = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.TOTAL_VALUE,
            true,
          ); //Số tiền (nguyên tệ)
          const currencyCode = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.CURRENCY_CODE,
          )?.toString(); //Đơn vị tiền tệ
          const note = getValueOrResult(
            row,
            EColumnImportBudgetTransfer.NOTE,
          )?.toString(); //Ghi chú
          const isLock = false;

          ///Kiểm tra format ngày
          if (!effectiveStartDate || effectiveStartDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(errorMessage.E_1075()),
              row: i + 1,
            });
          }

          ///Kiểm tra format ngày
          if (!effectiveEndDate || effectiveEndDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(errorMessage.E_1076()),
              row: i + 1,
            });
          }

          if (
            budgetType == EBudgetType.CAPEX &&
            (!operationInvestment1 || !operationInvestment2)
          ) {
            errors.push({
              error: getErrorMessage(errorMessage.E_1067()),
              row: i + 1,
            });
          }

          const [verifyDataData1, verifyDataData2] = await Promise.all([
            this.verifyAdjustBudgetDataImport(
              {
                createType: 'giảm ngân sách',
                budgetType,
                budgetCodeCode: budgetCodeCode1,
                note,
                costcenterSubaccountCode: costcenterSubaccountCode1,
                effectiveStartDate,
                effectiveEndDate,
                status: EBudgetStatus.ACTIVE,
                totalValue,
                operationInvestment: operationInvestment1,
                currencyUnitCode: currencyCode,
              },
              i + 1,
              budgetCodes,
              adjustBudgets,
              costcenterSubaccounts,
              currencys,
            ),
            this.verifyAdjustBudgetDataImport(
              {
                createType: 'tăng ngân sách',
                budgetType,
                budgetCodeCode: budgetCodeCode2,
                note,
                costcenterSubaccountCode: costcenterSubaccountCode2,
                effectiveStartDate,
                effectiveEndDate,
                status: EBudgetStatus.ACTIVE,
                totalValue,
                operationInvestment: operationInvestment2,
                currencyUnitCode: currencyCode,
              },
              i + 1,
              budgetCodes,
              adjustBudgets,
              costcenterSubaccounts,
              currencys,
            ),
          ]);

          errors.push(...verifyDataData1.errors, ...verifyDataData2.errors);

          const timeObject = {
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };
          const generalBudgetData = {
            effectiveStartDate,
            effectiveEndDate,
            note,
            status: EBudgetStatus.ACTIVE,
            totalValue,
            isLock,
            ...timeObject,
            rowNumber: i + 1,
          };

          if (budgetType == EBudgetType.OPEX) {
            const budgetOpexObject1 = {
              ...(verifyDataData1?.budgetDataVerify?.budgetOpex || {}),
              id: undefined,
              budgetData: {
                budgetType,
                ...(verifyDataData1?.budgetDataVerify || {}),
                id: undefined,
                budgetOpexId: undefined,
                budgetOpex: undefined,
                ...generalBudgetData,
              },
              ...timeObject,
            };

            const budgetOpexObject2 = {
              ...(verifyDataData2?.budgetDataVerify?.budgetOpex || {}),
              id: undefined,
              budgetData: {
                budgetType,
                ...(verifyDataData2?.budgetDataVerify || {}),
                id: undefined,
                budgetOpexId: undefined,
                budgetOpex: undefined,
                ...generalBudgetData,
              },
              ...timeObject,
            };

            const budgetOpexDto1 = plainToInstance(CreateBudgetOpexDto, {
              ...budgetOpexObject1,
              isNeedGen: verifyDataData1?.budgetDataVerify?.isNeedGen || false,
            });
            const budgetOpexDto2 = plainToInstance(CreateBudgetOpexDto, {
              ...budgetOpexObject2,
              isNeedGen: verifyDataData2?.budgetDataVerify?.isNeedGen || false,
            });

            createBudgetOpexDtos.push(...[budgetOpexDto1, budgetOpexDto2]);
          } else {
            const budgetCapexObject1 = {
              ...(verifyDataData1?.budgetDataVerify?.budgetCapex || {}),
              id: undefined,
              investments: (
                verifyDataData1?.budgetDataVerify?.budgetCapex
                  ?.budgetInvestments || []
              ).map((item) => {
                return {
                  ...item,
                  id: undefined,
                  budgetCapexId: undefined,
                };
              }),
              budgetInvestments: undefined,
              budgetData: {
                budgetType,
                ...(verifyDataData1.budgetDataVerify || {}),
                id: undefined,
                budgetCapex: undefined,
                budgetCapexId: undefined,
                ...generalBudgetData,
              },
              ...timeObject,
            };
            const budgetCapexObject2 = {
              ...(verifyDataData2?.budgetDataVerify?.budgetCapex || {}),
              id: undefined,
              investments: (
                verifyDataData2?.budgetDataVerify?.budgetCapex
                  ?.budgetInvestments || []
              ).map((item) => {
                return {
                  ...item,
                  id: undefined,
                  budgetCapexId: undefined,
                };
              }),
              budgetInvestments: undefined,
              budgetData: {
                budgetType,
                ...(verifyDataData2.budgetDataVerify || {}),
                id: undefined,
                budgetCapex: undefined,
                budgetCapexId: undefined,
                ...generalBudgetData,
              },
              ...timeObject,
            };

            const budgetCapexDto1 = plainToInstance(CreateBudgetCapexDto, {
              ...budgetCapexObject1,
              isNeedGen: verifyDataData1?.budgetDataVerify?.isNeedGen || false,
            });
            const budgetCapexDto2 = plainToInstance(CreateBudgetCapexDto, {
              ...budgetCapexObject2,
              isNeedGen: verifyDataData2?.budgetDataVerify?.isNeedGen || false,
            });

            createBudgetCapexDtos.push(...[budgetCapexDto1, budgetCapexDto2]);
          }
        }

        let findDuplicateBudgets:
          | CreateBudgetOpexDto[]
          | CreateBudgetCapexDto[];
        if (budgetType == EBudgetType.OPEX) {
          findDuplicateBudgets = checkDuplicateBudget(
            (createBudgetOpexDtos as CreateBudgetOpexDto[])
              .filter((item) => item.isNeedGen)
              .filter(Boolean),
            EBudgetType.OPEX,
          );
        } else if (budgetType == EBudgetType.CAPEX) {
          findDuplicateBudgets = checkDuplicateBudget(
            (createBudgetCapexDtos as CreateBudgetCapexDto[])
              .filter((item) => item.isNeedGen)
              .filter(Boolean),
            EBudgetType.CAPEX,
          );
        }

        if (findDuplicateBudgets?.length) {
          for (let i = 0; i < findDuplicateBudgets.length || 0; i++) {
            errors.push({
              error: budgetErrorDetails.E_6002(),
              row: findDuplicateBudgets[i]?.budgetData?.rowNumber || 0,
            });
          }
        }

        errors = mergedErrors(errors);

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (budgetType == EBudgetType.OPEX) {
          const importBody: ImportOpexDto = {
            dataOpexes: createBudgetOpexDtos,
            fileImportHistoryId: fileImportHistory.id,
          };

          await this.import(importBody, EFileImportType.OPEX);
        } else {
          const importBody: ImportCapexDto = {
            dataCapexes: createBudgetCapexDtos,
            fileImportHistoryId: fileImportHistory.id,
          };

          await this.import(importBody, EFileImportType.CAPEX);
        }

        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.SUCCESS,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        // throw new HttpException(
        //   importErrorDetails.E_5000(),
        //   HttpStatus.BAD_REQUEST,
        // );

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
