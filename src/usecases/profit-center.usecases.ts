import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { CreateProfitCenterDto } from '../controller/profit-center/dtos/create-profit-center.dto';
import { GetDetailProfitCenterDto } from '../controller/profit-center/dtos/get-detail-profit-center.dto';
import { GetProfitCenterListDto } from '../controller/profit-center/dtos/get-profit-center-list.dto';
import { UpdateProfitCenterDto } from '../controller/profit-center/dtos/update-profit-center.dto';
import { ResponseDto } from '../domain/dtos/response.dto';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { ProfitCenterModel } from '../domain/model/profit-center.model';
import { IProfitCenterRepository } from '../domain/repositories/profit-center.repository';
import { profitCenterErrorDetails } from '../domain/messages/error-details/profit-center';

@Injectable()
export class ProfitCenterUsecases {
  constructor(
    @Inject(IProfitCenterRepository)
    private readonly profitCenterRepository: IProfitCenterRepository,
  ) {}

  async createProfitCenter(
    data: CreateProfitCenterDto,
  ): Promise<ProfitCenterModel> {
    await this.checkCodeProfitCenter(data.code);

    const profitCenterModel = new ProfitCenterModel({
      ...data,
    });
    const profitCenter =
      await this.profitCenterRepository.createProfitCenter(profitCenterModel);

    return profitCenter;
  }

  async updateProfitCenter(data: UpdateProfitCenterDto, id: string) {
    await this.checkCodeProfitCenter(data.code, id);
    await this.getDetailProfitCenter(
      plainToInstance(GetDetailProfitCenterDto, {
        id: id,
      }),
    );

    const profitCenterData = new ProfitCenterModel({
      id,
      ...data,
    });

    const profitCenter =
      await this.profitCenterRepository.updateProfitCenter(profitCenterData);

    return profitCenter;
  }

  async deleteProfitCenter(id: string): Promise<void> {
    await this.getDetailProfitCenter(
      plainToInstance(GetDetailProfitCenterDto, {
        id: id,
      }),
    );

    await this.profitCenterRepository.deleteProfitCenter(id);
  }

  async getProfitCenters(
    data: GetProfitCenterListDto,
  ): Promise<ResponseDto<ProfitCenterModel>> {
    return await this.profitCenterRepository.getProfitCenters(data);
  }

  async getDetailProfitCenter(
    conditions: GetDetailProfitCenterDto,
  ): Promise<ProfitCenterModel> {
    const detail =
      await this.profitCenterRepository.getDetailProfitCenter(conditions);

    if (!detail) {
      throw new HttpException(
        profitCenterErrorDetails.E_2550(),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async checkCodeProfitCenter(code: string, id?: string) {
    const checkCode = await this.profitCenterRepository.getProfitCenterByCode(
      code,
      id,
    );

    if (checkCode) {
      throw new HttpException(
        profitCenterErrorDetails.E_2551(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
