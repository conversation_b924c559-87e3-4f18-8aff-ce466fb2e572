import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { CreateBusinessOwnerDto } from '../controller/business-owner/dtos/create-business-owner.dto';
import { GetBusinessOwnerListDto } from '../controller/business-owner/dtos/get-business-owner-list.dto';
import { GetDetailBusinessOwnerDto } from '../controller/business-owner/dtos/get-detail-business-owner.dto';
import { UpdateBusinessOwnerDto } from '../controller/business-owner/dtos/update-business-owner.dto';
import { ImportBusinessOwnerDto } from '../controller/import/dtos/import-business-owner.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EBusinessOwnerStatus,
  EColumnImportBusinessOwner,
} from '../domain/config/enums/business-owner.enum';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { BusinessOwnerModel } from '../domain/model/business-owner.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { IBusinessOwnerRepository } from '../domain/repositories/business-owner.repository';
import {
  checkValuesEmptyRowExcel,
  getStatus,
  getStatusBusinessOwner,
  getValueOrResult,
} from '../utils/common';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';

@Injectable()
export class BusinessOwnerUsecases {
  constructor(
    @Inject(IBusinessOwnerRepository)
    private readonly businessOwnerRepository: IBusinessOwnerRepository,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
  ) {}
  async createBusinessOwner(
    data: CreateBusinessOwnerDto,
    authorization: string,
  ): Promise<BusinessOwnerModel> {
    await this.checkExistBusinessOwnerByCode(data.code);

    const businessOwnerModel = new BusinessOwnerModel({
      code: data.code,
      name: data.name,
      description: data.description,
      status: data.status,
    });

    const businessOwner =
      await this.businessOwnerRepository.createBusinessOwner(
        businessOwnerModel,
      );

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: businessOwner.name,
        refId: businessOwner.id,
        refCode: businessOwner.code,
        type: EDataRoleType.BUSINESS_OWNER,
        isEnabled: businessOwner.status === EBusinessOwnerStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      {
        authorization,
      },
    );

    return businessOwner;
  }

  async updateBusinessOwner(
    id: string,
    updateBusinessOwnerDto: UpdateBusinessOwnerDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<BusinessOwnerModel> {
    await this.checkExistBusinessOwnerByCode(updateBusinessOwnerDto.code, id);

    await this.getDetailBusinessOwner(
      plainToInstance(GetDetailBusinessOwnerDto, {
        id: id,
      }),
      jwtPayload,
    );

    const businessOwner =
      await this.businessOwnerRepository.updateBusinessOwner(
        id,
        updateBusinessOwnerDto,
      );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: businessOwner.name,
        refCode: businessOwner.code,
        isEnabled: businessOwner.status === EBusinessOwnerStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    return await this.getBusinessOwnerById(id);
  }

  async getBusinessOwnerById(id: string): Promise<BusinessOwnerModel> {
    const businessOwner =
      await this.businessOwnerRepository.getBusinessOwnerById(id);

    if (!businessOwner) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1020()),
        HttpStatus.BAD_REQUEST,
      );
    }

    return businessOwner;
  }

  async getBusinessOwners(
    conditions: GetBusinessOwnerListDto,
    jwtPayload,
  ): Promise<ResponseDto<BusinessOwnerModel>> {
    return await this.businessOwnerRepository.getBusinessOwners(
      conditions,
      jwtPayload,
    );
  }

  async deleteBusinessOwner(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailBusinessOwner(
      plainToInstance(GetDetailBusinessOwnerDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.businessOwnerRepository.deleteBusinessOwner(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistBusinessOwnerByCode(
    code: string,
    id?: string,
  ): Promise<void> {
    const businessOwner =
      await this.businessOwnerRepository.getBusinessOwnerByCode(code, id);

    if (businessOwner) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1021()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getBusinessOwnerByIds(
    ids: string[],
    jwtPayload: any,
  ): Promise<BusinessOwnerModel[]> {
    const businessOwners =
      await this.businessOwnerRepository.getBusinessOwnerByIds(ids, jwtPayload);

    if (!businessOwners || !businessOwners.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1020()),
        HttpStatus.BAD_REQUEST,
      );
    }

    const activeBusinessOwners = businessOwners?.filter(
      (item) => item.status === EBusinessOwnerStatus.ACTIVE,
    );

    const activeBusinessOwnerIds = activeBusinessOwners.map((item) => item.id);

    const missingBusinessOwnerIds = _.difference(ids, activeBusinessOwnerIds);

    if (missingBusinessOwnerIds.length) {
      throw new HttpException(
        getErrorMessage(
          errorMessage.E_1020(
            `Business owner ids ${missingBusinessOwnerIds.join(', ')} not found or inactive`,
          ),
        ),
        HttpStatus.NOT_FOUND,
      );
    }
    return businessOwners;
  }

  async getBusinessOwnerByCode(code: string): Promise<BusinessOwnerModel> {
    const businessOwner =
      await this.businessOwnerRepository.getBusinessOwnerByCode(code);

    return businessOwner;
  }

  async getBusinessOwnerByCodes(
    codes: string[],
  ): Promise<BusinessOwnerModel[]> {
    const businessOwners =
      await this.businessOwnerRepository.getBusinessOwnerByCodes(codes);

    return businessOwners;
  }

  async getDetailBusinessOwner(
    conditions: GetDetailBusinessOwnerDto,
    jwtPayload: any,
  ): Promise<BusinessOwnerModel> {
    const detail = await this.businessOwnerRepository.getDetailBusinessOwner(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1020()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async exportBusinessOwner(
    conditions: GetBusinessOwnerListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data = await this.businessOwnerRepository.getBusinessOwners(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-business-owner.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toBusinessOwnerModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-business-owner.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toBusinessOwnerModel(businessOwners: BusinessOwnerModel[]) {
    const items = [];

    for (let i = 0; i < businessOwners.length; i++) {
      items.push({
        code: businessOwners[i].code || '',
        name: businessOwners[i].name || '',
        description: businessOwners[i].description || '',
        status: getStatusBusinessOwner(businessOwners[i].status),
      });
    }

    return items;
  }

  async importBusinessOwner(
    file: Express.Multer.File,
    jwtPayload: any,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.BUSINESS_OWNER,
      });

      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createBusinessOwnerDtos: CreateBusinessOwnerDto[] = [];
        const updateBusinessOwnerDtos: UpdateBusinessOwnerDto[] = [];
        const errors: TErrorMessageImport[] = [];

        let totalRowHasValue = 0;

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportBusinessOwner.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const businessOwners =
          await this.businessOwnerRepository.getBusinessOwnersByCodesWithRole(
            codes,
            jwtPayload,
            false,
          );

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportBusinessOwner.CODE, // First Cell
            EColumnImportBusinessOwner.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          const code = getValueOrResult(
            row,
            EColumnImportBusinessOwner.CODE,
          )?.toString();

          const name = getValueOrResult(
            row,
            EColumnImportBusinessOwner.NAME,
          )?.toString();

          const description = getValueOrResult(
            row,
            EColumnImportBusinessOwner.DESCRIPTION,
          )?.toString();

          const status = getStatus(
            BusinessOwnerModel,
            getValueOrResult(
              row,
              EColumnImportBusinessOwner.STATUS,
            )?.toString(),
          );

          const businessOwnerObject = {
            id: undefined,
            code,
            name,
            description,
            status,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };

          if (!code) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1087(),
                'Mã khối chủ sở hữu không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const businessOwner = businessOwners.find(
              (item) => item.code === code,
            );
            if (businessOwner) {
              businessOwnerObject.id = businessOwner.id;
            }

            const checkDup = codes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1089(),
                  'Business Owner bị trùng lặp',
                ),
                row: i + 1,
              });
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1088(),
                'Tên khối chủ sở hữu không được để trống',
              ),
              row: i + 1,
            });
          }

          if (businessOwnerObject.id) {
            const businessOwnerDto = plainToInstance(
              UpdateBusinessOwnerDto,
              businessOwnerObject,
            );
            updateBusinessOwnerDtos.push({
              ...businessOwnerDto,
              createdAt: undefined,
            });
          } else {
            const businessOwnerDto = plainToInstance(
              CreateBusinessOwnerDto,
              businessOwnerObject,
            );
            createBusinessOwnerDtos.push(businessOwnerDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportBusinessOwnerDto = {
          dataBusinessOwners: createBusinessOwnerDtos,
          dataUpdateBusinessOwners: updateBusinessOwnerDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_BUSINESS_OWNER(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.businessOwnerRepository.getBusinessOwnersByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }
}
