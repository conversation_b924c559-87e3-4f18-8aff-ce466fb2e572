import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';

import { plainToInstance } from 'class-transformer';
import { CreateSectorDto } from '../controller/sector/dtos/create-sector.dto';
import { GetDetailSectorDto } from '../controller/sector/dtos/get-detail-sector.dto';
import { GetSectorListDto } from '../controller/sector/dtos/get-sector-list.dto';
import { UpdateSectorDto } from '../controller/sector/dtos/update-sector.dto';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ESectorStatus } from '../domain/config/enums/sector.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { SectorModel } from '../domain/model/sector.model';
import { ISectorRepository } from '../domain/repositories/sector.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';

@Injectable()
export class SectorUsecases {
  constructor(
    @Inject(ISectorRepository)
    private readonly sectorRepository: ISectorRepository,
  ) {}
  async createSector(
    data: CreateSectorDto,
    authorization: string,
  ): Promise<SectorModel> {
    await this.checkExistSectorByCode(data.code);

    const sectorModel = new SectorModel({
      code: data.code,
      name: data.name,
      description: data.description,
      status: data.status,
    });

    const sector = await this.sectorRepository.createSector(sectorModel);

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: sector.name,
        refId: sector.id,
        refCode: sector.code,
        type: EDataRoleType.SECTOR,
        isEnabled: sector.status === ESectorStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization },
    );

    return sector;
  }

  async updateSector(
    id: string,
    updateSectorDto: UpdateSectorDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<SectorModel> {
    if (updateSectorDto.code)
      await this.checkExistSectorByCode(updateSectorDto.code, id);

    await this.getDetailSector(
      plainToInstance(GetDetailSectorDto, {
        id: id,
      }),
      jwtPayload,
    );

    const sector = await this.sectorRepository.updateSector(
      id,
      updateSectorDto,
    );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: sector.name,
        refCode: sector.code,
        isEnabled: sector.status === ESectorStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    return sector;
  }

  async getSectorById(id: string): Promise<SectorModel> {
    const sector = await this.sectorRepository.getSectorById(id);

    if (!sector) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1036()),
        HttpStatus.BAD_REQUEST,
      );
    }

    return sector;
  }

  async getSectors(
    conditions: GetSectorListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<SectorModel>> {
    return await this.sectorRepository.getSectors(conditions, jwtPayload);
  }

  async deleteSector(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailSector(
      plainToInstance(GetDetailSectorDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.sectorRepository.deleteSector(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistSectorByCode(code: string, id?: string): Promise<void> {
    const sector = await this.sectorRepository.getSectorByCode(code, id);

    if (sector) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1037()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getSectorByCode(code: string): Promise<SectorModel> {
    const sector = await this.sectorRepository.getSectorByCode(code);

    if (!sector) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1036()),
        HttpStatus.BAD_REQUEST,
      );
    }

    return sector;
  }

  async getDetailSector(conditions: GetDetailSectorDto, jwtPayload: any) {
    const detail = await this.sectorRepository.getDetailSector(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1036()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async getSectorByIds(ids: string[], jwtPayload: any) {
    return await this.sectorRepository.getSectorByIds(ids, jwtPayload);
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.sectorRepository.getSectorsByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }
}
