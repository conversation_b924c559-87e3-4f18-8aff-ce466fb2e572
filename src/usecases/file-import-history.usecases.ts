import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { IFileImportHistoryRepository } from '../domain/repositories/file-import-history.repository';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { GetListFileImportHistoryDto } from '../controller/file-import-history/dtos/get-list-file-import-history.dto';
import { EFileImportStatus } from '../domain/config/enums/file-import.enum';
import { fileErrorDetails } from '../domain/messages/error-details/file';

@Injectable()
export class FileImportHistoryUsecases {
  constructor(
    @Inject(IFileImportHistoryRepository)
    private readonly fileImportHistoryRepository: IFileImportHistoryRepository,
  ) {}

  async createFileImportHistory(
    data: FileImportHistoryModel,
  ): Promise<FileImportHistoryModel> {
    return await this.fileImportHistoryRepository.createFileImportHistory(data);
  }

  async updateFileImportHistory(id: string, data: any) {
    const fileImportHistoryData = new FileImportHistoryModel({
      id,
      ...data,
    });

    return await this.fileImportHistoryRepository.updateFileImportHistory(
      fileImportHistoryData,
    );
  }

  async deleteFileImportHistory(id: string): Promise<void> {
    const checkFileImportHistory =
      await this.fileImportHistoryRepository.getFileImportHistoryById(id);

    if (!checkFileImportHistory) {
      throw new HttpException(fileErrorDetails.E_7000(), HttpStatus.NOT_FOUND);
    }

    if (
      checkFileImportHistory.status == EFileImportStatus.IN_PROCESS ||
      checkFileImportHistory.status == EFileImportStatus.WAITING
    ) {
      throw new HttpException(
        fileErrorDetails.E_7001(
          `HISTORY_STATUS_IS_${checkFileImportHistory.status}`,
        ),
        HttpStatus.BAD_REQUEST,
      );
    }
    return await this.fileImportHistoryRepository.deleteFileImportHistory(id);
  }

  async getListFileImportHistory(conditions: GetListFileImportHistoryDto) {
    return await this.fileImportHistoryRepository.getListFileImportHistory(
      conditions,
    );
  }

  async getFileImportHistoryDetail(id: string) {
    const detail =
      await this.fileImportHistoryRepository.getFileImportHistoryById(id);

    return detail;
  }
}
