import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { CreateMeasureDto } from '../controller/measure/dtos/create-measure.dto';
import { GetDetailMeasureDto } from '../controller/measure/dtos/get-detail-cost.dto';
import { GetMeasureListDto } from '../controller/measure/dtos/get-measure-list.dto';
import { UpdateMeasureDto } from '../controller/measure/dtos/update-measure.dto';
import { ResponseDto } from '../domain/dtos/response.dto';
import { measureErrorDetails } from '../domain/messages/error-details/measure';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { MeasureModel } from '../domain/model/measure.model';
import { IMeasureRepository } from '../domain/repositories/measure.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete } from '../utils/http';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import * as Excel from 'exceljs';
import * as moment from 'moment';
import { resolve } from 'path';
import { checkValuesEmptyRowExcel, getValueOrResult } from '../utils/common';
import {
  EColumnImportMeasure,
  EMeasureStatus,
} from '../domain/config/enums/measure.enum';
import { ImportMeasureDto } from '../controller/import/dtos/import-measure.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { GetMeasureListByIdsDto } from '../controller/measure/dtos/get-measure-list-by-ids.dto';

@Injectable()
export class MeasureUsecases {
  constructor(
    @Inject(IMeasureRepository)
    private readonly measureRepository: IMeasureRepository,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
  ) {}

  async createMeasure(
    data: CreateMeasureDto,
    authorization: string,
  ): Promise<MeasureModel> {
    await this.checkExistMeasureByCode(data.code);

    const systems = [
      ...new Set(
        data.codeConversions.map((item) => item.system).filter(Boolean),
      ),
    ];

    if (systems?.length != data.codeConversions?.length) {
      throw new HttpException(
        getErrorMessage(measureErrorDetails.E_6055()),
        HttpStatus.BAD_REQUEST,
      );
    }

    const measureModel = new MeasureModel(data);

    const measure = await this.measureRepository.createMeasure(measureModel);

    return measure;
  }

  async updateMeasure(
    id: string,
    updateMeasureDto: UpdateMeasureDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<MeasureModel> {
    await this.checkExistMeasureByCode(updateMeasureDto.code, id);

    await this.getDetailMeasure(
      plainToInstance(GetDetailMeasureDto, {
        id: id,
      }),
      jwtPayload,
    );

    const systems = [
      ...new Set(
        updateMeasureDto.codeConversions
          .map((item) => item.system)
          .filter(Boolean),
      ),
    ];

    if (systems?.length != updateMeasureDto.codeConversions?.length) {
      throw new HttpException(
        getErrorMessage(measureErrorDetails.E_6055()),
        HttpStatus.BAD_REQUEST,
      );
    }

    const measure = await this.measureRepository.updateMeasure(
      id,
      updateMeasureDto,
    );

    return measure;
  }

  async getMeasures(
    conditions: GetMeasureListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<MeasureModel>> {
    return await this.measureRepository.getMeasures(conditions, jwtPayload);
  }

  async deleteMeasure(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailMeasure(
      plainToInstance(GetDetailMeasureDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.measureRepository.deleteMeasure(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistMeasureByCode(code: string, id?: string): Promise<void> {
    const measure = await this.measureRepository.getMeasureByCode(code, id);

    if (measure) {
      throw new HttpException(
        getErrorMessage(measureErrorDetails.E_6051()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getDetailMeasure(conditions: GetDetailMeasureDto, jwtPayload: any) {
    const detail = await this.measureRepository.getDetailMeasure(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(measureErrorDetails.E_6050()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async getMeasuresByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<MeasureModel[]> {
    return await this.measureRepository.getMeasuresByCodesWithRole(
      codes,
      jwtPayload,
      isNeedPermission,
    );
  }

  async exportMeasure(conditions: GetMeasureListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.measureRepository.getMeasures(
      conditions,
      jwtPayload,
    );
    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-measure-export.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];
      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toMeasureModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }
      const buffer = await targetWorkbook.xlsx.writeBuffer();
      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-measure.xlsx',
      );
      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );
      return { ...uploadedFile, buffer: null };
    }
  }

  private async toMeasureModel(measures: MeasureModel[]) {
    const items = [];
    for (let i = 0; i < measures.length; i++) {
      items.push({
        code: measures[i].code || '',
        name: measures[i].name || '',
        description: measures[i].description || '',
        status: this.getStatusMeasure(measures[i].status),
      });
    }
    return items;
  }

  async importMeasure(
    file: Express.Multer.File,
    jwtPayload: any,
    authorization: string,
  ) {
    ///Upload file import

    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.MEASURE,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );
      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));
        const createMeasureDtos: CreateMeasureDto[] = [];
        const updateMeasureDtos: UpdateMeasureDto[] = [];
        const errors: TErrorMessageImport[] = [];
        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];
        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportMeasure.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);
        const measures =
          await this.measureRepository.getMeasuresByCodesWithRole(
            [...new Set(codes)],
            jwtPayload,
            false,
          );
        let totalRowHasValue = 0;
        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }
          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportMeasure.CODE, // First Cell
            EColumnImportMeasure.DESCRIPTION, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;
          const code = getValueOrResult(
            row,
            EColumnImportMeasure.CODE,
          )?.toString();
          const name = getValueOrResult(
            row,
            EColumnImportMeasure.NAME,
          )?.toString();
          const description = getValueOrResult(
            row,
            EColumnImportMeasure.DESCRIPTION,
          )?.toString();
          const measureObject = {
            id: undefined,
            name,
            code,
            description,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };
          if (!code) {
            errors.push({
              error: getErrorMessage(
                measureErrorDetails.E_6052(),
                'Mã đơn vị tính không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const checkCode = measures.find((item) => item.code == code);
            if (checkCode) {
              measureObject.id = checkCode.id;
            }
            const checkDup = codes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(
                  measureErrorDetails.E_6053(),
                  'Measure bị trùng lặp',
                ),
                row: i + 1,
              });
            }
          }
          if (!name) {
            errors.push({
              error: getErrorMessage(
                measureErrorDetails.E_6054(),
                'Tên đơn vị tính không được để trống',
              ),
              row: i + 1,
            });
          }
          if (measureObject.id) {
            const measureDto = plainToInstance(UpdateMeasureDto, measureObject);
            updateMeasureDtos.push({ ...measureDto, createdAt: undefined });
          } else {
            const measureDto = plainToInstance(CreateMeasureDto, measureObject);
            createMeasureDtos.push(measureDto);
          }
        }
        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );
          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportMeasureDto = {
          dataMeasures: createMeasureDtos,
          dataUpdateMeasures: updateMeasureDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await this.import(importBody, EFileImportType.BUDGET_CODE);

        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.SUCCESS,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.measureRepository.getMeasuresByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }

  async import(
    body: ImportMeasureDto,
    type: EFileImportType,
    jwtPayload?: IAuthUserPayload,
    authorization?: string,
  ) {
    const dataMeasure = body as ImportMeasureDto;
    for (let i = 0; i < dataMeasure.dataMeasures.length; i++) {
      await this.createMeasure(dataMeasure.dataMeasures[i], authorization);
    }

    for (let i = 0; i < dataMeasure.dataUpdateMeasures.length; i++) {
      await this.updateMeasure(
        dataMeasure.dataUpdateMeasures[i].id,
        dataMeasure.dataUpdateMeasures[i],
        jwtPayload,
        authorization,
      );
    }
  }

  async getListByIds(
    conditions: GetMeasureListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<MeasureModel>> {
    return await this.measureRepository.getListByIds(conditions, jwtPayload);
  }

  private getStatusMeasure(status?: EMeasureStatus) {
    switch (status) {
      case EMeasureStatus.ACTIVE:
        return 'Hoạt động';
      case EMeasureStatus.IN_ACTIVE:
        return 'Không hoạt động';
      default:
        return '';
    }
  }
}
