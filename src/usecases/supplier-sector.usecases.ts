import { Inject, Injectable } from '@nestjs/common';
import { SupplierSectorModel } from '../domain/model/supplier-sector.model';
import { ISupplierSectorRepository } from '../domain/repositories/supplier-sector.repository';

@Injectable()
export class SupplierSectorUsecases {
  constructor(
    @Inject(ISupplierSectorRepository)
    private readonly supplierSectorRepository: ISupplierSectorRepository,
  ) {}

  async createManySupplierSector(
    data: SupplierSectorModel[],
  ): Promise<SupplierSectorModel[]> {
    return await this.supplierSectorRepository.createSupplierSectors(data);
  }

  async updateManySupplierSector(
    data: SupplierSectorModel[],
  ): Promise<SupplierSectorModel[]> {
    return await this.supplierSectorRepository.updateSupplierSectors(data);
  }

  async deleteSupplierSectors(supplierId: string): Promise<void> {
    return await this.supplierSectorRepository.deleteSupplierSectors(
      supplierId,
    );
  }
}
