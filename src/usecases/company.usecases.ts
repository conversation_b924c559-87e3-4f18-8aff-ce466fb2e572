import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';

import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { CreateCompanyDto } from '../controller/company/dtos/create-company.dto';
import { GetCompanyListDto } from '../controller/company/dtos/get-company-list.dto';
import { GetDetailCompanyDto } from '../controller/company/dtos/get-detail-company.dto';
import { UpdateCompanyDto } from '../controller/company/dtos/update-company.dto';
import { ImportCompanyDto } from '../controller/import/dtos/import-company.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EAcutalEventFor,
  ISapEventCreate,
} from '../domain/config/enums/actual-spending.enum';
import {
  EColumnImportCompany,
  ECompanyStatus,
} from '../domain/config/enums/company.enum';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { CompanyModel } from '../domain/model/company.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { ICompanyRepository } from '../domain/repositories/company.repository';
import {
  checkValuesEmptyRowExcel,
  getStatus,
  getStatusCompany,
  getValueOrResult,
} from '../utils/common';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';

@Injectable()
export class CompanyUsecases {
  constructor(
    @Inject(ICompanyRepository)
    private readonly companyRepository: ICompanyRepository,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private eventEmitter: EventEmitter2,
  ) {}
  async createCompany(
    data: CreateCompanyDto,
    authorization: string,
  ): Promise<CompanyModel> {
    await this.checkExistCompanyByCode(data.code);

    const companyModel = new CompanyModel({
      code: data.code,
      name: data.name,
      description: data.description,
      status: data.status,
    });

    const company = await this.companyRepository.createCompany(companyModel);

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: company.name,
        refId: company.id,
        refCode: company.code,
        type: EDataRoleType.COMPANY,
        isEnabled: company.status === ECompanyStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization, 'x-api-key': process.env.API_KEY },
    );

    this.eventEmitter.emit('sap.company.created', {
      code: company.code,
      id: company.id,
      eventFor: EAcutalEventFor.COMPANY,
    } as ISapEventCreate);

    return company;
  }

  async updateCompany(
    id: string,
    updateCompanyDto: UpdateCompanyDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<CompanyModel> {
    await this.checkExistCompanyByCode(updateCompanyDto.code, id);

    const detail = await this.getDetailCompany(
      plainToInstance(GetDetailCompanyDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.companyRepository.updateCompany(id, updateCompanyDto);

    const company = await this.companyRepository.updateCompany(
      id,
      updateCompanyDto,
    );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: company.name,
        refCode: company.code,
        isEnabled: company.status === ECompanyStatus.ACTIVE,
      },
      {
        authorization,
        'x-api-key': process.env.API_KEY,
      },
    );

    if (detail.code != updateCompanyDto.code) {
      this.eventEmitter.emit('sap.company.created', {
        code: company.code,
        id: company.id,
        eventFor: EAcutalEventFor.COMPANY,
      } as ISapEventCreate);
    }

    return company;
  }

  async getCompanyById(id: string): Promise<CompanyModel> {
    const company = await this.companyRepository.getCompanyById(id);

    if (!company) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1024()),
        HttpStatus.BAD_REQUEST,
      );
    }

    return company;
  }

  async getCompanies(
    conditions: GetCompanyListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<CompanyModel>> {
    return await this.companyRepository.getCompanies(conditions, jwtPayload);
  }

  async deleteCompany(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailCompany(
      plainToInstance(GetDetailCompanyDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.companyRepository.deleteCompany(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
      'x-api-key': process.env.API_KEY,
    });
  }

  async checkExistCompanyByCode(code: string, id?: string): Promise<void> {
    const company = await this.companyRepository.getCompanyByCode(code, id);

    if (company) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1025()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getCompanyByCode(code: string): Promise<CompanyModel> {
    return await this.companyRepository.getCompanyByCode(code);
  }

  async getDetailCompany(conditions: GetDetailCompanyDto, jwtPayload: any) {
    const detail = await this.companyRepository.getDetailCompany(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1024()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async getCompanyByIds(ids: string[], jwtPayload: any) {
    const companies = await this.companyRepository.getCompanyByIds(
      ids,
      jwtPayload,
    );

    if (!companies || !companies.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1024()),
        HttpStatus.NOT_FOUND,
      );
    }

    const activeCompanies = companies?.filter(
      (item) => item.status === ECompanyStatus.ACTIVE,
    );

    const activeCompanyIds = activeCompanies.map((item) => item.id);

    const missingCompanyIds = _.difference(ids, activeCompanyIds);

    if (missingCompanyIds.length) {
      throw new HttpException(
        getErrorMessage(
          errorMessage.E_1024(
            `Company ids ${missingCompanyIds.join(', ')} not found or inactive`,
          ),
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return companies;
  }

  async exportCompany(conditions: GetCompanyListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.companyRepository.getCompanies(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-company.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toCompanyModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-company.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toCompanyModel(companies: CompanyModel[]) {
    const items = [];

    for (let i = 0; i < companies.length; i++) {
      items.push({
        code: companies[i].code || '',
        name: companies[i].name || '',
        description: companies[i].description || '',
        status: getStatusCompany(companies[i].status),
      });
    }

    return items;
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.companyRepository.getCompanyByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }

  async importCompany(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.COMPANY,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createCompanyDtos: CreateCompanyDto[] = [];
        const updateCompanyDtos: UpdateCompanyDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportCompany.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const companys = await this.companyRepository.getCompanyByCodesWithRole(
          [...new Set(codes)],
          jwtPayload,
          false,
        );

        let totalRowHasValue = 0;

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportCompany.CODE, // First Cell
            EColumnImportCompany.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          ///Data từng row trong file excel
          const code = getValueOrResult(
            row,
            EColumnImportCompany.CODE,
          )?.toString();
          const name = getValueOrResult(
            row,
            EColumnImportCompany.NAME,
          )?.toString();
          const description = getValueOrResult(
            row,
            EColumnImportCompany.DESCRIPTION,
          )?.toString();
          const status = getStatus(
            CompanyModel,
            getValueOrResult(row, EColumnImportCompany.STATUS)?.toString(),
          ); //Trạng thái

          const companyObject = {
            id: undefined,
            code,
            name,
            description,
            status,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };

          if (!code) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1095(),
                'Mã công ty không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const checkCode = companys.find((item) => item.code == code);

            const checkDup = codes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1097(),
                  'Mã công ty bị trùng lặp',
                ),
                row: i + 1,
              });
            }

            if (checkCode) {
              companyObject.id = checkCode.id;
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1096(),
                'Tên công ty không được để trống',
              ),
              row: i + 1,
            });
          }

          if (companyObject.id) {
            const companyDto = plainToInstance(UpdateCompanyDto, {
              ...companyObject,
              createdAt: undefined,
            });
            updateCompanyDtos.push(companyDto);
          } else {
            const companyDto = plainToInstance(CreateCompanyDto, companyObject);
            createCompanyDtos.push(companyDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportCompanyDto = {
          dataCompanys: createCompanyDtos,
          dataUpdateCompanys: updateCompanyDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_COMPANY(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
