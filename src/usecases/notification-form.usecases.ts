import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import mongoose from 'mongoose';
import { GetDetailNotificationFormDto } from '../controller/notification-form/dtos/get-detail-notification-form.dto';
import { GetNotificationFormListDto } from '../controller/notification-form/dtos/get-notification-form-list.dto';
import { UpdateNotificationFormDto } from '../controller/notification-form/dtos/update-notification-form.dto';
import { ResponseDto } from '../domain/dtos/response.dto';
import { notificationFormErrorDetails } from '../domain/messages/error-details/notification-form';
import { INotificationFormRepository } from '../domain/repositories/notification-form.repository';
import { NotificationForm } from '../infrastructure/entities/notification-form.entity';
import { CreateNotificationFormDto } from '../controller/notification-form/dtos/create-notification-form.dto';
import { removeUnicode } from '../utils/common';
import {
  ENotificationFormType,
  getNameOfNotificationFormType,
} from '../domain/config/enums/notification-form.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';

@Injectable()
export class NotificationFormUsecases {
  constructor(
    @Inject(INotificationFormRepository)
    private readonly notificationFormRepository: INotificationFormRepository,
  ) {}

  async createNotificationForm(
    data: CreateNotificationFormDto,
  ): Promise<NotificationForm> {
    const notificationFormData = {
      ...data,
      searchValue: removeUnicode(data.name),
    };

    const notificationForm =
      await this.notificationFormRepository.createNotificationForm(
        notificationFormData,
      );

    return await this.getNotificationFormDetail(
      plainToInstance(GetDetailNotificationFormDto, {
        id: notificationForm._id.toString(),
      }),
    );
  }

  async getNotificationForms(
    conditions: GetNotificationFormListDto,
  ): Promise<ResponseDto<NotificationForm>> {
    return await this.notificationFormRepository.getNotificationForms(
      conditions,
    );
  }

  // async deleteNotificationForm(id: string): Promise<void> {
  //   await this.getNotificationFormDetail(
  //     plainToInstance(GetDetailNotificationFormDto, {
  //       id,
  //     }),
  //   );

  //   return await this.notificationFormRepository.deleteNotificationForm(id);
  // }

  async updateNotificationForm(
    id: string,
    data: UpdateNotificationFormDto,
  ): Promise<NotificationForm> {
    const detail = await this.getNotificationFormDetail(
      plainToInstance(GetDetailNotificationFormDto, {
        id,
      }),
    );

    const notificationFormData: NotificationForm = { ...detail, ...data };

    notificationFormData._id = new mongoose.Types.ObjectId(id);

    await this.notificationFormRepository.updateNotificationForm(
      notificationFormData,
    );

    return await this.getNotificationFormDetail(
      plainToInstance(GetDetailNotificationFormDto, {
        id: id,
      }),
    );
  }

  async getNotificationFormDetail(conditions: GetDetailNotificationFormDto) {
    const detail =
      await this.notificationFormRepository.getNotificationFormDetail(
        conditions,
      );

    if (!detail) {
      throw new HttpException(
        notificationFormErrorDetails.E_2300(),
        HttpStatus.NOT_FOUND,
      );
    }
    return detail;
  }

  async getNotificationFormByType(type: ENotificationFormType) {
    return await this.notificationFormRepository.getNotificationFormByType(
      type,
    );
  }

  async initMasterDataNotificationForm() {
    for (const type of Object.values(ENotificationFormType)) {
      const detail =
        await this.notificationFormRepository.getNotificationFormByType(type);

      if (!detail) {
        await this.createNotificationForm({
          type: type,
          name: getNameOfNotificationFormType(type),
          platform: EPlatform.E_PURCHASE,
        });
      }
    }
  }
}
