import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { emailTransportService } from '../infrastructure/config/email-transport-config/email.service';
import { emailTemplateService } from '../infrastructure/config/email-transport-config/email-template.service';

@Injectable()
export class approvalEmailUsecases {
  private transporter;

  constructor(
    private configService: ConfigService,
    private _emailTransportService: emailTransportService,
    private _emailTemplateService: emailTemplateService,
  ) {
    this.transporter = this._emailTransportService.getTransporter();
  }
}
