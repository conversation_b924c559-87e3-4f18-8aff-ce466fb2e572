import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as moment from 'moment';
import { resolve } from 'path';
import { ImportMaterialGroupDto } from '../controller/import/dtos/import-material-group.dto';
import { CreateMaterialGroupDto } from '../controller/material-group/dtos/create-material-group.dto';
import { GetDetailMaterialGroupDto } from '../controller/material-group/dtos/get-detail-material-group.dto';
import { GetMaterialGroupListDto } from '../controller/material-group/dtos/get-material-group-list.dto';
import { UpdateMaterialGroupDto } from '../controller/material-group/dtos/update-material-group.model';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { EColumnImportMaterialGroup } from '../domain/config/enums/material.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { importErrorDetails } from '../domain/messages/error-details/import';
import { materialGroupErrorDetails } from '../domain/messages/error-details/material-group';
import { processTypeErrorDetails } from '../domain/messages/error-details/process-type';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { MaterialGroupModel } from '../domain/model/material-group.model';
import { IMaterialGroupRepository } from '../domain/repositories/material-group.repository';
import {
  checkValuesEmptyRowExcel,
  codeToIdMap,
  getStatus,
  getStatusMaterialGroup,
  getValueOrResult,
} from '../utils/common';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendPost } from '../utils/http';
import { BusinessOwnerUsecases } from './business-owner.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { ProcessTypeUsecases } from './process-type.usecases';
import * as _ from 'lodash';
import { GetMaterialGroupListByIdsDto } from '../controller/material-group/dtos/get-material-group-list-by-ids.dto';

@Injectable()
export class MaterialGroupUsecases {
  constructor(
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private readonly businessOwnerUsecases: BusinessOwnerUsecases,
    private readonly processTypeUsecases: ProcessTypeUsecases,
    @Inject(IMaterialGroupRepository)
    private readonly materialGroupRepository: IMaterialGroupRepository,
  ) {}

  async createMaterialGroup(
    data: CreateMaterialGroupDto,
    jwtPayload: any,
  ): Promise<MaterialGroupModel> {
    await this.checkExistMaterialGroupByCode(data.code);

    const [businessOwners, processTypes] = await Promise.all([
      data.businessOwnerIds?.length
        ? this.businessOwnerUsecases.getBusinessOwnerByIds(
            [...new Set(data.businessOwnerIds)],
            jwtPayload,
          )
        : Promise.resolve(null),
      data.processTypeIds?.length
        ? this.processTypeUsecases.getProcessTypeByIds([
            ...new Set(data.processTypeIds),
          ])
        : Promise.resolve(null),
    ]);

    if (businessOwners) data.businessOwners = businessOwners;
    if (processTypes) data.processTypes = processTypes;

    const materialGroupModel = new MaterialGroupModel(data);

    const materialGroup =
      await this.materialGroupRepository.createMaterialGroup(
        materialGroupModel,
      );

    // await sendPost(
    //   IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
    //   {
    //     description: materialGroup.name,
    //     refId: materialGroup.id,
    //     refCode: materialGroup.code,
    //     type: EDataRoleType.MATERIAL_GROUP,
    //     isEnabled: materialGroup.status === EMaterialGroupStatus.ACTIVE,
    //     platform: EPlatform.E_PURCHASE,
    //   },
    //   { authorization, 'x-api-key': process.env.API_KEY },
    // );

    return materialGroup;
  }

  async updateMaterialGroup(
    id: string,
    updateMaterialGroupDto: UpdateMaterialGroupDto,
    jwtPayload: any,
  ): Promise<MaterialGroupModel> {
    if (updateMaterialGroupDto.code)
      await this.checkExistMaterialGroupByCode(updateMaterialGroupDto.code, id);

    await this.getMaterialGroupDetail(
      plainToInstance(GetDetailMaterialGroupDto, {
        id,
      }),
      jwtPayload,
    );

    const [businessOwners, processTypes] = await Promise.all([
      updateMaterialGroupDto.businessOwnerIds
        ? updateMaterialGroupDto.businessOwnerIds.length
          ? this.businessOwnerUsecases.getBusinessOwnerByIds(
              [...new Set(updateMaterialGroupDto.businessOwnerIds)],
              jwtPayload,
            )
          : Promise.resolve([])
        : Promise.resolve(null),
      updateMaterialGroupDto.processTypeIds
        ? updateMaterialGroupDto.processTypeIds.length
          ? this.processTypeUsecases.getProcessTypeByIds([
              ...new Set(updateMaterialGroupDto.processTypeIds),
            ])
          : Promise.resolve([])
        : Promise.resolve(null),
    ]);

    if (businessOwners) updateMaterialGroupDto.businessOwners = businessOwners;
    if (processTypes) updateMaterialGroupDto.processTypes = processTypes;

    const materialGroup =
      await this.materialGroupRepository.updateMaterialGroup(
        id,
        updateMaterialGroupDto,
      );

    // await sendPatch(
    //   IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
    //   {
    //     description: materialGroup.name,
    //     refCode: materialGroup.code,
    //     isEnabled: materialGroup.status === EMaterialGroupStatus.ACTIVE,
    //   },
    //   {
    //     authorization,
    //     'x-api-key': process.env.API_KEY,
    //   },
    // );

    return materialGroup;
  }

  // async getMaterialGroupById(id: string): Promise<MaterialGroupModel> {
  //   const materialGroup =
  //     await this.materialGroupRepository.getMaterialGroupById(id);

  //   if (!materialGroup) {
  //     throw new HttpException(
  //       materialGroupErrorDetails.E_2200(),
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   return materialGroup;
  // }

  async getMaterialGroups(
    conditions: GetMaterialGroupListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<MaterialGroupModel>> {
    return await this.materialGroupRepository.getMaterialGroups(
      conditions,
      jwtPayload,
    );
  }

  async deleteMaterialGroup(id: string, jwtPayload: any): Promise<void> {
    await this.getMaterialGroupDetail(
      plainToInstance(GetDetailMaterialGroupDto, {
        id,
      }),
      jwtPayload,
    );

    await this.materialGroupRepository.deleteMaterialGroup(id);

    // await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
    //   authorization,
    //   'x-api-key': process.env.API_KEY,
    // });
  }

  async checkExistMaterialGroupByCode(
    code: string,
    id?: string,
  ): Promise<MaterialGroupModel> {
    const materialGroup =
      await this.materialGroupRepository.getMaterialGroupByCode(code, id);

    if (materialGroup) {
      throw new HttpException(
        materialGroupErrorDetails.E_2201(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return materialGroup;
  }

  async getMaterialGroupDetail(
    conditions: GetDetailMaterialGroupDto,
    jwtPayload: any,
  ) {
    const detail = await this.materialGroupRepository.getMaterialGroupDetail(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        materialGroupErrorDetails.E_2200(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return detail;
  }

  async exportMaterialGroup(
    conditions: GetMaterialGroupListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data = await this.materialGroupRepository.getMaterialGroups(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-material-group.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toMaterialGroupModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-material-group.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toMaterialGroupModel(materialGroups: MaterialGroupModel[]) {
    const items = [];

    for (let i = 0; i < materialGroups.length; i++) {
      items.push({
        code: materialGroups[i].code || '',
        name: materialGroups[i].name || '',
        processTypes: materialGroups[i].processTypes?.length
          ? (materialGroups[i].processTypes || [])
              .map((item) => item.code)
              .join(', ')
          : '',
        businessOwners: materialGroups[i].businessOwners?.length
          ? (materialGroups[i].businessOwners || [])
              .map((item) => item.code)
              .join(', ')
          : '',
        description: materialGroups[i].description || '',
        status: getStatusMaterialGroup(materialGroups[i].status),
      });
    }

    return items;
  }

  async importMaterialGroup(
    file: Express.Multer.File,
    jwtPayload: any,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.MATERIAL_GROUP,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createMaterialGroupDtos: CreateMaterialGroupDto[] = [];
        const updateMaterialGroupDtos: UpdateMaterialGroupDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            2,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        // Extract column values using helper function
        const getColumnValues = (column) =>
          rows
            .map((row) => getValueOrResult(row, column)?.toString())
            .filter(Boolean) || [];

        const getFlattenColumnValues = (column) => [
          ...new Set(
            _.flatten(
              rows
                .map((row) =>
                  getValueOrResult(row, column)
                    ?.toString()
                    ?.split(',')
                    ?.map((item) => item.trim()),
                )
                .filter(Boolean) || [],
            ),
          ),
        ];

        const [materialGroupCodes] = [EColumnImportMaterialGroup.CODE].map(
          getColumnValues,
        );

        const [processTypeCodes, businessOwnerCodes] = [
          EColumnImportMaterialGroup.PROCESS_TYPE_CODE,
          EColumnImportMaterialGroup.BUSINESS_OWNER_CODE,
        ].map(getFlattenColumnValues);

        const [materialGroups, processTypes, businessOwners] =
          await Promise.all([
            this.listByCodes(materialGroupCodes, jwtPayload, false),
            this.processTypeUsecases.listByCodes(processTypeCodes),
            this.businessOwnerUsecases.listByCodes(
              businessOwnerCodes,
              jwtPayload,
            ),
          ]);

        const [dataProcessTypes, dataBusinessOwners] = [
          processTypes,
          businessOwners,
        ].map((items: any) => codeToIdMap(items, 'code', ['id']));

        let totalRowHasValue = 0;

        for (let i = 0; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportMaterialGroup.CODE, // First Cell
            EColumnImportMaterialGroup.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          ///Data từng row trong file excel
          const code = getValueOrResult(
            row,
            EColumnImportMaterialGroup.CODE,
          )?.toString();
          const name = getValueOrResult(
            row,
            EColumnImportMaterialGroup.NAME,
          )?.toString();
          const processTypeCodes = [
            ...new Set(
              getValueOrResult(
                row,
                EColumnImportMaterialGroup.PROCESS_TYPE_CODE,
              )
                ?.toString()
                ?.split(',')
                ?.map((item) => item.trim())
                ?.filter(Boolean),
            ),
          ] as string[];
          const businessOwnerCodes = [
            ...new Set(
              getValueOrResult(
                row,
                EColumnImportMaterialGroup.BUSINESS_OWNER_CODE,
              )
                ?.toString()
                ?.split(',')
                ?.map((item) => item.trim())
                ?.filter(Boolean),
            ),
          ] as string[];

          const description = getValueOrResult(
            row,
            EColumnImportMaterialGroup.DESCRIPTION,
          )?.toString();
          const status = getStatus(
            MaterialGroupModel,
            getValueOrResult(
              row,
              EColumnImportMaterialGroup.STATUS,
            )?.toString(),
          ); //Trạng thái

          const materialGroupObject = {
            id: undefined,
            code,
            name,
            description,
            status,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            processTypes: [],
            businessOwners: [],
          };

          if (!code) {
            errors.push({
              error: getErrorMessage(
                materialGroupErrorDetails.E_2203(),
                'Mã nhóm vật tư không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const checkCode = materialGroups.find((item) => item.code == code);

            const checkDup = materialGroupCodes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(
                  materialGroupErrorDetails.E_2205(),
                  'Mã nhóm vật tư bị trùng lặp',
                ),
                row: i + 1,
              });
            }

            if (checkCode) {
              materialGroupObject.id = checkCode.id;
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(
                materialGroupErrorDetails.E_2204(),
                'Tên nhóm vật tư không được để trống',
              ),
              row: i + 1,
            });
          }

          if (processTypeCodes && processTypeCodes?.length) {
            for (const processTypeCode of processTypeCodes) {
              const processTypeId = dataProcessTypes[processTypeCode];

              if (!processTypeId) {
                errors.push({
                  error: processTypeErrorDetails.E_2700(),
                  row: i + 1,
                });
              } else {
                materialGroupObject.processTypes.push({ id: processTypeId });
              }
            }
          }

          if (businessOwnerCodes && businessOwnerCodes?.length) {
            for (const businessOwnerCode of businessOwnerCodes) {
              const businessOwnerId = dataBusinessOwners[businessOwnerCode];

              if (!businessOwnerId) {
                errors.push({
                  error: processTypeErrorDetails.E_2700(),
                  row: i + 1,
                });
              } else {
                materialGroupObject.businessOwners.push({
                  id: businessOwnerId,
                });
              }
            }
          }

          if (materialGroupObject.id) {
            const materialGroupDto = plainToInstance(UpdateMaterialGroupDto, {
              ...materialGroupObject,
              createdAt: undefined,
            });
            updateMaterialGroupDtos.push(materialGroupDto);
          } else {
            const materialGroupDto = plainToInstance(
              CreateMaterialGroupDto,
              materialGroupObject,
            );
            createMaterialGroupDtos.push(materialGroupDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportMaterialGroupDto = {
          dataMaterialGroups: createMaterialGroupDtos,
          dataUpdateMaterialGroups: updateMaterialGroupDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_MATERIAL_GROUP(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.materialGroupRepository.getMaterialGroupByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }

  async getListByIds(
    conditions: GetMaterialGroupListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<MaterialGroupModel>> {
    return await this.materialGroupRepository.getListByIds(
      conditions,
      jwtPayload,
    );
  }
}
