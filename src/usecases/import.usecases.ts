import { Injectable } from '@nestjs/common';
import { ImportActualSpendingDto } from '../controller/import/dtos/import-actual-spending.dto';
import { ImportBudgetCodeDto } from '../controller/import/dtos/import-budget-code.dto';
import { ImportBusinessOwnerDto } from '../controller/import/dtos/import-business-owner.dto';
import { ImportBusinessUnitDto } from '../controller/import/dtos/import-business-unit.dto';
import { ImportCapexDto } from '../controller/import/dtos/import-capex.dto';
import { ImportCompanyDto } from '../controller/import/dtos/import-company.dto';
import { ImportCostDto } from '../controller/import/dtos/import-cost.dto';
import { ImportCostcenterSubaccountDto } from '../controller/import/dtos/import-costcenter-subaccount.dto';
import { ImportCurrencyUnitExchangeDto } from '../controller/import/dtos/import-currency-exchange.dto';
import { ImportDepartmentDto } from '../controller/import/dtos/import-department.dto';
import { ImportInventoryStandardDto } from '../controller/import/dtos/import-inventory-standard.dto';
import { ImportMaterialGroupDto } from '../controller/import/dtos/import-material-group.dto';
import { ImportMaterialTypeDto } from '../controller/import/dtos/import-material-type.dto';
import { ImportMaterialDto } from '../controller/import/dtos/import-material.dto';
import { ImportOpexDto } from '../controller/import/dtos/import-opex.dto';
import { ImportSupplierDto } from '../controller/import/dtos/import-supplier.dto';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { CurrencyUnitExchangeModel } from '../domain/model/currency-unit-exchange.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { ActualSpendingUsecases } from './actual-spending.usecases';
import { BudgetCodeUsecases } from './budget-code.usecases';
import { BudgetUsecases } from './budget.usecases';
import { BusinessOwnerUsecases } from './business-owner.usecases';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CompanyUsecases } from './company.usecases';
import { CostUsecases } from './cost.usecases';
import { CostcenterSubaccountUsecases } from './costcenter-subaccount.usecases';
import { CurrencyUnitExchangeUsecases } from './currency-unit-exchange.usecases';
import { DepartmentUsecases } from './department.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { InventoryStandardUsecases } from './inventory-standard.usecases';
import { MaterialGroupUsecases } from './material-group.usecases';
import { MaterialTypeUsecases } from './material-type.usecases';
import { MaterialUsecases } from './material.usecases';
import { SupplierUsecases } from './supplier.usecases';

@Injectable()
export class ImportUsecases {
  constructor(
    private readonly budgetUsecases: BudgetUsecases,
    private readonly budgetCodeUsecases: BudgetCodeUsecases,
    private readonly costcenterSubaccountUsecases: CostcenterSubaccountUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private readonly supplierUsecases: SupplierUsecases,
    private readonly materialUsecases: MaterialUsecases,
    private readonly costUsecases: CostUsecases,
    private readonly currencyUnitExchangeUsecases: CurrencyUnitExchangeUsecases,
    private readonly inventoryStandardUsecases: InventoryStandardUsecases,
    private readonly businessOwnerUsecases: BusinessOwnerUsecases,
    private readonly materialGroupUsecases: MaterialGroupUsecases,
    private readonly materialTypeUsecases: MaterialTypeUsecases,
    private readonly departmentUsecases: DepartmentUsecases,
    private readonly companyUsecases: CompanyUsecases,
    private readonly businessUnitUsecases: BusinessUnitUsecases,
    private readonly actualSpendingUsecases: ActualSpendingUsecases,
  ) {}

  async import(
    body:
      | ImportOpexDto
      | ImportCapexDto
      | ImportBudgetCodeDto
      | ImportCostcenterSubaccountDto
      | ImportSupplierDto
      | ImportMaterialDto
      | ImportCostDto
      | ImportCurrencyUnitExchangeDto
      | ImportInventoryStandardDto
      | ImportBusinessOwnerDto
      | ImportMaterialGroupDto
      | ImportMaterialTypeDto
      | ImportDepartmentDto
      | ImportCompanyDto
      | ImportBusinessUnitDto
      | ImportActualSpendingDto,
    type: EFileImportType,
    jwtPayload?: IAuthUserPayload,
    authorization?: string,
  ) {
    try {
      switch (type) {
        case EFileImportType.OPEX:
          const dataOpex = body as ImportOpexDto;
          for (let i = 0; i < (dataOpex.dataOpexes?.length ?? 0); i++) {
            await this.budgetUsecases.createBudgetOpex(
              dataOpex.dataOpexes[i],
              jwtPayload,
              true,
            );
          }
          break;
        case EFileImportType.CAPEX:
          const dataCapex = body as ImportCapexDto;
          for (let i = 0; i < dataCapex.dataCapexes?.length; i++) {
            await this.budgetUsecases.createBudgetCapex(
              dataCapex.dataCapexes[i],
              jwtPayload,
              true,
            );
          }
          break;
        case EFileImportType.BUDGET_CODE:
          const dataBudgetCode = body as ImportBudgetCodeDto;
          for (let i = 0; i < dataBudgetCode.dataBudgetCodes.length; i++) {
            await this.budgetCodeUsecases.createBudgetCode(
              dataBudgetCode.dataBudgetCodes[i],
              jwtPayload,
              true,
            );
          }

          for (
            let i = 0;
            i < dataBudgetCode.dataUpdateBudgetCodes.length;
            i++
          ) {
            await this.budgetCodeUsecases.updateBudgetCode(
              dataBudgetCode.dataUpdateBudgetCodes[i].id,
              dataBudgetCode.dataUpdateBudgetCodes[i],
              jwtPayload,
              true,
            );
          }
          break;
        case EFileImportType.COST_CENTER_SUB_ACCOUNT:
          const dataCostCenter = body as ImportCostcenterSubaccountDto;
          for (
            let i = 0;
            i < dataCostCenter.dataCostcenterSubaccounts.length;
            i++
          ) {
            await this.costcenterSubaccountUsecases.createCostcenterSubaccount(
              dataCostCenter.dataCostcenterSubaccounts[i],
              jwtPayload,
              true,
              authorization,
            );
          }

          for (
            let i = 0;
            i < dataCostCenter.dataUpdateCostcenterSubaccounts.length;
            i++
          ) {
            await this.costcenterSubaccountUsecases.updateCostcenterSubaccount(
              dataCostCenter.dataUpdateCostcenterSubaccounts[i].id,
              dataCostCenter.dataUpdateCostcenterSubaccounts[i],
              jwtPayload,
              true,
            );
          }
          break;

        case EFileImportType.SUPPLIER:
          const dataSupplier = body as ImportSupplierDto;
          for (let i = 0; i < dataSupplier.dataSuppliers.length; i++) {
            await this.supplierUsecases.createSupplier(
              dataSupplier.dataSuppliers[i],
              jwtPayload,
              authorization,
              true,
            );
          }

          for (let i = 0; i < dataSupplier.dataUpdateSuppliers.length; i++) {
            await this.supplierUsecases.updateSupplier(
              dataSupplier.dataUpdateSuppliers[i].id,
              dataSupplier.dataUpdateSuppliers[i],
              jwtPayload,
              authorization,
              true,
            );
          }
          break;
        case EFileImportType.MATERIAL:
          const dataMaterial = body as ImportMaterialDto;
          for (let i = 0; i < dataMaterial.dataMaterials.length; i++) {
            await this.materialUsecases.createMaterial(
              dataMaterial.dataMaterials[i],
              jwtPayload,
              authorization,
              true,
            );
          }

          for (let i = 0; i < dataMaterial.dataUpdateMaterials.length; i++) {
            await this.materialUsecases.updateMaterial(
              dataMaterial.dataUpdateMaterials[i].id,
              dataMaterial.dataUpdateMaterials[i],
              jwtPayload,
              authorization,
              true,
            );
          }
          break;
        case EFileImportType.COST:
          const dataCost = body as ImportCostDto;
          for (let i = 0; i < dataCost.dataCosts.length; i++) {
            await this.costUsecases.createCost(
              dataCost.dataCosts[i],
              authorization,
            );
          }

          for (let i = 0; i < dataCost.dataUpdateCosts.length; i++) {
            await this.costUsecases.updateCost(
              dataCost.dataUpdateCosts[i].id,
              dataCost.dataUpdateCosts[i],
              jwtPayload,
              authorization,
            );
          }
          break;
        case EFileImportType.EXCHANGE_RATE:
          const dataCurrencyUnitExchange =
            body as ImportCurrencyUnitExchangeDto;
          if (
            dataCurrencyUnitExchange.dataCurrencyUnitExchanges &&
            dataCurrencyUnitExchange.dataCurrencyUnitExchanges.length
          ) {
            await this.currencyUnitExchangeUsecases.createManyCurrencyExchange(
              dataCurrencyUnitExchange.dataCurrencyUnitExchanges.map((item) => {
                return new CurrencyUnitExchangeModel(item);
              }),
            );
          }

          if (
            dataCurrencyUnitExchange.dataUpdateCurrencyUnitExchanges &&
            dataCurrencyUnitExchange.dataUpdateCurrencyUnitExchanges.length
          ) {
            await this.currencyUnitExchangeUsecases.updateManyCurrencyUnitExchange(
              dataCurrencyUnitExchange.dataUpdateCurrencyUnitExchanges.map(
                (item) => {
                  return new CurrencyUnitExchangeModel(item);
                },
              ),
            );
          }

          break;

        case EFileImportType.INVENTORY_STANDARD:
          const dataInventoryStandard = body as ImportInventoryStandardDto;
          for (
            let i = 0;
            i < dataInventoryStandard.dataInventoryStandards.length;
            i++
          ) {
            await this.inventoryStandardUsecases.createInventoryStandard(
              dataInventoryStandard.dataInventoryStandards[i],
              authorization,
              true,
            );
          }

          for (
            let i = 0;
            i < dataInventoryStandard.dataUpdateInventoryStandards.length;
            i++
          ) {
            await this.inventoryStandardUsecases.updateInventoryStandard(
              dataInventoryStandard.dataUpdateInventoryStandards[i].id,
              dataInventoryStandard.dataUpdateInventoryStandards[i],
              jwtPayload,
              true,
            );
          }
          break;
        case EFileImportType.BUSINESS_OWNER:
          const dataBusinessOwner = body as ImportBusinessOwnerDto;
          for (
            let i = 0;
            i < dataBusinessOwner.dataBusinessOwners.length;
            i++
          ) {
            await this.businessOwnerUsecases.createBusinessOwner(
              dataBusinessOwner.dataBusinessOwners[i],
              authorization,
            );
          }

          for (
            let i = 0;
            i < dataBusinessOwner.dataUpdateBusinessOwners.length;
            i++
          ) {
            await this.businessOwnerUsecases.updateBusinessOwner(
              dataBusinessOwner.dataUpdateBusinessOwners[i].id,
              dataBusinessOwner.dataUpdateBusinessOwners[i],
              jwtPayload,
              authorization,
            );
          }
          break;
        case EFileImportType.MATERIAL_GROUP:
          const dataMaterialGroup = body as ImportMaterialGroupDto;
          for (
            let i = 0;
            i < dataMaterialGroup.dataMaterialGroups.length;
            i++
          ) {
            await this.materialGroupUsecases.createMaterialGroup(
              dataMaterialGroup.dataMaterialGroups[i],
              jwtPayload,
            );
          }

          for (
            let i = 0;
            i < dataMaterialGroup.dataUpdateMaterialGroups.length;
            i++
          ) {
            await this.materialGroupUsecases.updateMaterialGroup(
              dataMaterialGroup.dataUpdateMaterialGroups[i].id,
              dataMaterialGroup.dataUpdateMaterialGroups[i],
              jwtPayload,
            );
          }
          break;
        case EFileImportType.MATERIAL_TYPE:
          const dataMaterialType = body as ImportMaterialTypeDto;
          for (let i = 0; i < dataMaterialType.dataMaterialTypes.length; i++) {
            await this.materialTypeUsecases.createMaterialType(
              dataMaterialType.dataMaterialTypes[i],
              authorization,
            );
          }

          for (
            let i = 0;
            i < dataMaterialType.dataUpdateMaterialTypes.length;
            i++
          ) {
            await this.materialTypeUsecases.updateMaterialType(
              dataMaterialType.dataUpdateMaterialTypes[i].id,
              dataMaterialType.dataUpdateMaterialTypes[i],
              jwtPayload,
              authorization,
            );
          }
          break;
        case EFileImportType.DEPARTMENT:
          const dataDepartment = body as ImportDepartmentDto;
          for (let i = 0; i < dataDepartment.dataDepartments.length; i++) {
            await this.departmentUsecases.createDepartment(
              dataDepartment.dataDepartments[i],
              authorization,
            );
          }

          for (
            let i = 0;
            i < dataDepartment.dataUpdateDepartments.length;
            i++
          ) {
            await this.departmentUsecases.updateDepartment(
              dataDepartment.dataUpdateDepartments[i],
              dataDepartment.dataUpdateDepartments[i].id,
              jwtPayload,
              authorization,
            );
          }
          break;
        case EFileImportType.COMPANY:
          const dataCompany = body as ImportCompanyDto;
          for (let i = 0; i < dataCompany.dataCompanys.length; i++) {
            await this.companyUsecases.createCompany(
              dataCompany.dataCompanys[i],
              authorization,
            );
          }

          for (let i = 0; i < dataCompany.dataUpdateCompanys.length; i++) {
            await this.companyUsecases.updateCompany(
              dataCompany.dataUpdateCompanys[i].id,
              dataCompany.dataUpdateCompanys[i],
              jwtPayload,
              authorization,
            );
          }
          break;
        case EFileImportType.BUSINESS_UNIT:
          const dataBusinessUnit = body as ImportBusinessUnitDto;
          for (let i = 0; i < dataBusinessUnit.dataBusinessUnits.length; i++) {
            await this.businessUnitUsecases.createBusinessUnit(
              dataBusinessUnit.dataBusinessUnits[i],
              authorization,
              jwtPayload,
            );
          }

          for (
            let i = 0;
            i < dataBusinessUnit.dataUpdateBusinessUnits.length;
            i++
          ) {
            await this.businessUnitUsecases.updateBusinessUnit(
              dataBusinessUnit.dataUpdateBusinessUnits[i],
              dataBusinessUnit.dataUpdateBusinessUnits[i].id,
              jwtPayload,
              authorization,
            );
          }
          break;
        case EFileImportType.ACTUAL_SPENDING:
          const dataActualSpending = body as ImportActualSpendingDto;

          await this.actualSpendingUsecases.createOrUpdateActualSpendingList(
            dataActualSpending.createActualSpendingDtos,
            jwtPayload,
          );
          break;
        default:
          break;
      }

      const updateFileImportHistory = new FileImportHistoryModel({
        status: EFileImportStatus.SUCCESS,
      });
      await this.fileImportHistoryUsecases.updateFileImportHistory(
        body.fileImportHistoryId,
        updateFileImportHistory,
      );

      return {
        message: 'Import excel done',
        fileImportHistoryId: body.fileImportHistoryId,
      };
    } catch (error) {
      if (body.fileImportHistoryId) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          body.fileImportHistoryId,
          updateFileImportHistory,
        );
      }

      throw error;
    }
  }
}
