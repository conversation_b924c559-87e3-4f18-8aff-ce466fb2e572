import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as _ from 'lodash';
import { CreatePurchaseOrderTypeDto } from '../controller/purchase-order-type/dtos/create-purchase-order-type.dto';
import { GetDetailPurchaseOrderTypeDto } from '../controller/purchase-order-type/dtos/get-detail-purchase-order-type.dto';
import { GetPurchaseOrderTypeListDto } from '../controller/purchase-order-type/dtos/get-purchase-order-type-list.dto';
import { UpdatePurchaseOrderTypeDto } from '../controller/purchase-order-type/dtos/update-purchase-order-type.dto';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EPurchaseOrderStatus } from '../domain/config/enums/purchasing.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { PurchaseOrderTypeErrorDetails } from '../domain/messages/error-details/purchase-order-type';
import { PurchaseOrderTypeModel } from '../domain/model/purchase-order-type.model';
import { IPurchaseOrderTypeRepository } from '../domain/repositories/purchase-order-type.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';

@Injectable()
export class PurchaseOrderTypeUsecases {
  constructor(
    @Inject(IPurchaseOrderTypeRepository)
    private readonly purchaseOrderTypeRepository: IPurchaseOrderTypeRepository,
  ) {}

  async createPurchaseType(
    data: CreatePurchaseOrderTypeDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PurchaseOrderTypeModel> {
    await this.verifyDataDto(data, jwtPayload);
    const purchaseOrderTypeModel = new PurchaseOrderTypeModel(data);
    const purchaseOrderType =
      await this.purchaseOrderTypeRepository.createPurchaseOrderType(
        purchaseOrderTypeModel,
      );
    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: purchaseOrderType.name,
        refId: purchaseOrderType.id,
        refCode: purchaseOrderType.code,
        type: EDataRoleType.PURCHASE_ORDER_TYPE,
        isEnabled: purchaseOrderType.status === EPurchaseOrderStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization },
    );

    return purchaseOrderType;
  }

  async updatePurchaseType(
    id: string,
    updatePurchaseOrderTypeDto: UpdatePurchaseOrderTypeDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PurchaseOrderTypeModel> {
    await this.verifyDataDto(updatePurchaseOrderTypeDto, jwtPayload, id);

    const purchaseOrder =
      await this.purchaseOrderTypeRepository.updatePurchaseOrderType(
        id,
        updatePurchaseOrderTypeDto,
      );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: purchaseOrder.name,
        refCode: purchaseOrder.code,
        isEnabled: purchaseOrder.status === EPurchaseOrderStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    return purchaseOrder;
  }

  async getPurchaseOrderTypes(
    conditions: GetPurchaseOrderTypeListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PurchaseOrderTypeModel>> {
    return await this.purchaseOrderTypeRepository.getPurchaseOrderTypes(
      conditions,
      jwtPayload,
    );
  }

  async deletePurchaseOrderType(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getPurchaseOrderTypeDetail(
      plainToInstance(GetDetailPurchaseOrderTypeDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.purchaseOrderTypeRepository.deletePurchaseOrderType(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistPurchaseOrderTypeByCode(
    code: string,
    id?: string,
  ): Promise<void> {
    const prType =
      await this.purchaseOrderTypeRepository.getPurchaseOrderTypeByCode(
        code,
        id,
      );

    if (prType) {
      throw new HttpException(
        PurchaseOrderTypeErrorDetails.E_3401(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async verifyDataDto(
    conditions: CreatePurchaseOrderTypeDto | UpdatePurchaseOrderTypeDto,
    jwtPayload: any,
    id?: string,
  ) {
    if (id) {
      await this.getPurchaseOrderTypeDetail(
        plainToInstance(GetDetailPurchaseOrderTypeDto, {
          id: id,
        }),
        jwtPayload,
      );
    }

    await this.checkExistPurchaseOrderTypeByCode(conditions.code, id);
  }

  async getPurchaseOrderTypeDetail(
    conditions: GetDetailPurchaseOrderTypeDto,
    jwtPayload,
  ) {
    const purchaseOrder =
      await this.purchaseOrderTypeRepository.getPurchaseOrderTypeDetail(
        conditions,
        jwtPayload,
      );

    if (!purchaseOrder) {
      throw new HttpException(
        PurchaseOrderTypeErrorDetails.E_3400(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return purchaseOrder;
  }

  async getPoTypeByIds(ids: string[], jwtPayload: any) {
    const poTypes = await this.purchaseOrderTypeRepository.getPoTypeByIds(
      ids,
      jwtPayload,
    );

    if (!poTypes || !poTypes.length) {
      throw new HttpException(
        PurchaseOrderTypeErrorDetails.E_3400(),
        HttpStatus.NOT_FOUND,
      );
    }

    const activePoTypes = poTypes?.filter(
      (item) => item.status === EPurchaseOrderStatus.ACTIVE,
    );

    const activePoTypeIds = activePoTypes.map((item) => item.id);

    const missingPoTypeIds = _.difference(ids, activePoTypeIds);

    if (missingPoTypeIds.length) {
      throw new HttpException(
        PurchaseOrderTypeErrorDetails.E_3400(
          `PO Type ids ${missingPoTypeIds.join(', ')} not found or inactive`,
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return poTypes;
  }
}
