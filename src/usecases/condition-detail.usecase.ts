import { Inject, Injectable } from '@nestjs/common';
import { CreateConditionDetailDto } from '../controller/process/dtos/create-condition-detail.dto';
import { ConditionDetailModel } from '../domain/model/condition-detail.model';
import { IConditionDetailRepository } from '../domain/repositories/condition-detail.repository';
import { processInBatches } from '../utils/common';
import { BudgetCodeUsecases } from './budget-code.usecases';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CompanyUsecases } from './company.usecases';
import { CostcenterSubaccountUsecases } from './costcenter-subaccount.usecases';
import { DepartmentUsecases } from './department.usecases';
import { FunctionUnitUsecases } from './function-unit.usecases';
import { PlantUsecases } from './plant.usecases';
import { ProcessTypeUsecases } from './process-type.usecases';
import { PurchaseOrderTypeUsecases } from './purchase-order-type.usecases';
import { PurchaseRequestTypeUsecases } from './purchase-request-type.usecases';
import { SectorUsecases } from './sector.usecases';

@Injectable()
export class ConditionDetailUsecases {
  constructor(
    private sectorUsecases: SectorUsecases,
    private companyUsecases: CompanyUsecases,
    private businessUnitUsecases: BusinessUnitUsecases,
    private departmentUsecases: DepartmentUsecases,
    private costcenterSubaccountUsecases: CostcenterSubaccountUsecases,
    private budgetCodeUsecases: BudgetCodeUsecases,
    private purchaseRequestTypeUsecases: PurchaseRequestTypeUsecases,
    private purchaseOrderTypeUsecases: PurchaseOrderTypeUsecases,
    private processTypeUsecases: ProcessTypeUsecases,
    private plantUsecases: PlantUsecases,
    private functionUnitUsecases: FunctionUnitUsecases,
    @Inject(IConditionDetailRepository)
    private readonly conditionRepository: IConditionDetailRepository,
  ) {}
  async createConditionDetails(
    createConditionDtos: CreateConditionDetailDto[],
    jwtPayload: string,
  ): Promise<ConditionDetailModel[]> {
    const batchSize = 5;

    const enrichedDtos: CreateConditionDetailDto[] = [];
    await processInBatches(createConditionDtos, batchSize, async (batch) => {
      const enrichedBatch = await Promise.all(
        batch.map(async (dto) => {
          const [
            sectors,
            companies,
            businessUnits,
            departments,
            costCenters,
            budgetCodes,
            prTypes,
            poTypes,
            processTypes,
            plants,
            functionUnits,
          ] = await Promise.all([
            dto.sectorIds?.length
              ? this.sectorUsecases.getSectorByIds(
                  [...new Set(dto.sectorIds)],
                  jwtPayload,
                )
              : [],
            dto.companyIds?.length
              ? this.companyUsecases.getCompanyByIds(
                  [...new Set(dto.companyIds)],
                  jwtPayload,
                )
              : [],
            dto.businessUnitIds?.length
              ? this.businessUnitUsecases.getBuByIds(
                  [...new Set(dto.businessUnitIds)],
                  jwtPayload,
                )
              : [],
            dto.departmentIds?.length
              ? this.departmentUsecases.getDepartmentByIds(
                  [...new Set(dto.departmentIds)],
                  jwtPayload,
                )
              : [],
            dto.costCenterIds?.length
              ? this.costcenterSubaccountUsecases.getCostCenterByIds(
                  [...new Set(dto.costCenterIds)],
                  jwtPayload,
                )
              : [],
            dto.budgetCodeIds?.length
              ? this.budgetCodeUsecases.getBudgetCodeByIds(
                  [...new Set(dto.budgetCodeIds)],
                  jwtPayload,
                )
              : [],
            dto.prTypeIds?.length
              ? this.purchaseRequestTypeUsecases.getPrTypeByIds(
                  [...new Set(dto.prTypeIds)],
                  jwtPayload,
                )
              : [],
            dto.poTypeIds?.length
              ? this.purchaseOrderTypeUsecases.getPoTypeByIds(
                  [...new Set(dto.poTypeIds)],
                  jwtPayload,
                )
              : [],
            dto.processTypeIds?.length
              ? this.processTypeUsecases.getProcessTypeByIds([
                  ...new Set(dto.processTypeIds),
                ])
              : [],
            dto.plantIds?.length
              ? this.plantUsecases.getPlantByIds(
                  [...new Set(dto.plantIds)],
                  jwtPayload,
                )
              : [],
            dto.functionUnitIds?.length
              ? this.functionUnitUsecases.getFunctionUnitByIds(
                  [...new Set(dto.functionUnitIds)],
                  jwtPayload,
                )
              : [],
          ]);

          return {
            ...dto,
            sectors,
            companies,
            businessUnits,
            departments,
            costCenters,
            budgetCodes,
            prTypes,
            poTypes,
            processTypes,
            plants,
            functionUnits,
          };
        }),
      );
      enrichedDtos.push(...enrichedBatch);
    });

    return await this.conditionRepository.createConditionDetails(enrichedDtos);
  }

  async deleteConditionDetailByConditionId(id: string): Promise<void> {
    await this.conditionRepository.deleteConditionDetailByConditionId(id);
  }
}
