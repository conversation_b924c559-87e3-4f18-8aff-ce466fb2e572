import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import { resolve } from 'path';
import {
  ApprovalLevelDto,
  StatusLevel,
} from '../controller/approve/dtos/approve.dto';
import { GetPurchaseOrderDto } from '../controller/purchase-order/dtos/get-all-purchase-order.dto';
import { GetPoDetailReportBudgetDetailDto } from '../controller/purchase-order/dtos/get-po-detail-report-budget.dto';
import { GetPOWithBudgetDto } from '../controller/purchase-order/dtos/get-po-with-budget.dto';
import { PurchaseOrderDetailDto } from '../controller/purchase-order/dtos/purchase-order-detail.dto';
import {
  PurchaseOrderDto,
  UpdatePurchaseOrderDto,
} from '../controller/purchase-order/dtos/purchase-order.dto';
import { exportFileUploadPath } from '../domain/config/constant';
import {
  EApprover,
  PO_APPROVER_KEYS,
} from '../domain/config/enums/approver.enum';
import {
  EBudgetCreateType,
  EBudgetType,
} from '../domain/config/enums/budget.enum';
import { EConditionType } from '../domain/config/enums/condition-type.enum';
import { EProcessType } from '../domain/config/enums/process-type.enum';
import {
  EDisplayStatus,
  EPaymentMethod,
  Priority,
  State,
  Status,
} from '../domain/config/enums/purchase-order.enum';
import { EStatus } from '../domain/config/enums/status.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { approvalErrorDetails } from '../domain/messages/error-details/approval';
import { measureErrorDetails } from '../domain/messages/error-details/measure';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessage,
} from '../domain/messages/error-message';
import { ApprovalProcessDetailModel } from '../domain/model/approval-process-detail.model';
import { BudgetModel } from '../domain/model/budget.model';
import { PurchaseOrderModel } from '../domain/model/purchase_order.model';
import { PurchaseOrderDetailModel } from '../domain/model/purchase_order_detail.model';
import { PurchaseRequestModel } from '../domain/model/purchase_request.model';
import { PurchaseRequestDetailModel } from '../domain/model/purchase_request_detail.model';
import { StaffModel } from '../domain/model/staff.model';
import { IPrApprovalFlowRepository } from '../domain/repositories/prApprovalFlowRepository.repository';
import { IPriceInformationRecordRepository } from '../domain/repositories/priceInformationRecordRepository.repository';
import { IPurchaseOrderRepository } from '../domain/repositories/purchaseOrderRepository.repository';
import { IPurchaseRequestRepository } from '../domain/repositories/purchaseRequestRepository.repository';
import { emailTemplateService } from '../infrastructure/config/email-transport-config/email-template.service';
import { emailTransportService } from '../infrastructure/config/email-transport-config/email.service';
import { ApproveType } from '../infrastructure/entities/approval-level.entity';
import { HttpService } from '../infrastructure/http/http.service';
import {
  checkEffectiveTimeOverlaps,
  codeToIdMap,
  comparisonResult,
  convertToGMT7,
  embedIdInUuid,
  startOfDay,
} from '../utils/common';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendPost } from '../utils/http';
import { ApprovalProcessDetailUsecases } from './approval-process-detail.usecases';
import { BudgetCodeUsecases } from './budget-code.usecases';
import { BudgetUsecases } from './budget.usecases';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CostcenterSubaccountUsecases } from './costcenter-subaccount.usecases';
import { CurrencyUnitUsecases } from './currency-unit.usecases';
import { DepartmentUsecases } from './department.usecases';
import { FileUsecases } from './file.usecases';
import { FunctionUnitUsecases } from './function-unit.usecases';
import { MaterialGroupUsecases } from './material-group.usecases';
import { MaterialUsecases } from './material.usecases';
import { MeasureUsecases } from './measure.usecases';
import { PlantUsecases } from './plant.usecases';
import { ProcessTypeUsecases } from './process-type.usecases';
import { ProcessUsecases } from './process.usecases';
import { PurchaseOrderTypeUsecases } from './purchase-order-type.usecases';
import { purchaseRequestUsecases } from './purchase_request.usecases';
import { PurchasingDepartmentUsecases } from './purchasing-department.usecases';
import { PurchasingGroupUsecases } from './purchasing-group.usecases';
import { SectorUsecases } from './sector.usecases';
import { StaffUsecases } from './staff.usecase';
import { SupplierUsecases } from './supplier.usecases';
import { TaxCodeUsecases } from './tax-code.usecases';
import { taxCodeErrorDetails } from '../domain/messages/error-details/6550-tax-code';
import { IActualSpendingRepository } from '../domain/repositories/actual-spending.repository';
import { EStatusActualSpending } from '../domain/config/enums/actual-spending.enum';
import { ActualSpendingModel } from '../domain/model/actual-spending.model';
import { ResendEmailPoDto } from '../controller/purchase-order/dtos/resend-email-po.dto';
import { warehouseErrorDetails } from '../domain/messages/error-details/7050-warehouse';
import { WarehouseUsecases } from './warehouse.usecases';
import { EAccountAssignment } from '../domain/config/enums/account-assignment.enum';

@Injectable()
export class purchaseOrderUsecases {
  private transporter;

  constructor(
    @Inject('IPurchaseOrderRepository')
    private readonly purchaseOrderRepository: IPurchaseOrderRepository,
    @Inject('IPurchaseRequestRepository')
    private readonly purchaseRequestRepository: IPurchaseRequestRepository,
    @Inject('IPriceInformationRecordRepository')
    private readonly priceInformationRecordRepository: IPriceInformationRecordRepository,
    @Inject('IPrApprovalFlowRepository')
    private readonly prApprovalFlowRepository: IPrApprovalFlowRepository,
    private configService: ConfigService,
    private _emailTransportService: emailTransportService,
    private _emailTemplateService: emailTemplateService,
    private readonly httpService: HttpService,
    private readonly _purchaseRequestUsecases: purchaseRequestUsecases,
    private fileUsecases: FileUsecases,
    @Inject(IActualSpendingRepository)
    private readonly actualSpendingRepository: IActualSpendingRepository,
    private businessUnitUsecases: BusinessUnitUsecases,
    private sectorUsecases: SectorUsecases,
    private staffUsecases: StaffUsecases,
    private purchaseOrderTypeUsecases: PurchaseOrderTypeUsecases,
    private budgetCodeUsecases: BudgetCodeUsecases,
    private costcenterSubaccountUsecases: CostcenterSubaccountUsecases,
    private purchasingDepartmentUsecases: PurchasingDepartmentUsecases,
    private purchasingGroupUsecases: PurchasingGroupUsecases,
    private currencyUnitUsecases: CurrencyUnitUsecases,
    private processTypeUsecases: ProcessTypeUsecases,
    private plantUsecases: PlantUsecases,
    private functionUnitUsecases: FunctionUnitUsecases,
    private departmentUsecases: DepartmentUsecases,
    private readonly processUsecases: ProcessUsecases,
    private readonly materialUsecases: MaterialUsecases,
    private readonly supplierUsecases: SupplierUsecases,
    private readonly materialGroupUsecases: MaterialGroupUsecases,
    private readonly budgetUsecases: BudgetUsecases,
    private measureUsecases: MeasureUsecases,
    private readonly approvalProcessDetailUsecases: ApprovalProcessDetailUsecases,
    private readonly taxCodeUsecases: TaxCodeUsecases,
    private readonly warehouseUsecases: WarehouseUsecases,
  ) {
    this.transporter = this._emailTransportService.getTransporter();
  }

  async findAll(
    paginationDto: GetPurchaseOrderDto,
    authorization,
    jwtPayload,
    isImport: boolean = false,
  ): Promise<ResponseDto<PurchaseOrderModel>> {
    // if (paginationDto.businessUnitIds && paginationDto.companyIds) {
    //   const businessUnits = await this.businessUnitUsecases.getBusinessUnits(
    //     {
    //       ids: paginationDto.businessUnitIds,
    //       companyIds: paginationDto.companyIds,
    //       getAll: 1,
    //       limit: 5,
    //       page: 1,
    //       searchString: '',
    //     },
    //     jwtPayload,
    //   );
    //   const businessUnitIds =
    //     businessUnits?.results?.map((item) => item.id) || [];
    //   paginationDto.businessUnitIds =
    //     businessUnitIds && businessUnitIds.length ? businessUnitIds : [null];
    // }

    // if (!jwtPayload?.isSuperAdmin) {
    //   const [businessUnits, sectors] = await Promise.all([
    //     this.businessUnitUsecases.getBusinessUnits(
    //       {
    //         getAll: 1,
    //         limit: 5,
    //         page: 1,
    //         searchString: '',
    //       },
    //       jwtPayload,
    //     ),
    //     this.sectorUsecases.getSectors(
    //       {
    //         getAll: 1,
    //         limit: 5,
    //         page: 1,
    //         searchString: '',
    //       },
    //       jwtPayload,
    //     ),
    //   ]);

    //   paginationDto.businessUnitIds = paginationDto.businessUnitIds?.length
    //     ? paginationDto.businessUnitIds.filter((item) =>
    //         (businessUnits?.results || [])
    //           .map((data) => data.id)
    //           .includes(item),
    //       )
    //     : (businessUnits?.results || []).map((data) => data.id);
    //   paginationDto.sectorIds = paginationDto.sectorIds?.length
    //     ? paginationDto.sectorIds.filter((item) =>
    //         (sectors?.results || []).map((data) => data.id).includes(item),
    //       )
    //     : (sectors?.results || []).map((data) => data.id);

    //   if (!paginationDto.businessUnitIds?.length) {
    //     paginationDto.businessUnitIds = [null];
    //   }

    //   if (!paginationDto.sectorIds?.length) {
    //     paginationDto.sectorIds = [null];
    //   }
    // }

    const data = await this.purchaseOrderRepository.findAll(
      paginationDto,
      jwtPayload,
      isImport,
    );

    const promises = data.results.map(async (item) => {
      const approvalLevels =
        await this.prApprovalFlowRepository.findApprovalLevels(null, item.id);

      const sortedLevels =
        approvalLevels?.sort((a, b) => a.level - b.level) || [];
      item.levels = sortedLevels.slice(1);

      const sortedDetails = item.details.sort((a, b) => a.id - b.id);
      item.details = sortedDetails;

      // Mapping result vào dữ liệu của item
      return item;
    });

    // Chờ tất cả các Promise hoàn thành
    const mappedDataArray = await Promise.all(promises);

    // Thay thế dữ liệu cũ bằng dữ liệu mới đã được mapping
    return {
      ...data,
      results: mappedDataArray,
    };
  }

  async getPoById(id: number): Promise<PurchaseOrderModel> {
    return await this.purchaseOrderRepository.findOneById(id);
  }

  async findPoWithIds(ids: number[]): Promise<PurchaseOrderModel[]> {
    return await this.purchaseOrderRepository.findPoWithIds(ids);
  }

  async findById(id: number): Promise<PurchaseOrderModel> {
    const po = await this.purchaseOrderRepository.findOne(id);
    if (!po) {
      throw new NotFoundException(`Purchase order with id ${id} not found`);
    }
    return po;
  }

  async findOne(
    id: number,
    authorization?: string,
  ): Promise<PurchaseOrderModel> {
    let item = await this.purchaseOrderRepository.findOne(id);
    const approvalLevels =
      await this.prApprovalFlowRepository.findApprovalLevels(null, id);

    if (!item) {
      throw new NotFoundException(`Purchase order with id ${id} not found`);
    }

    const sortedLevels = approvalLevels.sort((a, b) => a.level - b.level);
    item.levels = sortedLevels;

    const sortedDetails = item.details.sort((a, b) => b.id - a.id);
    item.details = sortedDetails;

    return item;
  }

  async create(
    purchases: PurchaseOrderDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<
    | PurchaseOrderModel
    | { approvalSteps: ApprovalLevelDto[]; staffApprovalWorkflowId: string }
  > {
    purchases.displayStatusPo = await this.getDisplayStatus(purchases.statusPo);

    //Validate details
    purchases = await this.validatePurchases(
      purchases,
      authorization,
      jwtPayload,
    );

    const result = await this.callServices(
      purchases,
      authorization,
      jwtPayload,
    );

    if (!result.sector) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5105()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!result.businessUnit) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5106()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!result.requester) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5107()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!result.typePo) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5208()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (result.typePo?.status !== 'ACTIVE') {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5209()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.costCenterId) {
      if (!result.costCenter) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_5025()),
          HttpStatus.BAD_REQUEST,
        );
      } else {
        const currentDate = startOfDay(new Date());
        if (
          (!result.costCenter?.effectiveEndDate &&
            currentDate < new Date(result.costCenter?.effectiveStartDate)) ||
          (result.costCenter?.effectiveEndDate &&
            (currentDate < new Date(result.costCenter?.effectiveStartDate) ||
              currentDate > new Date(result.costCenter?.effectiveEndDate)))
        ) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_5029()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    }

    if (purchases.purchaseOrgId && !result.purchaseOrg) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5005()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.purchaseGroupId && !result.purchaseGroup) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5006()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.budgetCodeId && !result.budgetCode) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5024()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.currencyId && !result.currency) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5028()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.plantId && !result.plant) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5031()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!result.processType) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5030()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.functionUnitId && !result.functionUnit) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5033()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.departmentId && !result.department) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5032()),
        HttpStatus.BAD_REQUEST,
      );
    }

    purchases.details = await this.newCalculateRemainingBudget(
      purchases.details,
      authorization,
      new Date(),
      jwtPayload,
    );

    const processes = await this.processUsecases.getProcessList({
      statuses: [EStatus.ACTIVE],
      types: [EProcessType.PO],
      getAll: 1,
      page: 1,
      limit: 10,
      searchString: '',
    });

    if (!processes?.results?.length) {
      throw new HttpException(
        approvalErrorDetails.E_5410(),
        HttpStatus.BAD_REQUEST,
      );
    }

    // const getFlows = await this.prApprovalFlowRepository.findActive();
    // const sortedLevels = getFlows[0].levels.sort((a, b) => a.level - b.level);
    // purchases.history = sortedLevels.slice(1).map((level) => {
    //   const { id, ...rest } = level;
    //   return rest;
    // });

    const purchase =
      await this.purchaseOrderRepository.createPurchaseOrder(purchases);

    const prReferenceIds = [
      ...new Set(
        (purchase.details || [])
          .map((item) => item.prReference)
          .filter(Boolean),
      ),
    ];

    if (prReferenceIds && prReferenceIds?.length) {
      await this.purchaseRequestRepository.checkIsPO(
        prReferenceIds.map((item) => Number(item)),
      );
    }

    // const refId = embedIdInUuid(purchase.id);
    // await this.purchaseOrderRepository.updatePurchaseOrder(purchase.id, {
    //   refId,
    // });

    const processedItem = await this.handleObjectProcessing(
      purchase.id,
      authorization,
    );

    let approvalSteps: ApprovalLevelDto[] = [];
    if (!purchases.approvalLevelDtos?.length) {
      const processMatching = {};

      const managersOfRequester = result?.requester?.managers || [];

      for (const process of processes.results) {
        processMatching[process.id] = [];
        const processDetail = await this.processUsecases.getDetailProcessGraph(
          process.id,
        );
        const node = processDetail;

        await this.traverseAndCheckNodes(
          purchases,
          node,
          async (node, purchases) =>
            await this.checkConditionNode(node, purchases),
          processMatching[process.id],
          authorization,
        );
      }

      if (!Object.keys(processMatching).length) {
        throw new HttpException(
          approvalErrorDetails.E_5410(),
          HttpStatus.BAD_REQUEST,
        );
      }

      const prIds = [
        ...new Set(
          (purchases.details || [])
            .map((item) => item.prReference)
            .filter(Boolean),
        ),
      ];
      const prs = prIds?.length
        ? await this.purchaseRequestRepository.getPrListByIds(prIds)
        : [];

      const prTypeIds = [
        ...new Set((prs || []).map((item) => item.typePrId).filter(Boolean)),
      ];
      // Get ApprovalProcessDetail
      const approvalProcessDetails =
        await this.approvalProcessDetailUsecases.getApprovalProcessDetails(
          {
            sectorIds: [purchases.sectorId],
            departmentIds: purchases.departmentId
              ? [purchases.departmentId]
              : [null],
            functionUnitIds: purchases.functionUnitId
              ? [purchases.functionUnitId]
              : [null],
            prTypeIds: prTypeIds?.length ? prTypeIds : [null],
            businessUnitIds: [purchases.businessUnitId],
            poCreatedByIds: purchases.requesterId
              ? [purchases.requesterId]
              : [null],
            limit: 1,
            page: 1,
            searchString: '',
          },
          jwtPayload,
        );

      for (const [key, value] of Object.entries(processMatching)) {
        if (!value || !(value as string[]).length) {
          throw new HttpException(
            approvalErrorDetails.E_5410(),
            HttpStatus.BAD_REQUEST,
          );
        }
        const processParentDetail =
          await this.processUsecases.getDetailParentProcess(key);

        if (processParentDetail?.parentApprovalWorkflows?.length) {
          //Approval-Level
          for (const parentApprovalWorkflow of processParentDetail.parentApprovalWorkflows) {
            const { processes, staffApprovalWorkflows, sendEmailToCreator } =
              parentApprovalWorkflow;
            const processIds = processes.map((process) => process.id);

            if (
              processIds?.some((processId) =>
                (value as string[]).includes(processId),
              )
            ) {
              // const logInStaff = await getData(PurchaseServiceApiUrlsConst.GET_DETAIL_STAFF(jwtPayload.staffId), { authorization });

              // Sort levels in ascending order
              const sortedStaffApprovalWorkflows = staffApprovalWorkflows.sort(
                (a, b) => a.level - b.level,
              );

              const getManagerDetails = (approver: EApprover) => {
                const levelMap = {
                  [EApprover.LEVEL_1]: 1,
                  [EApprover.LEVEL_2]: 2,
                  [EApprover.LEVEL_3]: 3,
                  [EApprover.LEVEL_4]: 4,
                };
                const level = levelMap[approver];
                const manager = managersOfRequester?.find(
                  (manager) => manager.level === level,
                );
                if (!manager) {
                  throw new HttpException(
                    approvalErrorDetails.E_5411(
                      `Không tìm thấy quản lý cấp ${level} duyệt`,
                    ),
                    HttpStatus.BAD_REQUEST,
                  );
                }
                return {
                  email: manager?.email || '',
                  name: `${manager?.lastName || ''} ${manager?.firstName || ''}`,
                };
              };

              const getStaffDetails = async (
                id: string,
                errorMessage: TErrorMessage,
              ) => {
                const staff = await this.staffUsecases.getDetailStaff(
                  { staffId: id },
                  jwtPayload,
                );
                if (!staff) {
                  throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
                }

                return {
                  email: staff?.email || '',
                  name: `${staff?.lastName || ''} ${staff?.firstName || ''}`,
                };
              };

              for (
                let index = 0;
                index < sortedStaffApprovalWorkflows.length;
                index++
              ) {
                const workflow = sortedStaffApprovalWorkflows[index];
                let approvalLevel: ApprovalLevelDto = {
                  role: `Người duyệt cấp ${workflow.level}`,
                  isSendMail: workflow.receiveEmail,
                  level: workflow.level,
                  purchaseOrderId: purchase.id,
                  status: index === 0 ? 'Pending' : null,
                  approveType: ApproveType.PO,
                  isSendMailCreator: sendEmailToCreator,
                  isAccountantApproved: workflow.accountantApproved,
                  allowSelect: workflow.allowSelect,
                  staffApprovalWorkflowId: workflow.id,
                };

                switch (workflow.approver) {
                  case EApprover.LEVEL_1:
                  case EApprover.LEVEL_2:
                  case EApprover.LEVEL_3:
                  case EApprover.LEVEL_4: {
                    const { email, name } = getManagerDetails(
                      workflow.approver,
                    );
                    approvalLevel.email = email;
                    approvalLevel.name = name;
                    break;
                  }
                  case EApprover.ASSIGN: {
                    const { email, name } = await getStaffDetails(
                      workflow?.staff?.id,
                      approvalErrorDetails.E_5411(
                        'Không tìm thấy người chỉ định duyệt',
                      ),
                    );
                    approvalLevel.email = email;
                    approvalLevel.name = name;
                    break;
                  }
                  case EApprover.POSITION: {
                    const staffs = await this.staffUsecases.approverByPosition(
                      {
                        statuses: [EStatus.ACTIVE],
                        positionIds: [workflow?.position?.id],
                        businessUnitIds: [purchases.businessUnitId],
                        functionUnitIds: [purchases.functionUnitId],
                        departmentIds: [purchases.departmentId],
                      },
                      jwtPayload,
                      authorization,
                    );

                    if (!staffs?.length) {
                      throw new HttpException(
                        approvalErrorDetails.E_5411(
                          'Không tìm thấy người duyệt',
                        ),
                        HttpStatus.BAD_REQUEST,
                      );
                    }

                    const staff = staffs[0];

                    approvalLevel.email = staff?.email || '';
                    approvalLevel.name = `${staff?.lastName || ''} ${staff?.firstName || ''}`;
                    break;
                  }
                  case EApprover.PR_APPROVER_1:
                  case EApprover.PR_APPROVER_2:
                  case EApprover.PR_APPROVER_3:
                  case EApprover.PR_APPROVER_4:
                  case EApprover.PR_APPROVER_5:
                  case EApprover.PR_APPROVER_6:
                  case EApprover.PR_APPROVER_7:
                  case EApprover.PO_APPROVER_1:
                  case EApprover.PO_APPROVER_2:
                  case EApprover.PO_APPROVER_3: {
                    approvalLevel = this.getApprovalLevelByAprovalProcessDetail(
                      approvalProcessDetails,
                      approvalLevel,
                      workflow.approver,
                      purchases,
                    );
                    break;
                  }
                }

                approvalSteps.push(approvalLevel);
              }
              const allowSelectStaffApprovalWorkflow =
                sortedStaffApprovalWorkflows?.find(
                  (workflow) => workflow.allowSelect,
                );
              if (
                allowSelectStaffApprovalWorkflow &&
                !sortedStaffApprovalWorkflows?.find(
                  (workflow) => workflow.accountantApproved,
                )
              ) {
                return {
                  approvalSteps,
                  staffApprovalWorkflowId: allowSelectStaffApprovalWorkflow?.id,
                };
              }
              break;
            }
          }
        }
      }
    } else {
      approvalSteps = purchases.approvalLevelDtos;

      approvalSteps.forEach((level) => {
        level.approveType = ApproveType.PO;
        level.purchaseOrderId = purchase.id;
      });

      const emailChecks = approvalSteps?.map((step) => step.email);
      await this.staffUsecases.getStaffByEmails(
        { emails: emailChecks },
        jwtPayload,
      );
    }

    if (!approvalSteps?.length) {
      throw new HttpException(
        approvalErrorDetails.E_5410(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const approvalLevels =
      await this.prApprovalFlowRepository.createApprovalLevels(approvalSteps);
    purchases.history = approvalLevels.map((level) => {
      const { id, ...rest } = level;
      return rest;
    });

    // lowestStaffApprovalWorkflowLevel.status = 'Pending';
    await this.prApprovalFlowRepository.changeStatusPurchaseOrder(
      { levels: approvalLevels },
      purchase.id,
    );
    // purchase.approval_steps = approvalSteps;
    purchase.levels = approvalLevels;

    const sortedApprovalLevels = approvalLevels.sort(
      (a, b) => a.level - b.level,
    );

    if (sortedApprovalLevels[0].isSendMail) {
      if (!sortedApprovalLevels[0].email) {
        throw new HttpException(
          approvalErrorDetails.E_5410(`Không tìm thấy nhân viên`),
          HttpStatus.BAD_REQUEST,
        );
      }

      const mailOptions = {
        from: this.configService.get<string>('MAIL_SMTP_USER'),
        to: sortedApprovalLevels[0].email,
        subject: `Phê duyệt PO #${processedItem.id}${processedItem.businessUnit?.code ? ` - ${processedItem.businessUnit.code}` : ''}${processedItem.reason ? ` - ${processedItem.reason}` : ''}`,
        html: this._emailTemplateService.getApprovalPOEmailHtml(
          processedItem,
          sortedApprovalLevels[0].email,
          `${this.configService.get<string>('STATUS_PO_EMAIL_URL')}?levelId=${sortedApprovalLevels[0].id}&poId=${purchase.id}`,
          sortedApprovalLevels[0],
        ),
      };

      if (purchase.statusPo !== Status.Draft) {
        try {
          if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
            await sendPost(
              QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
              mailOptions,
              { authorization },
            );
          } else {
            await this.transporter.sendMail(mailOptions);
          }
        } catch (e) {}
      }
    }

    const prIds = purchase.details
      .map((detail) => detail.prReference)
      .filter(Boolean);
    await this._purchaseRequestUsecases.changeStatePr(prIds);

    const refId = embedIdInUuid(purchase.id);
    await this.purchaseOrderRepository.updatePurchaseOrder(purchase.id, {
      refId,
      history: purchases.history?.map((history) => {
        return {
          ...history,
          status: Status.Pending,
        };
      }),
    });

    return purchase;
  }
  // Kiểm tra yêu cầu budget
  private checkBudgetRequirement(purchases: PurchaseOrderDto) {
    if (purchases.isCheckBudget && !purchases.budgetCodeId) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5100()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
  // Tính toán số lượng và kiểm tra điều kiện vật tư
  private async validateQuantity(
    poDetail: PurchaseOrderDetailDto,
    poId?: number,
    prs?: PurchaseRequestModel[],
    poDetails?: PurchaseOrderDetailModel[],
  ) {
    if (poDetail.prDetailId && poDetail.prReference) {
      const standardQuantity =
        (
          (prs || []).find((item) => item.id == poDetail.prReference)
            ?.details || []
        ).find((item) => item.id === poDetail.prDetailId)?.quantity || 0;
      const countNumberPoCreated = poId
        ? (poDetails || [])
            .filter(
              (item) =>
                item.prDetailId == poDetail.prDetailId &&
                item.purchaseOrder?.id != poId,
            )
            .reduce((a, b) => a + Number(b.quantity || 0), 0)
        : (poDetails || [])
            .filter((item) => item.prDetailId == poDetail.prDetailId)
            .reduce((a, b) => a + Number(b.quantity || 0), 0);
      const totalQuantity =
        countNumberPoCreated + Number(poDetail.quantity || 0);

      if (totalQuantity > standardQuantity) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_5022()),
          HttpStatus.BAD_REQUEST,
        );
      }
      poDetail.requestedQuantity =
        poDetail.requestedQuantity ?? standardQuantity - countNumberPoCreated;
    }
  }
  // Tính toán tổng số tiền sau khi quy đổi và thuế VAT
  private calculateAmounts(poDetail: PurchaseOrderDetailDto) {
    poDetail.totalConvertedAmount =
      Number(poDetail.totalAmount || 0) * Number(poDetail.exchangeRate);
    poDetail.totalAmountVat =
      Number(poDetail.totalConvertedAmount) *
      (1 + Number(poDetail.vat || 0) / 100);
  }
  // Kiểm tra chi tiết vật tư và gọi dịch vụ material
  private validateResultDetail(
    dataDetails: {
      budgetCodes: Record<string, any>;
      materialCodes: Record<string, any>;
      costCenters: Record<string, any>;
      suppliers: Record<string, any>;
      materialGroups: Record<string, any>;
      currencys: Record<string, any>;
      pirs: Record<string, any>;
      measureCodes: Record<string, any>;
      taxCodes: Record<string, any>;
      warehouses: Record<string, any>;
    },
    poDetail: PurchaseOrderDetailDto,
    isCheckBudget: boolean,
    jwtPayload: any,
  ) {
    const errorChecks = [
      {
        condition:
          poDetail.materialCodeId &&
          !dataDetails.materialCodes[poDetail.materialCodeId],
        error: errorMessage.E_5003(),
      },
      {
        condition:
          poDetail.budgetCodeId &&
          !dataDetails.budgetCodes[poDetail.budgetCodeId],
        error: errorMessage.E_5024(),
      },
      {
        condition:
          poDetail.costCenterId &&
          !dataDetails.costCenters[poDetail.costCenterId],
        error: errorMessage.E_5025(),
      },
      {
        condition:
          poDetail.materialGroupId &&
          !dataDetails.materialGroups[poDetail.materialGroupId],
        error: errorMessage.E_5026(),
      },
      {
        condition: poDetail.pirId && !dataDetails.pirs[poDetail.pirId],
        error: errorMessage.E_5008(),
      },
      {
        condition:
          poDetail.supplierId && !dataDetails.suppliers[poDetail.supplierId],
        error: errorMessage.E_5027(),
      },
      {
        condition:
          poDetail.currencyId && !dataDetails.currencys[poDetail.currencyId],
        error: errorMessage.E_5028(),
      },
      {
        condition:
          poDetail.measureId && !dataDetails.measureCodes[poDetail.measureId],
        error: measureErrorDetails.E_6050(),
      },
      {
        condition:
          poDetail.warehouseId && !dataDetails.warehouses[poDetail.warehouseId],
        error: warehouseErrorDetails.E_7050(),
      },
      {
        condition: !dataDetails.taxCodes[poDetail.taxCodeId],
        error: taxCodeErrorDetails.E_6550(),
      },
    ];

    errorChecks.forEach(({ condition, error }) => {
      if (condition) {
        throw new HttpException(getErrorMessage(error), HttpStatus.BAD_REQUEST);
      }
    });

    const checkBudgetError = isCheckBudget
      ? 'NO_CHECK_BUDGET'
      : 'YES_CHECK_BUDGET';
    if (
      dataDetails.materialCodes[poDetail.materialCodeId]?.checkBudget ===
      checkBudgetError
    ) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5101()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
  // Hàm tổng hợp để validate từng chi tiết
  private async validateDetail(
    poDetail: PurchaseOrderDetailDto,
    isCheckBudget: boolean,
    authorization: string,
    jwtPayload: any,
    dataDetails: {
      budgetCodes: Record<string, any>;
      materialCodes: Record<string, any>;
      costCenters: Record<string, any>;
      suppliers: Record<string, any>;
      materialGroups: Record<string, any>;
      currencys: Record<string, any>;
      pirs: Record<string, any>;
      measureCodes: Record<string, any>;
      taxCodes: Record<string, any>;
      warehouses: Record<string, any>;
    },
    poId?: number,
    prs?: PurchaseRequestModel[],
    poDetails?: PurchaseOrderDetailModel[],
  ) {
    if (
      poDetail.accountAssignment != EAccountAssignment.A &&
      !poDetail.materialCodeId &&
      !poDetail.costCenterId &&
      !poDetail.budgetCodeId
    ) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5023()),
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.validateQuantity(poDetail, poId, prs, poDetails);
    this.calculateAmounts(poDetail);

    // const resultDetail = await this.callMaterialService(
    //   poDetail,
    //   authorization,
    //   jwtPayload,
    // );
    this.validateResultDetail(dataDetails, poDetail, isCheckBudget, jwtPayload);
  }
  // Hàm chính để validate purchases
  private async validatePurchases(
    purchases: PurchaseOrderDto,
    authorization: string,
    jwtPayload: any,
    poId?: number,
  ) {
    this.checkBudgetRequirement(purchases);

    const prReferenceIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.prReference)
          .filter(Boolean),
      ),
    ];
    const prDetailIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.prDetailId)
          .filter(Boolean),
      ),
    ];
    let prs: PurchaseRequestModel[];
    let poDetails: PurchaseOrderDetailModel[];
    if (prReferenceIds?.length && prDetailIds?.length) {
      prs = await this.purchaseRequestRepository.getPrListByIds(prReferenceIds);
      poDetails =
        await this.purchaseOrderRepository.numberPoCreatedByPrDetailIds(
          prDetailIds,
          poId,
        );
    }

    const budgetCodeIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.budgetCodeId)
          .filter(Boolean),
      ),
    ];
    const materialCodeIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.materialCodeId)
          .filter(Boolean),
      ),
    ];
    const costCenterIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.costCenterId)
          .filter(Boolean),
      ),
    ];
    const supplierIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.supplierId)
          .filter(Boolean),
      ),
    ];
    const materialGroupIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.materialGroupId)
          .filter(Boolean),
      ),
    ];
    const currencyIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.currencyId)
          .filter(Boolean),
      ),
    ];
    const pirIds = [
      ...new Set(
        (purchases.details || []).map((item) => item.pirId).filter(Boolean),
      ),
    ];
    const measureIds = [
      ...new Set(
        (purchases.details || []).map((item) => item.measureId).filter(Boolean),
      ),
    ];
    const warehouseIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.warehouseId)
          .filter(Boolean),
      ),
    ];
    const taxCodeIds = [
      ...new Set(
        (purchases.details || []).map((item) => item.taxCodeId).filter(Boolean),
      ),
    ];

    const [
      budgetCodes,
      materialCodes,
      costCenters,
      suppliers,
      materialGroups,
      currencys,
      pirs,
      measureCodes,
      taxCodes,
      warehouses,
      prDetails,
    ] = await Promise.all([
      this.budgetCodeUsecases.getListByIds(
        {
          ids: budgetCodeIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.materialUsecases.getListByIds(
        {
          ids: materialCodeIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.costcenterSubaccountUsecases.getListByIds(
        {
          ids: costCenterIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.supplierUsecases.getListByIds(
        {
          ids: supplierIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.materialGroupUsecases.getListByIds(
        {
          ids: materialGroupIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.currencyUnitUsecases.getListByIds(
        {
          ids: currencyIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.priceInformationRecordRepository.getListByIds(
        {
          ids: pirIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.measureUsecases.getListByIds(
        {
          ids: measureIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.taxCodeUsecases.getTaxCodes(
        {
          ids: taxCodeIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.warehouseUsecases.getListByIds(
        {
          ids: warehouseIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this._purchaseRequestUsecases.getPrDetailByIds(prDetailIds),
    ]);

    const dataDetails = {
      budgetCodes: codeToIdMap(budgetCodes.results, 'id'),
      materialCodes: codeToIdMap(materialCodes.results, 'id'),
      costCenters: codeToIdMap(costCenters.results, 'id'),
      suppliers: codeToIdMap(suppliers.results, 'id'),
      materialGroups: codeToIdMap(materialGroups.results, 'id', ['id', 'name']),
      currencys: codeToIdMap(currencys.results, 'id'),
      pirs: codeToIdMap(pirs.results, 'id'),
      measureCodes: codeToIdMap(measureCodes.results, 'id'),
      taxCodes: codeToIdMap(taxCodes.results, 'id', ['id', 'taxRate']),
      warehouses: codeToIdMap(warehouses.results, 'id'),
    };

    const mapPrDetails = codeToIdMap(prDetails, 'id', ['id', 'warehouseId']);

    purchases.details.forEach((detail) => {
      detail.materialGroupName =
        detail.materialGroupName ||
        dataDetails.materialGroups[detail.materialGroupId]?.name;
      detail.supplierInfo =
        detail.supplierInfo && typeof detail.supplierInfo === 'string'
          ? JSON.parse(detail.supplierInfo)
          : detail.supplierInfo;
      detail.vat = Number(dataDetails.taxCodes[detail.taxCodeId]?.taxRate || 0);
    });

    const chunkDetails = _.chunk(purchases.details, 50);
    for (let i = 0; i < (chunkDetails?.length || 0); i++) {
      await Promise.all(
        (chunkDetails[i] || []).map((poDetail) =>
          this.validateDetail(
            poDetail,
            purchases.isCheckBudget,
            authorization,
            jwtPayload,
            dataDetails,
            poId,
            prs,
            poDetails,
          ),
        ),
      );
    }

    purchases.details.forEach((detail) => {
      detail.warehouseId = detail?.prDetailId
        ? mapPrDetails[detail?.prDetailId]?.warehouseId
        : detail.warehouseId;
    });

    return purchases;
  }
  private async traverseAndCheckNodes(
    purchases: PurchaseOrderDto,
    processes: any,
    checkConditionNode: (process: any, purchases: any) => Promise<boolean>,
    processMatching: any,
    authorization: any,
    level: number = 1,
  ) {
    // Lọc các node thoả điều kiện tại cấp hiện tại (sử dụng Promise.all)
    // const validNodes = processes?.filter(async (node) => await checkConditionNode(node, purchases, authorization)) || [];

    const validNodes = (
      await Promise.all(
        processes?.map(async (node) => ({
          node,
          isValid: await checkConditionNode(node, purchases),
        })) || [],
      )
    )
      .filter(({ isValid }) => isValid)
      .map(({ node }) => node);

    if (validNodes.length === 0) {
      return [];
    }

    // Kiểm tra xem có các node con hay không
    for (const parentNode of validNodes) {
      if (parentNode.children && parentNode.children.length > 0) {
        // Đệ quy kiểm tra các node con của cấp tiếp theo
        await this.traverseAndCheckNodes(
          purchases,
          parentNode.children,
          checkConditionNode,
          processMatching,
          authorization,
          level + 1,
        );
      } else {
        processMatching.push(parentNode.id);
      }
    }

    return validNodes;
  }

  async checkConditionNode(process: any, purchases: PurchaseOrderDto) {
    const conditionDetails = process?.conditionDetails;

    for (const conditionDetail of conditionDetails) {
      let value, target;
      switch (conditionDetail.type) {
        case EConditionType.SECTOR:
          value = conditionDetail.sectors.map((sector) => sector.id);
          target = purchases.sectorId;
          break;
        case EConditionType.BUSINESS_UNIT:
          value = conditionDetail.businessUnits.map((bu) => bu.id);
          target = purchases.businessUnitId;
          break;
        case EConditionType.PO_TYPE:
          value = conditionDetail.poTypes.map((poType) => poType.id);
          target = purchases.typePoId;
          break;
        case EConditionType.COST_CENTER:
          value = conditionDetail.costCenters.map(
            (costCenter) => costCenter.id,
          );
          target = purchases.costCenterId;
          break;
        case EConditionType.BUDGET_CODE:
          value = conditionDetail.budgetCodes.map(
            (budgetCode) => budgetCode.id,
          );
          target = purchases.budgetCodeId;
          break;
        case EConditionType.VALUE_PO:
          value = purchases.details.reduce(
            (total, detail) => total + Number(detail.totalAmount || 0),
            0,
          );
          target = conditionDetail.valuePO;
          break;
        case EConditionType.BUDGET_OVERRUN:
          value =
            Number(purchases.details[0].remainingBudget) < 0
              ? Math.abs(Number(purchases.details[0].remainingBudget || 0))
              : 0;
          target = Number(conditionDetail.budgetOverrun);
          break;
        case EConditionType.COMPANY: {
          value = conditionDetail.companies.map((company) => company.id);
          // target = purchases.sector;
          break;
        }
        case EConditionType.DEPARTMENT: {
          value = conditionDetail.departments.map(
            (department) => department.id,
          );
          target = purchases.departmentId;
          break;
        }
        case EConditionType.CHECK_BUDGET: {
          value = purchases.isCheckBudget ? 1 : 0;
          target = conditionDetail.comparisonType;
          break;
        }
        case EConditionType.PLANT: {
          value = conditionDetail.plants.map((plant) => plant.id);
          target = purchases.plantId;
          break;
        }
        case EConditionType.PROCESS_TYPE: {
          value = conditionDetail.processTypes.map(
            (processType) => processType.id,
          );
          target = purchases.processTypeId;
          break;
        }
        case EConditionType.BUDGET_OVERRUN_RATE: {
          // Tổng số tiền detail
          // const total_amount_details = purchases?.details?.reduce((total, detail) => total + Number(detail.total_amount || 0), 0) || 0;

          // if (total_amount_details <= purchases.details[0].remaining_budget) continue;
          // // [Ngân sách] (Cộng ngân sách cha (NEW) với tất cả ngân sách con (TĂNG/GIẢM))
          // const total_budget = purchases?.details?.reduce((total, detail) => total + Number(detail.budget || 0), 0) || 0;
          // // Tỷ lệ vượt ngân sách = Tổng số tiền - Ngân sách còn lại / Giá trị ngân sách (bao gồm Ngân sách mới, Ngân sách điều chỉnh tăng, Ngân sách điều chỉnh giảm) * 100
          // const budgetOverrunRate = total_budget > 0 ? (total_amount_details - Number(purchases.details[0].remaining_budget)) / (total_budget * 100) : 0;

          const firstDetail = purchases?.details[0];

          if (Number(firstDetail?.remainingBudget || 0) >= 0) {
            value = 0;
          } else {
            value =
              Number(firstDetail?.budget || 0) > 0
                ? (Math.abs(Number(firstDetail?.remainingBudget || 0)) /
                    Number(firstDetail?.budget || 0)) *
                  100
                : 0;
          }

          target = conditionDetail.budgetOverrunRate;
          break;
        }
        case EConditionType.FUNCTION_UNIT:
          value = conditionDetail.functionUnits.map((fu) => fu.id);
          target = purchases.functionUnitId;
          break;
        case EConditionType.DIFFERENCE_AMOUNT:
        case EConditionType.DIFFERENCE_AMOUNT_ALL_ITEMS: {
          const isConditionMet = await this.handleDifferenceAmountCondition(
            purchases,
            conditionDetail,
            conditionDetail.type === EConditionType.DIFFERENCE_AMOUNT_ALL_ITEMS,
          );

          if (!isConditionMet) {
            return false;
          }
          break;
        }
        case EConditionType.PR_TYPE:
          const prIds = purchases.details
            .filter((detail) => detail.prReference) // Lọc các detail có pr_detail_id
            .map((detail) => detail.prReference);

          if (!prIds.length) {
            return false; // Không có ID để xử lý
          }

          const prs = await this._purchaseRequestUsecases.getPrListByIds(prIds);

          if (!prs || !prs.length) {
            return false;
          }

          const checkPrTypes = purchases.details.every((detail) => {
            if (!detail.prReference) return false;

            const pr = prs.find((pr) => pr.id == detail.prReference);

            if (!pr) return false;

            return comparisonResult(
              conditionDetail.comparisonType,
              (conditionDetail.prTypes || [])
                .map((item) => item.id)
                .filter(Boolean),
              pr.typePrId,
            );
          });

          if (!checkPrTypes) {
            return false;
          }

          break;
        case EConditionType.FIRST_BUDGET: {
          const firstDetail = purchases?.details[0];

          value = purchases.budgetCodeId ? Number(firstDetail?.budget || 0) : 0;
          target = conditionDetail.firstBudget;
          break;
        }
        default:
          return false;
      }

      if (
        ![
          EConditionType.DIFFERENCE_AMOUNT,
          EConditionType.DIFFERENCE_AMOUNT_ALL_ITEMS,
          EConditionType.PR_TYPE,
        ].includes(conditionDetail.type) &&
        !comparisonResult(conditionDetail.comparisonType, value, target)
      ) {
        return false;
      }
    }

    return true;
  }

  private checkDifferenceAmount(
    purchases: PurchaseOrderDto,
    prDetails: PurchaseRequestDetailModel[],
    conditionDetail: any,
    checkAll = false,
  ): boolean {
    return checkAll
      ? purchases.details.every((detail) =>
          this.isDifferenceAmount(detail, prDetails, conditionDetail, checkAll),
        )
      : purchases.details.some((detail) =>
          this.isDifferenceAmount(detail, prDetails, conditionDetail, checkAll),
        );
  }

  private isDifferenceAmount(
    detail: PurchaseOrderDetailDto,
    prDetails: PurchaseRequestDetailModel[],
    conditionDetail,
    checkAll = false,
  ): boolean {
    if (!detail.prDetailId) return false;

    const prDetail = prDetails.find(
      (prDetail) => prDetail.id == detail.prDetailId,
    );
    if (!prDetail) return false;

    // Tổng số tiền sau VAT từ poDetails
    const totalAmountVatPoDetails =
      prDetail.poDetails?.reduce(
        (total, poDetail) => total + Number(poDetail.totalAmountVat || 0),
        0,
      ) || 0;

    // Tính tiền chênh lệch
    const differenceAmount =
      totalAmountVatPoDetails - Number(prDetail.totalAmount || 0);
    // So sánh với điều kiện
    return comparisonResult(
      conditionDetail.comparisonType,
      differenceAmount,
      checkAll
        ? conditionDetail.differenceAmountAllItems
        : conditionDetail.differenceAmount,
    );
  }

  private async handleDifferenceAmountCondition(
    purchases: PurchaseOrderDto,
    conditionDetail: any,
    checkAll = false,
  ): Promise<boolean> {
    const prDetailIds = purchases.details
      .filter((detail) => detail.prDetailId) // Lọc các detail có pr_detail_id
      .map((detail) => detail.prDetailId);

    if (!prDetailIds.length) {
      return false; // Không có ID để xử lý
    }

    const prDetails =
      await this._purchaseRequestUsecases.getPrDetails(prDetailIds);

    if (!prDetails || !prDetails.length) {
      return false;
    }

    return this.checkDifferenceAmount(
      purchases,
      prDetails,
      conditionDetail,
      checkAll,
    );
  }

  async update(
    id: number,
    purchases: UpdatePurchaseOrderDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<
    | PurchaseOrderModel
    | { approvalSteps: ApprovalLevelDto[]; staffApprovalWorkflowId: string }
  > {
    purchases.displayStatusPo = await this.getDisplayStatus(purchases.statusPo);

    const findPo = await this.purchaseOrderRepository.findOneById(id);
    if (!findPo) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5210()),
        HttpStatus.NOT_FOUND,
      );
    }

    purchases = await this.validatePurchases(
      purchases,
      authorization,
      jwtPayload,
      id,
    );

    const result = await this.callServices(
      purchases,
      authorization,
      jwtPayload,
    );

    if (!result.sector) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5105()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!result.businessUnit) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5106()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!result.requester) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5107()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!result.typePo) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5208()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (result.typePo?.status !== 'ACTIVE') {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5209()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!result.processType) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5030()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.plantId && !result.plant) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5031()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.functionUnitId && !result.functionUnit) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5033()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.departmentId && !result.department) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5032()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.costCenterId) {
      if (!result.costCenter) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_5025()),
          HttpStatus.BAD_REQUEST,
        );
      } else {
        const currentDate = startOfDay(new Date(findPo.createdAt));
        if (
          (!result.costCenter?.effectiveEndDate &&
            currentDate < new Date(result.costCenter?.effectiveStartDate)) ||
          (result.costCenter?.effectiveEndDate &&
            (currentDate < new Date(result.costCenter?.effectiveStartDate) ||
              currentDate > new Date(result.costCenter?.effectiveEndDate)))
        ) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_5029()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    }

    if (purchases.purchaseOrgId && !result.purchaseOrg) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5005()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.purchaseGroupId && !result.purchaseGroup) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5006()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.budgetCodeId && !result.budgetCode) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5024()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.currencyId && !result.currency) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5028()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.functionUnitId && !result.functionUnit) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5033()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (purchases.departmentId && !result.department) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5032()),
        HttpStatus.BAD_REQUEST,
      );
    }

    purchases.details = await this.newCalculateRemainingBudget(
      purchases.details,
      authorization,
      new Date(findPo.createdAt),
      jwtPayload,
    );

    const purchaseOrder =
      await this.purchaseOrderRepository.updatePurchaseOrder(id, purchases);

    const processedItem = await this.handleObjectProcessing(id, authorization);
    let approvalSteps = [];
    if (!purchases.statusPo || purchases.statusPo === Status.Draft) {
      // await this.prApprovalFlowRepository.deleteApprovalLevels(null, id);

      const processes = await this.processUsecases.getProcessList({
        statuses: [EStatus.ACTIVE],
        types: [EProcessType.PO],
        getAll: 1,
        page: 0,
        limit: 0,
        searchString: '',
      });

      if (!processes.results?.length) {
        throw new HttpException(
          approvalErrorDetails.E_5410(),
          HttpStatus.BAD_REQUEST,
        );
      }

      const processMatching = {};

      const managers = await this.staffUsecases.getDetailStaff(
        { staffId: result?.requester?.id },
        jwtPayload,
      );
      const managersOfRequester = managers?.managers || [];

      for (const process of processes.results) {
        processMatching[process.id] = [];
        const processDetail = await this.processUsecases.getDetailProcessGraph(
          process.id,
        );
        const node = processDetail;

        await this.traverseAndCheckNodes(
          purchases,
          node,
          async (node, purchases) =>
            await this.checkConditionNode(node, purchases),
          processMatching[process.id],
          authorization,
        );

        // processMatching[process.id] = processMatching;
      }

      const prIds = [
        ...new Set(
          (purchases.details || [])
            .map((item) => item.prReference)
            .filter(Boolean),
        ),
      ];
      const prs = prIds?.length
        ? await this.purchaseRequestRepository.getPrListByIds(prIds)
        : [];

      const prTypeIds = [
        ...new Set((prs || []).map((item) => item.typePrId).filter(Boolean)),
      ];
      // Get ApprovalProcessDetail
      const approvalProcessDetails =
        await this.approvalProcessDetailUsecases.getApprovalProcessDetails(
          {
            sectorIds: [purchases.sectorId],
            departmentIds: purchases.departmentId
              ? [purchases.departmentId]
              : [null],
            functionUnitIds: purchases.functionUnitId
              ? [purchases.functionUnitId]
              : [null],
            prTypeIds: prTypeIds?.length ? prTypeIds : [null],
            businessUnitIds: [purchases.businessUnitId],
            poCreatedByIds: purchases.requesterId
              ? [purchases.requesterId]
              : [null],
            limit: 1,
            page: 1,
            searchString: '',
          },
          jwtPayload,
        );

      for (const [key, value] of Object.entries(processMatching)) {
        if (!value || !(value as string[]).length) {
          throw new HttpException(
            approvalErrorDetails.E_5410(),
            HttpStatus.BAD_REQUEST,
          );
        }
        const processParentDetail =
          await this.processUsecases.getDetailParentProcess(key);

        if (processParentDetail?.parentApprovalWorkflows?.length) {
          //Approval-Level
          for (const parentApprovalWorkflow of processParentDetail.parentApprovalWorkflows) {
            const { processes, staffApprovalWorkflows, sendEmailToCreator } =
              parentApprovalWorkflow;
            const processIds = processes.map((process) => process.id);

            if (
              processIds?.some((processId) =>
                (value as string[]).includes(processId),
              )
            ) {
              // Sort levels in ascending order
              const sortedStaffApprovalWorkflows = staffApprovalWorkflows.sort(
                (a, b) => a.level - b.level,
              );

              const getManagerDetails = (approver: EApprover) => {
                const levelMap = {
                  [EApprover.LEVEL_1]: 1,
                  [EApprover.LEVEL_2]: 2,
                  [EApprover.LEVEL_3]: 3,
                  [EApprover.LEVEL_4]: 4,
                };
                const level = levelMap[approver];
                const manager = managersOfRequester?.find(
                  (manager) => manager.level === level,
                );
                if (!manager) {
                  throw new HttpException(
                    approvalErrorDetails.E_5411(
                      `Không tìm thấy quản lý cấp ${level} duyệt`,
                    ),
                    HttpStatus.BAD_REQUEST,
                  );
                }
                return {
                  email: manager?.email || '',
                  name: `${manager?.lastName || ''} ${manager?.firstName || ''}`,
                };
              };

              const getStaffDetails = async (
                id: string,
                errorMessage: TErrorMessage,
              ) => {
                const staff = await this.staffUsecases.getDetailStaff(
                  { staffId: id },
                  jwtPayload,
                );
                if (!staff) {
                  throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
                }

                return {
                  email: staff?.email || '',
                  name: `${staff?.lastName || ''} ${staff?.firstName || ''}`,
                };
              };

              for (
                let index = 0;
                index < sortedStaffApprovalWorkflows.length;
                index++
              ) {
                const workflow = sortedStaffApprovalWorkflows[index];
                let approvalLevel: ApprovalLevelDto = {
                  role: `Người duyệt cấp ${workflow.level}`,
                  isSendMail: workflow.receiveEmail,
                  level: workflow.level,
                  purchaseOrderId: purchaseOrder.id,
                  status: index === 0 ? 'Pending' : null,
                  approveType: ApproveType.PO,
                  isSendMailCreator: sendEmailToCreator,
                  isAccountantApproved: workflow.accountantApproved,
                  allowSelect: workflow.allowSelect,
                  staffApprovalWorkflowId: workflow.id,
                };

                switch (workflow.approver) {
                  case EApprover.LEVEL_1:
                  case EApprover.LEVEL_2:
                  case EApprover.LEVEL_3:
                  case EApprover.LEVEL_4: {
                    const { email, name } = getManagerDetails(
                      workflow.approver,
                    );
                    approvalLevel.email = email;
                    approvalLevel.name = name;
                    break;
                  }
                  case EApprover.ASSIGN: {
                    const { email, name } = await getStaffDetails(
                      workflow.staffId,
                      approvalErrorDetails.E_5411(
                        'Không tìm thấy người chỉ định duyệt',
                      ),
                    );
                    approvalLevel.email = email;
                    approvalLevel.name = name;
                    break;
                  }
                  case EApprover.POSITION: {
                    const staffList =
                      await this.staffUsecases.approverByPosition(
                        {
                          statuses: [EStatus.ACTIVE],
                          positionIds: [workflow?.position?.id],
                          businessUnitIds: [purchases.businessUnitId],
                          functionUnitIds: [purchases.functionUnitId],
                          departmentIds: [purchases.departmentId],
                        },
                        jwtPayload,
                        authorization,
                      );

                    if (!staffList?.length) {
                      throw new HttpException(
                        approvalErrorDetails.E_5411(
                          'Không tìm thấy người duyệt',
                        ),
                        HttpStatus.BAD_REQUEST,
                      );
                    }

                    const staff = staffList[0];

                    approvalLevel.email = staff?.email || '';
                    approvalLevel.name = `${staff?.lastName || ''} ${staff?.firstName || ''}`;
                    break;
                  }
                  case EApprover.PR_APPROVER_1:
                  case EApprover.PR_APPROVER_2:
                  case EApprover.PR_APPROVER_3:
                  case EApprover.PR_APPROVER_4:
                  case EApprover.PR_APPROVER_5:
                  case EApprover.PR_APPROVER_6:
                  case EApprover.PR_APPROVER_7:
                  case EApprover.PO_APPROVER_1:
                  case EApprover.PO_APPROVER_2:
                  case EApprover.PO_APPROVER_3: {
                    approvalLevel = this.getApprovalLevelByAprovalProcessDetail(
                      approvalProcessDetails,
                      approvalLevel,
                      workflow.approver,
                      purchases,
                    );
                    break;
                  }
                }

                approvalSteps.push(approvalLevel);
              }

              break;
            }
          }
        }
      }
    } else {
      approvalSteps = purchases?.approvalLevelDtos?.length
        ? purchases.approvalLevelDtos
        : (await this.prApprovalFlowRepository.findApprovalLevels(null, id)) ||
          [];
    }

    if (!approvalSteps.length) {
      throw new HttpException(
        approvalErrorDetails.E_5410(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const approvalLevels = approvalSteps;
    //Tìm cấp duyệt cho phép chọn nhân viên
    const hasStaffApprovalWorkflow = approvalLevels.find(
      (level) => level.allowSelect,
    );
    //Tìm cấp duyệt là kế toán
    const isAccountantApproved = approvalLevels.find(
      (level) => level.isAccountantApproved,
    );

    let sortedApprovalLevels = approvalLevels?.sort(
      (a, b) => a.level - b.level,
    );

    // If no approval levels exist in purchases
    // Nếu trong luồng duyệt tồn tại cấp duyệt tích "Cho phép điều chỉnh” và không có cấp duyệt nào tích “Kế toán”,
    // thì khi nhấn button “Lưu và gửi” hoặc gửi sẽ hiển thị popup hiển thị các cấp duyệt và cho phép chọn người duyệt
    if (
      !purchases?.approvalLevelDtos?.length &&
      hasStaffApprovalWorkflow &&
      !isAccountantApproved
    ) {
      return {
        approvalSteps: approvalLevels,
        staffApprovalWorkflowId:
          hasStaffApprovalWorkflow.staffApprovalWorkflowId,
      };
    } else {
      approvalLevels.forEach((level) => {
        level.approveType = ApproveType.PO;
        level.purchaseOrderId = id;
      });

      const emailChecks = approvalLevels
        .filter((step) => step.email)
        .map((step) => step.email);
      await this.staffUsecases.getStaffByEmails(
        { emails: emailChecks },
        jwtPayload,
      );

      if (approvalLevels.length > 0) {
        await this.prApprovalFlowRepository.deleteApprovalLevels(null, id);
        const newApprovalLevels =
          await this.prApprovalFlowRepository.createApprovalLevels(
            approvalLevels,
          );

        sortedApprovalLevels = newApprovalLevels.sort(
          (a, b) => a.level - b.level,
        );

        purchases.history = newApprovalLevels.map(({ id, ...rest }) => rest);

        await this.prApprovalFlowRepository.changeStatusPurchaseOrder(
          { levels: newApprovalLevels },
          purchaseOrder.id,
        );
        purchaseOrder.levels = newApprovalLevels;
      }
    }

    await this.handleEmailNotification(
      sortedApprovalLevels[0],
      purchaseOrder,
      processedItem,
      authorization,
    );

    const prIds = purchaseOrder.details
      .map((detail) => detail.prReference)
      .filter(Boolean);
    await this._purchaseRequestUsecases.changeStatePr(prIds);

    const refId = embedIdInUuid(purchaseOrder.id);
    await this.purchaseOrderRepository.updatePurchaseOrder(purchaseOrder.id, {
      refId,
      history: purchases.history?.map((history) => {
        return {
          ...history,
          status: Status.Pending,
        };
      }),
    });

    return purchaseOrder;
  }

  private async handleEmailNotification(
    approvalLevel,
    purchaseOrder: PurchaseOrderModel,
    processedItem: PurchaseOrderModel,
    authorization,
  ) {
    if (approvalLevel?.isSendMail) {
      if (!approvalLevel.email) {
        throw new HttpException(
          approvalErrorDetails.E_5410(`Không tìm thấy nhân viên`),
          HttpStatus.BAD_REQUEST,
        );
      }

      const mailOptions = {
        from: this.configService.get<string>('MAIL_SMTP_USER'),
        to: approvalLevel.email,
        subject: `Phê duyệt PO #${processedItem.id}${processedItem.businessUnit?.code ? ` - ${processedItem.businessUnit.code}` : ''}${processedItem.reason ? ` - ${processedItem.reason}` : ''}`,
        html: this._emailTemplateService.getApprovalPOEmailHtml(
          processedItem,
          approvalLevel.email,
          `${this.configService.get<string>('STATUS_PO_EMAIL_URL')}?levelId=${approvalLevel.id}&poId=${purchaseOrder.id}`,
          approvalLevel,
        ),
      };

      if (purchaseOrder.statusPo !== Status.Draft) {
        try {
          if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
            await sendPost(
              QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
              mailOptions,
              { authorization },
            );
          } else {
            await this.transporter.sendMail(mailOptions);
          }
        } catch (e) {}
      }
    }
  }

  //TODO: Nhận từ bàn giao, sẽ refactor sau
  private async callServices(
    requestData: PurchaseOrderDto | UpdatePurchaseOrderDto,
    authorization: string,
    jwtPayload: any,
  ): Promise<Record<string, any>> {
    const [
      sector,
      businessUnit,
      requester,
      typePo,
      budgetCode,
      costCenter,
      purchaseOrg,
      purchaseGroup,
      currency,
      processType,
      plant,
      functionUnit,
      department,
    ] = await Promise.all([
      requestData.sectorId
        ? this.sectorUsecases.getDetailSector(
            {
              id: requestData.sectorId,
            },
            jwtPayload,
          )
        : null,
      requestData.businessUnitId
        ? this.businessUnitUsecases.getDetailBusinessUnit(
            {
              id: requestData.businessUnitId,
            },
            jwtPayload,
          )
        : null,
      requestData.requesterId
        ? this.staffUsecases.getDetailStaff(
            {
              staffId: requestData.requesterId,
            },
            jwtPayload,
          )
        : null,
      requestData.typePoId
        ? this.purchaseOrderTypeUsecases.getPurchaseOrderTypeDetail(
            {
              id: requestData.typePoId,
            },
            jwtPayload,
          )
        : null,
      requestData.budgetCodeId
        ? this.budgetCodeUsecases.getBudgetCodeDetail(
            {
              id: requestData.budgetCodeId,
            },
            jwtPayload,
          )
        : null,
      requestData.costCenterId
        ? this.costcenterSubaccountUsecases.getCostcenterSubaccountDetail(
            {
              id: requestData.costCenterId,
            },
            jwtPayload,
          )
        : null,
      requestData.purchaseOrgId
        ? this.purchasingDepartmentUsecases.getPurchasingDepartmentDetail(
            {
              id: requestData.purchaseOrgId,
            },
            jwtPayload,
          )
        : null,
      requestData.purchaseGroupId
        ? this.purchasingGroupUsecases.getPurchasingGroupDetail(
            {
              id: requestData.purchaseGroupId,
            },
            jwtPayload,
          )
        : null,
      requestData.currencyId
        ? this.currencyUnitUsecases.getCurrencyUnitDetail(
            {
              id: requestData.currencyId,
            },
            jwtPayload,
          )
        : null,
      requestData.processTypeId
        ? this.processTypeUsecases.getDetailProcessType({
            id: requestData.processTypeId,
          })
        : null,
      requestData.plantId
        ? this.plantUsecases.getPlantDetail(
            {
              id: requestData.plantId,
            },
            jwtPayload,
          )
        : null,
      requestData.functionUnitId
        ? this.functionUnitUsecases.getDetailFunctionUnit(
            {
              id: requestData.functionUnitId,
            },
            jwtPayload,
          )
        : null,
      requestData.departmentId
        ? this.departmentUsecases.getDetailDepartment(
            {
              id: requestData.departmentId,
            },
            jwtPayload,
          )
        : null,
    ]);

    return {
      sector,
      businessUnit,
      requester,
      typePo,
      budgetCode,
      costCenter,
      purchaseOrg,
      purchaseGroup,
      currency,
      processType,
      plant,
      functionUnit,
      department,
    };
  }

  private async callMaterialService(
    requestDetailData: PurchaseOrderDetailDto,
    authorization: string,
    jwtPayload: any,
  ): Promise<any> {
    const [
      budgetCode,
      materialCode,
      costCenter,
      supplier,
      materialGroup,
      currency,
      pir,
      measureCode,
    ] = await Promise.all([
      requestDetailData.budgetCodeId
        ? this.budgetCodeUsecases.getBudgetCodeDetail(
            {
              id: requestDetailData.budgetCodeId,
            },
            jwtPayload,
          )
        : null,
      requestDetailData.materialCodeId
        ? this.materialUsecases.getMaterialDetail(
            {
              id: requestDetailData.materialCodeId,
            },
            jwtPayload,
          )
        : null,
      requestDetailData.costCenterId
        ? this.costcenterSubaccountUsecases.getCostcenterSubaccountDetail(
            {
              id: requestDetailData.costCenterId,
            },
            jwtPayload,
          )
        : null,
      requestDetailData.supplierId
        ? this.supplierUsecases.getSupplierDetail(
            {
              id: requestDetailData.supplierId,
            },
            jwtPayload,
          )
        : null,
      requestDetailData.materialGroupId
        ? this.materialGroupUsecases.getMaterialGroupDetail(
            {
              id: requestDetailData.materialGroupId,
            },
            jwtPayload,
          )
        : null,
      requestDetailData.currencyId
        ? this.currencyUnitUsecases.getCurrencyUnitDetail(
            {
              id: requestDetailData.currencyId,
            },
            jwtPayload,
          )
        : null,
      requestDetailData.pirId
        ? this.priceInformationRecordRepository.findOne(
            Number(requestDetailData.pirId),
          )
        : null,
      requestDetailData.measureId
        ? this.measureUsecases.getDetailMeasure(
            { id: requestDetailData.measureId },
            jwtPayload,
          )
        : null,
    ]);

    return {
      budgetCode,
      materialCode,
      costCenter,
      supplier,
      materialGroup,
      currency,
      pir,
      measureCode,
    };
  }

  private async handleObjectProcessing(id: number, authorization) {
    const po = await this.purchaseOrderRepository.findOne(id);
    return po;
  }

  async findPOWithBudget(
    conditions: GetPOWithBudgetDto,
  ): Promise<PurchaseOrderModel[]> {
    return await this.purchaseOrderRepository.findPOWithBudget(conditions);
  }

  async materialPurchaseOrder(
    paginationDto: GetPurchaseOrderDto,
    authorization: string,
  ): Promise<any> {
    // const data =
    //   await this.purchaseOrderRepository.materialPurchaseOrder(paginationDto);
    // // Khởi tạo mảng chứa các Promise cho từng request
    // const promises = data.map(async (item) => {
    //   const requestData = {
    //     sector: item.sector,
    //     business_unit: item.business_unit,
    //     requester: item.requester,
    //     type_po: item.type_po,
    //     budget_code: item.budget_code,
    //     cost_center: item.cost_center,
    //     purchase_org: item.purchase_org,
    //     purchase_group: item.purchase_group,
    //   };
    //   const result = await this.callServices(requestData, authorization);
    //   const detailsPromises = item.details.map(async (detail) => {
    //     const requestDetailData = {
    //       budget_code: detail.budget_code,
    //       material_code: detail.material_code,
    //       cost_center: detail.cost_center,
    //     };
    //     // Gọi các dịch vụ để lấy dữ liệu cho material và budget_code
    //     const resultDetail = await this.callMaterialService(
    //       requestDetailData,
    //       authorization,
    //     );
    //     // Mapping kết quả vào dữ liệu của detail
    //     return {
    //       ...detail,
    //       material: resultDetail?.material_code?.data || null,
    //       budget_code: resultDetail?.budget_code?.data || null,
    //       cost_center: resultDetail?.cost_center?.data || null,
    //       pr_id: item.id,
    //       sector: result?.sector?.data || null,
    //       business_unit: result?.business_unit?.data || null,
    //     };
    //   });
    //   // Chờ tất cả các Promise cho details hoàn thành
    //   const mappedDetails = await Promise.all(detailsPromises);
    //   return mappedDetails; // Trả về mảng details
    // });
    // // Chờ tất cả các Promise hoàn thành
    // const mappedDataArray = await Promise.all(promises);
    // // Gộp tất cả các mảng chi tiết thành một mảng 1 chiều
    // const flatDetails = mappedDataArray.flat(); // Chuyển từ mảng 2 chiều thành mảng 1 chiều
    // // Áp dụng phân trang cho mảng flatDetails
    // const { pageNumber, pageSize } = paginationDto;
    // const skip = (pageNumber - 1) * pageSize;
    // const paginatedDetails = flatDetails.slice(skip, skip + pageSize);
    // const totalDetails = flatDetails.length;
    // const totalPage = Math.ceil(totalDetails / pageSize);
    // return {
    //   results: paginatedDetails,
    //   page: pageNumber,
    //   limit: pageSize,
    //   total: totalDetails,
    //   totalPage: totalPage,
    // };
  }

  async newCalculateRemainingBudget(
    data: PurchaseOrderDetailDto[],
    authorization: string,
    createdAt: Date,
    jwtPayload: any,
  ): Promise<PurchaseOrderDetailDto[]> {
    (data || []).map((item) => (item.budgetId = null));

    const budgetCodeIds = (data || [])
      .map((item) => item.budgetCodeId)
      .filter(Boolean);

    const allBudgets: BudgetModel[] = [];
    if (budgetCodeIds?.length) {
      const [opexApiResponse, capexApiResponse] = await Promise.all([
        this.budgetUsecases.getBudgetList(
          {
            budgetCodeIds: budgetCodeIds,
            budgetType: EBudgetType.OPEX,
            limit: 5,
            page: 1,
            getAll: 1,
            isLock: true,
            createTypes: [EBudgetCreateType.NEW],
            createdAt: createdAt.toISOString(),
            searchString: '',
          },
          jwtPayload,
        ),
        this.budgetUsecases.getBudgetList(
          {
            budgetCodeIds: budgetCodeIds,
            budgetType: EBudgetType.CAPEX,
            limit: 5,
            page: 1,
            getAll: 1,
            isLock: true,
            createTypes: [EBudgetCreateType.NEW],
            createdAt: createdAt.toISOString(),
            searchString: '',
          },
          jwtPayload,
        ),
      ]);

      const dataOpexes = opexApiResponse?.results || [];
      const dataCapexes = capexApiResponse?.results || [];
      allBudgets.push(...dataOpexes, ...dataCapexes);
    }
    const currencyUnitResponse =
      await this.currencyUnitUsecases.getCurrencyUnits(
        {
          page: 1,
          limit: 10,
          getAll: 1,
          searchString: '',
        },
        jwtPayload,
      );
    const currencyUnits = currencyUnitResponse?.results || [];
    const currencyVND = (currencyUnits || []).find(
      (item) => item.currencyCode == 'VND',
    );
    if (!currencyVND) {
      throw new HttpException(errorMessage.E_1103(), HttpStatus.BAD_REQUEST);
    }

    const dataCalculated = [];

    for (let i = 0; i < data.length; i++) {
      const matchingBudgets = allBudgets.filter(
        (budget) =>
          budget.budgetCodeId === data[i].budgetCodeId &&
          new Date(budget.effectiveStartDate) <= createdAt &&
          createdAt <= new Date(budget.effectiveEndDate),
      );

      if (matchingBudgets?.length) {
        const firstBudget = matchingBudgets[0];
        const existBudget = data.find(
          (item) => item.budgetId == firstBudget.id,
        );

        /// Filter tỷ giá để quy đổi tiền cho budget
        const matchCurrencyExchangeBudget = (
          (currencyUnits || []).find(
            (item) => item.id == firstBudget.currencyUnitId,
          )?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              firstBudget.effectiveStartDate,
              item.effectiveStartDate,
              firstBudget.effectiveEndDate,
              item.effectiveEndDate,
            ) && item.exchangeBudget,
        );

        const matchCurrencyExchangeFilter = (
          currencyVND?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              firstBudget.effectiveStartDate,
              item.effectiveStartDate,
              firstBudget.effectiveEndDate,
              item.effectiveEndDate,
            ) && item.exchangeBudget,
        );

        if (!matchCurrencyExchangeBudget || !matchCurrencyExchangeFilter) {
          throw new HttpException(
            errorMessage.E_1103(),
            HttpStatus.BAD_REQUEST,
          );
        }

        // Gán budget id để lưu vết xem ngân sách dự chi được tính toán từ budget nào
        data[i].budgetId = firstBudget.id;
        data[i].matchingBudget = firstBudget;

        // [Ngân sách] (Cộng ngân sách cha (NEW) với tất cả ngân sách con (TĂNG/GIẢM))
        let totalBudget = 0;
        for (let i = 0; i < matchingBudgets.length; i++) {
          totalBudget +=
            ((matchingBudgets[i].totalValue +
              (matchingBudgets[i]?.children || []).reduce(function (
                sum,
                budget,
              ) {
                if (budget.isLock == true) {
                  if (budget.createType == 'INCREASE') {
                    return sum + (budget.totalValue || 0);
                  }
                  if (budget.createType == 'DECREASE') {
                    return sum - (budget.totalValue || 0);
                  }
                }

                return sum;
              }, 0)) *
              matchCurrencyExchangeBudget.exchangeRate) /
            matchCurrencyExchangeFilter.exchangeRate;
        }

        data[i].budget = totalBudget;

        if (existBudget) {
          data[i].budgetId = existBudget.budgetId;
          data[i].matchingBudget = existBudget.matchingBudget;
          data[i].remainingBudget = existBudget.remainingBudget;
        } else {
          // Gán budget id để lưu vết xem ngân sách dự chi được tính toán từ budget nào
          data[i].budgetId = firstBudget.id;
          data[i].matchingBudget = firstBudget;

          // [Tổng số tiền các item (tương ứng) trong PO đã được duyệt cuối (trạng thái Đã duyệt)]
          const poDetails =
            await this.purchaseOrderRepository.getPoDetailsForRemainingBudget(
              firstBudget.budgetCodeId,
              new Date(firstBudget.effectiveStartDate),
              new Date(firstBudget.effectiveEndDate),
            );

          const totalCostPoDetails = poDetails.reduce(
            (sum, item) =>
              sum +
              (item.purchasePrice || 0) *
                (item.quantity || 0) *
                (item.exchangeRate || 1) *
                (1 + (item.vat || 0) / 100),
            0,
          );

          /// Tìm actual spending
          // const actualSpendingResponse =
          //   await this.actualSpendingRepository.actualSpendingForReport({
          //     postingDateFrom: firstBudget.effectiveStartDate.toString(),
          //     postingDateTo: firstBudget.effectiveEndDate.toString(),
          //     statuses: [EStatusActualSpending.CONFIRMED],
          //     budgetCodeCode: firstBudget?.budgetCode?.code || null,
          //   });

          const actualSpendingResponse: ActualSpendingModel[] = [];

          let totalAcutalSpending = 0;

          /// Quy đổi actual spending sang VND
          for (const actualSpending of actualSpendingResponse || []) {
            const matchCurrencyExchangeFilter = (
              currencyVND?.currencyExchanges || []
            ).find(
              (item) =>
                checkEffectiveTimeOverlaps(
                  actualSpending.postingDate,
                  item.effectiveStartDate,
                  null,
                  item.effectiveEndDate,
                ) && item.exchangeBudget == false,
            );

            if (!matchCurrencyExchangeFilter) {
              throw new HttpException(
                errorMessage.E_1103(),
                HttpStatus.BAD_REQUEST,
              );
            }

            let docAmount = 0;
            if (firstBudget.budgetType == EBudgetType.CAPEX) {
              docAmount = Number(actualSpending.docAmount || 0);
            } else {
              const taxCode = (actualSpending.taxCode || '')
                .split('')[0]
                ?.toLowerCase();
              docAmount = ['a', 'c', 'e', 'g'].includes(taxCode)
                ? Number(actualSpending.docAmount || 0)
                : Number(actualSpending.docAmount || 0) *
                  (1 +
                    (isNaN(Number(actualSpending.taxRate || 0))
                      ? 0
                      : Number(actualSpending.taxRate || 0) / 100));
            }

            totalAcutalSpending +=
              (docAmount * Number(actualSpending.exchangeRate || 0)) /
              matchCurrencyExchangeFilter.exchangeRate;
          }

          // [Tổng tiền các item (tương ứng) trong PR/PO đang tạo]
          const filterItems = data.filter(
            (item) => item.budgetCodeId == firstBudget.budgetCodeId,
          );
          const totalCostCurrentItems = filterItems.reduce(
            (sum, item) =>
              sum +
              (item.purchasePrice || 0) *
                (item.quantity || 0) *
                (item.exchangeRate || 1) *
                (1 + (item.vat || 0) / 100),
            0,
          );
          // [Ngân sách dự chi còn lại]  = [Ngân sách] - [Tổng số tiền các item (tương ứng) trong PO đã được duyệt cuối (trạng thái Đã duyệt)] - [Tổng tiền các item (tương ứng) trong PR/PO đang tạo]
          data[i].remainingBudget =
            data[i].budget -
            (totalCostPoDetails > totalAcutalSpending
              ? totalCostPoDetails
              : totalAcutalSpending) -
            totalCostCurrentItems;
        }
      } else {
        data[i].budgetId = null;
        data[i].budget = 0;
        data[i].remainingBudget = 0;
      }

      dataCalculated.push(data[i]);
    }

    return dataCalculated;
  }

  // @TODO: gọi hàm này khi tạo PO SAP từ PO
  async changeStatePo(poIds: number[]) {
    if (poIds?.length) {
      const data = await this.purchaseOrderRepository.findPoWithIds(poIds);

      for (const po of data) {
        const countPoDetails = po.details.length;
        const poDetailsHasSAP = po.details.filter(
          (detail) => detail.sapCreatedQuantity > 0,
        ).length;

        if (countPoDetails == poDetailsHasSAP) {
          await this.purchaseOrderRepository.updateState(
            po.id,
            State.Completed,
          );
          continue;
        }

        if (poDetailsHasSAP == 0) {
          await this.purchaseOrderRepository.updateState(po.id, State.Pending);
          continue;
        }

        await this.purchaseOrderRepository.updateState(
          po.id,
          State.PartialCompleted,
        );
      }
    }
  }

  async exportPo(
    paginationDto: GetPurchaseOrderDto,
    authorization,
    jwtPayload,
  ) {
    paginationDto.getAll = 1;
    const data = await this.findAll(
      paginationDto,
      authorization,
      jwtPayload,
      true,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(__dirname, '../domain/template-export/template-export-po.xlsx'),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Copy value
        targetCell.style = { ...cell.style }; // Copy full style
        targetCell.border = cell.border; // Copy border
        targetCell.font = cell.font; // Copy font
        targetCell.alignment = cell.alignment; // Copy alignment
        targetCell.numFmt = cell.numFmt; // Copy number format
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toPoModel(data.results);
      for (let i = 0; i < items.length; i++) {
        const targetRow = targetWorksheet.getRow(i + 2);
        Object.values(items[i]).forEach((value: any, colIndex) => {
          const targetCell = targetRow.getCell(colIndex + 1);
          const sourceCell = sourceWorksheet.getRow(2).getCell(colIndex + 1); // Lấy format từ dòng 2

          let finalValue = value; // Giá trị thực tế
          let numFmt = sourceCell.numFmt; // Format Excel

          // Nếu giá trị rỗng, giữ nguyên ''
          if (value === '' || value === null || value === undefined) {
            finalValue = '';
          } else if (sourceCell.numFmt) {
            if (
              sourceCell.numFmt.includes('yyyy') ||
              sourceCell.numFmt.includes('mm') ||
              sourceCell.numFmt.includes('dd')
            ) {
              // Nếu là ngày, giữ đúng kiểu Date
              if (value instanceof Date) {
                finalValue = value;
              } else {
                const parsedDate = new Date(value);
                finalValue = isNaN(parsedDate.getTime()) ? '' : parsedDate;
              }
              numFmt = 'dd/mm/yyyy'; // Format ngày
            } else {
              // Nếu là số hoặc tiền tệ, ép kiểu về number nếu có thể
              const numericValue = Number(value);
              if (!isNaN(numericValue)) {
                finalValue = numericValue;
              }
            }
          }

          targetCell.value = finalValue; // Gán giá trị vào ô

          if (numFmt) {
            targetCell.numFmt = numFmt; // Áp dụng định dạng Excel
          }
        });

        targetRow.commit();
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-po.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toPoModel(pos: PurchaseOrderModel[]) {
    const items = [];

    for (let i = 0; i < (pos || []).length; i++) {
      const sortedHistory =
        pos[i]?.history
          ?.filter((item) => item.level)
          ?.sort((a, b) => a.level - b.level) || [];
      for (let y = 0; y < (pos[i]?.details || []).length; y++) {
        items.push({
          poCode: pos[i].id || '',
          requester:
            `${pos[i]?.requester?.firstName || ''} ${pos[i]?.requester?.lastName || ''}`.trim(),
          createdAt: pos[i]?.createdAt ? new Date(pos[i]?.createdAt) : '',
          sectorCode: pos[i]?.sector?.code || '',
          sectorName: pos[i]?.sector?.name || '',
          functionUnitName: pos[i]?.functionUnit?.name || '',
          businessUnitCode: pos[i]?.businessUnit?.code || '',
          businessUnitName: pos[i]?.businessUnit?.name || '',
          typePo: pos[i]?.typePo?.name || '',
          isCheckBudget: this.getIsCheckBudget(pos[i]?.isCheckBudget || false),
          purchaseOrgCode: pos[i]?.purchaseOrg?.code || '',
          purchaseOrgName: pos[i]?.purchaseOrg?.name || '',
          purchaseGroupCode: pos[i]?.purchaseGroup?.code || '',
          purchaseGroupName: pos[i]?.purchaseGroup?.name || '',
          priority: this.getPriorityPr(pos[i]?.priority),
          reason: pos[i]?.reason || '',
          paymentMethod: this.getPaymentMethod(pos[i]?.paymentMethod),
          status: this.getStatusPo(pos[i].statusPo),
          totalAmount: (pos[i].details || []).reduce(
            (sum, item) => sum + Number(item.totalAmount),
            0,
          ),
          budgetCode: pos[i]?.details[y]?.budgetCode?.code || '',
          costCenter: pos[i]?.details[y]?.costCenter?.name || '',
          materialCode: pos[i]?.details[y]?.material?.code || '',
          materialName: pos[i]?.details[y]?.materialName || '',
          materialGroup: pos[i]?.details[y]?.materialGroupName || '',
          accountAssignment: pos[i]?.details[y]?.accountAssignment || '',
          accountGl: pos[i]?.details[y]?.accountGl || '',
          internalOrder: pos[i]?.details[y]?.internalOrder || '',
          wbs: pos[i]?.details[y]?.wbs || '',
          supplier: pos[i]?.details[y]?.supplier?.name || '',
          deliveryTime: pos[i]?.details[y]?.deliveryTime
            ? new Date(pos[i]?.details[y]?.deliveryTime)
            : '',
          warehouse: pos[i]?.details[y]?.warehouse?.code || '',
          estimatedPrice: pos[i]?.details[y]?.estimatedPrice || 0,
          purchasePrice: pos[i]?.details[y]?.purchasePrice || 0,
          quantity: pos[i]?.details[y]?.quantity || 0,
          unit: pos[i]?.details[y]?.unit || '',
          total: pos[i]?.details[y]?.totalAmount || 0,
          taxCode: pos[i]?.details[y]?.taxCode?.code || '',
          vat: pos[i]?.details[y]?.vat || 0,
          totalAmountVat: pos[i]?.details[y]?.totalAmountVat || 0,
          note: pos[i]?.details[y]?.note || '',
          prId: pos[i]?.details[y]?.prReference || '',
          approver1: sortedHistory[0]?.name || '',
          approvedDate1:
            sortedHistory[0]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[0]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
          approver2: sortedHistory[1]?.name || '',
          approvedDate2:
            sortedHistory[1]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[1]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
          approver3: sortedHistory[2]?.name || '',
          approvedDate3:
            sortedHistory[2]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[2]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
          approver4: sortedHistory[3]?.name || '',
          approvedDate4:
            sortedHistory[3]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[3]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
          headerError: (pos[i]?.details[y]?.sapPurchaseOrderItems || [])
            .map((item) => item.sapPurchaseOrder?.message)
            .filter(Boolean)
            .join(', '),
          itemError: (pos[i]?.details[y]?.sapPurchaseOrderItems || [])
            .map((item) => item.message)
            .filter(Boolean)
            .join(', '),
        });
      }
    }

    return items;
  }

  getIsCheckBudget(isCheck: boolean) {
    switch (isCheck) {
      case true:
        return 'Có';
      case false:
        return 'Không';
      default:
        return '';
    }
  }

  getPriorityPr(priority?: Priority) {
    switch (priority) {
      case Priority.Urgent:
        return 'Khẩn cấp';
      case Priority.Important:
        return 'Quan trọng';
      case Priority.Normal:
        return 'Bình thường';
      default:
        return '';
    }
  }

  getPaymentMethod(paymentMethod?: EPaymentMethod) {
    switch (paymentMethod) {
      case EPaymentMethod.OFFER:
        return 'Chào giá';
      case EPaymentMethod.COMPETITIVE_QUOTATION:
        return 'Báo giá cạnh tranh';
      case EPaymentMethod.OPEN_BIDDING:
        return 'Đấu thầu mở rộng';
      case EPaymentMethod.SELECTIVE_BIDDING:
        return 'Đấu thầu có chọn lọc';
      case EPaymentMethod.DIRECT_APPOINTMENT:
        return 'Chỉ định thầu';
      default:
        return '';
    }
  }

  getStatusPo(status?: Status) {
    switch (status) {
      case Status.Temporary:
        return 'Lưu tạm';
      case Status.Pending:
        return 'Chờ duyệt';
      case Status.InProgress:
        return 'Đang duyệt';
      case Status.Approved:
        return 'Duyệt';
      case Status.Rejected:
        return 'Từ chối';
      case Status.Rechecked:
        return 'Kiểm tra lại';
      case Status.Cancel:
        return 'Hủy';
      case Status.WaitingProcess:
        return 'Chờ duyệt';
      case Status.Draft:
        return 'Lưu tạm';
      case Status.Closed:
        return 'Đóng';
      default:
        return '';
    }
  }

  async findPoReportBudget(
    conditions: GetPoDetailReportBudgetDetailDto,
    authorization,
  ): Promise<PurchaseOrderDetailModel[]> {
    return await this.purchaseOrderRepository.findPoReportBudget(
      conditions,
      authorization,
    );
  }

  async findAdjustBudgetIdInPoDetail(
    adjustBudgetIds: string[],
  ): Promise<PurchaseOrderDetailModel[]> {
    return await this.purchaseOrderRepository.findAdjustBudgetIdInPoDetail(
      adjustBudgetIds,
    );
  }

  private async getDisplayStatus(status?: Status) {
    if (!status) {
      return EDisplayStatus.Draft;
    } else {
      const displayStatus = Object.values(EDisplayStatus).find(
        (value) => value == status.toString(),
      );

      return displayStatus ? displayStatus : EDisplayStatus.Draft;
    }
  }

  private getApprovalLevelByAprovalProcessDetail(
    approvalProcessDetails: ResponseDto<ApprovalProcessDetailModel>,
    approvalLevel: ApprovalLevelDto,
    approver: EApprover,
    purchases: PurchaseOrderDto,
  ) {
    if (!approvalProcessDetails?.results.length) {
      throw new HttpException(
        approvalErrorDetails.E_5411('Không tìm thấy quy trình duyệt chi tiết'),
        HttpStatus.BAD_REQUEST,
      );
    }
    //Only get top 1
    const approvalProcessDetail = approvalProcessDetails.results[0];

    if (approvalProcessDetail.poCreatedById !== purchases.requesterId) {
      throw new HttpException(
        approvalErrorDetails.E_5411(
          'Người yêu cầu không tìm thấy trong quy trình duyệt chi tiết',
        ),
        HttpStatus.BAD_REQUEST,
      );
    }

    const approverKey = PO_APPROVER_KEYS.get(approver);
    if (!approverKey) {
      throw new HttpException(
        approvalErrorDetails.E_5411('Cấp duyệt không hợp lệ'),
        HttpStatus.BAD_REQUEST,
      );
    }

    const approverData = approvalProcessDetail[approverKey] as StaffModel;
    if (!approverData) {
      throw new HttpException(
        approvalErrorDetails.E_5411(
          `Người duyệt cấp ${approver} không tìm thấy trong quy trình duyệt chi tiết`,
        ),
        HttpStatus.BAD_REQUEST,
      );
    }

    // Trả về thông tin duyệt
    return {
      ...approvalLevel,
      email: approverData?.email || '',
      name: `${approverData?.lastName || ''} ${approverData?.firstName || ''}`,
    };
  }

  async resendEmailPo(data: ResendEmailPoDto, authorization?: string) {
    const pos = await this.purchaseOrderRepository.getPosForResendEmail(
      data.poIds,
    );

    for (const po of pos) {
      po.levels = po.levels?.sort((a, b) => a.level - b.level) || [];
      await this.handleEmailNotification(po.levels[0], po, po, authorization);
    }

    return { message: 'Successfully!!!' };
  }
}
