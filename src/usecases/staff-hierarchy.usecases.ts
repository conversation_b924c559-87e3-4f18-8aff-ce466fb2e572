import { Inject, Injectable } from '@nestjs/common';
import { StaffHierarchyModel } from '../domain/model/staff-hierarchy.model';
import { IStaffHierarchyRepository } from '../domain/repositories/staff-hierarchy.repository';

@Injectable()
export class StaffHierarchyUsecases {
  constructor(
    @Inject(IStaffHierarchyRepository)
    private readonly staffHierarchyRepository: IStaffHierarchyRepository,
  ) {}
  async createStaffHierarchy(data: StaffHierarchyModel): Promise<any> {
    return await this.staffHierarchyRepository.createStaffHierarchy(data);
  }

  async getExistManagementLevel(
    subordinateId: string,
  ): Promise<StaffHierarchyModel[]> {
    return (
      (await this.staffHierarchyRepository.getExistManagementLevel(
        subordinateId,
      )) || []
    );
  }
}
