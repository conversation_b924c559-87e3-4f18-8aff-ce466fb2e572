import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import { CreateProcessTypeDto } from '../controller/process-type/dtos/create-process-type.dto';
import { GetDetailProcessTypeDto } from '../controller/process-type/dtos/get-detail-process-type.dto';
import { GetProcessTypeListDto } from '../controller/process-type/dtos/get-process-type-list.dto';
import { UpdateProcessTypeDto } from '../controller/process-type/dtos/update-process-type.dto';
import { EStatus } from '../domain/config/enums/status.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { processTypeErrorDetails } from '../domain/messages/error-details/process-type';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { ProcessTypeModel } from '../domain/model/process-type.model';
import { IProcessTypeRepository } from '../domain/repositories/process-type.repository';

@Injectable()
export class ProcessTypeUsecases {
  constructor(
    @Inject(IProcessTypeRepository)
    private readonly processTypeRepository: IProcessTypeRepository,
  ) {}
  async createProcessType(
    data: CreateProcessTypeDto,
    jwtPayload: any,
  ): Promise<ProcessTypeModel> {
    await this.checkExistProcessTypeByCode(data.code);

    data.createdBy = {
      id: jwtPayload?.userId,
      firstName: jwtPayload?.firstName,
      lastName: jwtPayload?.lastName,
      email: jwtPayload?.email,
      phone: jwtPayload?.phone,
      staffId: jwtPayload?.staffId,
      staffCode: jwtPayload?.staffCode,
    };

    const processType =
      await this.processTypeRepository.createProcessType(data);

    // await sendPost(
    //   IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
    //   {
    //     description: processType.name,
    //     refId: processType.id,
    //     refCode: processType.code,
    //     type: EDataRoleType.processType,
    //     isEnabled: processType.status === EStatus.ACTIVE,
    //     platform: EPlatform.E_PURCHASE,
    //   },
    //   { authorization },
    // );

    return processType;
  }

  async updateProcessType(
    id: string,
    updateProcessTypeDto: UpdateProcessTypeDto,
    jwtPayload: any,
  ): Promise<ProcessTypeModel> {
    await this.getDetailProcessType({ id });

    if (updateProcessTypeDto.code)
      await this.checkExistProcessTypeByCode(updateProcessTypeDto.code, id);

    updateProcessTypeDto.updatedBy = {
      id: jwtPayload?.userId,
      firstName: jwtPayload?.firstName,
      lastName: jwtPayload?.lastName,
      email: jwtPayload?.email,
      phone: jwtPayload?.phone,
      staffId: jwtPayload?.staffId,
      staffCode: jwtPayload?.staffCode,
    };

    const processType = await this.processTypeRepository.updateProcessType(
      id,
      updateProcessTypeDto,
    );

    // await sendPatch(
    //   IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
    //   {
    //     description: processType.name,
    //     refCode: processType.code,
    //     isEnabled: processType.status === EStatus.ACTIVE,
    //   },
    //   {
    //     authorization,
    //   },
    // );

    return processType;
  }

  async getProcessTypeById(id: string): Promise<ProcessTypeModel> {
    const processType = await this.processTypeRepository.getProcessTypeById(id);

    if (!processType) {
      throw new HttpException(
        processTypeErrorDetails.E_2700(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return processType;
  }

  async getProcessTypes(
    conditions: GetProcessTypeListDto,
  ): Promise<ResponseDto<ProcessTypeModel>> {
    return await this.processTypeRepository.getProcessTypes(conditions);
  }

  async deleteProcessType(id: string): Promise<void> {
    await this.getDetailProcessType({ id });

    await this.processTypeRepository.deleteProcessType(id);

    // await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
    //   authorization,
    // });
  }

  async checkExistProcessTypeByCode(code: string, id?: string): Promise<void> {
    const processType = await this.processTypeRepository.getProcessTypeByCode(
      code,
      id,
    );

    if (processType) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1037()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getProcessTypeByCode(code: string): Promise<ProcessTypeModel> {
    const processType =
      await this.processTypeRepository.getProcessTypeByCode(code);

    if (!processType) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1036()),
        HttpStatus.BAD_REQUEST,
      );
    }

    return processType;
  }

  async getDetailProcessType(conditions: GetDetailProcessTypeDto) {
    const detail =
      await this.processTypeRepository.getDetailProcessType(conditions);

    if (!detail) {
      throw new HttpException(
        processTypeErrorDetails.E_2700(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return detail;
  }

  async getProcessTypeByIds(ids: string[]) {
    const processTypes =
      await this.processTypeRepository.getProcessTypeByIds(ids);

    if (!processTypes || !processTypes.length) {
      throw new HttpException(
        processTypeErrorDetails.E_2700(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const activeProcessTypes = processTypes?.filter(
      (item) => item.status === EStatus.ACTIVE,
    );

    const activeProcessTypeIds = activeProcessTypes.map((item) => item.id);

    const missingProcessTypeIds = _.difference(ids, activeProcessTypeIds);

    if (missingProcessTypeIds.length) {
      throw new HttpException(
        processTypeErrorDetails.E_2700(
          `Process type ids ${missingProcessTypeIds.join(', ')} not found or inactive`,
        ),

        HttpStatus.NOT_FOUND,
      );
    }

    return processTypes;
  }

  async listByCodes(codes: string[], isNeedPermission: boolean = true) {
    return await this.processTypeRepository.getProcessTypesByCodesWithRole(
      [...new Set(codes)],
      isNeedPermission,
    );
  }
}
