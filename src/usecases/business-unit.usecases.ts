import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { CreateBusinessUnitDto } from '../controller/business-unit/dtos/create-business-unit.dto';
import { DeleteBusinessUnitDto } from '../controller/business-unit/dtos/delete-business-unit.dto';
import { GetBusinessUnitListDto } from '../controller/business-unit/dtos/get-business-unit-list.dto';
import { GetDetailBusinessUnitDto } from '../controller/business-unit/dtos/get-detail-business-unit.dto';
import { UpdateBusinessUnitDto } from '../controller/business-unit/dtos/update-business-unit.dto';
import { GetDetailCompanyDto } from '../controller/company/dtos/get-detail-company.dto';
import { ImportBusinessUnitDto } from '../controller/import/dtos/import-business-unit.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EAcutalEventFor,
  ISapEventCreate,
} from '../domain/config/enums/actual-spending.enum';
import {
  EBusinessUnitStatus,
  EColumnImportBusinessUnit,
} from '../domain/config/enums/business-unit.enum';
import { ECompanyStatus } from '../domain/config/enums/company.enum';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { BusinessUnitModel } from '../domain/model/business-unit.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { IBusinessUnitRepository } from '../domain/repositories/business-unit.repository';
import { ICompanyRepository } from '../domain/repositories/company.repository';
import {
  checkValuesEmptyRowExcel,
  getStatus,
  getStatusBusinessUnit,
  getValueOrResult,
} from '../utils/common';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { CompanyUsecases } from './company.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';

@Injectable()
export class BusinessUnitUsecases {
  constructor(
    @Inject(IBusinessUnitRepository)
    private readonly businessUnitRepository: IBusinessUnitRepository,
    private readonly companyUsecases: CompanyUsecases,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    @Inject(ICompanyRepository)
    private readonly companyRepository: ICompanyRepository,
    private eventEmitter: EventEmitter2,
  ) {}

  async createBusinessUnit(
    data: CreateBusinessUnitDto,
    authorization: string,
    jwtPayload: any,
  ): Promise<BusinessUnitModel> {
    await this.checkCodeBusinessUnit(data.code);

    const businessUnitModel = new BusinessUnitModel({
      ...data,
    });

    const company = await this.companyUsecases.getDetailCompany(
      plainToInstance(GetDetailCompanyDto, {
        id: data?.companyId,
      }),
      jwtPayload,
    );

    if (company.status == ECompanyStatus.IN_ACTIVE) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1028()),
        HttpStatus.BAD_REQUEST,
      );
    }

    businessUnitModel.company = company;

    const businessUnit =
      await this.businessUnitRepository.createBusinessUnit(businessUnitModel);

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: businessUnit.name,
        refId: businessUnit.id,
        refCode: businessUnit.code,
        type: EDataRoleType.BUSINESS_UNIT,
        isEnabled: businessUnit.status === EBusinessUnitStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization, 'x-api-key': process.env.API_KEY },
    );

    this.eventEmitter.emit('sap.bu.created', {
      code: businessUnit.code,
      id: businessUnit.id,
      eventFor: EAcutalEventFor.BU,
    } as ISapEventCreate);

    return businessUnit;
  }

  async getBusinessUnitById(id: string): Promise<BusinessUnitModel> {
    return await this.businessUnitRepository.getBusinessUnitById(id);
  }

  async updateBusinessUnit(
    data: UpdateBusinessUnitDto,
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<BusinessUnitModel> {
    const detail = await this.getDetailBusinessUnit(
      plainToInstance(GetDetailBusinessUnitDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.checkCodeBusinessUnit(data.code, id);

    const businessUnitData = new BusinessUnitModel({
      id,
      ...data,
    });

    if (data?.companyId != detail?.company?.id) {
      const company = await this.companyUsecases.getDetailCompany(
        plainToInstance(GetDetailCompanyDto, {
          id: data?.companyId,
        }),
        jwtPayload,
      );

      if (company.status == ECompanyStatus.IN_ACTIVE) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1028()),
          HttpStatus.BAD_REQUEST,
        );
      }

      businessUnitData.company = company;
    }

    const businessUnit =
      await this.businessUnitRepository.updateBusinessUnit(businessUnitData);

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: businessUnit.name,
        refCode: businessUnit.code,
        isEnabled: businessUnit.status === EBusinessUnitStatus.ACTIVE,
      },
      {
        authorization,
        'x-api-key': process.env.API_KEY,
      },
    );

    if (detail.code != businessUnitData.code) {
      this.eventEmitter.emit('sap.bu.created', {
        code: businessUnit.code,
        id: businessUnit.id,
        eventFor: EAcutalEventFor.BU,
      } as ISapEventCreate);
    }

    return businessUnit;
  }

  async deleteBusinessUnit(
    conditions: DeleteBusinessUnitDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailBusinessUnit(
      plainToInstance(GetDetailBusinessUnitDto, {
        id: conditions.id,
      }),
      jwtPayload,
    );

    await this.businessUnitRepository.deleteBusinessUnit(conditions.id);

    await sendDelete(
      IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(conditions.id),
      {
        authorization,
        'x-api-key': process.env.API_KEY,
      },
    );
  }

  async getBusinessUnits(
    data: GetBusinessUnitListDto,
    jwtPayload,
  ): Promise<ResponseDto<BusinessUnitModel>> {
    return await this.businessUnitRepository.getBusinessUnits(data, jwtPayload);
  }

  async getDetailBusinessUnit(
    conditions: GetDetailBusinessUnitDto,
    jwtPayload: any,
  ): Promise<BusinessUnitModel> {
    const detail = await this.businessUnitRepository.getDetailBusinessUnit(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1022()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async getBusinessUnitByCode(code: string): Promise<BusinessUnitModel> {
    return await this.businessUnitRepository.getBusinessUnitByCode(code);
  }

  async checkCodeBusinessUnit(code: string, id?: string) {
    const checkCode = await this.businessUnitRepository.getBusinessUnitByCode(
      code,
      id,
    );

    if (checkCode) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1080()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getBuByIds(ids: string[], jwtPayload: any) {
    const businessUnits = await this.businessUnitRepository.getBuByIds(
      ids,
      jwtPayload,
    );

    if (!businessUnits || !businessUnits.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1022()),
        HttpStatus.NOT_FOUND,
      );
    }

    const activeBusinessUnits = businessUnits?.filter(
      (item) => item.status === EBusinessUnitStatus.ACTIVE,
    );

    const activeBusinessUnitIds = activeBusinessUnits.map((item) => item.id);

    const missingBusinessUnitIds = _.difference(ids, activeBusinessUnitIds);

    if (missingBusinessUnitIds.length) {
      throw new HttpException(
        getErrorMessage(
          errorMessage.E_1022(
            `Business unit ids ${missingBusinessUnitIds.join(', ')} not found or inactive`,
          ),
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return businessUnits;
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.businessUnitRepository.getBusinessUnitsByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }

  async exportBusinessUnit(
    conditions: GetBusinessUnitListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data = await this.businessUnitRepository.getBusinessUnits(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-business-unit.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toBusinessUnitModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-business-unit.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toBusinessUnitModel(businessUnits: BusinessUnitModel[]) {
    const items = [];

    for (let i = 0; i < businessUnits.length; i++) {
      items.push({
        code: businessUnits[i].code || '',
        name: businessUnits[i].name || '',
        description: businessUnits[i].description || '',
        companyCode: businessUnits[i].company?.code || '',
        companyName: businessUnits[i].company?.name || '',
        status: getStatusBusinessUnit(businessUnits[i].status),
      });
    }

    return items;
  }

  async importBusinessUnit(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.BUSINESS_UNIT,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createBusinessUnitDtos: CreateBusinessUnitDto[] = [];
        const updateBusinessUnitDtos: UpdateBusinessUnitDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportBusinessUnit.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const companyCodes = rows
          .map((item) =>
            getValueOrResult(
              item,
              EColumnImportBusinessUnit.COMPANY_CODE,
            )?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const [businessUnits, companys] = await Promise.all([
          this.businessUnitRepository.getBusinessUnitsByCodesWithRole(
            [...new Set(codes)],
            jwtPayload,
            false,
          ),
          this.companyRepository.getCompanyByCodesWithRole(
            [...new Set(companyCodes)],
            jwtPayload,
          ),
        ]);

        let totalRowHasValue = 0;

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportBusinessUnit.CODE, // First Cell
            EColumnImportBusinessUnit.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          ///Data từng row trong file excel
          const code = getValueOrResult(
            row,
            EColumnImportBusinessUnit.CODE,
          )?.toString();
          const name = getValueOrResult(
            row,
            EColumnImportBusinessUnit.NAME,
          )?.toString();
          const description = getValueOrResult(
            row,
            EColumnImportBusinessUnit.DESCRIPTION,
          )?.toString();
          const companyCode = getValueOrResult(
            row,
            EColumnImportBusinessUnit.COMPANY_CODE,
          )?.toString();
          const status = getStatus(
            BusinessUnitModel,
            getValueOrResult(row, EColumnImportBusinessUnit.STATUS)?.toString(),
          ); //Trạng thái

          const businessUnitObject = {
            id: undefined,
            code,
            name,
            description,
            status,
            companyId: null,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };

          if (!code) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1098(),
                'Mã đơn vị kinh doanh không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const checkCode = businessUnits.find((item) => item.code == code);

            const checkDup = codes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1100(),
                  'Mã đơn vị kinh doanh bị trùng lặp',
                ),
                row: i + 1,
              });
            }

            if (checkCode) {
              businessUnitObject.id = checkCode.id;
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1099(),
                'Tên đơn vị kinh doanh không được để trống',
              ),
              row: i + 1,
            });
          }

          if (!companyCode) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1095(),
                'Mã công ty không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const company = companys.find((item) => item.code == companyCode);
            if (!company) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1024(),
                  'Mã công ty chưa đúng',
                ),
                row: i + 1,
              });
            }

            businessUnitObject.companyId = company?.id;
          }

          if (businessUnitObject.id) {
            const businessUnitDto = plainToInstance(UpdateBusinessUnitDto, {
              ...businessUnitObject,
              createdAt: undefined,
            });
            updateBusinessUnitDtos.push(businessUnitDto);
          } else {
            const businessUnitDto = plainToInstance(
              CreateBusinessUnitDto,
              businessUnitObject,
            );
            createBusinessUnitDtos.push(businessUnitDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportBusinessUnitDto = {
          dataBusinessUnits: createBusinessUnitDtos,
          dataUpdateBusinessUnits: updateBusinessUnitDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_BUSINESS_UNIT(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
