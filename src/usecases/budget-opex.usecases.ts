import { Inject, Injectable } from '@nestjs/common';
import { IBudgetOpexRepository } from '../domain/repositories/budget-opex.repository';
import { BudgetOpexModel } from '../domain/model/budget-opex.model';
import { CreateBudgetOpexDto } from '../controller/budget/dtos/create-budget-opex.dto';
import { UpdateBudgetOpexDto } from '../controller/budget/dtos/update-budget-opex.dto';

@Injectable()
export class BudgetOpexUsecases {
  constructor(
    @Inject(IBudgetOpexRepository)
    private readonly budgetOpexRepository: IBudgetOpexRepository,
  ) {}

  async createBudgetOpex(data: CreateBudgetOpexDto): Promise<BudgetOpexModel> {
    const createBudgetOpex = new BudgetOpexModel({
      ...data,
    });

    return await this.budgetOpexRepository.createBudgetOpex(createBudgetOpex);
  }

  async updateBudgetOpex(
    id: string,
    data: UpdateBudgetOpexDto,
  ): Promise<BudgetOpexModel> {
    const updateBudgetOpex = new BudgetOpexModel({
      id: id,
      ...data,
    });

    return await this.budgetOpexRepository.updateBudgetOpex(updateBudgetOpex);
  }

  async getBudgetOpexById(id: string): Promise<BudgetOpexModel> {
    return await this.budgetOpexRepository.getBudgetOpexById(id);
  }
}
