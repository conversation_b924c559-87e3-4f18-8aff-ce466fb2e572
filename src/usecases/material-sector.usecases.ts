import { Inject, Injectable } from '@nestjs/common';
import { MaterialSectorModel } from '../domain/model/material-sector.model';
import { IMaterialSectorRepository } from '../domain/repositories/material-sector.repository';

@Injectable()
export class MaterialSectorUsecases {
  constructor(
    @Inject(IMaterialSectorRepository)
    private readonly materialSectorRepository: IMaterialSectorRepository,
  ) {}

  async createManyMaterialSector(
    data: MaterialSectorModel[],
  ): Promise<MaterialSectorModel[]> {
    return await this.materialSectorRepository.createMaterialSectors(data);
  }

  async updateManyMaterialSector(
    data: MaterialSectorModel[],
  ): Promise<MaterialSectorModel[]> {
    return await this.materialSectorRepository.updateMaterialSectors(data);
  }

  async deleteMaterialSectors(materialId: string): Promise<void> {
    return await this.materialSectorRepository.deleteMaterialSectors(
      materialId,
    );
  }
}
