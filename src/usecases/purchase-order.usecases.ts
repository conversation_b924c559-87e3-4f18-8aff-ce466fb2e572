import { Inject, Injectable, NotFoundException } from '@nestjs/common';

import { ResponseDto } from '../domain/dtos/response.dto';
import { IPurchaseOrderRepository } from '../domain/repositories/purchase-order.repository';
import { GetListPurchaseOrderDto } from '../controller/purchase-order/dtos/get-list-purchase-order.dto';
import { PurchaseOrderModel } from '../domain/model/purchase_order.model';
import { IApprovalLevelRepository } from '../domain/repositories/approval-level.repository';

@Injectable()
export class PurchaseOrderUsecases {
  constructor(
    @Inject(IPurchaseOrderRepository)
    private readonly purchaseOrderRepository: IPurchaseOrderRepository,
    @Inject(IApprovalLevelRepository)
    private readonly prApprovalFlowRepository: IApprovalLevelRepository,
  ) {}

  async getListPurchaseOrders(
    conditions: GetListPurchaseOrderDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PurchaseOrderModel>> {
    return await this.purchaseOrderRepository.getListPurchaseOrder(
      conditions,
      jwtPayload,
    );
  }

  async findOne(
    id: number,
    authorization: string,
  ): Promise<PurchaseOrderModel> {
    let item = await this.purchaseOrderRepository.findOne(id);
    const approvalLevels =
      await this.prApprovalFlowRepository.findApprovalLevels(null, id);

    if (!item) {
      throw new NotFoundException(`Purchase order with id ${id} not found`);
    }

    const sortedLevels = approvalLevels.sort((a, b) => a.level - b.level);
    item.levels = sortedLevels;
    return item;
  }
}
