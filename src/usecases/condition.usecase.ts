import { Inject, Injectable } from '@nestjs/common';
import { CreateConditionDto } from '../controller/process/dtos/create-condition.dto';
import { ConditionModel } from '../domain/model/condition.model';
import { IConditionRepository } from '../domain/repositories/condition.repository';
import { ConditionDetailUsecases } from './condition-detail.usecase';

@Injectable()
export class ConditionUsecases {
  constructor(
    private conditionDetailUsecases: ConditionDetailUsecases,
    @Inject(IConditionRepository)
    private readonly conditionRepository: IConditionRepository,
  ) {}
  async createCondition(
    data: CreateConditionDto,
    jwtPayload: string,
  ): Promise<ConditionModel> {
    const condition = await this.conditionRepository.createCondition(data);

    if (data?.createConditionDetailDtos?.length) {
      data.createConditionDetailDtos.forEach((conditionDetail) => {
        conditionDetail.conditionId = condition.id;
        conditionDetail.createdBy = data.createdBy;
        conditionDetail.updatedBy = data.updatedBy;
      });
      await this.conditionDetailUsecases.createConditionDetails(
        data.createConditionDetailDtos,
        jwtPayload,
      );
    }

    return condition;
  }

  async deleteCondition(id: string): Promise<void> {
    await this.conditionDetailUsecases.deleteConditionDetailByConditionId(id);
    await this.conditionRepository.deleteCondition(id);
  }

  async getConditionByProcessId(processId: string) {
    return await this.conditionRepository.getConditionByProcessId(processId);
  }
}
