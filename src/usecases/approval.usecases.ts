import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ApprovalLevelDto,
  ApprovePrDto,
  CancelPrPoDto,
  RejectPrDto,
  StatusLevel,
} from '../controller/approve/dtos/approve.dto';
import { PurchaseOrderDetailDto } from '../controller/purchase-order/dtos/purchase-order-detail.dto';
import { PurchaseRequestDetailDto } from '../controller/purchase-request/dtos/purchase-request-detail.dto';

import { IPurchaseOrderRepository } from '../domain/repositories/purchaseOrderRepository.repository';
import { IPurchaseRequestRepository } from '../domain/repositories/purchaseRequestRepository.repository';
import { emailTemplateService } from '../infrastructure/config/email-transport-config/email-template.service';
import { emailTransportService } from '../infrastructure/config/email-transport-config/email.service';
import {
  ApprovalLevelEntity,
  ApproveType,
} from '../infrastructure/entities/approval-level.entity';
import { HistoryApproveEntity } from '../infrastructure/entities/history-approve.entity';
import { PurchaseOrderEntity } from '../infrastructure/entities/purchase_order.entity';
import { PurchaseOrderDetailEntity } from '../infrastructure/entities/purchase_order_detail.entity';
import { PurchaseRequestEntity } from '../infrastructure/entities/purchase_request.entity';
import { HttpService } from '../infrastructure/http/http.service';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { getData, sendPost } from '../utils/http';
import { DataSource, MoreThan, QueryRunner } from 'typeorm';
import { purchaseOrderUsecases } from './purchase_order.usecases';
import { purchaseRequestUsecases } from './purchase_request.usecases';
import { SapPurchaseOrderUsecases } from './sap-purchase-order.usecases';
import { approvalErrorDetails } from '../domain/messages/error-details/approval';
import { IPrApprovalFlowRepository } from '../domain/repositories/prApprovalFlowRepository.repository';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { EDisplayStatus } from '../domain/config/enums/purchase-order.enum';
import { StaffUsecases } from './staff.usecase';
import { BudgetUsecases } from './budget.usecases';
import { CreateBudgetOpexDto } from '../controller/budget/dtos/create-budget-opex.dto';
import { CreateBudgetDto } from '../controller/budget/dtos/create-budget.dto';
import {
  EBudgetCreateType,
  EBudgetType,
} from '../domain/config/enums/budget.enum';
import { CreateBudgetCapexDto } from '../controller/budget/dtos/create-budget-capex.dto';
import { PurchaseOrderDetailModel } from '../domain/model/purchase_order_detail.model';
import { BudgetCodeUsecases } from './budget-code.usecases';
import { PurchaseOrderModel } from '../domain/model/purchase_order.model';
import { PurchaseRequestModel } from '../domain/model/purchase_request.model';
import { ApprovalLevelModel } from '../domain/model/approval-level.model';
import { State, Status } from '../domain/config/enums/purchase-request.enum';
import { EProcessType } from '../domain/config/enums/process-type.enum';
import { SolomonPurchaseOrderUsecases } from './solomon-purchase-order.usecases';
import { ENTITY_MANAGER_KEY } from '../infrastructure/config/typeorm/tracsaction.interceptor';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { Request } from 'express';

@Injectable()
export class approvalUsecases {
  private transporter;

  constructor(
    private readonly dataSource: DataSource,
    private sapPurchaseOrderUsecases: SapPurchaseOrderUsecases,
    private solomonPurchaseOrderUsecases: SolomonPurchaseOrderUsecases,
    @Inject('IPurchaseRequestRepository')
    private readonly purchaseRequestRepository: IPurchaseRequestRepository,
    @Inject('IPurchaseOrderRepository')
    private readonly purchaseOrderRepository: IPurchaseOrderRepository,
    @Inject('IPrApprovalFlowRepository')
    private readonly prApprovalFlowRepository: IPrApprovalFlowRepository,
    private _emailTransportService: emailTransportService,
    private _emailTemplateService: emailTemplateService,
    private readonly configService: ConfigService,
    private _purchaseRequestUsecases: purchaseRequestUsecases,
    private _purchaseOrderUsecases: purchaseOrderUsecases,
    private readonly httpService: HttpService,

    private readonly staffUsecases: StaffUsecases,
    private readonly budgetUsecases: BudgetUsecases,
    private readonly budgetCodeUsecases: BudgetCodeUsecases,
  ) {
    this.transporter = this._emailTransportService.getTransporter();
  }

  async approveLowestLevel(
    data: ApprovePrDto,
    authorization: string,
    jwtPayload: any,
    request: Request,
  ): Promise<void | {
    approvalSteps: ApprovalLevelDto[];
    staffApprovalWorkflowId: string;
  }> {
    const { levelId: id, purchaseId: purchase_id } = data;
    const queryRunner: QueryRunner =
      request[ENTITY_MANAGER_KEY] ?? this.dataSource.createQueryRunner();

    try {
      if (!queryRunner.isTransactionActive) {
        await queryRunner.connect();
        await queryRunner.startTransaction();
      }

      const [currentLevel, purchase] = await Promise.all([
        this.prApprovalFlowRepository.findOne(id),
        this._purchaseRequestUsecases.findById(purchase_id),
      ]);

      if (jwtPayload?.email !== currentLevel.email) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_5333()),
          HttpStatus.FORBIDDEN,
        );
      }

      if (
        purchase.statusPr == Status.Cancel ||
        purchase.statusPr == Status.Closed
      ) {
        throw new HttpException(
          approvalErrorDetails.E_5414('PR đã bị hủy'),
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!currentLevel)
        throw new HttpException(
          approvalErrorDetails.E_5413(`Không tìm thấy cấp duyệt ${id}`),
          HttpStatus.NOT_FOUND,
        );

      // Update history approval status
      const historyWithCurrentLevel = purchase?.history?.find(
        (item) =>
          item.level == currentLevel.level && item.email == currentLevel.email,
      );

      if (historyWithCurrentLevel) {
        await queryRunner.manager.update(
          HistoryApproveEntity,
          historyWithCurrentLevel.id,
          {
            status: StatusLevel.Approved,
            reason: data.reason,
          },
        );
      }

      // Update purchase request status
      await queryRunner.manager.update(PurchaseRequestEntity, purchase.id, {
        statusPr: Status.InProgress,
        isAccountantApproved: purchase.isAccountantApproved
          ? purchase.isAccountantApproved
          : currentLevel.isAccountantApproved,
      });

      // Update current approval level status
      await queryRunner.manager.update(ApprovalLevelEntity, currentLevel.id, {
        status: StatusLevel.Approved,
      });

      // Handle accountant approval and return next levels
      // Nếu cấp duyệt có tích Kế toán, popup Nhập lý do duyệt sẽ hiển thị các cấp duyệt
      //và trong luồng duyệt có 1 cấp cho phép user chọn người duyệt (cho các cấp sau cấp của user đăng nhập và được phép điều chỉnh).
      if (currentLevel.isAccountantApproved) {
        const nextLevels =
          await this.prApprovalFlowRepository.findApprovalLevels(
            purchase_id,
            null,
            Number(currentLevel.level),
          );

        const hasLevelAllowSelect = nextLevels.find(
          (level) => level.allowSelect,
        );
        if (!data.approvalLevelDtos?.length && hasLevelAllowSelect) {
          if (queryRunner.isTransactionActive) {
            await queryRunner.rollbackTransaction();
          }

          return {
            approvalSteps: nextLevels,
            staffApprovalWorkflowId: currentLevel.staffApprovalWorkflowId,
          };
        } else {
          // Check emails in the system
          const emailChecks = data.approvalLevelDtos
            .filter((step) => step.email)
            .map((step) => step.email) as string[];
          await this.staffUsecases.getStaffByEmails(
            {
              emails: emailChecks,
            },
            jwtPayload,
          );

          // Map DTOs to entity instances and save
          data.approvalLevelDtos?.forEach((item) => {
            return {
              ...item,
              purchaseRequestId: purchase.id,
              approveType: ApproveType.PR,
            };
          });

          await queryRunner.manager.save(
            ApprovalLevelEntity,
            data.approvalLevelDtos,
          );

          // Retrieve updated approval levels
          const newApprovalLevels = await queryRunner.manager.find(
            ApprovalLevelEntity,
            {
              where: { purchaseRequestId: purchase.id },
            },
          );

          // Update history with approval status and reason
          const historyUpdates = newApprovalLevels.map((item) => {
            const { id, ...rest } = item;
            return {
              ...rest,
              status:
                item.level === currentLevel.level
                  ? StatusLevel.Approved
                  : item.status,
              reason: item.level === currentLevel.level ? data.reason : null,
            };
          });
          await queryRunner.manager.save(PurchaseRequestEntity, {
            id: purchase.id,
            history: historyUpdates,
          });
        }
      }

      const nextLevel = await this.approveMultipleLevel(
        queryRunner,
        purchase.id,
        currentLevel,
        data.reason,
      );
      // Get next approval level and send mail if required

      if (nextLevel) {
        if (nextLevel?.isSendMail) {
          const mailOptions = {
            from: this.configService.get<string>('MAIL_SMTP_USER'),
            to: nextLevel.email,
            subject: `Phê duyệt PR #${purchase.id}${purchase.businessUnit?.code ? ` - ${purchase.businessUnit.code}` : ''}${purchase.reason ? ` - ${purchase.reason}` : ''}`,
            html: this._emailTemplateService.getApprovalPREmailHtml(
              purchase,
              nextLevel.email,
              `${this.configService.get<string>('STATUS_PR_EMAIL_URL')}?levelId=${nextLevel.id}&prId=${purchase.id}`,
              nextLevel,
            ),
          };
          try {
            if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
              await sendPost(
                QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
                mailOptions,
                { authorization },
              );
            } else {
              await this.transporter.sendMail(mailOptions);
            }
          } catch (e) {}
        }
      } else {
        // Nếu cấp cuối duyệt
        // Sinh 1 dòng ngân sách điều chỉnh tăng (từ ngân sách gốc có Mã ngân sách và thời gian tạo PO nằm trong thời gian hiệu lực ngân sách
        // nếu có nhiều thì lấy dòng có thời gian tạo nhỏ nhất) có giá trị bằng mức vượt với trạng thái Công bố và Ghi chú là “Cấp vượt PR/PO <Mã PR/PO>”.
        // Lưu ý:  Khi PO bị Hủy, sẽ sinh 1 dòng ngân sách điều chỉnh giảm trạng thái Công bố và Ghi chú là “Thu hồi ngân sách cấp vượt từ PR/PO <Mã PR/PO>”
        // const details =
        //   await this._purchaseRequestUsecases.newCalculateRemainingBudget(
        //     (purchase.details || []).map(
        //       (item) => item as PurchaseRequestDetailDto,
        //     ),
        //     authorization,
        //     new Date(purchase.createdAt),
        //     jwtPayload,
        //   );

        const groupDetails = this.groupDetails(
          (purchase.details || []).map(
            (item) => item as PurchaseRequestDetailDto,
          ),
        );
        for (const group of groupDetails) {
          const item = group[0];
          if (item.adjustBudgetId) {
            continue;
          }
          // Ngân sách bị vượt
          const dataBudget = item.matchingBudget;
          if (dataBudget && item.remainingBudget > item.budget) {
            const budgetDataDto: CreateBudgetDto = {
              createType: EBudgetCreateType.INCREASE,
              adjustBudgetId: item.budgetId,
              budgetType: dataBudget.budgetType,
              currencyUnitId: dataBudget.currencyUnitId,
              budgetCodeId: dataBudget.budgetCodeId,
              costcenterSubaccountId: dataBudget.costcenterSubaccountId,
              note: `Cấp vượt PR ${purchase.id}`,
              effectiveStartDate: dataBudget.effectiveStartDate,
              effectiveEndDate: dataBudget.effectiveEndDate,
              totalValue: item.remainingBudget - item.budget,
              isLock: true,
            };

            if (dataBudget.budgetType == EBudgetType.OPEX) {
              const budgetOpexDto: CreateBudgetOpexDto = {
                form: dataBudget.budgetOpex?.form,
                operations: dataBudget.budgetOpex?.operations,
                budgetData: budgetDataDto,
              };

              const budgetOpex = await this.budgetUsecases.createBudgetOpex(
                { ...budgetOpexDto },
                jwtPayload,
              );

              if (!budgetOpex) {
                throw new HttpException(
                  approvalErrorDetails.E_5416(
                    'Tạo ngân sách điều chỉnh thất bại',
                  ),
                  HttpStatus.BAD_REQUEST,
                );
              }

              for (let i = 0; i < purchase.details.length; i++) {
                if (purchase.details[i].budgetCodeId == item.budgetCodeId) {
                  purchase.details[i].budgetId = item.budgetId;
                  purchase.details[i].budget = item.budget;
                  purchase.details[i].remainingBudget = item.remainingBudget;
                  purchase.details[i].adjustBudgetId = budgetOpex?.id;
                }
              }
            } else if (dataBudget.budgetType == EBudgetType.CAPEX) {
              const budgetCapexDto: CreateBudgetCapexDto = {
                useTime: dataBudget.budgetCapex?.useTime,
                startDate: dataBudget.budgetCapex?.startDate,
                expectedAcceptanceTime:
                  dataBudget.budgetCapex?.expectedAcceptanceTime,
                classify: dataBudget.budgetCapex?.classify,
                priority: dataBudget.budgetCapex?.priority,
                investmentPurpose: dataBudget.budgetCapex?.investmentPurpose,
                files: dataBudget.budgetCapex?.files,
                investments: (dataBudget.budgetCapex?.investments || []).map(
                  (item) => {
                    return {
                      ...item,
                      id: undefined,
                      budgetCapexId: undefined,
                    };
                  },
                ),
                budgetData: budgetDataDto,
              };

              const budgetCapex = await this.budgetUsecases.createBudgetCapex(
                budgetCapexDto,
                jwtPayload,
              );

              if (!budgetCapex) {
                throw new HttpException(
                  approvalErrorDetails.E_5416(
                    'Tạo ngân sách điều chỉnh thất bại',
                  ),
                  HttpStatus.BAD_REQUEST,
                );
              }

              for (let i = 0; i < purchase.details.length; i++) {
                if (purchase.details[i].budgetCodeId == item.budgetCodeId) {
                  purchase.details[i].budgetId = item.budgetId;
                  purchase.details[i].budget = item.budget;
                  purchase.details[i].remainingBudget = item.remainingBudget;
                  purchase.details[i].adjustBudgetId = budgetCapex?.id;
                }
              }
            }
          }
        }

        await queryRunner.manager.save(PurchaseRequestEntity, {
          id: purchase.id,
          details: (purchase.details || []).map(
            (item) => item as PurchaseRequestDetailDto,
          ),
        });
        await queryRunner.manager.update(PurchaseRequestEntity, purchase.id, {
          statusPr: Status.Approved,
        });

        // Send mail to purchaser
        if (purchase.purchaserId) {
          try {
            const staff = await this.staffUsecases.getDetailStaff(
              { staffId: purchase?.purchaserId },
              jwtPayload,
            );

            const requesterEmail = staff?.email;

            if (requesterEmail) {
              const mailOptions = {
                from: this.configService.get<string>('MAIL_SMTP_USER'),
                to: requesterEmail,
                subject: `PR ${purchase.id} đã được duyệt${purchase.businessUnit?.code ? ` - ${purchase.businessUnit.code}` : ''}${purchase.reason ? ` - ${purchase.reason}` : ''}`,
                html: this._emailTemplateService.getSendMailRequesterHtml(
                  purchase,
                  requesterEmail,
                  `${this.configService.get<string>('STATUS_PR_EMAIL_URL')}?prId=${purchase.id}`,
                  staff,
                  Status.Approved,
                ),
              };
              if (
                this.configService.get<string>('SEND_MAIL_BY_API') === 'true'
              ) {
                await sendPost(
                  QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
                  mailOptions,
                  { authorization },
                );
              } else {
                await this.transporter.sendMail(mailOptions);
              }
            }
          } catch (error) {
            console.error('Error sending mail to purchaser:', error);
          }
        }
      }

      if (queryRunner.isTransactionActive) {
        await queryRunner.commitTransaction();
      }
    } catch (err) {
      console.error('Error approving lowest level:', err);
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }

      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    } finally {
      if (!queryRunner.isReleased) {
        await queryRunner.release();
      }
    }
  }

  async approvePOLowestLevel(
    data: ApprovePrDto,
    approveType: ApproveType,
    authorization: string,
    jwtPayload: any,
    request: Request,
  ): Promise<
    | void
    | { approvalSteps: ApprovalLevelDto[]; staffApprovalWorkflowId: string }
    | PurchaseOrderDetailModel[]
  > {
    const { levelId: id, purchaseId } = data;
    const queryRunner: QueryRunner =
      request[ENTITY_MANAGER_KEY] ?? this.dataSource.createQueryRunner();

    try {
      if (!queryRunner.isTransactionActive) {
        await queryRunner.connect();
        await queryRunner.startTransaction();
      }

      const [currentLevel, purchase] = await Promise.all([
        this.prApprovalFlowRepository.findOne(id),
        this._purchaseOrderUsecases.findById(purchaseId),
      ]);

      if (!currentLevel)
        throw new HttpException(
          approvalErrorDetails.E_5413(`Không tìm thấy cấp duyệt ${id}`),
          HttpStatus.NOT_FOUND,
        );

      if (jwtPayload.email !== currentLevel.email) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_5333()),
          HttpStatus.FORBIDDEN,
        );
      }

      if (
        purchase.statusPo == Status.Cancel ||
        purchase.statusPo == Status.Closed
      ) {
        throw new HttpException(
          approvalErrorDetails.E_5414('PO đã bị hủy'),
          HttpStatus.BAD_REQUEST,
        );
      }

      // Update history approval status
      const historyWithCurrentLevel = purchase?.history?.find(
        (item) =>
          item.level == currentLevel.level && item.email == currentLevel.email,
      );

      if (historyWithCurrentLevel) {
        await queryRunner.manager.update(
          HistoryApproveEntity,
          historyWithCurrentLevel.id,
          {
            status: StatusLevel.Approved,
            reason: data.reason,
          },
        );
      }

      // Trạng thái hiển thị
      let displayStatusPo: EDisplayStatus = purchase.displayStatusPo;
      purchase.levels = purchase.levels.sort((a, b) => a.level - b.level);
      const findHasAccountantApproved = purchase.levels.filter(
        (item) => item.isAccountantApproved == true,
      );

      if (findHasAccountantApproved?.length > 0) {
        // Có kế toán
        if (currentLevel.level == 1) {
          displayStatusPo = EDisplayStatus.WaitingAccountantProcess;
        } else {
          displayStatusPo = EDisplayStatus.WaitingManagerProcess;
        }
      } else {
        // Không có kế toán
        if (currentLevel.level == 1) {
          displayStatusPo = EDisplayStatus.WaitingManagerProcess;
        }
      }

      await Promise.all([
        // Update purchase request status
        queryRunner.manager.update(PurchaseOrderEntity, purchase.id, {
          statusPo: Status.InProgress,
          displayStatusPo: displayStatusPo,
          isAccountantApproved: purchase.isAccountantApproved
            ? purchase.isAccountantApproved
            : currentLevel.isAccountantApproved,
        }),
        // Update current approval level status
        queryRunner.manager.update(ApprovalLevelEntity, currentLevel.id, {
          status: StatusLevel.Approved,
        }),
      ]);

      // Xử lý approval của cấp Kế toán và kiểm tra các cấp tiếp theo
      if (currentLevel.isAccountantApproved) {
        if (!data.updateApprovedPoDto) {
          if (queryRunner.isTransactionActive) {
            await queryRunner.rollbackTransaction();
          }

          return purchase.details;
        }

        const { budgetCodeId, updateApprovedItemPoDtos } =
          data.updateApprovedPoDto;

        // Kiểm tra và cập nhật mã ngân sách nếu có sự thay đổi
        if (budgetCodeId && budgetCodeId !== purchase.budgetCodeId) {
          await this.budgetCodeUsecases.getBudgetCodeDetail(
            { id: budgetCodeId },
            jwtPayload,
          );

          await queryRunner.manager.save(PurchaseOrderEntity, {
            id: purchase.id,
            budgetCodeId,
          });
        }

        // Chuẩn bị danh sách mã ngân sách cần lấy thông tin
        const budgetCodesToFetch = new Set<string>();
        const itemsToUpdate = [];

        if (updateApprovedItemPoDtos?.length) {
          updateApprovedItemPoDtos.forEach((item) => {
            const existingPoDetail = purchase.details?.find(
              (detail) => detail.id === item.id,
            );
            if (!existingPoDetail) {
              throw new HttpException(
                approvalErrorDetails.E_5419(
                  `Không tìm thấy item ${item.id} trong PO ${purchase.id}`,
                ),
                HttpStatus.BAD_REQUEST,
              );
            }
            // Thêm mã ngân sách vào tập hợp nếu có sự thay đổi
            if (
              item.budgetCodeId &&
              item.budgetCodeId !== existingPoDetail.budgetCodeId
            ) {
              budgetCodesToFetch.add(item.budgetCodeId);
            }

            // Gom dữ liệu để cập nhật
            itemsToUpdate.push({
              id: item.id,
              budgetCodeId: item.budgetCodeId,
              internalOrder: item.internalOrder,
            });
          });

          // Truy vấn thông tin cho tất cả các mã ngân sách cần lấy
          await Promise.all(
            Array.from(budgetCodesToFetch).map((budgetCodeId) =>
              this.budgetCodeUsecases.getBudgetCodeDetail(
                { id: budgetCodeId },
                jwtPayload,
              ),
            ),
          );

          // Cập nhật tất cả các chi tiết PO trong một lần gọi
          await queryRunner.manager.save(
            PurchaseOrderDetailEntity,
            itemsToUpdate,
          );
        }
      }

      // Handle accountant approval and return next levels
      // Nếu cấp duyệt có tích Kế toán, popup Nhập lý do duyệt sẽ hiển thị các cấp duyệt
      // và trong luồng duyệt có 1 cấp cho phép user chọn người duyệt (cho các cấp sau cấp của user đăng nhập và được phép điều chỉnh).
      // Xử lý cấp duyệt của kế toán và kiểm tra các cấp tiếp theo
      if (currentLevel.isAccountantApproved) {
        const nextLevels =
          await this.prApprovalFlowRepository.findApprovalLevels(
            null,
            purchaseId,
            Number(currentLevel.level),
          );
        const hasLevelAllowSelect = nextLevels.some(
          (level) => level.allowSelect,
        );

        if (!data.approvalLevelDtos?.length && hasLevelAllowSelect) {
          if (queryRunner.isTransactionActive) {
            await queryRunner.rollbackTransaction();
          }

          return {
            approvalSteps: nextLevels,
            staffApprovalWorkflowId: currentLevel.staffApprovalWorkflowId,
          };
        }

        // Kiểm tra các email có trong hệ thống
        const emailChecks = [
          ...new Set(
            (data.approvalLevelDtos || [])
              .filter((step) => step.email)
              .map((step) => step.email),
          ),
        ];
        if (emailChecks.length > 0) {
          await this.staffUsecases.getStaffByEmails(
            { emails: emailChecks },
            jwtPayload,
          );
        }

        // Cập nhật thông tin approval level
        const approvalLevelsToSave = (data.approvalLevelDtos || []).map(
          (item) => ({
            ...item,
            purchaseOrderId: purchase.id,
            approveYype: ApproveType.PO,
          }),
        );

        // Lưu các cấp duyệt mới
        await queryRunner.manager.save(
          ApprovalLevelEntity,
          approvalLevelsToSave,
        );

        // Tạo lịch sử cập nhật
        const historyUpdates = approvalLevelsToSave.map((item) => ({
          ...item,
          status:
            item.level === currentLevel.level
              ? StatusLevel.Approved
              : item.status,
          reason: item.level === currentLevel.level ? data.reason : null,
        }));

        // Lưu lịch sử vào đơn hàng
        await queryRunner.manager.save(PurchaseOrderEntity, {
          id: purchase.id,
          history: historyUpdates,
        });
      }

      const nextLevel = await this.approveMultipleLevel(
        queryRunner,
        purchase.id,
        currentLevel,
        data.reason,
      );

      if (nextLevel) {
        if (nextLevel.isSendMail) {
          const mailOptions = {
            from: this.configService.get<string>('MAIL_SMTP_USER'),
            to: nextLevel.email,
            subject: `Phê duyệt PO #${purchase.id}${purchase.businessUnit?.code ? ` - ${purchase.businessUnit.code}` : ''}${purchase.reason ? ` - ${purchase.reason}` : ''}`,
            html: this._emailTemplateService.getApprovalPOEmailHtml(
              purchase,
              nextLevel.email,
              `${this.configService.get<string>('STATUS_PO_EMAIL_URL')}?levelId=${nextLevel.id}&poId=${purchase.id}`,
              nextLevel,
            ),
          };
          try {
            if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
              await sendPost(
                QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
                mailOptions,
                { authorization },
              );
            } else {
              await this.transporter.sendMail(mailOptions);
            }
          } catch (e) {}
        }
      } else {
        // Nếu cấp cuối duyệt
        // Sinh 1 dòng ngân sách điều chỉnh tăng (từ ngân sách gốc có Mã ngân sách và thời gian tạo PO nằm trong thời gian hiệu lực ngân sách
        // nếu có nhiều thì lấy dòng có thời gian tạo nhỏ nhất) có giá trị bằng mức vượt với trạng thái Công bố và Ghi chú là “Cấp vượt PR/PO <Mã PR/PO>”.
        // Lưu ý:  Khi PO bị Hủy, sẽ sinh 1 dòng ngân sách điều chỉnh giảm trạng thái Công bố và Ghi chú là “Thu hồi ngân sách cấp vượt từ PR/PO <Mã PR/PO>”
        // const details =
        //   await this._purchaseOrderUsecases.newCalculateRemainingBudget(
        //     purchase.details.map((item) => item as PurchaseOrderDetailDto),
        //     authorization,
        //     new Date(purchase.createdAt),
        //     purchase.isAccountantApproved == true,
        //     jwtPayload,
        //   );

        const groupDetails = this.groupDetails(
          (purchase.details || []).map(
            (item) => item as PurchaseRequestDetailDto,
          ),
        );
        const updatePromises = [];

        for (const group of groupDetails) {
          const item = group[0];
          if (item.adjustBudgetId) {
            continue;
          }
          // Ngân sách bị vượt
          const dataBudget = item.matchingBudget;
          if (dataBudget && item.remainingBudget > item.budget) {
            const budgetDataDto: CreateBudgetDto = {
              createType: EBudgetCreateType.INCREASE,
              adjustBudgetId: item.budgetId,
              budgetType: dataBudget.budgetType,
              currencyUnitId: dataBudget.currencyUnitId,
              budgetCodeId: dataBudget.budgetCodeId,
              costcenterSubaccountId: dataBudget.costcenterSubaccountId,
              note: `Cấp vượt PO ${purchase.id}`,
              effectiveStartDate: dataBudget.effectiveStartDate,
              effectiveEndDate: dataBudget.effectiveEndDate,
              totalValue: item.remainingBudget - item.budget,
              isLock: true,
            };

            if (dataBudget.budgetType == EBudgetType.OPEX) {
              const budgetOpexDto = {
                form: dataBudget.budgetOpex?.form,
                operations: dataBudget.budgetOpex?.operations,
                budgetData: budgetDataDto,
              };

              updatePromises.push(
                this.budgetUsecases
                  .createBudgetOpex(budgetOpexDto, jwtPayload)
                  .then((budgetOpex) => {
                    if (!budgetOpex) {
                      throw new HttpException(
                        approvalErrorDetails.E_5416(
                          'Tạo ngân sách điều chỉnh thất bại',
                        ),
                        HttpStatus.BAD_REQUEST,
                      );
                    }
                    this.updatePurchaseDetails(
                      purchase.details,
                      item,
                      budgetOpex?.id,
                    );
                  }),
              );
            } else if (dataBudget.budgetType == EBudgetType.CAPEX) {
              const budgetCapexDto: CreateBudgetCapexDto = {
                useTime: dataBudget.budgetCapex?.useTime,
                startDate: dataBudget.budgetCapex?.startDate,
                expectedAcceptanceTime:
                  dataBudget.budgetCapex?.expectedAcceptanceTime,
                classify: dataBudget.budgetCapex?.classify,
                priority: dataBudget.budgetCapex?.priority,
                investmentPurpose: dataBudget.budgetCapex?.investmentPurpose,
                files: dataBudget.budgetCapex?.files,
                investments: (dataBudget.budgetCapex?.investments || []).map(
                  (item) => {
                    return {
                      ...item,
                      id: undefined,
                      budgetCapexId: undefined,
                    };
                  },
                ),
                budgetData: budgetDataDto,
              };

              updatePromises.push(
                await this.budgetUsecases
                  .createBudgetCapex(budgetCapexDto, jwtPayload)
                  .then((budgetCapex) => {
                    if (!budgetCapex) {
                      throw new HttpException(
                        approvalErrorDetails.E_5416(
                          'Tạo ngân sách điều chỉnh thất bại',
                        ),
                        HttpStatus.BAD_REQUEST,
                      );
                    }
                    this.updatePurchaseDetails(
                      purchase.details,
                      item,
                      budgetCapex?.id,
                    );
                  }),
              );
            }
          }
        }

        await Promise.all(updatePromises);

        const updatedPo = {
          id: purchase.id,
          statusPo: Status.Approved,
          displayStatusPo: EDisplayStatus.Approved,
          details: (purchase.details || []).map(
            (item) => item as PurchaseOrderDetailDto,
          ),
          isCreatedSap: false,
          statePo: purchase.statePo,
          isCreatedSolomon: false,
        };

        //Create Sap Po
        try {
          if (purchase.businessUnit?.company?.code != '3600') {
            const sapPoList =
              await this.sapPurchaseOrderUsecases.syncAndSaveSapPurchaseOrder(
                purchase.id,
                authorization,
              );

            if (sapPoList && sapPoList.length) {
              const errorSapPo = sapPoList.filter(
                (item) => item.messageType == 'E',
              );

              const sapPoDetails = sapPoList.map((sapPo) => sapPo.items).flat();

              if (!errorSapPo?.length) {
                updatedPo.statePo = State.Completed;
              } else if (errorSapPo?.length != sapPoList.length) {
                updatedPo.isCreatedSap = true;
                updatedPo.details = updatedPo.details.map((item) => {
                  const sapPoDetail = sapPoDetails.find(
                    (detail) => detail.purchaseOrderDetailId == item.id,
                  );

                  return {
                    ...item,
                    sapCreatedQuantity:
                      sapPoDetail && sapPoDetail.messageType != 'E'
                        ? sapPoDetail.quantity
                        : 0,
                  } as PurchaseOrderDetailDto;
                });
              }
            }
          } else {
            const solomonPo =
              await this.solomonPurchaseOrderUsecases.syncAndSaveSolomonPurchaseOrder(
                purchase.id,
                authorization,
              );

            if (solomonPo?.messageType != 'E') {
              updatedPo.statePo = State.Completed;
              updatedPo.isCreatedSolomon = true;
            }
          }
        } catch (error) {}

        await queryRunner.manager.save(PurchaseOrderEntity, updatedPo as any);

        if (currentLevel.isSendMailCreator) {
          try {
            const staff = await this.staffUsecases.getDetailStaff(
              { staffId: purchase?.requesterId },
              jwtPayload,
            );
            if (staff?.email) {
              const mailOptions = {
                from: this.configService.get<string>('MAIL_SMTP_USER'),
                to: staff?.email,
                subject: `PO ${purchase.id} đã được duyệt${purchase.businessUnit?.code ? ` - ${purchase.businessUnit.code}` : ''}${purchase.reason ? ` - ${purchase.reason}` : ''}`,
                html: this._emailTemplateService.getSendMailRequesterHtml(
                  purchase,
                  staff?.email,
                  `${this.configService.get<string>('STATUS_PO_EMAIL_URL')}?poId=${purchase.id}`,
                  staff,
                  Status.Approved,
                ),
              };
              if (
                this.configService.get<string>('SEND_MAIL_BY_API') === 'true'
              ) {
                await sendPost(
                  QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
                  mailOptions,
                  { authorization },
                );
              } else {
                await this.transporter.sendMail(mailOptions);
              }
            }
          } catch (error) {}
        }
      }

      if (queryRunner.isTransactionActive) {
        await queryRunner.commitTransaction();
      }
    } catch (err) {
      console.error('Error approving lowest level:', err);
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }

      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    } finally {
      if (!queryRunner.isReleased) {
        await queryRunner.release();
      }
    }
  }

  private updatePurchaseDetails(details, item, adjustBudgetId) {
    for (let i = 0; i < details.length; i++) {
      if (details[i].budgetCodeId === item.budgetCodeId) {
        details[i].budgetId = item.budgetId;
        details[i].budget = item.budget;
        details[i].remainingBudget = item.remainingBudget;
        details[i].adjustBudgetId = adjustBudgetId;
      }
    }
  }

  async declinePRLowestLevel(
    data: RejectPrDto,
    authorization: string,
    jwtPayload: any,
  ): Promise<void> {
    const id = data.levelId;
    const purchase_id = data.purchaseId;

    const currentLevel = await this.prApprovalFlowRepository.findOne(id);
    const purchase = await this.purchaseRequestRepository.findOne(purchase_id);

    if (
      purchase.statusPr == Status.Cancel ||
      purchase.statusPr == Status.Closed
    ) {
      throw new HttpException(
        approvalErrorDetails.E_5417('PR đã bị hủy'),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!currentLevel)
      throw new HttpException(
        approvalErrorDetails.E_5413(`Không tìm thấy cấp duyệt ${id}`),
        HttpStatus.NOT_FOUND,
      );

    purchase.history.map(async (item) => {
      if (item.userId === currentLevel.userId) {
        await this.prApprovalFlowRepository.changeStatusHistory(
          { status: StatusLevel.Rejected, reason: data.reason },
          item.id,
        );
      }
    });

    // const purchaseDetailAdjustBudgetIds = [...new Set(purchase.details.map((item) => item.adjust_budget_id).filter(Boolean))];

    // if (purchaseDetailAdjustBudgetIds.length) {
    //   await this.createAdjustBudgetDecrease(purchase_id, purchaseDetailAdjustBudgetIds, authorization, true);
    // }

    await this.purchaseRequestRepository.updateStatus(
      purchase.id,
      Status.Rejected,
    );

    if (currentLevel.isSendMailCreator) {
      try {
        const staff = await this.staffUsecases.getDetailStaff(
          { staffId: purchase?.requesterId },
          jwtPayload,
        );
        if (staff?.email) {
          const mailOptions = {
            from: this.configService.get<string>('MAIL_SMTP_USER'),
            to: staff?.email,
            subject: `PR ${purchase.id} đã bị từ chối${purchase.businessUnit?.code ? ` - ${purchase.businessUnit.code}` : ''}${purchase.reason ? ` - ${purchase.reason}` : ''}`,
            html: this._emailTemplateService.getSendMailRequesterHtml(
              purchase,
              staff?.email,
              `${this.configService.get<string>('STATUS_PR_EMAIL_URL')}?levelId=${currentLevel.id}&prId=${purchase.id}`,
              staff,
              Status.Rejected,
            ),
          };
          if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
            await sendPost(
              QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
              mailOptions,
              { authorization },
            );
          } else {
            await this.transporter.sendMail(mailOptions);
          }
        }
      } catch (error) {}
    }
  }

  async declinePOLowestLevel(
    data: RejectPrDto,
    authorization: string,
    jwtPayload: any,
  ): Promise<void> {
    const id = data.levelId;
    const purchase_id = data.purchaseId;

    const currentLevel = await this.prApprovalFlowRepository.findOne(id);
    const purchase = await this.purchaseOrderRepository.findOne(purchase_id);

    if (
      purchase.statusPo == Status.Cancel ||
      purchase.statusPo == Status.Closed
    ) {
      throw new HttpException(
        approvalErrorDetails.E_5417('PO đã bị hủy'),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!currentLevel)
      throw new HttpException(
        approvalErrorDetails.E_5413(`Không tìm thấy cấp duyệt ${id}`),
        HttpStatus.NOT_FOUND,
      );

    purchase.history.map(async (item) => {
      if (item.userId === currentLevel.userId) {
        await this.prApprovalFlowRepository.changeStatusHistory(
          { status: StatusLevel.Rejected, reason: data.reason },
          item.id,
        );
      }
    });

    // const purchaseDetailAdjustBudgetIds = [...new Set(purchase.details.map((item) => item.adjust_budget_id).filter(Boolean))];

    // if (purchaseDetailAdjustBudgetIds.length) {
    //   await this.createAdjustBudgetDecrease(purchase_id, purchaseDetailAdjustBudgetIds, authorization, false);
    // }

    await this.purchaseOrderRepository.updateStatus(
      purchase.id,
      Status.Rejected,
      EDisplayStatus.Rejected,
    );

    if (currentLevel.isSendMailCreator) {
      try {
        const staff = await this.staffUsecases.getDetailStaff(
          { staffId: purchase?.requesterId },
          jwtPayload,
        );
        if (staff?.email) {
          const mailOptions = {
            from: this.configService.get<string>('MAIL_SMTP_USER'),
            to: staff?.email,
            subject: `PO ${purchase.id} đã bị từ chối${purchase.businessUnit?.code ? ` - ${purchase.businessUnit.code}` : ''}${purchase.reason ? ` - ${purchase.reason}` : ''}`,
            html: this._emailTemplateService.getSendMailRequesterHtml(
              purchase,
              staff?.email,
              `${this.configService.get<string>('STATUS_PO_EMAIL_URL')}?levelId=${currentLevel.id}&poId=${purchase.id}`,
              staff,
              Status.Rejected,
            ),
          };
          if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
            await sendPost(
              QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
              mailOptions,
              { authorization },
            );
          } else {
            await this.transporter.sendMail(mailOptions);
          }
        }
      } catch (error) {}
    }
  }

  async isApprovePR(id: number, purchaseId: number): Promise<string | null> {
    const currentLevel = await this.prApprovalFlowRepository.findOne(id);
    const purchase = await this.purchaseRequestRepository.findOne(purchaseId);

    for (const item of purchase.history) {
      if (item.userId === currentLevel.userId) {
        if (item.status === StatusLevel.Approved) return StatusLevel.Approved;
        if (item.status === StatusLevel.Rejected) return StatusLevel.Rejected;
      }
    }

    if (purchase.statusPr === Status.Draft) return Status.Draft;

    return null; // If no item is Approved or Rejected, return null
  }

  async isApprovePO(id: number, purchase_id: number): Promise<string | null> {
    const currentLevel = await this.prApprovalFlowRepository.findOne(id);
    const purchase = await this.purchaseOrderRepository.findOne(purchase_id);

    for (const item of purchase.history) {
      if (item.userId === currentLevel.userId) {
        if (item.status === StatusLevel.Approved) return StatusLevel.Approved;
        if (item.status === StatusLevel.Rejected) return StatusLevel.Rejected;
      }
    }

    if (purchase.statusPo === Status.Draft) return Status.Draft;

    return null; // If no item is Approved or Rejected, return null
  }

  async verifyLevel(id: number): Promise<any> {
    return await this.prApprovalFlowRepository.findOne(id);
  }

  async createApprovalLevels(
    approvalLevelDtos: ApprovalLevelDto[],
  ): Promise<any> {
    return await this.prApprovalFlowRepository.createApprovalLevels(
      approvalLevelDtos,
    );
  }

  async findApprovalLevels(
    prId: number,
    poId: number,
  ): Promise<ApprovalLevelModel[]> {
    return await this.prApprovalFlowRepository.findApprovalLevels(prId, poId);
  }

  async deleteApprovalLevels(prId: number, poId: number): Promise<void> {
    await this.prApprovalFlowRepository.deleteApprovalLevels(prId, poId);
  }

  private groupDetails(
    items: PurchaseRequestDetailDto[] | PurchaseOrderDetailDto[],
  ): (PurchaseRequestDetailDto | PurchaseOrderDetailDto)[][] {
    const groupedBudgets: (
      | PurchaseRequestDetailDto
      | PurchaseOrderDetailDto
    )[][] = [];

    items.forEach((item) => {
      // Tìm trong danh sách các nhóm đã tồn tại
      const existingGroup = groupedBudgets.find(
        (group) =>
          group[0].budgetCodeId === item.budgetCodeId &&
          group[0].costCenterId === item.costCenterId,
      );

      if (existingGroup) {
        // Nếu đã có nhóm này, thêm object vào nhóm
        existingGroup.push(item);
      } else {
        // Nếu chưa có, tạo nhóm mới và thêm vào danh sách
        groupedBudgets.push([item]);
      }
    });

    return groupedBudgets;
  }

  private async createAdjustBudgetDecrease(
    id: number,
    adjustBudgetIds: string[],
    authorization: any,
    isPr: boolean,
    jwtPayload: any,
  ) {
    // Nếu cấp cuối duyệt
    // Sinh 1 dòng ngân sách điều chỉnh tăng (từ ngân sách gốc có Mã ngân sách và thời gian tạo PO nằm trong thời gian hiệu lực ngân sách
    // nếu có nhiều thì lấy dòng có thời gian tạo nhỏ nhất) có giá trị bằng mức vượt với trạng thái Công bố và Ghi chú là “Cấp vượt PR/PO <Mã PR/PO>”.
    // Lưu ý:  Khi PO bị Hủy, sẽ sinh 1 dòng ngân sách điều chỉnh giảm trạng thái Công bố và Ghi chú là “Thu hồi ngân sách cấp vượt từ PR/PO <Mã PR/PO>”
    const urlFindBudgetCapexes = `${this.configService.get<string>('API_ENDPOINT')}/budget/list`;
    const urlFindBudgetOpexes = `${this.configService.get<string>('API_ENDPOINT')}/budget/list`;
    const headers = { Authorization: authorization };

    const allBudgets = [];
    if (adjustBudgetIds && adjustBudgetIds.length) {
      const [opexApiResponse, capexApiResponse] = await Promise.all([
        this.budgetUsecases.getBudgetList(
          {
            ids: adjustBudgetIds,
            budgetType: EBudgetType.OPEX,
            limit: 5,
            page: 1,
            getAll: 1,
            searchString: '',
          },
          jwtPayload,
        ),
        this.budgetUsecases.getBudgetList(
          {
            ids: adjustBudgetIds,
            budgetType: EBudgetType.CAPEX,
            limit: 5,
            page: 1,
            getAll: 1,
            searchString: '',
          },
          jwtPayload,
        ),
      ]);

      const dataOpexes = opexApiResponse?.results || [];
      const dataCapexes = capexApiResponse?.results || [];
      allBudgets.push(...dataOpexes, ...dataCapexes);
    }

    for (const adjustBudgetId of adjustBudgetIds) {
      const adjustBudget = allBudgets.find((item) => item.id == adjustBudgetId);
      if (adjustBudget) {
        const budgetDataDto: CreateBudgetDto = {
          createType: EBudgetCreateType.DECREASE,
          adjustBudgetId: adjustBudget.adjustBudgetId,
          budgetType: adjustBudget.budgetType,
          currencyUnitId: adjustBudget.currencyUnitId,
          budgetCodeId: adjustBudget.budgetCodeId,
          costcenterSubaccountId: adjustBudget.costcenterSubaccountId,
          note: `Thu hồi ngân sách cấp vượt từ ${isPr ? 'PR' : 'PO'} ${id}`,
          effectiveStartDate: adjustBudget.effectiveStartDate,
          effectiveEndDate: adjustBudget.effectiveEndDate,
          totalValue: adjustBudget.totalValue,
          isLock: true,
        };

        if (adjustBudget.budgetType == EBudgetType.OPEX) {
          const budgetOpexDto: CreateBudgetOpexDto = {
            form: adjustBudget.budgetOpex?.form,
            operations: adjustBudget.budgetOpex?.operations,
            budgetData: budgetDataDto,
          };

          const budgetOpex = await this.budgetUsecases.createBudgetOpex(
            budgetOpexDto,
            jwtPayload,
          );

          if (!budgetOpex) {
            throw new HttpException(
              approvalErrorDetails.E_5416('Tạo ngân sách điều chỉnh thất bại'),
              HttpStatus.BAD_REQUEST,
            );
          }
        } else if (adjustBudget.budgetType == EBudgetType.CAPEX) {
          const budgetCapexDto: CreateBudgetCapexDto = {
            useTime: adjustBudget.budgetCapex?.useTime,
            startDate: adjustBudget.budgetCapex?.startDate,
            expectedAcceptanceTime:
              adjustBudget.budgetCapex?.expectedAcceptanceTime,
            classify: adjustBudget.budgetCapex?.classify,
            priority: adjustBudget.budgetCapex?.priority,
            investmentPurpose: adjustBudget.budgetCapex?.investmentPurpose,
            files: adjustBudget.budgetCapex?.files,
            investments: (adjustBudget.budgetCapex?.investments || []).map(
              (item) => {
                return {
                  ...item,
                  id: undefined,
                  budgetCapexId: undefined,
                };
              },
            ),
            budgetData: budgetDataDto,
          };

          const budgetCapex = await this.budgetUsecases.createBudgetCapex(
            budgetCapexDto,
            jwtPayload,
          );

          if (!budgetCapex) {
            throw new HttpException(
              approvalErrorDetails.E_5416('Tạo ngân sách điều chỉnh thất bại'),
              HttpStatus.BAD_REQUEST,
            );
          }
        }
      }
    }
  }

  async cancelPRPo(
    data: CancelPrPoDto,
    authorization: string,
    isPr: boolean,
    jwtPayload: any,
    status: Status,
  ): Promise<void> {
    const purchaseId = data.purchaseId;

    let purchase: PurchaseRequestModel | PurchaseOrderModel;
    if (isPr) {
      purchase = await this.purchaseRequestRepository.findOne(purchaseId);

      const prs = await this.purchaseRequestRepository.findPrWithPo([
        purchaseId,
      ]);
      const pr = prs[0];
      if (status == Status.Cancel) {
        const prDetailsHasPoDetails = (pr?.details || []).filter(
          (detail) =>
            (detail.poDetails || []).filter((item) => item.purchaseOrder)
              .length,
        ).length;
        if (prDetailsHasPoDetails > 0) {
          throw new HttpException(
            approvalErrorDetails.E_5415(
              `Đơn đề nghị mua sắm đã được tạo. Đơn mua hàng, không thể ${status == Status.Cancel ? 'hủy' : 'đóng'}!`,
            ),
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        if (purchase.statePr == State.Completed) {
          throw new HttpException(
            approvalErrorDetails.E_5415(
              `Đơn đề nghị mua sắm đã được hoàn thành, không thể đóng!`,
            ),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      (purchase?.history || []).push({
        name: `${jwtPayload?.firstName} ${jwtPayload?.lastName}`.trim(),
        userId: jwtPayload?.userId,
        status: status,
        role: status == Status.Cancel ? 'Người hủy đơn' : 'Người đóng đơn',
        reason: data.reason,
      });

      await this.purchaseRequestRepository.updatePurchaseRequest(purchaseId, {
        history: purchase?.history || [],
      });

      await this.purchaseRequestRepository.updateStatus(purchase.id, status);
    } else {
      purchase = await this.purchaseOrderRepository.findOne(purchaseId);

      // if (purchase.is_created_sap) {
      //   throw new HttpException(approvalErrorDetails.E_5415(`Đơn mua hàng đã được đặt, không thể ${status == Status.Cancel ? 'hủy' : 'đóng'}!`), HttpStatus.BAD_REQUEST);
      // }

      (purchase?.history || []).push({
        name: `${jwtPayload?.firstName} ${jwtPayload?.lastName}`.trim(),
        userId: jwtPayload?.userId,
        status: status,
        role: status == Status.Cancel ? 'Người hủy đơn' : 'Người đóng đơn',
        reason: data.reason,
      });

      await this.purchaseOrderRepository.updatePurchaseOrder(purchaseId, {
        history: purchase?.history || [],
      });

      await this.purchaseOrderRepository.updateStatus(
        purchase.id,
        status,
        status == Status.Cancel ? EDisplayStatus.Cancel : EDisplayStatus.Closed,
      );

      if (status == Status.Cancel) {
        const prIds = (purchase?.details || [])
          .map((detail) => detail.prReference)
          .filter(Boolean);
        await this._purchaseRequestUsecases.changeStatePr(prIds);
      }

      await this.sapPurchaseOrderUsecases.cancelOrDeletePurchaseOrderSap(
        purchase.id,
        purchase.sapPos,
        jwtPayload,
      );
    }

    const purchaseDetailAdjustBudgetIds = [
      ...new Set(
        (purchase?.details || [])
          .map((item) => item.adjustBudgetId)
          .filter(Boolean),
      ),
    ] as string[];

    if (purchaseDetailAdjustBudgetIds.length) {
      await this.createAdjustBudgetDecrease(
        purchaseId,
        purchaseDetailAdjustBudgetIds,
        authorization,
        true,
        jwtPayload,
      );
    }

    try {
      const staff = await this.staffUsecases.getDetailStaff(
        { staffId: purchase?.requesterId },
        jwtPayload,
      );
      if (staff?.email) {
        const mailOptions = {
          from: this.configService.get<string>('MAIL_SMTP_USER'),
          to: staff?.email,
          subject: `${isPr ? 'PR' : 'PO'} ${purchaseId} ${status == Status.Cancel ? 'đã bị hủy' : 'đã bị đóng'}${purchase.businessUnit?.code ? ` - ${purchase.businessUnit.code}` : ''}${purchase.reason ? ` - ${purchase.reason}` : ''}`,
          html: this._emailTemplateService.getSendMailRequesterHtml(
            purchase,
            staff?.email,
            isPr
              ? `${this.configService.get<string>('STATUS_PR_EMAIL_URL')}?prId=${purchase.id}`
              : `${this.configService.get<string>('STATUS_PO_EMAIL_URL')}?poId=${purchase.id}`,
            staff,
            status,
          ),
        };
        if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
          await sendPost(
            QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
            mailOptions,
            { authorization },
          );
        } else {
          await this.transporter.sendMail(mailOptions);
        }
      }
    } catch (error) {}
  }

  private async approveMultipleLevel(
    queryRunner: QueryRunner,
    id: number,
    currentLevel: ApprovalLevelModel,
    reason: string,
  ): Promise<ApprovalLevelEntity | null> {
    const nextLevelQueryBuilder = await queryRunner.manager
      .createQueryBuilder(ApprovalLevelEntity, 'approvalLevels')
      .andWhere('approvalLevels.level > :currentLevel', {
        currentLevel: Number(currentLevel.level),
      });

    const nextHistorieQueryBuilder = await queryRunner.manager
      .createQueryBuilder(HistoryApproveEntity, 'histories')
      .leftJoin('histories.purchaseRequest', 'purchaseRequest')
      .leftJoin('histories.purchaseOrder', 'purchaseOrder');

    if (currentLevel.approveType == ApproveType.PR) {
      nextLevelQueryBuilder.andWhere('approvalLevels.purchaseRequestId = :id', {
        id: id,
      });

      nextHistorieQueryBuilder.andWhere('purchaseRequest.id = :id', {
        id: id,
      });
    }

    if (currentLevel.approveType == ApproveType.PO) {
      nextLevelQueryBuilder.andWhere('approvalLevels.purchaseOrderId = :id', {
        id: id,
      });

      nextHistorieQueryBuilder.andWhere('purchaseOrder.id = :id', {
        id: id,
      });
    }

    const nextLevels = currentLevel.approveType
      ? await nextLevelQueryBuilder.getMany()
      : [];

    const nextHistories = currentLevel.approveType
      ? await nextHistorieQueryBuilder.getMany()
      : [];

    nextLevels.sort((a, b) => a.level - b.level);

    const updateMultipleLevels: ApprovalLevelEntity[] = [];
    const updateMultipleHistories: HistoryApproveEntity[] = [];
    let lastLevel = currentLevel.level;
    ///Tìm những level liền kề có cùng email với cấp hiện tại
    for (let i = 0; i < nextLevels.length; i++) {
      if (nextLevels[i].email == currentLevel.email) {
        updateMultipleLevels.push(nextLevels[i]);
        const history = (nextHistories || []).find(
          (item) =>
            item.email == nextLevels[i].email &&
            item.level == nextLevels[i].level,
        );
        if (history) {
          updateMultipleHistories.push(history);
        }
        lastLevel = nextLevels[i].level;
      } else {
        break;
      }
    }

    ///Update multiple levels
    if (updateMultipleLevels.length) {
      await queryRunner.manager
        .createQueryBuilder()
        .update(ApprovalLevelEntity)
        .set({ status: StatusLevel.Approved })
        .where('id IN (:...ids)', {
          ids: updateMultipleLevels
            ?.map((item) => item.id)
            ?.filter(Boolean) || [null],
        })
        .execute();
    }

    ///Update multiple histories
    if (updateMultipleHistories.length) {
      await queryRunner.manager
        .createQueryBuilder()
        .update(HistoryApproveEntity)
        .set({ status: StatusLevel.Approved, reason: reason })
        .where('id IN (:...ids)', {
          ids: updateMultipleHistories
            ?.map((item) => item.id)
            ?.filter(Boolean) || [null],
        })
        .execute();
    }

    const nextLevel = nextLevels.find((item) => item.level == lastLevel + 1);
    return nextLevel;
  }
}
