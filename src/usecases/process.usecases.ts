import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CreateProcessDto } from '../controller/process/dtos/create-process.dto';
import { GetProcessListDto } from '../controller/process/dtos/get-process-list.dto';
import { UpdateParentProcessDto } from '../controller/process/dtos/update-parent-process.dto';
import { UpdateProcessDto } from '../controller/process/dtos/update-process.dto';
import { EConditionType } from '../domain/config/enums/condition-type.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { processErrorDetails } from '../domain/messages/error-details/process';
import { ProcessModel } from '../domain/model/process.model';
import { IProcessRepository } from '../domain/repositories/process.repository';
import { uidToPath } from '../utils/common';
import { ApprovalWorkflowUsecases } from './approval-workflow.usecases';
import { ConditionUsecases } from './condition.usecase';
import { ProcessConditionUsecases } from './process-condition.usecases';
import { StaffApprovalWorkflowUsecases } from './staff-approval-workflow.usecases';

@Injectable()
export class ProcessUsecases {
  constructor(
    private processConditionUsecases: ProcessConditionUsecases,
    private conditionUsecases: ConditionUsecases,
    private approvalWorkflowUsecases: ApprovalWorkflowUsecases,
    private staffApprovalWorkflowUsecases: StaffApprovalWorkflowUsecases,
    @Inject(IProcessRepository)
    private readonly processRepository: IProcessRepository,
  ) {}

  async createProcess(
    data: CreateProcessDto,
    jwtPayload: any,
  ): Promise<ProcessModel> {
    data.createdBy = {
      id: jwtPayload?.userId,
      firstName: jwtPayload?.firstName,
      lastName: jwtPayload?.lastName,
      email: jwtPayload?.email,
      phone: jwtPayload?.phone,
      staffId: jwtPayload?.staffId,
      staffCode: jwtPayload?.staffCode,
    };

    const process = await this.processRepository.createProcess(data);

    for (const createConditionDto of data.createConditionDtos) {
      const condition = await this.conditionUsecases.createCondition(
        {
          ...createConditionDto,
          createdBy: {
            id: jwtPayload?.userId,
            firstName: jwtPayload?.firstName,
            lastName: jwtPayload?.lastName,
            email: jwtPayload?.email,
            phone: jwtPayload?.phone,
            staffId: jwtPayload?.staffId,
            staffCode: jwtPayload?.staffCode,
          },
        },
        jwtPayload,
      );

      await this.processConditionUsecases.createProcessCondition({
        conditionId: condition.id,
        processId: process.id,
      });
    }

    return process;
  }

  async updateProcess(
    id: string,
    data: UpdateProcessDto,
    jwtPayload: any,
  ): Promise<void> {
    data.updatedBy = {
      id: jwtPayload?.userId,
      firstName: jwtPayload?.firstName,
      lastName: jwtPayload?.lastName,
      email: jwtPayload?.email,
      phone: jwtPayload?.phone,
      staffId: jwtPayload?.staffId,
      staffCode: jwtPayload?.staffCode,
    };

    const oldProcess = await this.processRepository.getProcessById(id);

    const newProcess = Object.assign(oldProcess, data);

    if (data.parentId) {
      const parentProcess = await this.processRepository.getProcessById(
        data.parentId,
      );

      newProcess.path = `${parentProcess.path || ''}.${uidToPath(id)}`;
    }

    await this.processRepository.updateProcess(id, newProcess);

    if (data.createConditionDtos?.length) {
      await this.processConditionUsecases.deleteProcessConditionByProcessId(id);

      for (const createConditionDto of data.createConditionDtos) {
        const condition = await this.conditionUsecases.createCondition(
          {
            ...createConditionDto,
            createdBy: {
              id: jwtPayload?.userId,
              firstName: jwtPayload?.firstName,
              lastName: jwtPayload?.lastName,
              email: jwtPayload?.email,
              phone: jwtPayload?.phone,
              staffId: jwtPayload?.staffId,
              staffCode: jwtPayload?.staffCode,
            },
          },
          jwtPayload,
        );

        await this.processConditionUsecases.createProcessCondition({
          conditionId: condition.id,
          processId: id,
        });
      }
    }

    // return await this.getDetailProcess(id);
  }

  async getDetailProcessGraph(id: string): Promise<ProcessModel> {
    await this.checkProcessById(id);

    let dataCondition = await this.processRepository.getDetailProcessGraph(id);

    if (dataCondition?.length) {
      dataCondition = this.buildProcessTree(dataCondition);
    }

    return dataCondition;
  }
  //List phẳng
  async getDetailProcess(id: string): Promise<ProcessModel> {
    const process = await this.processRepository.getDetailProcessById(id);

    if (!process) {
      throw new HttpException(
        processErrorDetails.E_3910(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return await this.processRepository.getDetailProcessGraph(id);
  }

  async getDetailNodeProcess(id: string): Promise<any> {
    return await this.checkProcessById(id);
  }

  async getConditionsByProcessId(processId: string) {
    return await this.conditionUsecases.getConditionByProcessId(processId);
  }

  async getProcessList(
    conditions: GetProcessListDto,
  ): Promise<ResponseDto<ProcessModel>> {
    return await this.processRepository.getProcessList(conditions);
  }

  private buildProcessTree(data: ProcessModel[]) {
    const tree = [];
    const lookup = {};

    // Tạo lookup table để truy cập nhanh
    data.forEach((node) => {
      lookup[node.id] = {
        ...node,
        nlevel: node.path.split('.').length,
        conditionDetails:
          node.processConditions[0]?.condition?.conditionDetails || [],
        // sectors: node.processConditions[0]?.condition?.conditionDetails
        //   ?.filter((item) => item.type == EConditionType.SECTOR)
        //   ?.map((item) => item.sectors),
        // companies: node.processConditions[0]?.condition?.conditionDetails
        //   ?.filter((item) => item.type == EConditionType.COMPANY)
        //   ?.map((item) => item.companies),
        // businessUnits: node.processConditions[0]?.condition?.conditionDetails
        //   ?.filter((item) => item.type == EConditionType.BUSINESS_UNIT)
        //   ?.map((item) => item.businessUnits),
        // departments: node.processConditions[0]?.condition?.conditionDetails
        //   ?.filter((item) => item.type == EConditionType.DEPARTMENT)
        //   ?.map((item) => item.departments),
        // costCenters: node.processConditions[0]?.condition?.conditionDetails
        //   ?.filter((item) => item.type == EConditionType.COST_CENTER)
        //   ?.map((item) => item.costCenters),
        // budgetCodes: node.processConditions[0]?.condition?.conditionDetails
        //   ?.filter((item) => item.type == EConditionType.COST_CENTER)
        //   ?.map((item) => item.budgetCodes),
        // prTypes: node.processConditions[0]?.condition?.conditionDetails
        //   ?.filter((item) => item.type == EConditionType.COST_CENTER)
        //   ?.map((item) => item.prTypes),
        // poTypes: node.processConditions[0]?.condition?.conditionDetails
        //   ?.filter((item) => item.type == EConditionType.COST_CENTER)
        //   ?.map((item) => item.poTypes),
        children: [],
      };
      // delete node.processConditions[0];
    });
    //
    // Duyệt qua từng node và gán con vào parent
    data.forEach((node) => {
      if (node.parentId) {
        // Nếu node có parentId thì thêm nó vào children của parent
        lookup[node.parentId]?.children?.push(lookup[node.id]);
      } else {
        // Nếu không có parentId thì đây là node root
        tree.push(lookup[node.id]);
      }
    });

    return tree;
  }

  async deleteProcess(id: string): Promise<void> {
    await this.checkProcessById(id);

    const processes = await this.processRepository.getDetailProcessGraph(id);

    if (processes?.length) {
      const maxNLevel = Math.max(...processes.map((process) => process.nlevel));
      for (let i = maxNLevel; i > 0; i--) {
        const nlevelProcesses = processes.filter(
          (process) => process.nlevel === i,
        );

        const nlevelProcessIds = nlevelProcesses.map((process) => process.id);

        await this.processRepository.deleteProcess(nlevelProcessIds);
      }
    }
  }

  async checkProcessById(id: string): Promise<any> {
    const process = await this.processRepository.getProcessById(id);

    if (!process) {
      throw new HttpException(
        processErrorDetails.E_3910(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return this.toProcessDetailModel(process);
  }

  private toProcessDetailModel(process: ProcessModel) {
    for (const processCondition of process.processConditions) {
      processCondition.conditionDetails =
        processCondition.condition?.conditionDetails?.map((conditionDetail) => {
          switch (conditionDetail.type) {
            case EConditionType.SECTOR: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                sectors: conditionDetail.sectors,
              };
            }
            case EConditionType.COMPANY: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                companies: conditionDetail.companies,
              };
            }
            case EConditionType.BUSINESS_UNIT: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                businessUnits: conditionDetail.businessUnits,
              };
            }
            case EConditionType.DEPARTMENT: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                departments: conditionDetail.departments,
              };
            }
            case EConditionType.COST_CENTER: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                costCenters: conditionDetail.costCenters,
              };
            }
            case EConditionType.BUDGET_CODE: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                budgetCodes: conditionDetail.budgetCodes,
              };
            }
            case EConditionType.PR_TYPE: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                prTypes: conditionDetail.prTypes,
              };
            }
            case EConditionType.PO_TYPE: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                poTypes: conditionDetail.poTypes,
              };
            }
            case EConditionType.VALUE_PR: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                valuePR: conditionDetail.valuePR,
              };
            }
            case EConditionType.VALUE_PO: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                valuePO: conditionDetail.valuePO,
              };
            }
            case EConditionType.VALUE_BUDGET: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                valueBudget: conditionDetail.valueBudget,
              };
            }
            case EConditionType.BUDGET_OVERRUN: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                budgetOverrun: conditionDetail.budgetOverrun,
              };
            }
            case EConditionType.CHECK_BUDGET: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
              };
            }
            case EConditionType.PROCESS_TYPE: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                processTypes: conditionDetail.processTypes,
              };
            }
            case EConditionType.PLANT: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                plants: conditionDetail.plants,
              };
            }
            case EConditionType.BUDGET_OVERRUN_RATE: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                budgetOverrunRate: conditionDetail.budgetOverrunRate,
              };
            }
            case EConditionType.DIFFERENCE_AMOUNT: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                differenceAmount: conditionDetail.differenceAmount,
              };
            }
            case EConditionType.FUNCTION_UNIT: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                functionUnits: conditionDetail.functionUnits,
              };
            }
            case EConditionType.DIFFERENCE_AMOUNT_ALL_ITEMS: {
              return {
                id: conditionDetail.id,
                type: conditionDetail.type,
                comparisonType: conditionDetail.comparisonType,
                differenceAmountAllItems:
                  conditionDetail.differenceAmountAllItems,
              };
            }
          }

          return conditionDetail;
        });

      delete processCondition.condition;
    }

    return {
      id: process.id,
      name: process.name,
      parentId: process.parentId,
      path: process.path,
      description: process.description,
      processConditions: process.processConditions,
    };
  }

  async updateParentProcess(
    id: string,
    data: UpdateParentProcessDto,
    jwtPayload: any,
  ) {
    await this.checkProcessById(id);

    await this.processRepository.updateProcess(id, {
      name: data.name,
      description: data.description,
      status: data.status,
    });

    // if (data.createApprovalWorkflowDtos?.length) {
    const approvalWorkflows =
      await this.approvalWorkflowUsecases.getApprovalWorkflowByParentProcessId(
        id,
      );

    const approvalWorkflowIds = approvalWorkflows.map(
      (approvalWorkflow) => approvalWorkflow.id,
    );

    if (approvalWorkflowIds?.length) {
      await this.staffApprovalWorkflowUsecases.deleteStaffApprovalWorkflowByApprovalWorkflowIds(
        approvalWorkflowIds,
      );
    }

    await this.approvalWorkflowUsecases.deleteApprovalWorkflow(id);

    for (const createApprovalWorkflowDto of data.createApprovalWorkflowDtos) {
      createApprovalWorkflowDto.parentProcessId = id;

      const processes = await this.getProcessByIds(
        createApprovalWorkflowDto.processIds,
      );

      createApprovalWorkflowDto.processes = processes;

      await this.approvalWorkflowUsecases.createApprovalWorkflow(
        createApprovalWorkflowDto,
        jwtPayload,
      );
    }
    // }

    return await this.getDetailParentProcess(id);
  }

  async getProcessByIds(ids: string[]): Promise<ProcessModel[]> {
    return await this.processRepository.getProcessByIds(ids);
  }

  async getDetailParentProcess(id: string): Promise<ProcessModel> {
    return await this.processRepository.getDetailParentProcess(id);
  }
}
