import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CreateReceiptSolomonDto } from '../controller/receipt-solomon/dtos/create-receipt-solomon.dto';
import { ReceiptSolomonModel } from '../domain/model/receipt-solomon.model';
import { IReceiptSolomonRepository } from '../domain/repositories/receipt-solomon.repository';
import { BudgetCodeUsecases } from './budget-code.usecases';
import { purchaseOrderUsecases } from './purchase_order.usecases';
import { receiptSolomonErrorDetails } from '../domain/messages/error-details/7050-receipt-solomon';
import { ApiLogUsecases } from './api-log.usecase';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { CreateApiLogDto } from '../controller/api-log/dtos/create-api-log.dto';
import { QueryFailedError } from 'typeorm';
import { AxiosError } from 'axios';
import { sendPost } from '../utils/http';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { EnvironmentConfigService } from '../infrastructure/config/environment-config/environment-config.service';
import { GetListReceiptDto } from '../controller/receipt-solomon/dtos/get-list-receipt.dto';
import { GetUuidDto } from '../domain/dtos/get-uuid.dto';
import { codeToIdMap, processInBatches } from '../utils/common';

@Injectable()
export class ReceiptSolomonUsecases {
  constructor(
    @Inject(IReceiptSolomonRepository)
    private readonly receiptSolomonRepository: IReceiptSolomonRepository,
    private readonly _purchaseOrderUsecases: purchaseOrderUsecases,
    private readonly budgetCodeUsescases: BudgetCodeUsecases,
    private apiLogUsecases: ApiLogUsecases,
  ) {}

  async createReceipt(
    receipts: CreateReceiptSolomonDto[],
  ): Promise<CreateReceiptSolomonDto[]> {
    receipts = receipts.map((item) => {
      return {
        ...item,
        budgetCodeCode: item.budgetCode,
      };
    });

    const poIds = [
      ...new Set(receipts.map((item) => item.poId).filter(Boolean)),
    ];

    const budgetCodeCodes = [
      ...new Set(receipts.map((item) => item.budgetCodeCode).filter(Boolean)),
    ];

    const pos = await this._purchaseOrderUsecases.findPoWithIds(poIds);

    let budgetCodes;
    if (budgetCodeCodes?.length) {
      budgetCodes = await this.budgetCodeUsescases.getBudgetCodeByCodes(
        budgetCodeCodes,
        null,
        false,
      );
    }

    const posMap = codeToIdMap(pos || [], 'id');
    const budgetCodesMap = codeToIdMap(budgetCodes || [], 'code', ['id']);

    let needCreateLogError = false;
    receipts.forEach((item) => {
      const errors = [];
      const poId = posMap[item.poId];
      if (!poId) {
        errors.push(
          receiptSolomonErrorDetails.E_7050(`PO ${item.poId} không tồn tại`),
        );
      } else {
        item.poId = poId;
      }

      if (item.budgetCode) {
        const budgetCodeId = budgetCodesMap[item.budgetCodeCode];
        if (budgetCodeId) {
          item.budgetCodeId = budgetCodeId;
        } else {
          errors.push(
            receiptSolomonErrorDetails.E_7051(
              `Mã ngân sách ${item.budgetCode} không tồn tại`,
            ),
          );
        }
      }

      if (errors?.length) {
        needCreateLogError = true;
        item.errorStatus = 'FAILED';
      } else {
        item.errorStatus = 'SUCCESSFUL';
      }

      item.errorDetails = errors;
    });

    if (needCreateLogError) {
      const errorLogData: CreateApiLogDto = {
        controller: 'ReceiptSolomonController', // Tên controller
        method: 'POST', // Method: GET, POST...
        route: 'create-receipt', // Endpoint API
        statusCode: 500, // HTTP Status Code
        isSuccess: false, // Thành công hay thất bại
        body: receipts.map((item) => {
          return {
            ...item,
            errorDetails: undefined,
            errorStatus: undefined,
          };
        }),
        errorMessage: null,
      };

      await this.createApiLogError(receipts, errorLogData);
    } else {
      await processInBatches(receipts, 500, async (batch) => {
        await this.receiptSolomonRepository.createReceipt(batch);
      });
    }

    receipts.forEach((item) => {
      item.budgetCodeCode = undefined;
      item.budgetCodeId = undefined;
    });

    return receipts;
  }

  private async createApiLogError(err: any, errorLogData?: CreateApiLogDto) {
    console.log('136 [ERROR]', err);
    if (errorLogData) {
      errorLogData.errorMessage = err;

      if (err instanceof QueryFailedError) {
        console.log(492, err?.driverError?.detail);
        errorLogData.errorMessage = err?.driverError?.detail;
      }
      if (err instanceof HttpException) {
        console.log(496, err?.getResponse());
        errorLogData.errorMessage = err?.getResponse();
      }
      if (err instanceof AxiosError) {
        console.log(500, err?.message);
        errorLogData.errorMessage = err?.message;
      }

      await this.apiLogUsecases.create(errorLogData);
    }
  }

  async getReceipts(param: GetListReceiptDto) {
    return await this.receiptSolomonRepository.getReceipts(param);
  }

  async getDetailReceipt(param: GetUuidDto) {
    const detail = await this.receiptSolomonRepository.getDetailReceipt(param);

    if (!detail) {
      throw new HttpException(
        receiptSolomonErrorDetails.E_7052(),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }
}
