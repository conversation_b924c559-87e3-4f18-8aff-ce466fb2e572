import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ITaxCodeRepository } from '../domain/repositories/tax-code.repository';
import { CreateTaxCodeDto } from '../controller/tax-code/dtos/create-tax-code.dto';
import { TaxCodeModel } from '../domain/model/tax-code.model';
import { GetTaxCodeListDto } from '../controller/tax-code/dtos/get-tax-code-list.dto';
import { ResponseDto } from '../domain/dtos/response.dto';
import { DeleteTaxCodeDto } from '../controller/tax-code/dtos/delete-tax-code.dto';
import { plainToInstance } from 'class-transformer';
import { GetDetailTaxCodeDto } from '../controller/tax-code/dtos/get-detail-tax-code.dto';
import { getErrorMessage } from '../domain/messages/error-message';
import { taxCodeErrorDetails } from '../domain/messages/error-details/6550-tax-code';
import { UpdateTaxCodeDto } from '../controller/tax-code/dtos/update-tax-code.dto';

@Injectable()
export class TaxCodeUsecases {
  constructor(
    @Inject(ITaxCodeRepository)
    private readonly taxCodeRepository: ITaxCodeRepository,
  ) {}

  async createTaxCode(data: CreateTaxCodeDto): Promise<TaxCodeModel> {
    await this.checkTaxCode(data.code);

    return await this.taxCodeRepository.createTaxCode(data);
  }

  async getTaxCodes(
    conditions: GetTaxCodeListDto,
    jwtPayload,
  ): Promise<ResponseDto<TaxCodeModel>> {
    return await this.taxCodeRepository.getTaxCodes(conditions, jwtPayload);
  }

  async deleteTaxCode(conditions: DeleteTaxCodeDto, jwtPayload): Promise<void> {
    const taxCode = await this.taxCodeRepository.getTaxCodeDetail(
      plainToInstance(GetDetailTaxCodeDto, {
        id: conditions.id,
      }),
      jwtPayload,
    );
    if (!taxCode) {
      throw new HttpException(
        getErrorMessage(taxCodeErrorDetails.E_6550()),
        HttpStatus.NOT_FOUND,
      );
    }

    return await this.taxCodeRepository.deleteTaxCode(conditions.id);
  }

  async updateTaxCode(
    id: string,
    data: UpdateTaxCodeDto,
  ): Promise<TaxCodeModel> {
    await this.checkTaxCode(data.code, id);

    return await this.taxCodeRepository.updateTaxCode(data);
  }

  async getTaxCodeDetail(
    conditions: GetDetailTaxCodeDto,
    jwtPayload,
  ): Promise<TaxCodeModel> {
    const detail = await this.taxCodeRepository.getTaxCodeDetail(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(taxCodeErrorDetails.E_6550()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async checkTaxCode(code: string, id?: string) {
    const checkTaxCode = await this.taxCodeRepository.getTaxCodeByCode(
      code,
      id,
    );

    if (checkTaxCode) {
      throw new HttpException(
        getErrorMessage(taxCodeErrorDetails.E_6551()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
