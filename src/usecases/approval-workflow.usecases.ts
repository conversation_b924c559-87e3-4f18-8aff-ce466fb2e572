import { Inject, Injectable } from '@nestjs/common';
import { CreateApprovalWorkflowDto } from '../controller/process/dtos/create-approval-workflow.dto';
import { IApprovalWorkflowRepository } from '../domain/repositories/approval-workflow.repository';
import { StaffApprovalWorkflowUsecases } from './staff-approval-workflow.usecases';

@Injectable()
export class ApprovalWorkflowUsecases {
  constructor(
    private staffApprovalWorkflowUsecases: StaffApprovalWorkflowUsecases,
    @Inject(IApprovalWorkflowRepository)
    private readonly staffApprovalWorkflowRepository: IApprovalWorkflowRepository,
  ) {}
  async createApprovalWorkflow(
    data: CreateApprovalWorkflowDto,
    jwtPayload: any,
  ): Promise<any> {
    const approvalWorkflow =
      await this.staffApprovalWorkflowRepository.createApprovalWorkflow(data);

    if (data.createStaffApprovalWorkflowDtos?.length) {
      for (const createStaffApprovalWorkflowDto of data.createStaffApprovalWorkflowDtos) {
        createStaffApprovalWorkflowDto.approvalWorkflowId = approvalWorkflow.id;

        await this.staffApprovalWorkflowUsecases.createStaffApprovalWorkflow(
          createStaffApprovalWorkflowDto,
          jwtPayload,
        );
      }
    }
  }

  async deleteApprovalWorkflow(parentProcessId: string): Promise<void> {
    return await this.staffApprovalWorkflowRepository.deleteApprovalWorkflowByParentProcessId(
      parentProcessId,
    );
  }

  async getApprovalWorkflowByParentProcessId(
    parentProcessId: string,
  ): Promise<any> {
    return await this.staffApprovalWorkflowRepository.getApprovalWorkflowByParentProcessId(
      parentProcessId,
    );
  }
}
