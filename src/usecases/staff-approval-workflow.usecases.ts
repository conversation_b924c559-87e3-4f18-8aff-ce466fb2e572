import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import * as _ from 'lodash';
import { CreateStaffApprovalWorkflowDto } from '../controller/process/dtos/create-staff-approval-workflow.dto';
import { GetStaffListDto } from '../controller/staff/dtos/get-staff-list.dto';
import { EStatus } from '../domain/config/enums/status.enum';
import { staffErrorDetails } from '../domain/messages/error-details/staff';
import { StaffModel } from '../domain/model/staff.model';
import { IStaffApprovalWorkflowRepository } from '../domain/repositories/staff-approval-workflow.repository';
import { PositionUsecases } from './position.usecases';
import { StaffUsecases } from './staff.usecase';

@Injectable()
export class StaffApprovalWorkflowUsecases {
  constructor(
    private positionUsecases: PositionUsecases,
    private staffUsecases: StaffUsecases,
    @Inject(IStaffApprovalWorkflowRepository)
    private readonly staffApprovalWorkflowRepository: IStaffApprovalWorkflowRepository,
  ) {}
  async createStaffApprovalWorkflow(
    data: CreateStaffApprovalWorkflowDto,
    jwtPayload: any,
  ): Promise<any> {
    if (data.positionId) {
      await this.positionUsecases.getPositionById(data.positionId);
    }

    if (data.staffId) {
      await this.staffUsecases.getDetailStaff(
        { staffId: data.staffId },
        jwtPayload,
      );
    }

    if (data.selectedStaffIds?.length) {
      const checkStaffs = await this.staffUsecases.getStaffByIds(
        plainToClass(GetStaffListDto, {
          ids: data.selectedStaffIds,
          statuses: [EStatus.ACTIVE],
        }),
        jwtPayload,
      );

      const checkStaffsIds = checkStaffs.map((staff) => staff.id);
      const difference = _.difference(data.selectedStaffIds, checkStaffsIds);

      if (difference.length) {
        throw new HttpException(
          staffErrorDetails.E_3000(
            `StaffIds ${difference.join(', ')} not found`,
          ),
          HttpStatus.NOT_FOUND,
        );
      }

      data.selectedStaffs = checkStaffs;
    }

    return await this.staffApprovalWorkflowRepository.createStaffApprovalWorkflow(
      data,
    );
  }

  async deleteStaffApprovalWorkflowByApprovalWorkflowIds(
    approvalWorkflowIds: string[],
  ): Promise<void> {
    return await this.staffApprovalWorkflowRepository.deleteStaffApprovalWorkflow(
      approvalWorkflowIds,
    );
  }

  async getSelectedStaffs(
    staffApprovalWorkflowId: string,
  ): Promise<StaffModel[]> {
    return await this.staffApprovalWorkflowRepository.getSelectedStaffs(
      staffApprovalWorkflowId,
    );
  }
}
