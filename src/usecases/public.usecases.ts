import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import { CreateMaterialDto } from '../controller/material/dtos/create-material.dto';
import { UpdateMaterialDto } from '../controller/material/dtos/update-material.dto';
import { CreateMaterialFromSapDto } from '../controller/public/dto/create-material-from-sap.dto';
import { CreateSupplierFromSapDto } from '../controller/public/dto/create-supplier-from-sap.dto';
import { UpdateMaterialFromSapDto } from '../controller/public/dto/update-material-from-sap.dto';
import { UpdateSupplierFromSapDto } from '../controller/public/dto/update-supplier-from-sap.dto';
import { CreateSupplierDto } from '../controller/supplier/dtos/create-supplier.dto';
import { UpdateSupplierDto } from '../controller/supplier/dtos/update-supplier.dto';
import { EBusinessUnitStatus } from '../domain/config/enums/business-unit.enum';
import { EMaterialStatus } from '../domain/config/enums/material.enum';
import { ESupplierSectorStatus } from '../domain/config/enums/supplier-sector.enum';
import { ESupplierType } from '../domain/config/enums/supplier.enum';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { BusinessUnitModel } from '../domain/model/business-unit.model';
import { MaterialGroupModel } from '../domain/model/material-group.model';
import { MaterialTypeModel } from '../domain/model/material-type.model';
import { MaterialModel } from '../domain/model/material.model';
import { PurchasingDepartmentModel } from '../domain/model/purchasing-department.model';
import { PurchasingGroupModel } from '../domain/model/purchasing-group.model';
import { SupplierModel } from '../domain/model/supplier.model';
import { IMaterialRepository } from '../domain/repositories/material.repository';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { MaterialGroupUsecases } from './material-group.usecases';
import { MaterialTypeUsecases } from './material-type.usecases';
import { MaterialUsecases } from './material.usecases';
import { PurchasingDepartmentUsecases } from './purchasing-department.usecases';
import { PurchasingGroupUsecases } from './purchasing-group.usecases';
import { SectorUsecases } from './sector.usecases';
import { SupplierUsecases } from './supplier.usecases';

@Injectable()
export class PublicUsecases {
  constructor(
    private materialTypeUsecases: MaterialTypeUsecases,
    private materialGroupUsecases: MaterialGroupUsecases,
    private purchasingDepartmentUsecases: PurchasingDepartmentUsecases,
    private purchasingGroupUsecases: PurchasingGroupUsecases,
    private businessUnitUsecases: BusinessUnitUsecases,
    private sectorUsecases: SectorUsecases,
    private materialUsecases: MaterialUsecases,
    private supplierUsecases: SupplierUsecases,
    @Inject(IMaterialRepository)
    private readonly materialRepository: IMaterialRepository,
  ) {}

  async createMaterial(
    data: CreateMaterialFromSapDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<MaterialModel> {
    const material = await this.materialUsecases.getMaterialByCode(data.code);
    // Khởi tạo các promises để kiểm tra các entities liên quan
    const promises: (
      | Promise<MaterialTypeModel>
      | Promise<MaterialGroupModel>
      | Promise<PurchasingDepartmentModel>
      | Promise<PurchasingGroupModel>
      | Promise<BusinessUnitModel[]>
    )[] = [
      this.materialTypeUsecases.getMaterialTypeDetail(
        {
          code: data.materialTypeCode,
        },
        jwtPayload,
      ),
      this.materialGroupUsecases.getMaterialGroupDetail(
        {
          code: data.materialGroupCode,
        },
        jwtPayload,
      ),
    ];

    // Thêm vào các promises nếu có giá trị tương ứng
    if (data.purchasingDepartmentCode) {
      promises.push(
        this.purchasingDepartmentUsecases.getPurchasingDepartmentDetail(
          {
            code: data.purchasingDepartmentCode,
          },
          jwtPayload,
        ),
      );
    }

    if (data.purchasingGroupCode) {
      promises.push(
        this.purchasingGroupUsecases.getPurchasingGroupDetail(
          { code: data.purchasingGroupCode },
          jwtPayload,
        ),
      );
    }

    if (data.businessUnitCodes?.length) {
      promises.push(
        this.businessUnitUsecases.listByCodes(
          data.businessUnitCodes,
          jwtPayload,
        ),
      );
    }

    // Chờ tất cả các promises hoàn thành
    const [
      materialType,
      materialGroup,
      purchasingDepartment,
      purchasingGroup,
      checkBusinessUnits,
    ] = await Promise.all(promises);

    // Kiểm tra business units
    if ((checkBusinessUnits as BusinessUnitModel[])?.length) {
      const activeBusinessUnits = (
        checkBusinessUnits as BusinessUnitModel[]
      )?.filter((item) => item.status === EBusinessUnitStatus.ACTIVE);
      const activeBusinessUnitCodes = activeBusinessUnits.map(
        (item) => item.code,
      );

      const missingBuCodes = _.difference(
        data.businessUnitCodes,
        activeBusinessUnitCodes,
      );
      if (missingBuCodes.length) {
        throw new HttpException(
          getErrorMessage(
            errorMessage.E_1022(
              `BU Codes ${missingBuCodes.join(', ')} not found or inactive`,
            ),
          ),
          HttpStatus.NOT_FOUND,
        );
      }
    }

    const sectors = data.industries?.length
      ? await Promise.all(
          data.industries.map((item) =>
            this.sectorUsecases.getSectorByCode(item.sectorCode),
          ),
        )
      : [await this.sectorUsecases.getSectorByCode('FEED')];

    // Tạo DTO cho việc tạo mới material
    const createMaterialDto: CreateMaterialDto = {
      ...data,
      materialTypeId: (materialType as MaterialTypeModel)?.id,
      materialGroupId: (materialGroup as MaterialGroupModel)?.id,
      purchasingDepartmentId:
        (purchasingDepartment as PurchasingDepartmentModel)?.id || null,
      purchasingGroupId: (purchasingGroup as PurchasingGroupModel)?.id || null,
      businessUnitIds:
        (checkBusinessUnits as BusinessUnitModel[])?.map((bu) => bu.id) || [],
      industries: sectors.map((sector) => ({
        sectorId: sector.id,
        status: data.status || EMaterialStatus.ACTIVE,
        codeSAP: data.code,
      })),
    };

    // Tạo material và trả về kết quả
    if (!material?.id) {
      return await this.materialUsecases.createMaterial(
        createMaterialDto,
        jwtPayload,
        authorization,
        false,
        true,
      );
    } else {
      return this.materialUsecases.updateMaterial(
        material.id,
        { ...createMaterialDto },
        jwtPayload,
        authorization,
        false,
        true,
      );
    }
  }

  async updateMaterial(
    oldCode: string,
    data: UpdateMaterialFromSapDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<MaterialModel> {
    const oldMaterial = await this.materialUsecases.getMaterialDetail(
      { code: oldCode },
      jwtPayload,
    );

    if (oldCode !== data.code) {
      await this.materialUsecases.checkExistMaterialByCode(
        data.code,
        oldMaterial.id,
      );
    }

    // Initialize promises to check related entities
    const promises = [
      oldMaterial?.materialType?.code !== data.materialTypeCode
        ? this.materialTypeUsecases.getMaterialTypeDetail(
            { code: data.materialTypeCode },
            jwtPayload,
          )
        : null,
      oldMaterial?.materialGroup?.code !== data.materialGroupCode
        ? this.materialGroupUsecases.getMaterialGroupDetail(
            { code: data.materialGroupCode },
            jwtPayload,
          )
        : null,
      oldMaterial?.purchasingDepartment?.code !== data.purchasingDepartmentCode
        ? this.purchasingDepartmentUsecases.getPurchasingDepartmentDetail(
            { code: data.purchasingDepartmentCode },
            jwtPayload,
          )
        : null,
      oldMaterial?.purchasingGroup?.code !== data.purchasingGroupCode
        ? this.purchasingGroupUsecases.getPurchasingGroupDetail(
            { code: data.purchasingGroupCode },
            jwtPayload,
          )
        : null,
      data.businessUnitCodes?.length
        ? this.businessUnitUsecases.listByCodes(
            data.businessUnitCodes,
            jwtPayload,
          )
        : oldMaterial.businessUnits,
    ];

    const [
      materialType,
      materialGroup,
      purchasingDepartment,
      purchasingGroup,
      checkBusinessUnits,
    ] = await Promise.all(promises);

    // Verify business units
    if ((checkBusinessUnits as BusinessUnitModel[])?.length) {
      const activeBusinessUnits = (
        checkBusinessUnits as BusinessUnitModel[]
      ).filter((bu) => bu.status === EBusinessUnitStatus.ACTIVE);
      const missingBuCodes = _.difference(
        data.businessUnitCodes,
        activeBusinessUnits.map((bu) => bu.code),
      );
      if (missingBuCodes.length) {
        throw new HttpException(
          getErrorMessage(
            errorMessage.E_1022(
              `BU Codes ${missingBuCodes.join(', ')} not found or inactive`,
            ),
          ),
          HttpStatus.NOT_FOUND,
        );
      }
    }

    const sectors = data.industries?.length
      ? await Promise.all(
          data.industries.map((item) =>
            this.sectorUsecases.getSectorByCode(item.sectorCode),
          ),
        )
      : [await this.sectorUsecases.getSectorByCode('FEED')];

    // Prepare update DTO while ensuring old values are preserved if not updated
    const updateMaterialDto: UpdateMaterialDto = {
      ...data,
      materialTypeId:
        oldMaterial?.materialType?.code !== data.materialTypeCode
          ? (materialType as MaterialTypeModel)?.id
          : oldMaterial.materialTypeId,
      materialGroupId:
        oldMaterial?.materialGroup?.code !== data.materialGroupCode
          ? (materialGroup as MaterialGroupModel)?.id
          : oldMaterial.materialGroupId,
      purchasingDepartmentId:
        oldMaterial?.purchasingDepartment?.code !==
        data.purchasingDepartmentCode
          ? (purchasingDepartment as PurchasingDepartmentModel)?.id
          : oldMaterial.purchasingDepartmentId,
      purchasingGroupId:
        oldMaterial?.purchasingGroup?.code !== data.purchasingGroupCode
          ? (purchasingGroup as PurchasingGroupModel)?.id
          : oldMaterial.purchasingGroupId,
      businessUnitIds: (checkBusinessUnits as BusinessUnitModel[])
        ? (checkBusinessUnits as BusinessUnitModel[])?.map((bu) => bu.id)
        : oldMaterial.businessUnits?.map((bu) => bu.id) || [],
      industries: sectors.map((sector) => ({
        sectorId: sector.id,
        status: data.status || EMaterialStatus.ACTIVE,
        codeSAP: data.code,
      })),
    };

    // Update material and return result
    return this.materialUsecases.updateMaterial(
      oldMaterial.id,
      updateMaterialDto,
      jwtPayload,
      authorization,
      false,
      true,
    );
  }

  async deleteMaterial(code: string, jwtPayload: any, authorization: string) {
    const material = await this.materialUsecases.getMaterialDetail(
      { code },
      jwtPayload,
    );

    return await this.materialUsecases.deleteMaterial(
      material.id,
      jwtPayload,
      authorization,
      true,
    );
  }

  async createSupplier(
    data: CreateSupplierFromSapDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<SupplierModel> {
    // Kiểm tra sự tồn tại của supplier với mã code đã cho
    await this.supplierUsecases.checkExistSupplierByCode(data.code);

    // Lấy danh sách sectors từ data hoặc dùng mặc định là 'FEED'
    const sectors = data.industries?.length
      ? await Promise.all(
          data.industries.map((item) =>
            this.sectorUsecases.getSectorByCode(item.sectorCode),
          ),
        )
      : [await this.sectorUsecases.getSectorByCode('FEED')];

    // Tạo DTO cho supplier mới
    const createSupplierDto: CreateSupplierDto = {
      ...data,
      type: data.type || ESupplierType.OFFICIAL,
      industries: sectors.map((sector) => ({
        sectorId: sector.id,
        status: data.status || ESupplierSectorStatus.ACTIVE,
        codeSAP: data.code,
      })),
    };

    // Tạo supplier và trả về kết quả
    return this.supplierUsecases.createSupplier(
      createSupplierDto,
      jwtPayload,
      authorization,
      false,
      true,
    );
  }

  async updateSupplier(
    oldCode: string,
    data: UpdateSupplierFromSapDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<SupplierModel> {
    const oldSupplier = await this.supplierUsecases.getSupplierDetail(
      { code: oldCode },
      jwtPayload,
    );

    if (oldCode !== data.code) {
      await this.materialUsecases.checkExistMaterialByCode(
        data.code,
        oldSupplier.id,
      );
    }

    // Lấy danh sách sectors từ data hoặc dùng mặc định là 'FEED'
    const sectors = data.industries?.length
      ? await Promise.all(
          data.industries.map((item) =>
            this.sectorUsecases.getSectorByCode(item.sectorCode),
          ),
        )
      : [await this.sectorUsecases.getSectorByCode('FEED')];

    // Prepare update DTO
    const updateSupplierDto: UpdateSupplierDto = {
      ...data,
      industries: sectors.map((sector) => ({
        sectorId: sector.id,
        status: data.status || ESupplierSectorStatus.ACTIVE,
        codeSAP: data.code,
      })),
    };

    // Update supplier and return result
    return this.supplierUsecases.updateSupplier(
      oldSupplier.id,
      updateSupplierDto,
      jwtPayload,
      authorization,
      false,
      true,
    );
  }

  async deleteSupplier(code: string, jwtPayload: any, authorization: string) {
    const material = await this.supplierUsecases.getSupplierDetail(
      { code },
      jwtPayload,
    );

    return await this.supplierUsecases.deleteSupplier(
      material.id,
      jwtPayload,
      authorization,
      true,
    );
  }
}
