import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { CreateCostcenterSubaccountDto } from '../controller/costcenter-subaccount/dtos/create-costcenter-subaccount.dto';
import { DeleteCostcenterSubaccountDto } from '../controller/costcenter-subaccount/dtos/delete-costcenter-subaccount.dto';
import { GetCostcenterSubaccountListDto } from '../controller/costcenter-subaccount/dtos/get-costcenter-subaccount-list.dto';
import { GetDetailCostcenterSubaccountDto } from '../controller/costcenter-subaccount/dtos/get-detail-costcenter-subaccount.dto';
import { UpdateCostcenterSubaccountDto } from '../controller/costcenter-subaccount/dtos/update-costcenter-subaccount.dto';
import { ImportCostcenterSubaccountDto } from '../controller/import/dtos/import-costcenter-subaccount.dto';
import { GetDetailSectorDto } from '../controller/sector/dtos/get-detail-sector.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EAcutalEventFor,
  ISapEventCreate,
} from '../domain/config/enums/actual-spending.enum';
import {
  EColumnImportCostSub,
  ECostcenterSubaccountStatus,
} from '../domain/config/enums/costcenter-subaccount.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { ESectorStatus } from '../domain/config/enums/sector.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  TErrorMessageImport,
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { CostSubHistoryModel } from '../domain/model/cost-sub-history.model';
import { CostcenterSubaccountModel } from '../domain/model/costcenter-subaccount.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { IBusinessUnitRepository } from '../domain/repositories/business-unit.repository';
import { ICompanyRepository } from '../domain/repositories/company.repository';
import { ICostcenterSubaccountRepository } from '../domain/repositories/costcenter-subaccount.repository';
import { IDepartmentRepository } from '../domain/repositories/department.repository';
import { ISectorRepository } from '../domain/repositories/sector.repository';
import {
  codeToIdMap,
  excelSerialToDate,
  getFlattenColumnValues,
  getStatus,
  getStatusCostcenterSubaccount,
  getValueOrResult,
  mergedErrors,
  removeUnicode,
} from '../utils/common';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CompanyUsecases } from './company.usecases';
import { CostSubHistoryUsecases } from './cost-sub-history.usecases';
import { DepartmentUsecases } from './department.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { SectorUsecases } from './sector.usecases';
import { GetCostCenterListByIdsDto } from '../controller/costcenter-subaccount/dtos/get-cost-center-list-by-ids.dto';
import { FunctionUnitUsecases } from './function-unit.usecases';

@Injectable()
export class CostcenterSubaccountUsecases {
  constructor(
    @Inject(ICostcenterSubaccountRepository)
    private readonly costcenterSubaccountRepository: ICostcenterSubaccountRepository,
    @Inject(ISectorRepository)
    private readonly sectorRepository: ISectorRepository,
    private readonly sectorUsecases: SectorUsecases,
    @Inject(ICompanyRepository)
    private readonly companyRepository: ICompanyRepository,
    private readonly companyUsecases: CompanyUsecases,
    @Inject(IBusinessUnitRepository)
    private readonly businessUnitRepository: IBusinessUnitRepository,
    private readonly businessUnitUsecases: BusinessUnitUsecases,
    @Inject(IDepartmentRepository)
    private readonly departmentRepository: IDepartmentRepository,
    private readonly departmentUsecases: DepartmentUsecases,
    private readonly costSubHistoryUsecases: CostSubHistoryUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private readonly fileUsecases: FileUsecases,
    private eventEmitter: EventEmitter2,
    private readonly functionUnitUsecases: FunctionUnitUsecases,
  ) {}

  async createCostcenterSubaccount(
    data: CreateCostcenterSubaccountDto,
    jwtPayload: any,
    isImport: boolean = false,
    authorization: string,
  ): Promise<CostcenterSubaccountModel> {
    await this.verifyDataCostcenterSubaccount(data, jwtPayload, isImport);

    const createCostcenterSubaccount = new CostcenterSubaccountModel({
      ...data,
      searchValue: removeUnicode(data.code) + ' ' + removeUnicode(data.name),
    });

    if (data.functionUnitIds) {
      const functionUnits =
        await this.functionUnitUsecases.getFunctionUnitByIds(
          data.functionUnitIds,
          jwtPayload,
        );

      data.functionUnits = functionUnits;
    }

    if (!data.effectiveEndDate) {
      data.effectiveEndDate = null;
    }
    const costcenter =
      await this.costcenterSubaccountRepository.createCostcenterSubaccount(
        createCostcenterSubaccount,
      );

    this.eventEmitter.emit('sap.costcenter.created', {
      code: costcenter.code,
      id: costcenter.id,
      eventFor: EAcutalEventFor.COST_CENTER,
    } as ISapEventCreate);

    return await this.getCostcenterSubaccountById(costcenter.id);
  }

  async getCostcenterSubaccountById(
    id: string,
  ): Promise<CostcenterSubaccountModel> {
    return await this.costcenterSubaccountRepository.getCostcenterSubaccountById(
      id,
    );
  }

  async getCostcenterSubaccounts(
    conditions: GetCostcenterSubaccountListDto,
    jwtPayload,
  ): Promise<ResponseDto<CostcenterSubaccountModel>> {
    return await this.costcenterSubaccountRepository.getCostcenterSubaccounts(
      conditions,
      jwtPayload,
    );
  }

  async deleteCostcenterSubaccount(
    conditions: DeleteCostcenterSubaccountDto,
    jwtPayload,
  ): Promise<void> {
    const costcenter =
      await this.costcenterSubaccountRepository.getCostcenterSubaccountDetail(
        plainToInstance(GetDetailCostcenterSubaccountDto, {
          ...conditions,
        }),
        jwtPayload,
      );

    if (!costcenter) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1005()),
        HttpStatus.NOT_FOUND,
      );
    }

    return await this.costcenterSubaccountRepository.deleteCostcenterSubaccount(
      conditions.id,
    );
  }

  async updateCostcenterSubaccount(
    id: string,
    data: UpdateCostcenterSubaccountDto,
    jwtPayload,
    isImport: boolean = false,
  ): Promise<CostcenterSubaccountModel> {
    const costcenter = await this.verifyDataCostcenterSubaccount(
      data,
      jwtPayload,
      isImport,
      id,
    );

    const oldFunctionUnitIds =
      costcenter?.functionUnits?.map((item) => item.id) || [];

    if (
      data.functionUnitIds?.length &&
      _.difference(data.functionUnitIds, oldFunctionUnitIds).length
    ) {
      const functionUnits =
        await this.functionUnitUsecases.getFunctionUnitByIds(
          data.functionUnitIds,
          jwtPayload,
        );

      data.functionUnits = functionUnits;
    }

    if (!data.effectiveEndDate) {
      data.effectiveEndDate = null;
    }

    const updateCostcenterSubaccount = new CostcenterSubaccountModel({
      id: id,
      ...data,
      searchValue: removeUnicode(data.code) + ' ' + removeUnicode(data.name),
    });

    const updateCostcenter =
      await this.costcenterSubaccountRepository.updateCostcenterSubaccount(
        updateCostcenterSubaccount,
      );

    const detail = await this.getCostcenterSubaccountById(updateCostcenter.id);

    await this.costSubHistoryUsecases.createCostSubHistory(
      new CostSubHistoryModel({
        costcenterSubaccountId: id,
        oldData: costcenter,
        newData: detail,
        userInfo: {
          id: jwtPayload?.userId,
          userName: jwtPayload?.userName,
          fullName: jwtPayload?.fullName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
        },
      }),
    );

    if (costcenter.code != data.code) {
      this.eventEmitter.emit('sap.costcenter.created', {
        code: detail.code,
        id: detail.id,
        eventFor: EAcutalEventFor.COST_CENTER,
      } as ISapEventCreate);
    }

    return detail;
  }

  async getHistory(id: string) {
    const costcenter =
      await this.costcenterSubaccountRepository.getCostcenterSubaccountById(id);

    if (!costcenter) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1005()),
        HttpStatus.NOT_FOUND,
      );
    }

    return await this.costcenterSubaccountRepository.getHistory(id);
  }

  async getCostcenterSubaccountByCode(
    code: string,
  ): Promise<CostcenterSubaccountModel> {
    return await this.costcenterSubaccountRepository.getCostcenterSubaccountByCode(
      code,
    );
  }

  private async verifyDataCostcenterSubaccount(
    conditions: CreateCostcenterSubaccountDto | UpdateCostcenterSubaccountDto,
    jwtPayload,
    isImport: boolean = false,
    id?: string,
  ) {
    let costcenter: CostcenterSubaccountModel;
    if (id) {
      costcenter =
        await this.costcenterSubaccountRepository.getCostcenterSubaccountDetail(
          plainToInstance(GetDetailCostcenterSubaccountDto, {
            id: id,
            ...conditions,
          }),
          jwtPayload,
        );

      if (!costcenter) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1005()),
          HttpStatus.NOT_FOUND,
        );
      }
    }

    if (!conditions.effectiveEndDate) {
      conditions.effectiveEndDate = null;
    }

    const checkDuplicate =
      await this.costcenterSubaccountRepository.checkDuplicateCostCenter(
        conditions.code,
        conditions.effectiveStartDate,
        conditions.effectiveEndDate,
      );

    if (checkDuplicate && checkDuplicate?.id != id) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1101()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!isImport) {
      if (!conditions.sectorId) {
        const sector = await this.sectorUsecases.getSectorByCode('FEED');
        conditions.sectorId = sector.id;
      } else {
        if (costcenter?.sectorId != conditions.sectorId) {
          const sector = await this.sectorUsecases.getDetailSector(
            plainToInstance(GetDetailSectorDto, {
              id: conditions.sectorId,
            }),
            jwtPayload,
          );
          if (sector.status == ESectorStatus.IN_ACTIVE) {
            throw new HttpException(
              getErrorMessage(errorMessage.E_1027()),
              HttpStatus.BAD_REQUEST,
            );
          }
        }
      }

      // if (costcenter?.companyId != conditions.companyId) {
      //   const company = await this.companyUsecases.getDetailCompany(
      //     plainToInstance(GetDetailCompanyDto, {
      //       id: conditions.companyId,
      //     }),
      //     jwtPayload,
      //   );

      //   if (company.status == ECompanyStatus.IN_ACTIVE) {
      //     throw new HttpException(
      //       getErrorMessage(errorMessage.E_1028()),
      //       HttpStatus.BAD_REQUEST,
      //     );
      //   }
      // }

      // if (costcenter?.businessUnitId != conditions.businessUnitId) {
      //   const businessUnit =
      //     await this.businessUnitUsecases.getDetailBusinessUnit(
      //       plainToInstance(GetDetailBusinessUnitDto, {
      //         id: conditions.businessUnitId,
      //       }),
      //       jwtPayload,
      //     );

      //   if (businessUnit.status == EBusinessUnitStatus.IN_ACTIVE) {
      //     throw new HttpException(
      //       getErrorMessage(errorMessage.E_1029()),
      //       HttpStatus.BAD_REQUEST,
      //     );
      //   }
      // }

      // if (costcenter?.departmentId != conditions.departmentId) {
      //   const department = await this.departmentUsecases.getDetailDepartment(
      //     plainToInstance(GetDetailDepartmentDto, {
      //       id: conditions.departmentId,
      //     }),
      //     jwtPayload,
      //   );

      //   if (department.status == EDepartmentStatus.IN_ACTIVE) {
      //     throw new HttpException(
      //       getErrorMessage(errorMessage.E_1030()),
      //       HttpStatus.BAD_REQUEST,
      //     );
      //   }
      // }

      if (
        conditions.effectiveEndDate &&
        new Date(conditions.effectiveStartDate) >
          new Date(conditions.effectiveEndDate)
      ) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1019()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    return costcenter;
  }

  async importCostcenterSubaccount(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.COST_CENTER_SUB_ACCOUNT,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createCostcenterSubaccountDtos: CreateCostcenterSubaccountDto[] =
          [];
        const updateCostcenterSubaccountDtos: UpdateCostcenterSubaccountDto[] =
          [];
        const totalData: any = [];
        let errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0].actualRowCount,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportCostSub.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const functionUnitCodes = getFlattenColumnValues(
          rows,
          EColumnImportCostSub.FUNCTION_UNIT_CODE,
        ) as string[];

        // const sectorCodes = [
        //   ...new Set([
        //     ...rows
        //       .map((item) =>
        //         getValueOrResult(
        //           item,
        //           EColumnImportCostSub.SECTOR_CODE,
        //         )?.toString(),
        //       )
        //       ?.filter(Boolean),
        //     'FEED',
        //   ]),
        // ];
        const sectorCodes = ['FEED'];

        const [cosctenterSubaccountCodes, sectors, functionUnits] =
          await Promise.all([
            this.costcenterSubaccountRepository.getCostcenterSubaccountsByCodesWithRole(
              [...new Set(codes)],
              jwtPayload,
              false,
            ),
            this.sectorRepository.getSectorsByCodesWithRole(
              sectorCodes,
              jwtPayload,
            ),
            this.functionUnitUsecases.listByCodes(
              functionUnitCodes,
              jwtPayload,
            ),
          ]);

        const [dataSectors, dataFunctionUnits] = [sectors, functionUnits].map(
          (items: any) => codeToIdMap(items, 'code', ['id']),
        );

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];

          const code = getValueOrResult(
            row,
            EColumnImportCostSub.CODE,
          )?.toString(); //Mã cost/sub
          const name = ''; //Tên
          const description = getValueOrResult(
            row,
            EColumnImportCostSub.DESCRIPTION,
          )?.toString(); //Mô tả
          // const sectorCode = getValueOrResult(
          //   row,
          //   EColumnImportCostSub.SECTOR_CODE,
          // )?.toString(); //Mảng
          const note1 = getValueOrResult(
            row,
            EColumnImportCostSub.NOTE1,
          )?.toString();
          const note2 = getValueOrResult(
            row,
            EColumnImportCostSub.NOTE2,
          )?.toString();
          const note3 = getValueOrResult(
            row,
            EColumnImportCostSub.NOTE3,
          )?.toString();
          const note4 = getValueOrResult(
            row,
            EColumnImportCostSub.NOTE4,
          )?.toString();
          const note5 = getValueOrResult(
            row,
            EColumnImportCostSub.NOTE5,
          )?.toString();
          const note6 = getValueOrResult(
            row,
            EColumnImportCostSub.NOTE6,
          )?.toString();
          let status = ECostcenterSubaccountStatus.ACTIVE; //Trạng thái
          const effectiveStartDate = row.getCell(
            EColumnImportCostSub.EFFECTIVE_START_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportCostSub.EFFECTIVE_START_DATE)
                    .value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian bắt đầu
          const effectiveEndDate = row.getCell(
            EColumnImportCostSub.EFFECTIVE_END_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportCostSub.EFFECTIVE_END_DATE).value as
                    | string
                    | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian kết thúc
          const functionUnitCodes = [
            ...new Set(
              getValueOrResult(row, EColumnImportCostSub.FUNCTION_UNIT_CODE)
                ?.toString()
                ?.split(',')
                ?.map((item) => item.trim())
                ?.filter(Boolean) || [],
            ),
          ] as string[];

          const costcenterSubaccountObject = {
            id: undefined,
            name,
            code,
            description,
            status,
            sectorId: null,
            note1,
            note2,
            note3,
            note4,
            note5,
            note6,
            effectiveStartDate,
            effectiveEndDate,
            functionUnitIds: [],
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            row: i + 1,
          };

          ///Kiểm tra các field required
          if (!code) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1043(),
                'Cost Center không được để trống',
              ),
              row: i + 1,
            });
          } else {
            if (effectiveEndDate) {
              const checkCode = cosctenterSubaccountCodes.find(
                (item) =>
                  item.code == code &&
                  ((item.effectiveEndDate &&
                    item.effectiveStartDate?.toString() <=
                      effectiveEndDate?.toString() &&
                    item.effectiveEndDate?.toString() >=
                      effectiveStartDate?.toString()) ||
                    (!item.effectiveEndDate &&
                      item.effectiveStartDate?.toString() >=
                        effectiveStartDate?.toString() &&
                      item.effectiveStartDate?.toString() <=
                        effectiveEndDate?.toString())),
              );

              if (checkCode) {
                errors.push({
                  error: getErrorMessage(
                    errorMessage.E_1101(),
                    'Cost Center đã tồn tại',
                  ),
                  row: i + 1,
                });
              }
            } else {
              const checkCode = cosctenterSubaccountCodes.find(
                (item) =>
                  item.code == code &&
                  (((item.effectiveStartDate?.toString() <=
                    effectiveStartDate?.toString() ||
                    item.effectiveStartDate?.toString() >=
                      effectiveStartDate?.toString()) &&
                    !item.effectiveEndDate) ||
                    (item.effectiveEndDate &&
                      item.effectiveStartDate?.toString() <=
                        effectiveStartDate?.toString() &&
                      item.effectiveEndDate?.toString() >=
                        effectiveStartDate?.toString())),
              );

              if (checkCode) {
                errors.push({
                  error: getErrorMessage(
                    errorMessage.E_1101(),
                    'Cost Center đã tồn tại',
                  ),
                  row: i + 1,
                });
              }
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1044(),
                'Diễn giải Cost Center không được để trống',
              ),
              row: i + 1,
            });
          }

          if (!status) {
            status = ECostcenterSubaccountStatus.ACTIVE;
          } else {
            if (!(status in ECostcenterSubaccountStatus)) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1052(),
                  'Trạng thái không đúng',
                ),
                row: i + 1,
              });
            }
          }

          const sectorId = dataSectors['FEED'];

          if (!sectorId) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1036(),
                'Mã ngành chưa đúng',
              ),
              row: i + 1,
            });
          } else {
            costcenterSubaccountObject.sectorId = sectorId;
          }

          if (!effectiveStartDate || effectiveStartDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1075(),
                'Ngày hiệu lực không đúng định dạng',
              ),
              row: i + 1,
            });
          }

          if (
            effectiveStartDate &&
            effectiveEndDate &&
            effectiveStartDate > effectiveEndDate
          ) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1019(),
                'Ngày hiệu lực không được lớn hơn Ngày hiệu lực',
              ),
              row: i + 1,
            });
          }

          if (effectiveEndDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1076(),
                'Ngày hiệu lực không đúng định dạng',
              ),
              row: i + 1,
            });
          }

          if (functionUnitCodes && functionUnitCodes?.length) {
            for (const functionUnitCode of functionUnitCodes) {
              const functionUnitId = dataFunctionUnits[functionUnitCode];

              if (!functionUnitId) {
                errors.push({
                  error: getErrorMessage(errorMessage.E_1034()),
                  row: i + 1,
                });
              } else {
                costcenterSubaccountObject.functionUnitIds.push(functionUnitId);
              }
            }
          }

          const checkExist = cosctenterSubaccountCodes.find(
            (item) =>
              item.code == code &&
              item.effectiveStartDate == new Date(effectiveStartDate),
          );

          if (checkExist) {
            costcenterSubaccountObject.id = checkExist.id;
          }

          const costcenterSubaccountDto = plainToInstance(
            CreateCostcenterSubaccountDto,
            costcenterSubaccountObject,
          );

          if (costcenterSubaccountObject.id) {
            const updateCostcenterSubaccountDto = plainToInstance(
              UpdateCostcenterSubaccountDto,
              costcenterSubaccountObject,
            );
            updateCostcenterSubaccountDtos.push(updateCostcenterSubaccountDto);
          } else {
            createCostcenterSubaccountDtos.push(costcenterSubaccountDto);
          }

          if (
            effectiveStartDate &&
            effectiveStartDate != 'Invalid date' &&
            effectiveEndDate != 'Invalid date'
          ) {
            totalData.push(costcenterSubaccountDto);
          }
        }

        for (let i = 0; i < totalData.length; i++) {
          const data = totalData[i];

          if (data.effectiveEndDate) {
            const checkCode = totalData.find(
              (item, index) =>
                index != i &&
                item.code == data.code &&
                ((item.effectiveEndDate &&
                  item.effectiveStartDate?.toString() <=
                    data.effectiveEndDate?.toString() &&
                  item.effectiveEndDate?.toString() >=
                    data.effectiveStartDate?.toString()) ||
                  (!item.effectiveEndDate &&
                    item.effectiveStartDate?.toString() >=
                      data.effectiveStartDate?.toString() &&
                    item.effectiveStartDate?.toString() <=
                      data.effectiveEndDate?.toString())),
            );

            if (checkCode) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1101(),
                  'Cost Center đã tồn tại',
                ),
                row: data.row,
              });
            }
          } else {
            const checkCode = totalData.find(
              (item, index) =>
                index != i &&
                item.code == data.code &&
                (((item.effectiveStartDate?.toString() <=
                  data.effectiveStartDate?.toString() ||
                  item.effectiveStartDate?.toString() >=
                    data.effectiveStartDate?.toString()) &&
                  !item.effectiveEndDate) ||
                  (item.effectiveEndDate &&
                    item.effectiveStartDate?.toString() <=
                      data.effectiveStartDate?.toString() &&
                    item.effectiveEndDate?.toString() >=
                      data.effectiveStartDate?.toString())),
            );

            if (checkCode) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1101(),
                  'Cost Center đã tồn tại',
                ),
                row: data.row,
              });
            }
          }
        }

        errors = mergedErrors(errors);

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportCostcenterSubaccountDto = {
          dataCostcenterSubaccounts: createCostcenterSubaccountDtos,
          dataUpdateCostcenterSubaccounts: updateCostcenterSubaccountDtos,
          fileImportHistoryId: fileImportHistory.id,
        };

        await this.import(
          importBody,
          EFileImportType.COST_CENTER_SUB_ACCOUNT,
          jwtPayload,
          authorization,
        );
        // await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
        //   importBody: importBody,
        //   importHeader: {
        //     authorization,
        //     'x-api-key': process.env.API_KEY,
        //   },
        //   importUrl: PurchaseServiceApiUrlsConst.IMPORT_COSTCENTER_SUBACCOUNT(),
        //   updateStatusFileUrl:
        //     PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
        //       fileImportHistory.id,
        //     ),
        // });

        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.SUCCESS,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
  async getCostcenterSubaccountDetail(
    conditions: GetDetailCostcenterSubaccountDto,
    jwtPayload,
  ) {
    const detail =
      await this.costcenterSubaccountRepository.getCostcenterSubaccountDetail(
        conditions,
        jwtPayload,
      );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1005()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async getCostCenterByIds(ids: string[], jwtPayload: any) {
    const costCenters =
      await this.costcenterSubaccountRepository.getCostCenterByIds(
        ids,
        jwtPayload,
      );

    if (!costCenters || !costCenters.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1005()),
        HttpStatus.NOT_FOUND,
      );
    }

    const activeCostCenters = costCenters?.filter(
      (item) => item.status === ECostcenterSubaccountStatus.ACTIVE,
    );

    const activeCostCenterIds = activeCostCenters.map((item) => item.id);

    const missingCostCenterIds = _.difference(ids, activeCostCenterIds);

    if (missingCostCenterIds.length) {
      throw new HttpException(
        getErrorMessage(
          errorMessage.E_1005(
            `Cost Center ids ${missingCostCenterIds.join(', ')} not found or inactive`,
          ),
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return costCenters;
  }

  async getCostCenterByCodes(codes: string[], jwtPayload: any) {
    return await this.costcenterSubaccountRepository.getCostcenterSubaccountsByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
    );
  }

  async exportCostCenter(
    conditions: GetCostcenterSubaccountListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data =
      await this.costcenterSubaccountRepository.getCostcenterSubaccounts(
        conditions,
        jwtPayload,
      );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-cost-center.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toCostCenterModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-cost-center.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toCostCenterModel(costCenters: CostcenterSubaccountModel[]) {
    const items = [];

    for (let i = 0; i < costCenters.length; i++) {
      items.push({
        note1: costCenters[i].note1 || '',
        note2: costCenters[i].note2 || '',
        note3: costCenters[i].note3 || '',
        note4: costCenters[i].note4 || '',
        note5: costCenters[i].note5 || '',
        note6: costCenters[i].note6 || '',
        code: costCenters[i].code || '',
        name: costCenters[i].name || '',
        effectiveStartDate: costCenters[i].effectiveStartDate
          ? moment(costCenters[i].effectiveStartDate).format('DD/MM/YYYY')
          : '',
        effectiveEndDate: costCenters[i].effectiveEndDate
          ? moment(costCenters[i].effectiveEndDate).format('DD/MM/YYYY')
          : '',
        description: costCenters[i].description || '',
        status: getStatusCostcenterSubaccount(costCenters[i].status),
      });
    }

    return items;
  }

  async import(
    body: ImportCostcenterSubaccountDto,
    type: EFileImportType,
    jwtPayload?: IAuthUserPayload,
    authorization?: string,
  ) {
    const dataCostCenter = body as ImportCostcenterSubaccountDto;
    for (let i = 0; i < dataCostCenter.dataCostcenterSubaccounts.length; i++) {
      await this.createCostcenterSubaccount(
        dataCostCenter.dataCostcenterSubaccounts[i],
        jwtPayload,
        true,
        authorization,
      );
    }

    for (
      let i = 0;
      i < dataCostCenter.dataUpdateCostcenterSubaccounts.length;
      i++
    ) {
      await this.updateCostcenterSubaccount(
        dataCostCenter.dataUpdateCostcenterSubaccounts[i].id,
        dataCostCenter.dataUpdateCostcenterSubaccounts[i],
        jwtPayload,
        true,
      );
    }
  }

  async getListByIds(
    conditions: GetCostCenterListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<CostcenterSubaccountModel>> {
    return await this.costcenterSubaccountRepository.getListByIds(
      conditions,
      jwtPayload,
    );
  }
}
