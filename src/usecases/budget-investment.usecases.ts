import { Inject, Injectable } from '@nestjs/common';
import { IBudgetInvestmentRepository } from '../domain/repositories/budget-investment.repository';
import { BudgetInvestmentModel } from '../domain/model/budget-investment.model';
import { CreateBudgetInvestmentDto } from '../controller/budget-investment/dtos/create-budget-investment.dto';

@Injectable()
export class BudgetInvestmentUsecases {
  constructor(
    @Inject(IBudgetInvestmentRepository)
    private readonly budgetInvestmentRepository: IBudgetInvestmentRepository,
  ) {}

  async createManyBudgetInvestment(
    data: CreateBudgetInvestmentDto[],
  ): Promise<BudgetInvestmentModel[]> {
    const createManyBudgetInvestment: BudgetInvestmentModel[] = [];
    for (const investment of data) {
      createManyBudgetInvestment.push(new BudgetInvestmentModel(investment));
    }

    return await this.budgetInvestmentRepository.createManyBudgetInvestment(
      createManyBudgetInvestment,
    );
  }

  async updateManyBudgetInvestment(
    data: CreateBudgetInvestmentDto[],
  ): Promise<BudgetInvestmentModel[]> {
    const updateManyBudgetInvestment: BudgetInvestmentModel[] = [];
    for (const investment of data) {
      updateManyBudgetInvestment.push(new BudgetInvestmentModel(investment));
    }

    return await this.budgetInvestmentRepository.updateManyBudgetInvestment(
      updateManyBudgetInvestment,
    );
  }

  async deleteBudgetInvestmentsNotIn(
    ids: string[],
    budgetCapexId: string,
  ): Promise<void> {
    return await this.budgetInvestmentRepository.deleteBudgetInvestmentsNotIn(
      ids,
      budgetCapexId,
    );
  }
}
