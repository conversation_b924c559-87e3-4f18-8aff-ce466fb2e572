import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { CreatePurchasingDepartmentDto } from '../controller/purchasing-department/dtos/create-purchasing-department.dto';
import { GetDetailPurchasingDepartmentDto } from '../controller/purchasing-department/dtos/get-detail-purchasing-department.dto';
import { GetPurchasingDepartmentListDto } from '../controller/purchasing-department/dtos/get-purchasing-department-list.dto';
import { UpdatePurchasingDepartmentDto } from '../controller/purchasing-department/dtos/update-purchasing-department.dto';
import { GetDetailSectorDto } from '../controller/sector/dtos/get-detail-sector.dto';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EPurchasingDepartmentStatus } from '../domain/config/enums/purchasing.enum';
import { ESectorStatus } from '../domain/config/enums/sector.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { purchasingDepartmentErrorDetails } from '../domain/messages/error-details/purchasing-department';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { PurchasingDepartmentModel } from '../domain/model/purchasing-department.model';
import { IPurchasingDepartmentRepository } from '../domain/repositories/purchasing-department.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { SectorUsecases } from './sector.usecases';
import * as Excel from 'exceljs';
import { FileUsecases } from './file.usecases';
import { resolve } from 'path';
import { exportFileUploadPath } from '../domain/config/constant';
import { getStatusPurchasingDepartment } from '../utils/common';

@Injectable()
export class PurchasingDepartmentUsecases {
  constructor(
    @Inject(IPurchasingDepartmentRepository)
    private readonly purchasingDepartmentRepository: IPurchasingDepartmentRepository,
    private readonly sectorUsecases: SectorUsecases,
    private readonly fileUsecases: FileUsecases,
  ) {}

  async createPurchasingDepartment(
    data: CreatePurchasingDepartmentDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PurchasingDepartmentModel> {
    await this.verifyDataDto(data, jwtPayload);

    const purchasingDepartmentModel = new PurchasingDepartmentModel(data);

    const purchasingDepartment =
      await this.purchasingDepartmentRepository.createPurchasingDepartment(
        purchasingDepartmentModel,
      );

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: purchasingDepartment.name,
        refId: purchasingDepartment.id,
        refCode: purchasingDepartment.code,
        type: EDataRoleType.PURCHASING_DEPARTMENT,
        isEnabled:
          purchasingDepartment.status === EPurchasingDepartmentStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization },
    );

    return purchasingDepartment;
  }

  async updatePurchasingDepartment(
    id: string,
    updatePurchasingDepartmentDto: UpdatePurchasingDepartmentDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PurchasingDepartmentModel> {
    await this.verifyDataDto(updatePurchasingDepartmentDto, jwtPayload, id);

    const purchasingDepartment =
      await this.purchasingDepartmentRepository.updatePurchasingDepartment(
        id,
        updatePurchasingDepartmentDto,
      );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: purchasingDepartment.name,
        refCode: purchasingDepartment.code,
        isEnabled:
          purchasingDepartment.status === EPurchasingDepartmentStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    return purchasingDepartment;
  }

  // async getPurchasingDepartmentById(
  //   id: string,
  // ): Promise<PurchasingDepartmentModel> {
  //   const purchasingDepartment =
  //     await this.purchasingDepartmentRepository.getPurchasingDepartmentById(id);

  //   if (!purchasingDepartment) {
  //     throw new HttpException(
  //       purchasingDepartmentErrorDetails.E_2800(),
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   return purchasingDepartment;
  // }

  async getPurchasingDepartments(
    conditions: GetPurchasingDepartmentListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PurchasingDepartmentModel>> {
    return await this.purchasingDepartmentRepository.getPurchasingDepartments(
      conditions,
      jwtPayload,
    );
  }

  async deletePurchasingDepartment(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getPurchasingDepartmentDetail(
      plainToInstance(GetDetailPurchasingDepartmentDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.purchasingDepartmentRepository.deletePurchasingDepartment(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistPurchasingDepartmentByCode(
    code: string,
    id?: string,
  ): Promise<PurchasingDepartmentModel> {
    const purchasingDepartment =
      await this.purchasingDepartmentRepository.getPurchasingDepartmentByCode(
        code,
        id,
      );

    if (purchasingDepartment) {
      throw new HttpException(
        purchasingDepartmentErrorDetails.E_2801(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return purchasingDepartment;
  }

  async verifyDataDto(
    conditions: CreatePurchasingDepartmentDto | UpdatePurchasingDepartmentDto,
    jwtPayload: any,
    id?: string,
  ) {
    let detail: PurchasingDepartmentModel;
    if (id) {
      detail = await this.getPurchasingDepartmentDetail(
        plainToInstance(GetDetailPurchasingDepartmentDto, {
          id: id,
        }),
        jwtPayload,
      );
    }

    await this.checkExistPurchasingDepartmentByCode(conditions.code, id);

    if (detail?.sectorId != conditions.sectorId) {
      const sector = await this.sectorUsecases.getDetailSector(
        plainToInstance(GetDetailSectorDto, {
          id: conditions.sectorId,
        }),
        jwtPayload,
      );

      if (sector.status == ESectorStatus.IN_ACTIVE) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1027()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  async getPurchasingDepartmentDetail(
    conditions: GetDetailPurchasingDepartmentDto,
    jwtPayload,
  ) {
    const purchasingDepartment =
      await this.purchasingDepartmentRepository.getPurchasingDepartmentDetail(
        conditions,
        jwtPayload,
      );

    if (!purchasingDepartment) {
      throw new HttpException(
        purchasingDepartmentErrorDetails.E_2800(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return purchasingDepartment;
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.purchasingDepartmentRepository.getPurchasingDepartmentByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }

  //SAP is superAdmin
  async getPurchasingDepartmentDetailByCode(code: string) {
    const purchasingDepartment =
      await this.purchasingDepartmentRepository.getPurchasingDepartmentByCode(
        code,
      );

    if (!purchasingDepartment) {
      throw new HttpException(
        purchasingDepartmentErrorDetails.E_2800(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return purchasingDepartment;
  }

  async exportPurchasingDepartment(
    conditions: GetPurchasingDepartmentListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data =
      await this.purchasingDepartmentRepository.getPurchasingDepartments(
        conditions,
        jwtPayload,
      );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-purchasing-department.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toPurchasingDepartmentModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-purchasing-department.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toPurchasingDepartmentModel(
    purchasingDepartments: PurchasingDepartmentModel[],
  ) {
    const items = [];

    for (let i = 0; i < purchasingDepartments.length; i++) {
      items.push({
        code: purchasingDepartments[i].code || '',
        name: purchasingDepartments[i].name || '',
        description: purchasingDepartments[i].description || '',
        status: getStatusPurchasingDepartment(purchasingDepartments[i].status),
      });
    }

    return items;
  }
}
