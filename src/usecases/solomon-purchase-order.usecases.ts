import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ISolomonPurchaseOrderRepository } from '../domain/repositories/solomon-purchase-order.repository';
import { PurchaseOrderModel } from '../domain/model/purchase_order.model';
import { SolomonPurchaseOrderModel } from '../domain/model/solomon-purchase-order.model';
import { purchaseOrderUsecases } from './purchase_order.usecases';
import { ISolomonPurchaseOrderItemRepository } from '../domain/repositories/solomon-purchase-order-item.repository';
import { State, Status } from '../domain/config/enums/purchase-order.enum';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { CreateSolomonPurchaseOrderDto } from '../controller/solomon-purchase-order/dtos/create-solomon-purchase-order.dto';
import { PurchaseOrderDetailModel } from '../domain/model/purchase_order_detail.model';
import { EMeasureSystem } from '../domain/config/enums/measure.enum';
import { ensureUTC7 } from '../utils/common';
import { CreateSolomonPurchaseOrderItemDto } from '../controller/solomon-purchase-order/dtos/create-solomon-purchase-order-item.dto';
import { SolomonPurchaseOrderItemModel } from '../domain/model/solomon-purchase-order-item.model';
import { CreatePoSolomonDto } from '../controller/solomon-purchase-order/dtos/create-po-solomon.dto';
import { CreatePoItemSolomonDto } from '../controller/solomon-purchase-order/dtos/create-po-item-solomon.dto';
import { ApiLogUsecases } from './api-log.usecase';
import { sendPost } from '../utils/http';
import { SolomonApiUrlsConst } from '../utils/constants/solomon-api-url.const';
import { EnvironmentConfigService } from '../infrastructure/config/environment-config/environment-config.service';
import { ESolomonPoStatus } from '../domain/config/enums/solomon-po.enum';
import { PurchaseOrderEntity } from '../infrastructure/entities/purchase_order.entity';
import { DataSource, QueryRunner } from 'typeorm';
import { Request } from 'express';
import { ENTITY_MANAGER_KEY } from '../infrastructure/config/typeorm/tracsaction.interceptor';

const config: EnvironmentConfigService = new EnvironmentConfigService();

@Injectable()
export class SolomonPurchaseOrderUsecases {
  constructor(
    private readonly dataSource: DataSource,
    @Inject(ISolomonPurchaseOrderRepository)
    private readonly solomonPurchaseOrderRepository: ISolomonPurchaseOrderRepository,
    @Inject(ISolomonPurchaseOrderItemRepository)
    private readonly solomonPurchaseOrderItemRepository: ISolomonPurchaseOrderItemRepository,
    private _purchaseOrderUsecases: purchaseOrderUsecases,
    private apiLogUsecases: ApiLogUsecases,
  ) {}

  async syncAndSaveSolomonPurchaseOrder(
    poId: number,
    authorization: string,
    isApprove: boolean = true,
  ): Promise<any> {
    const po = await this._purchaseOrderUsecases.findOne(poId, authorization);
    if (po.statePo === State.Completed) {
      throw new HttpException(errorMessage.E_6057(), HttpStatus.BAD_REQUEST);
    }

    if (po.statusPo !== Status.Approved && !isApprove) {
      throw new HttpException(errorMessage.E_6055(), HttpStatus.BAD_REQUEST);
    }

    if (po.businessUnit?.company?.code != '3600') {
      throw new HttpException(errorMessage.E_6058(), HttpStatus.BAD_REQUEST);
    }

    if (!po?.details?.length) return;

    if (po?.solomonPo) {
      const solomonPoId = po?.solomonPo?.id;
      await Promise.all([
        this.solomonPurchaseOrderRepository.deleteSolomonPurchaseOrdersById([
          solomonPoId,
        ]),
        this.solomonPurchaseOrderItemRepository.deleteSolomonPOItemsBySolomonPurchaseOrderId(
          [solomonPoId],
        ),
      ]);
    }

    const solomonPurchaseOrder =
      await this.createAndPrepareSolomonPurchaseOrder(po);

    const { response } =
      await this.syncSolomonPurchaseOrder(solomonPurchaseOrder);

    if (response?.status != 200 && response?.status != 201) {
      solomonPurchaseOrder.messageType = 'E';
      solomonPurchaseOrder.message =
        response?.data || 'EP synchronization failed';
      solomonPurchaseOrder.status = 'SYNC_FAILED';
    } else {
      solomonPurchaseOrder.messageType = 'S';
      solomonPurchaseOrder.status = ESolomonPoStatus.COMPLETED;
    }

    const newSolomonPurchaseOrder =
      await this.solomonPurchaseOrderRepository.updateSolomonPurchaseOrder(
        solomonPurchaseOrder,
      );

    if (solomonPurchaseOrder.messageType == 'E') {
      throw new HttpException(
        getErrorMessage(errorMessage.E_6059(), solomonPurchaseOrder.message),
        HttpStatus.BAD_REQUEST,
      );
    } else {
      if (!isApprove) {
        const queryRunner = this.dataSource.createQueryRunner();

        try {
          if (!queryRunner.isTransactionActive) {
            await queryRunner.connect();
            await queryRunner.startTransaction();
          }

          await queryRunner.manager.save(PurchaseOrderEntity, {
            id: poId,
            isCreatedSolomon: true,
            statePo: State.Completed,
          });

          if (queryRunner.isTransactionActive) {
            await queryRunner.commitTransaction();
          }
        } catch (error) {
          if (queryRunner.isTransactionActive) {
            await queryRunner.rollbackTransaction();
          }
        } finally {
          if (!queryRunner.isReleased) {
            await queryRunner.release();
          }
        }
      }
    }

    return newSolomonPurchaseOrder;
  }

  private async createAndPrepareSolomonPurchaseOrder(
    po: PurchaseOrderModel,
  ): Promise<SolomonPurchaseOrderModel> {
    const createSolomonPurchaseOrderDto =
      this.createSolomonPurchaseOrderDto(po);

    return await this.createSolomonPurchaseOrder(createSolomonPurchaseOrderDto);
  }

  private createSolomonPurchaseOrderDto(
    po: PurchaseOrderModel,
  ): CreateSolomonPurchaseOrderDto {
    const [firstDetail] = po.details;

    return {
      poId: po.id, //Mã PO
      supplierId: firstDetail?.supplier?.id, //Mã nhà cung cấp
      supplierCode: firstDetail?.supplier?.code, //Mã nhà cung cấp
      supplierInfo: firstDetail?.supplierInfo,
      poCreatedDate: po.createdAt, //Ngày tạo PO
      budgetCodeCode: po.budgetCode?.code, //Mã ngân sách
      budgetCodeId: po.budgetCode?.id, //Mã ngân sách
      items: po.details
        ?.map((detail) => this.createSolomonPurchaseOrderItemDto(detail))
        ?.filter((item) => Object.keys(item).length > 0),
    };
  }

  private createSolomonPurchaseOrderItemDto(
    detail: PurchaseOrderDetailModel,
  ): CreateSolomonPurchaseOrderItemDto {
    return {
      materialId: detail.material?.id, //Mã vật tư
      materialCode: detail.material?.code,
      materialName: detail.materialName, //Mô tả sản phẩm cần mua đối với mua PO dich vụ không theo mã
      warehouseId: detail.warehouse?.id, //Mã kho
      warehouseCode: detail.warehouse?.code,
      description: detail.note, //Mô tả trong dòng item (note)
      quantity: detail.quantity, //Số lượng mua
      measureId: detail.measure?.id, //Mã đơn vị tính
      measureCode: (detail.measure?.codeConversions as any)?.find(
        (item) => item.system == EMeasureSystem.SOLOMON,
      )?.code, //Mã đơn vị tính (Lưu ý: Chuyển đổi sang đơn vị của hệ thống Solomon)
      unitPrice: detail.unitPrice, //Đơn giá
      totalAmountVat: detail.totalAmountVat, //Tổng tiền quy đổi * VAT
      unitWeight: 0, //Mặc định = 0
      extWeight: 0, //Mặc định = 0
      deliveryTime: ensureUTC7(detail.deliveryTime), //Ngày giao hàng
      taxCodeId: detail.taxCode?.id, //Mã thuế
      taxCodeCode: detail.taxCode?.code,
      cnvFact: 1, //Mặc định = 1
      budgetCodeCode: detail.budgetCode?.code, //Mã ngân sách
      budgetCodeId: detail.budgetCode?.id, //Mã ngân sách
      purchaseOrderDetailId: detail.id,
    };
  }

  async createSolomonPurchaseOrder(
    createSolomonPurchaseOrderDto: CreateSolomonPurchaseOrderDto,
  ): Promise<SolomonPurchaseOrderModel> {
    await this.solomonPurchaseOrderRepository.createSolomonPurchaseOrder(
      createSolomonPurchaseOrderDto,
    );

    return new SolomonPurchaseOrderModel({
      ...createSolomonPurchaseOrderDto,
      items: createSolomonPurchaseOrderDto.items?.map(
        (item) => new SolomonPurchaseOrderItemModel(item),
      ),
    });
  }

  async createSolomonPurchaseOrderItem(
    createSolomonPurchaseOrderItemDtos: CreateSolomonPurchaseOrderItemDto[],
  ): Promise<SolomonPurchaseOrderItemModel[]> {
    return await this.solomonPurchaseOrderItemRepository.createSolomonPurchaseOrderItem(
      createSolomonPurchaseOrderItemDtos,
    );
  }

  async syncSolomonPurchaseOrder(
    solomonPurchaseOrder: SolomonPurchaseOrderModel,
  ) {
    try {
      const syncPo: CreatePoSolomonDto = {
        poNbr: solomonPurchaseOrder.poId.toString(),
        vendID: solomonPurchaseOrder.supplierCode,
        poDate: solomonPurchaseOrder.poCreatedDate,
        budgetCode: solomonPurchaseOrder.budgetCodeCode,
        purOrdDet:
          solomonPurchaseOrder?.items
            ?.map((item) =>
              this.createPoItemSolomonDto(solomonPurchaseOrder, item),
            )
            ?.filter((item) => Object.keys(item).length > 0) || [],
      };

      const dataSendPost = {
        purchaseOrder: syncPo,
      };

      let response;
      try {
        response = await sendPost(
          SolomonApiUrlsConst.INTERGRATED_PO(),
          dataSendPost,
          {},
          {
            username: config.getSolomonBasicUsername(),
            password: config.getSolomonBasicPassword(),
          },
        );

        await this.apiLogUsecases.create({
          controller: 'SolomonPurchaseOrderController', // Tên controller
          method: 'POST', // Method: GET, POST...
          route: 'solomon', // Endpoint API
          statusCode: response?.status || 500, // HTTP Status Code
          isSuccess:
            response?.status == 200 || response?.status == 201 ? true : false, // Thành công hay thất bại
          body: dataSendPost,
          errorMessage:
            response?.status == 200 || response?.status == 201
              ? 'Success'
              : response?.data || 'EP synchronization failed',
        });

        return { response, dataSendPost };
      } catch (error) {
        response = error.response;

        await this.apiLogUsecases.create({
          controller: 'SolomonPurchaseOrderController', // Tên controller
          method: 'POST', // Method: GET, POST...
          route: 'solomon', // Endpoint API
          statusCode: response?.status || 500, // HTTP Status Code
          isSuccess: false, // Thành công hay thất bại
          body: dataSendPost,
          errorMessage: response?.data || error,
        });

        console.log(200, error);
      }

      return { dataSendPost, response };
    } catch (error) {
      console.log(205, error);
      return;
    }
  }

  private createPoItemSolomonDto(
    solomonPurchaseOrder: SolomonPurchaseOrderModel,
    item: SolomonPurchaseOrderItemModel,
  ): CreatePoItemSolomonDto {
    return {
      poNbr: solomonPurchaseOrder.poId.toString(),
      invtID: item.materialCode,
      siteID: item.warehouseCode,
      tranDesc: item.description,
      qtyOrd: Number(item.quantity || 0),
      purchUnit: item.measureCode,
      curyUnitCost: Number(item.unitPrice || 0),
      curyExtCost: Number(item.totalAmountVat || 0),
      unitWeight: 0,
      extWeight: 0,
      reqdDate: solomonPurchaseOrder.poCreatedDate,
      promDate: item.deliveryTime,
      taxID00: item.taxCodeCode,
      cnvFact: 1,
      user1: item.budgetCodeCode,
      user7: solomonPurchaseOrder.poCreatedDate,
    };
  }

  async getListSolomonPurchaseOrderByIds(
    ids: string[],
  ): Promise<SolomonPurchaseOrderModel[]> {
    return (
      (await this.solomonPurchaseOrderRepository.getListSolomonPurchaseOrderByIds(
        ids,
      )) || []
    );
  }
}
