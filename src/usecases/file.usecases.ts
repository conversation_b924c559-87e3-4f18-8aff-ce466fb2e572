import { Injectable } from '@nestjs/common';
import * as FormData from 'form-data';
import { sendPost } from '../utils/http';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import * as stream from 'stream';
import { FileServiceApiUrlsConst } from '../utils/constants/file-service-api-url.const';

@Injectable()
export class FileUsecases {
  constructor() {}

  async uploadFile(file: Express.Multer.File, data: any, path?: string) {
    let formData = new FormData();
    formData.append('file', file.buffer, file.originalname);

    if (path) {
      formData.append('path', path);
    }

    if (data) {
      for (const key in data) {
        formData.append(key, data[key]);
      }
    }

    const uploadFile = await sendPost(
      FileServiceApiUrlsConst.UPLOAD_FILE(),
      formData,
      {
        ...formData.getHeaders(),
      },
    );

    return uploadFile?.data?.['data'];
  }

  async uploadFiles(files: Express.Multer.File[], data: any, path?: string) {
    let formData = new FormData();

    files.forEach((file, index) =>
      formData.append(`files`, file.buffer, file.originalname),
    );

    if (path) {
      formData.append('path', path);
    }

    if (data) {
      for (const key in data) {
        formData.append(key, data[key]);
      }
    }

    const uploadFiles = await sendPost(
      FileServiceApiUrlsConst.UPLOAD_FILES(),
      formData,
      {
        ...formData.getHeaders(),
      },
    );

    return uploadFiles?.data?.['data'] || [];
  }

  async deleteFile(path: string) {
    await sendPost(FileServiceApiUrlsConst.DELETE_FILE(), {
      path,
    });
  }

  async deleteFiles(paths: string[]) {
    await sendPost(FileServiceApiUrlsConst.DELETE_FILES(), {
      paths,
    });
  }

  async bufferToMulterFile(
    buffer: Buffer,
    filename: string,
  ): Promise<Express.Multer.File> {
    const readable = new stream.PassThrough();
    readable.end(buffer);

    const multerFile: Express.Multer.File = {
      fieldname: 'file',
      originalname: filename,
      encoding: '7bit',
      mimetype:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      buffer,
      size: buffer.length,
      stream: readable as any,
      destination: '',
      filename,
      path: '',
    };

    return multerFile;
  }
}
