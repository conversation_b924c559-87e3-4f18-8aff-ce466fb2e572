import { Inject, Injectable } from '@nestjs/common';
import { CreateProcessConditionDto } from '../controller/process/dtos/create-process-condition.dto';
import { ProcessConditionModel } from '../domain/model/process-condition.model';
import { IProcessConditionRepository } from '../domain/repositories/process-condition.repository';

@Injectable()
export class ProcessConditionUsecases {
  constructor(
    @Inject(IProcessConditionRepository)
    private readonly processConditionRepository: IProcessConditionRepository,
  ) {}
  async createProcessCondition(
    createProcessConditionDto: CreateProcessConditionDto,
  ): Promise<ProcessConditionModel> {
    return await this.processConditionRepository.createProcessCondition(
      createProcessConditionDto,
    );
  }

  async deleteProcessConditionByProcessId(processId: string): Promise<void> {
    await this.processConditionRepository.deleteProcessConditionByProcessId(
      processId,
    );
  }
}
