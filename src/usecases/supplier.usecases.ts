import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { ImportSupplierDto } from '../controller/import/dtos/import-supplier.dto';
import { CreateSupplierSectorDto } from '../controller/supplier-sector/dtos/create-supplier-sector.dto';
import { CreateSupplierDto } from '../controller/supplier/dtos/create-supplier.dto';
import { GetDetailSupplierDto } from '../controller/supplier/dtos/get-detail-supplier.dto';
import { GetSupplierListByIdsDto } from '../controller/supplier/dtos/get-supplier-list-by-ids.dto';
import { GetSupplierListDto } from '../controller/supplier/dtos/get-supplier-list.dto';
import { UpdateSupplierDto } from '../controller/supplier/dtos/update-supplier.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EAcutalEventFor,
  ISapEventCreate,
} from '../domain/config/enums/actual-spending.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { ESectorStatus } from '../domain/config/enums/sector.enum';
import { ESupplierSectorStatus } from '../domain/config/enums/supplier-sector.enum';
import { EColumnImportSupplier } from '../domain/config/enums/supplier.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { importErrorDetails } from '../domain/messages/error-details/import';
import { supplierErrorDetails } from '../domain/messages/error-details/supplier';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { SupplierSectorModel } from '../domain/model/supplier-sector.model';
import { SupplierModel } from '../domain/model/supplier.model';
import { ISectorRepository } from '../domain/repositories/sector.repository';
import { ISupplierRepository } from '../domain/repositories/supplier.repository';
import {
  getStatus,
  getStatusSupplier,
  getSupplierType,
  getTypeSupplier,
  getValueOrResult,
} from '../utils/common';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendPost } from '../utils/http';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { SectorUsecases } from './sector.usecases';
import { SupplierSectorUsecases } from './supplier-sector.usecases';

@Injectable()
export class SupplierUsecases {
  constructor(
    @Inject(ISupplierRepository)
    private readonly supplierRepository: ISupplierRepository,
    private readonly supplierSectorUsecases: SupplierSectorUsecases,
    private readonly sectorUsecases: SectorUsecases,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    @Inject(ISectorRepository)
    private readonly sectorRepository: ISectorRepository,
    private eventEmitter: EventEmitter2,
  ) {}

  async createSupplier(
    data: CreateSupplierDto,
    jwtPayload: any,
    authorization: string,
    isImport: boolean = false,
    isSap: boolean = false,
  ): Promise<SupplierModel> {
    let supplierData;

    if (isSap) {
      supplierData = { ...data, industries: undefined };
    } else {
      await this.verifyDataDto(data, jwtPayload);
      supplierData = data;
    }

    const supplierModel = new SupplierModel({
      ...supplierData,
      createdBy: {
        id: jwtPayload?.userId,
        firstName: jwtPayload?.firstName,
        lastName: jwtPayload?.lastName,
        email: jwtPayload?.email,
        phone: jwtPayload?.phone,
        staffId: jwtPayload?.staffId,
        staffCode: jwtPayload?.staffCode,
      },
    });

    const supplier =
      await this.supplierRepository.createSupplier(supplierModel);

    await this.createSupplierSector(
      data.industries,
      supplier,
      jwtPayload,
      isImport,
    );

    this.eventEmitter.emit('sap.supplier.created', {
      code: supplier.code,
      id: supplier.id,
      eventFor: EAcutalEventFor.SUPPLIER,
    } as ISapEventCreate);

    // if (data.deletedAt) {
    //   await sendDelete(
    //     IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(supplier.id),
    //     {
    //       authorization,
    //       'x-api-key': process.env.API_KEY,
    //     },
    //   );
    // } else {
    //   await sendPost(
    //     IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
    //     {
    //       description: supplier.name,
    //       refId: supplier.id,
    //       refCode: supplier.code,
    //       type: EDataRoleType.SUPPLIER,
    //       isEnabled: true,
    //       platform: EPlatform.E_PURCHASE,
    //     },
    //     { authorization, 'x-api-key': process.env.API_KEY },
    //   );
    // }

    return await this.getSupplierDetail({ id: supplier.id }, jwtPayload);
  }

  async updateSupplier(
    id: string,
    updateSupplierDto: UpdateSupplierDto,
    jwtPayload: any,
    authorization: string,
    isImport: boolean = false,
    isSap: boolean = false,
  ): Promise<SupplierModel> {
    let detail;
    if (!isSap) {
      detail = await this.verifyDataDto(updateSupplierDto, jwtPayload, id);
    }

    const supplier = await this.supplierRepository.updateSupplier(id, {
      ...updateSupplierDto,
      industries: undefined,
    });

    await this.createSupplierSector(
      updateSupplierDto.industries,
      supplier,
      jwtPayload,
      isImport,
    );

    // if (updateSupplierDto.deletedAt) {
    //   await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
    //     authorization,
    //     'x-api-key': process.env.API_KEY,
    //   });
    // } else {
    //   await sendPatch(
    //     IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
    //     {
    //       description: supplier.name,
    //       refCode: supplier.code,
    //       isEnabled: true,
    //     },
    //     {
    //       authorization,
    //       'x-api-key': process.env.API_KEY,
    //     },
    //   );
    // }

    if (detail.code != updateSupplierDto.code) {
      this.eventEmitter.emit('sap.supplier.created', {
        code: supplier.code,
        id: supplier.id,
        eventFor: EAcutalEventFor.SUPPLIER,
      } as ISapEventCreate);
    }

    return await this.getSupplierDetail({ id: supplier.id }, jwtPayload);
  }

  // async getSupplierById(id: string): Promise<SupplierModel> {
  //   const supplier = await this.supplierRepository.getSupplierById(id);

  //   if (!supplier) {
  //     throw new HttpException(
  //       supplierErrorDetails.E_3000(),
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   return supplier;
  // }

  async getSuppliers(
    conditions: GetSupplierListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<SupplierModel>> {
    return await this.supplierRepository.getSuppliers(conditions, jwtPayload);
  }

  async deleteSupplier(
    id: string,
    jwtPayload: any,
    authorization: string,
    isSap: boolean = false,
  ): Promise<void> {
    if (!isSap) {
      await this.getSupplierDetail(
        plainToInstance(GetDetailSupplierDto, {
          id,
        }),
        jwtPayload,
      );
    }
    await this.supplierSectorUsecases.deleteSupplierSectors(id);
    await this.supplierRepository.deleteSupplier(id);

    // await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
    //   authorization,
    //   'x-api-key': process.env.API_KEY,
    // });
  }

  async checkExistSupplierByCode(code: string, id?: string): Promise<void> {
    const supplier = await this.supplierRepository.getSupplierByCode(code, id);

    if (supplier) {
      throw new HttpException(
        supplierErrorDetails.E_4001(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async verifyDataDto(
    conditions: CreateSupplierDto | UpdateSupplierDto,
    jwtPayload: any,
    id?: string,
  ) {
    let detail;
    if (id) {
      detail = await this.getSupplierDetail(
        plainToInstance(GetDetailSupplierDto, {
          id,
        }),
        jwtPayload,
      );
    }

    await this.checkExistSupplierByCode(conditions.code, id);

    return detail;
  }

  async getSupplierDetail(conditions: GetDetailSupplierDto, jwtPayload: any) {
    const detail = await this.supplierRepository.getSupplierDetail(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        supplierErrorDetails.E_4000(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return detail;
  }

  async createSupplierSector(
    data: CreateSupplierSectorDto[],
    supplier: SupplierModel,
    jwtPayload: any,
    isImport: boolean = false,
  ) {
    const sectorIds = [
      ...new Set((data || []).map((item) => item.sectorId).filter(Boolean)),
    ];
    const sectors = await this.sectorUsecases.getSectorByIds(
      sectorIds,
      jwtPayload,
    );

    // const supplier = await this.getSupplierDetail(
    //   plainToInstance(GetDetailSupplierDto, {
    //     id: supplierId,
    //   }),
    //   jwtPayload,
    // );

    const industries: SupplierSectorModel[] = [];

    for (const supplierSector of data) {
      const sector = sectors.find((item) => item.id == supplierSector.sectorId);

      if (!sector) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1036()),
          HttpStatus.NOT_FOUND,
        );
      }
      const checkSupplierSector = (supplier.industries || []).find(
        (item) => item.sector?.id == sector.id,
      );

      if (
        !checkSupplierSector &&
        sector.status == ESectorStatus.IN_ACTIVE &&
        !isImport
      ) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1027()),
          HttpStatus.BAD_REQUEST,
        );
      }

      industries.push(
        new SupplierSectorModel({
          ...supplierSector,
          sector: sector,
          supplier: supplier,
        }),
      );
    }

    await this.supplierSectorUsecases.deleteSupplierSectors(supplier.id);

    await this.supplierSectorUsecases.createManySupplierSector(industries);
  }

  async importSupplier(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.SUPPLIER,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createSupplierDtos: CreateSupplierDto[] = [];
        const updateSupplierDtos: UpdateSupplierDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0].actualRowCount,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportSupplier.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const sectorCodes = rows
          .map((item) =>
            getValueOrResult(
              item,
              EColumnImportSupplier.SECTOR_CODE,
            )?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const [suppliers, sectors] = await Promise.all([
          this.supplierRepository.getSupplierByCodesWithRole(
            [...new Set(codes)],
            jwtPayload,
            false,
          ),
          this.sectorRepository.getSectorsByCodesWithRole(
            [...new Set(sectorCodes)],
            jwtPayload,
            false,
          ),
        ]);

        for (const worksheet of workbook.worksheets) {
          const dataCreate = [];
          const dataUpdate = [];

          const rows = worksheet.getRows(1, worksheet.actualRowCount);
          for (let i = 1; i < rows.length; i++) {
            const row = rows[i];

            const code = getValueOrResult(
              row,
              EColumnImportSupplier.CODE,
            )?.toString();

            const codeSAP = getValueOrResult(
              row,
              EColumnImportSupplier.CODE_SAP,
            )?.toString();

            const name = getValueOrResult(
              row,
              EColumnImportSupplier.NAME,
            )?.toString();

            const sectorCode = getValueOrResult(
              row,
              EColumnImportSupplier.SECTOR_CODE,
            )?.toString();

            const type = getSupplierType(
              getValueOrResult(row, EColumnImportSupplier.TYPE)?.toString(),
            );

            const phone = getValueOrResult(
              row,
              EColumnImportSupplier.PHONE,
            )?.toString();

            const fax = getValueOrResult(
              row,
              EColumnImportSupplier.FAX,
            )?.toString();

            const address = getValueOrResult(
              row,
              EColumnImportSupplier.ADDRESS,
            )?.toString();

            const businessLicenseNumber = getValueOrResult(
              row,
              EColumnImportSupplier.BUSINESS_LICENSE_NUMBER,
            )?.toString();

            const contactPerson = getValueOrResult(
              row,
              EColumnImportSupplier.CONTACT_PERSON,
            )?.toString();

            const transactionCurrency = getValueOrResult(
              row,
              EColumnImportSupplier.TRANSACTION_CURRENCY,
            )?.toString();

            const paymentMethod = getValueOrResult(
              row,
              EColumnImportSupplier.PAYMENT_METHOD,
            )?.toString();

            const note = getValueOrResult(
              row,
              EColumnImportSupplier.NOTE,
            )?.toString();

            const status = getStatus(
              SupplierSectorModel,
              getValueOrResult(row, EColumnImportSupplier.STATUS)?.toString(),
            );

            const deleted =
              getValueOrResult(row, EColumnImportSupplier.DELETED)
                ?.toString()
                ?.toLowerCase() == 'x';

            const supplierObject = {
              id: undefined,
              code,
              name,
              type,
              address,
              phone,
              fax,
              businessLicenseNumber,
              contactPerson,
              transactionCurrency,
              paymentMethod,
              note,
              industries: [],
              updatedAt: moment()
                .add(i * 500, 'milliseconds')
                .toISOString(),
              deletedAt: deleted
                ? moment()
                    .add(i * 500, 'milliseconds')
                    .toISOString()
                : undefined,
            };

            if (!code) {
              errors.push({
                error: supplierErrorDetails.E_4003(),
                row: i + 1,
              });
            } else {
              const checkCode = suppliers.find((item) => item.code == code);

              if (checkCode) {
                supplierObject.id = checkCode.id;
              }
            }

            if (!name) {
              errors.push({
                error: supplierErrorDetails.E_4004(),
                row: i + 1,
              });
            }

            if (!type) {
              errors.push({
                error: supplierErrorDetails.E_4007(),
                row: i + 1,
              });
            }

            if (!codeSAP) {
              errors.push({
                error: supplierErrorDetails.E_4005(),
                row: i + 1,
              });
            }

            if (!sectorCode) {
              errors.push({
                error: supplierErrorDetails.E_4006(),
                row: i + 1,
              });
            } else {
              const checkSector = sectors.find(
                (item) => item.code == sectorCode,
              );
              if (!checkSector) {
                errors.push({
                  error: getErrorMessage(errorMessage.E_1036()),
                  row: i + 1,
                });
              } else {
                supplierObject.industries.push({
                  codeSAP,
                  status,
                  sectorId: checkSector.id,
                });
              }
            }

            if (supplierObject.id) {
              const supplierDto = plainToInstance(
                UpdateSupplierDto,
                supplierObject,
              );
              dataUpdate.push(supplierDto);
            } else {
              const supplierDto = plainToInstance(CreateSupplierDto, {
                ...supplierObject,
                createdAt: moment()
                  .add(i * 500, 'milliseconds')
                  .toISOString(),
              });
              dataCreate.push(supplierDto);
            }
          }

          const createGroups = this.groupDuplicateObjectsData(dataCreate);

          const updateGroups = this.groupDuplicateObjectsData(dataUpdate);

          createSupplierDtos.push(...createGroups);
          updateSupplierDtos.push(...updateGroups);
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportSupplierDto = {
          dataSuppliers: createSupplierDtos,
          dataUpdateSuppliers: updateSupplierDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_SUPPLIER(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  public groupDuplicateObjectsData(objects): any {
    const groups = [];

    // Sử dụng mảng để lưu trữ các nhóm object trùng nhau
    for (let i = 0; i < objects.length; i++) {
      let foundGroup = false;
      for (const group of groups) {
        // Kiểm tra xem object hiện tại có thuộc nhóm nào không

        if (this.areObjectsEqualCode(objects[i], group[0])) {
          group.push(objects[i]);
          foundGroup = true;
          break;
        }
      }
      // Nếu không tìm thấy nhóm nào phù hợp, tạo nhóm mới
      if (!foundGroup) {
        groups.push([objects[i]]);
      }
    }

    // Lọc ra những nhóm có nhiều hơn một object (tức là các nhóm trùng nhau)
    const dataGroups = [];
    for (let i = 0; i < groups.length; i++) {
      let data = { ...groups[i][0], industries: [] };
      for (let j = 0; j < groups[i].length; j++) {
        data = {
          ...groups[i][j],
          industries: data.industries,
        };

        data.industries.push(...(groups[i][j].industries ?? []));
      }

      dataGroups.push(data);
    }
    return dataGroups;
  }

  private areObjectsEqualCode(obj1, obj2): any {
    // Tạo bản sao của các object mà không có trường investments
    const obj1WithoutInvestments = {
      code: obj1.code,
      industries: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };
    const obj2WithoutInvestments = {
      code: obj2.code,
      industries: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };

    // So sánh các thuộc tính khác của hai object
    return _.isEqual(obj1WithoutInvestments, obj2WithoutInvestments);
  }

  async listByCodes(codes: string[], jwtPayload: any) {
    return await this.supplierRepository.getSupplierByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
    );
  }

  async exportSupplier(conditions: GetSupplierListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.supplierRepository.getSuppliers(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-supplier.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toSupplierModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-supplier.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toSupplierModel(suppliers: SupplierModel[]) {
    const items = [];

    for (let i = 0; i < suppliers.length; i++) {
      const sectors = (suppliers[i].industries || [])
        .filter((item) => item.status == ESupplierSectorStatus.ACTIVE)
        .map((data) => data.sector?.name || '')
        .filter(Boolean);

      items.push({
        code: suppliers[i].code || '',
        name: suppliers[i].name || '',
        type: getTypeSupplier(suppliers[i].type),
        phone: suppliers[i].phone || '',
        fax: suppliers[i].fax || '',
        address: suppliers[i].address || '',
        sectors: sectors.join(', '),
        transactionCurrency: suppliers[i].transactionCurrency || '',
        paymentMethod: suppliers[i].paymentMethod || '',
        note: suppliers[i].note || '',
        status: getStatusSupplier(
          sectors.length
            ? ESupplierSectorStatus.ACTIVE
            : ESupplierSectorStatus.INACTIVE,
        ),
      });
    }

    return items;
  }

  async getListByIds(
    conditions: GetSupplierListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<SupplierModel>> {
    return await this.supplierRepository.getListByIds(conditions, jwtPayload);
  }
}
