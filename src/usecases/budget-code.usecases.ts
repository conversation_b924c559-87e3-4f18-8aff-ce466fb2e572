import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import { CreateBudgetCodeDto } from '../controller/budget-code/dtos/create-budget-code.dto';
import { DeleteBudgetCodeDto } from '../controller/budget-code/dtos/delete-budget-code.dto';
import { GetBudgetCodeListDto } from '../controller/budget-code/dtos/get-budget-code-list.dto';
import { GetDetailBudgetCodeDto } from '../controller/budget-code/dtos/get-detail-budget-code.dto';
import { UpdateBudgetCodeDto } from '../controller/budget-code/dtos/update-budget-code.dto';
import {
  EBudgetCodeStatus,
  EColumnImportBudgetCode,
} from '../domain/config/enums/budget-code.enum';
import { EBusinessOwnerStatus } from '../domain/config/enums/business-owner.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import {
  TErrorMessageImport,
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { BudgetCodeModel } from '../domain/model/budget-code.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { IBudgetCodeRepository } from '../domain/repositories/budget-code.repository';
import { IBusinessOwnerRepository } from '../domain/repositories/business-owner.repository';

import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment';
import { resolve } from 'path';
import { GetBudgetCodeListByIdsDto } from '../controller/budget-code/dtos/get-budget-code-list-by-ids.dto';
import { GetDetailBusinessOwnerDto } from '../controller/business-owner/dtos/get-detail-business-owner.dto';
import { GetDetailCostDto } from '../controller/cost/dtos/get-detail-cost.dto';
import { ImportBudgetCodeDto } from '../controller/import/dtos/import-budget-code.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EAcutalEventFor,
  ISapEventCreate,
} from '../domain/config/enums/actual-spending.enum';
import { EBudgetType } from '../domain/config/enums/budget.enum';
import { ECostStatus } from '../domain/config/enums/cost.enum';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { costErrorDetails } from '../domain/messages/error-details/cost';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  checkValuesEmptyRowExcel,
  getBudgetCodeType,
  getStatus,
  getStatusBudgetCode,
  getValueOrResult,
} from '../utils/common';
import { BusinessOwnerUsecases } from './business-owner.usecases';
import { CostUsecases } from './cost.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';

@Injectable()
export class BudgetCodeUsecases {
  constructor(
    @Inject(IBudgetCodeRepository)
    private readonly budgetCodeRepository: IBudgetCodeRepository,
    @Inject(IBusinessOwnerRepository)
    private readonly businessOwnerRepository: IBusinessOwnerRepository,
    private readonly businessOwnerUsescases: BusinessOwnerUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private readonly costUsecases: CostUsecases,
    private readonly fileUsecases: FileUsecases,
    private eventEmitter: EventEmitter2,
  ) {}

  async createBudgetCode(
    data: CreateBudgetCodeDto,
    jwtPayload: any,
    isImport: boolean = false,
  ): Promise<BudgetCodeModel> {
    await this.checkCodeBudgetCode(data.code);

    if (!isImport) {
      const businessOwner =
        await this.businessOwnerUsescases.getDetailBusinessOwner(
          plainToInstance(GetDetailBusinessOwnerDto, {
            id: data.businessOwnerId,
          }),
          jwtPayload,
        );

      if (!businessOwner) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1003()),
          HttpStatus.NOT_FOUND,
        );
      }

      if (businessOwner.status == EBusinessOwnerStatus.IN_ACTIVE) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1004()),
          HttpStatus.NOT_FOUND,
        );
      }

      if (data.costId) {
        const cost = await this.costUsecases.getDetailCost(
          plainToInstance(GetDetailCostDto, {
            id: data.costId,
          }),
          jwtPayload,
        );

        if (!cost) {
          throw new HttpException(
            getErrorMessage(costErrorDetails.E_6024()),
            HttpStatus.NOT_FOUND,
          );
        }

        if (cost.status == ECostStatus.IN_ACTIVE) {
          throw new HttpException(
            getErrorMessage(costErrorDetails.E_6026()),
            HttpStatus.NOT_FOUND,
          );
        }
      }
    }

    const createBudgetCode = new BudgetCodeModel({
      ...data,
    });

    const budgetCode =
      await this.budgetCodeRepository.createBudgetCode(createBudgetCode);

    await this.budgetCodeRepository.updateBudgetCode({
      ...budgetCode,
    });

    this.eventEmitter.emit('sap.budgetCode.created', {
      code: budgetCode.code,
      id: budgetCode.id,
      eventFor: EAcutalEventFor.BUDGET_CODE,
      budgetType: budgetCode.budgetType,
    } as ISapEventCreate);

    return await this.getBudgetCodeById(budgetCode.id);
  }

  async getBudgetCodeById(id: string): Promise<BudgetCodeModel> {
    return await this.budgetCodeRepository.getBudgetCodeById(id);
  }

  async getBudgetCodes(
    conditions: GetBudgetCodeListDto,
    jwtPayload,
  ): Promise<ResponseDto<BudgetCodeModel>> {
    return await this.budgetCodeRepository.getBudgetCodes(
      conditions,
      jwtPayload,
    );
  }

  async deleteBudgetCode(
    conditions: DeleteBudgetCodeDto,
    jwtPayload,
  ): Promise<void> {
    const budgetCode = await this.budgetCodeRepository.getBudgetCodeDetail(
      plainToInstance(GetDetailBudgetCodeDto, {
        id: conditions.id,
        businessOwnerCodes: conditions.businessOwnerCodes,
      }),
      jwtPayload,
    );
    if (!budgetCode) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1001()),
        HttpStatus.NOT_FOUND,
      );
    }

    return await this.budgetCodeRepository.deleteBudgetCode(conditions.id);
  }

  async updateBudgetCode(
    id: string,
    data: UpdateBudgetCodeDto,
    jwtPayload,
    isImport: boolean = false,
  ): Promise<BudgetCodeModel> {
    await this.checkCodeBudgetCode(data.code, id);

    let budgetCode;
    if (!isImport) {
      const budgetCode = await this.budgetCodeRepository.getBudgetCodeDetail(
        plainToInstance(GetDetailBudgetCodeDto, {
          id: id,
          businessOwnerCodes: data.businessOwnerCodes,
        }),
        jwtPayload,
      );
      if (!budgetCode) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1001()),
          HttpStatus.NOT_FOUND,
        );
      }

      //Kiểm tra xem business owner có hay không
      if (budgetCode.businessOwnerId != data.businessOwnerId) {
        const businessOwner =
          await this.businessOwnerUsescases.getDetailBusinessOwner(
            plainToInstance(GetDetailBusinessOwnerDto, {
              id: data.businessOwnerId,
            }),
            jwtPayload,
          );

        if (!businessOwner) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1003()),
            HttpStatus.NOT_FOUND,
          );
        }

        if (businessOwner.status == EBusinessOwnerStatus.IN_ACTIVE) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1004()),
            HttpStatus.NOT_FOUND,
          );
        }
      }

      if (data.costId) {
        if (budgetCode.costId != data.costId) {
          const cost = await this.costUsecases.getDetailCost(
            plainToInstance(GetDetailCostDto, {
              id: data.costId,
            }),
            jwtPayload,
          );

          if (!cost) {
            throw new HttpException(
              getErrorMessage(costErrorDetails.E_6024()),
              HttpStatus.NOT_FOUND,
            );
          }

          if (cost.status == ECostStatus.IN_ACTIVE) {
            throw new HttpException(
              getErrorMessage(costErrorDetails.E_6026()),
              HttpStatus.NOT_FOUND,
            );
          }
        }
      } else {
        data.costId = null;
      }
    }

    const updateBudgetCode = new BudgetCodeModel({
      id: id,
      ...data,
    });

    const newBudget =
      await this.budgetCodeRepository.updateBudgetCode(updateBudgetCode);

    if (budgetCode && budgetCode.code != data.code) {
      this.eventEmitter.emit('sap.budgetCode.created', {
        code: newBudget.code,
        id: newBudget.id,
        eventFor: EAcutalEventFor.BUDGET_CODE,
        budgetType: newBudget.budgetType,
      } as ISapEventCreate);
    }

    return await this.getBudgetCodeById(id);
  }

  async getBudgetCodeByCode(code: string): Promise<BudgetCodeModel> {
    return await this.budgetCodeRepository.getBudgetCodeByCode(code);
  }

  async importBudgetCode(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.BUDGET_CODE,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createBudgetCodeDtos: CreateBudgetCodeDto[] = [];
        const updateBudgetCodeDtos: UpdateBudgetCodeDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportBudgetCode.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const costCodes = [
          ...new Set(
            rows
              .map((item) => {
                if (
                  getBudgetCodeType(
                    getValueOrResult(
                      item,
                      EColumnImportBudgetCode.TYPE,
                    )?.toString(),
                  ) == EBudgetType.OPEX
                ) {
                  return getValueOrResult(
                    item,
                    EColumnImportBudgetCode.COST_OR_INTERNAL_ORDER,
                  )?.toString();
                }
              })
              ?.slice(1)
              ?.filter(Boolean),
          ),
        ];

        const businessOwnerCodes = [
          ...new Set(
            _.flatten(
              rows
                .map((item) =>
                  getValueOrResult(
                    item,
                    EColumnImportBudgetCode.BUSINESS_OWNER_CODE,
                  )?.toString(),
                )
                ?.slice(1)
                ?.filter(Boolean),
            ),
          ),
        ];

        const [budgetCodeCodes, costs, businessOwners] = await Promise.all([
          this.budgetCodeRepository.getBudgetCodesByCodesWithRole(
            [...new Set(codes)],
            jwtPayload,
            false,
          ),
          this.costUsecases.getCostsByCodesWithRole(costCodes, jwtPayload),
          this.businessOwnerRepository.getBusinessOwnersByCodesWithRole(
            businessOwnerCodes,
            jwtPayload,
          ),
        ]);

        let totalRowHasValue = 0;

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportBudgetCode.TYPE, // First Cell
            EColumnImportBudgetCode.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;
          ///Data từng row trong file excel
          const type = getBudgetCodeType(
            getValueOrResult(row, EColumnImportBudgetCode.TYPE)?.toString(),
          );
          const code = getValueOrResult(
            row,
            EColumnImportBudgetCode.CODE,
          )?.toString(); //Mã ngân sách
          const name = getValueOrResult(
            row,
            EColumnImportBudgetCode.NAME,
          )?.toString(); //Tên
          const costOrInternalOrder = getValueOrResult(
            row,
            EColumnImportBudgetCode.COST_OR_INTERNAL_ORDER,
          )?.toString(); //Cost center code
          const businessOwnerCode = getValueOrResult(
            row,
            EColumnImportBudgetCode.BUSINESS_OWNER_CODE,
          )?.toString(); //Khối chủ sở hữu
          const status = getStatus(
            BudgetCodeModel,
            getValueOrResult(row, EColumnImportBudgetCode.STATUS)?.toString(),
          ); //Trạng thái

          const budgetCodeObject = {
            id: undefined,
            name,
            code,
            status,
            budgetType: type,
            internalOrder:
              type == EBudgetType.CAPEX ? costOrInternalOrder : null,
            businessOwnerId: null,
            costId: null,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };

          ///Kiểm tra các field required
          if (!code) {
            errors.push({
              error: getErrorMessage(errorMessage.E_1038()),
              row: i + 1,
            });
          } else {
            const checkCode = budgetCodeCodes.find((item) => item.code == code);

            if (checkCode) {
              budgetCodeObject.id = checkCode?.id;
            }

            const checkDup = codes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1072()),
                row: i + 1,
              });
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(errorMessage.E_1042()),
              row: i + 1,
            });
          }

          if (!status) {
            errors.push({
              error: getErrorMessage(errorMessage.E_1040()),
              row: i + 1,
            });
          } else {
            if (!(status in EBudgetCodeStatus)) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1051()),
                row: i + 1,
              });
            }
          }

          if (!businessOwnerCode) {
            errors.push({
              error: getErrorMessage(errorMessage.E_1039()),
              row: i + 1,
            });
          } else {
            ///Kiểm tra business owner
            const businessOwner = businessOwners.find(
              (item) => item.code == businessOwnerCode,
            );

            if (!businessOwner) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1003(),
                  `BUSINESS_OWNER_CODES_NOT_FOUND_${businessOwnerCode}`,
                ),
                row: i + 1,
              });
            }

            if (businessOwner?.status == EBusinessOwnerStatus.IN_ACTIVE) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1004(),
                  `BUSINESS_OWNER_CODES_IN_ACTIVE_${businessOwnerCode}`,
                ),
                row: i + 1,
              });
            }

            budgetCodeObject.businessOwnerId = businessOwner?.id;
          }

          if (!type) {
            errors.push({
              error: getErrorMessage(errorMessage.E_1083()),
              row: i + 1,
            });
          } else {
            if (type == EBudgetType.CAPEX && !costOrInternalOrder) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1084()),
                row: i + 1,
              });
            }

            if (type == EBudgetType.OPEX && !costOrInternalOrder) {
              errors.push({
                error: getErrorMessage(errorMessage.E_1085()),
                row: i + 1,
              });
            }
          }

          if (costOrInternalOrder && type == EBudgetType.OPEX) {
            const cost = costs.find((item) => item.code == costOrInternalOrder);
            if (!cost) {
              errors.push({
                error: getErrorMessage(
                  costErrorDetails.E_6024(),
                  `COST_${costOrInternalOrder}_NOT_FOUND`,
                ),
                row: i + 1,
              });
            } else {
              if (cost?.status == ECostStatus.IN_ACTIVE) {
                errors.push({
                  error: getErrorMessage(
                    costErrorDetails.E_6026(),
                    `COST_${costOrInternalOrder}_IN_ACTIVE`,
                  ),
                  row: i + 1,
                });
              }

              budgetCodeObject.costId = cost.id;
            }
          }

          if (budgetCodeObject.id) {
            const budgetCodeDto = plainToInstance(UpdateBudgetCodeDto, {
              ...budgetCodeObject,
              createdAt: undefined,
            });

            updateBudgetCodeDtos.push(budgetCodeDto);
          } else {
            const budgetCodeDto = plainToInstance(
              CreateBudgetCodeDto,
              budgetCodeObject,
            );

            createBudgetCodeDtos.push(budgetCodeDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportBudgetCodeDto = {
          dataBudgetCodes: createBudgetCodeDtos,
          fileImportHistoryId: fileImportHistory.id,
          dataUpdateBudgetCodes: updateBudgetCodeDtos,
        };
        // await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
        //   importBody: importBody,
        //   importHeader: {
        //     authorization,
        //     'x-api-key': process.env.API_KEY,
        //   },
        //   importUrl: PurchaseServiceApiUrlsConst.IMPORT_BUDGET_CODE(),
        //   updateStatusFileUrl:
        //     PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
        //       fileImportHistory.id,
        //     ),
        // });

        await this.import(importBody, EFileImportType.BUDGET_CODE);

        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.SUCCESS,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getBudgetCodeDetail(
    conditions: GetDetailBudgetCodeDto,
    jwtPayload,
  ): Promise<BudgetCodeModel> {
    const detail = await this.budgetCodeRepository.getBudgetCodeDetail(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1001()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async checkCodeBudgetCode(code: string, id?: string) {
    const checkCode = await this.budgetCodeRepository.getBudgetCodeByCode(
      code,
      id,
    );

    if (checkCode) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1002()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getBudgetCodeByIds(ids: string[], jwtPayload: any) {
    const budgetCodes = await this.budgetCodeRepository.getBudgetCodeByIds(
      ids,
      jwtPayload,
    );

    if (!budgetCodes || !budgetCodes.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1001()),
        HttpStatus.NOT_FOUND,
      );
    }

    const activeBudgetCodes = budgetCodes?.filter(
      (item) => item.status === EBudgetCodeStatus.ACTIVE,
    );

    const activeBudgetCodeIds = activeBudgetCodes.map((item) => item.id);

    const missingBudgetCodeIds = _.difference(ids, activeBudgetCodeIds);

    if (missingBudgetCodeIds.length) {
      throw new HttpException(
        getErrorMessage(
          errorMessage.E_1001(
            `Budget CodeIds ids ${missingBudgetCodeIds.join(', ')} not found or inactive`,
          ),
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return budgetCodes;
  }

  async getBudgetCodeByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) {
    return await this.budgetCodeRepository.getBudgetCodesByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }

  async exportBudgetCodes(conditions: GetBudgetCodeListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.budgetCodeRepository.getBudgetCodes(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-budget-code.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toBudgetCodeModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-budget-code.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toBudgetCodeModel(budgetCodes: BudgetCodeModel[]) {
    const items = [];

    for (let i = 0; i < budgetCodes.length; i++) {
      items.push({
        code: budgetCodes[i].code || '',
        name: budgetCodes[i].name || '',
        budgetCodeType: budgetCodes[i].budgetType || '',
        costCode:
          budgetCodes[i].cost?.code || budgetCodes[i].internalOrder || '',
        businessOwnerCodes: budgetCodes[i].businessOwner?.code,
        status: getStatusBudgetCode(budgetCodes[i].status),
      });
    }

    return items;
  }

  async import(
    body: ImportBudgetCodeDto,
    type: EFileImportType,
    jwtPayload?: IAuthUserPayload,
    authorization?: string,
  ) {
    const dataBudgetCode = body as ImportBudgetCodeDto;
    for (let i = 0; i < dataBudgetCode.dataBudgetCodes.length; i++) {
      await this.createBudgetCode(
        dataBudgetCode.dataBudgetCodes[i],
        jwtPayload,
        true,
      );
    }

    for (let i = 0; i < dataBudgetCode.dataUpdateBudgetCodes.length; i++) {
      await this.updateBudgetCode(
        dataBudgetCode.dataUpdateBudgetCodes[i].id,
        dataBudgetCode.dataUpdateBudgetCodes[i],
        jwtPayload,
        true,
      );
    }
  }

  async getListByIds(
    conditions: GetBudgetCodeListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<BudgetCodeModel>> {
    return await this.budgetCodeRepository.getListByIds(conditions, jwtPayload);
  }

  async getBudgetCodeByInternalOrderAndFunctionalArea(
    jwtPayload: any,
    functionalAreas?: string[],
    internalOrders?: string[],
  ): Promise<BudgetCodeModel[]> {
    return await this.budgetCodeRepository.getBudgetCodeByInternalOrderAndFunctionalArea(
      jwtPayload,
      functionalAreas,
      internalOrders,
    );
  }
}
