import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { GetListPurchaseRequestDto } from '../controller/purchase-request/dtos/get-list-purchase-request.dto';
import { ResponseDto } from '../domain/dtos/response.dto';
import { PurchaseRequestModel } from '../domain/model/purchase_request.model';
import { IPurchaseRequestRepository } from '../domain/repositories/purchase-request.repository';
import { IApprovalLevelRepository } from '../domain/repositories/approval-level.repository';

@Injectable()
export class PurchaseRequestUsecases {
  constructor(
    @Inject(IPurchaseRequestRepository)
    private readonly purchaseRequestRepository: IPurchaseRequestRepository,
    @Inject(IApprovalLevelRepository)
    private readonly prApprovalFlowRepository: IApprovalLevelRepository,
  ) {}

  async getListPurchaseRequests(
    conditions: GetListPurchaseRequestDto,
    jwtPayload: any,
    isNeedDetails: boolean,
  ): Promise<ResponseDto<PurchaseRequestModel>> {
    return await this.purchaseRequestRepository.getListPurchaseRequest(
      conditions,
      jwtPayload,
      isNeedDetails,
    );
  }

  async findOne(
    id: number,
    authorization: string,
  ): Promise<PurchaseRequestModel> {
    // Tìm dữ liệu PurchaseRequestEntity bằng id
    let item = await this.purchaseRequestRepository.findOne(id);
    const approvalLevels =
      await this.prApprovalFlowRepository.findApprovalLevels(id, null);

    if (!item) {
      throw new NotFoundException(`Purchase request with id ${id} not found`);
    }

    const sortedLevels = approvalLevels.sort((a, b) => a.level - b.level);
    item.levels = sortedLevels;

    return item;
  }
}
