import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as _ from 'lodash';
import { GetBusinessOwnerListDto } from '../controller/business-owner/dtos/get-business-owner-list.dto';
import { GetBusinessUnitListDto } from '../controller/business-unit/dtos/get-business-unit-list.dto';
import { GetCompanyListDto } from '../controller/company/dtos/get-company-list.dto';
import { GetDepartmentListDto } from '../controller/department/dtos/get-department-list.dto';
import { GetFunctionUnitListDto } from '../controller/function-unit/dtos/get-function-unit-list.dto';
import { GetSectorListDto } from '../controller/sector/dtos/get-sector-list.dto';
import { CreateManagerDto } from '../controller/staff/dtos/create-manger.dto';
import { CreateStaffDto } from '../controller/staff/dtos/create-staff.dto';
import { GetDetailStaffDto } from '../controller/staff/dtos/get-detail-staff.dto';
import { GetStaffByCodesDto } from '../controller/staff/dtos/get-staff-by-codes.dto';
import { GetStaffByEmailsDto } from '../controller/staff/dtos/get-staff-by-emails.dto';
import { GetStaffListDto } from '../controller/staff/dtos/get-staff-list.dto';
import { UpdateStaffDto } from '../controller/staff/dtos/update-staff.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { staffErrorDetails } from '../domain/messages/error-details/staff';
import { StaffHierarchyModel } from '../domain/model/staff-hierarchy.model';
import { StaffModel } from '../domain/model/staff.model';
import { IStaffRepository } from '../domain/repositories/staff.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { getData, sendDelete, sendPatch } from '../utils/http';
import { BusinessOwnerUsecases } from './business-owner.usecases';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CompanyUsecases } from './company.usecases';
import { DepartmentUsecases } from './department.usecases';
import { FunctionUnitUsecases } from './function-unit.usecases';
import { PositionUsecases } from './position.usecases';
import { SectorUsecases } from './sector.usecases';
import { StaffHierarchyUsecases } from './staff-hierarchy.usecases';
import { GetApproverByPositionDto } from '../controller/staff/dtos/get-approver-by-position.dto';
import { ResponseDto } from '../domain/dtos/response.dto';

@Injectable()
export class StaffUsecases {
  constructor(
    private readonly sectorUsecases: SectorUsecases,
    private readonly companyUsecases: CompanyUsecases,
    private readonly businessUnitUsecases: BusinessUnitUsecases,
    private readonly departmentUsecases: DepartmentUsecases,
    private readonly functionUnitUsecases: FunctionUnitUsecases,
    private readonly businessOwnerUsecases: BusinessOwnerUsecases,
    private readonly positionUsecases: PositionUsecases,
    private readonly staffHierarchyUsecases: StaffHierarchyUsecases,
    @Inject(IStaffRepository)
    private readonly staffRepository: IStaffRepository,
  ) {}
  async createStaff(
    data: CreateStaffDto,
    jwtPayload: IAuthUserPayload,
  ): Promise<any> {
    const [existEmail, existCode] = await Promise.all([
      this.staffRepository.getStaffByEmail(data.email),
      this.staffRepository.getStaffByCode(data.code),
    ]);

    if (existEmail) {
      throw new HttpException(
        staffErrorDetails.E_3001(),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (existCode) {
      throw new HttpException(
        staffErrorDetails.E_3002(),
        HttpStatus.BAD_REQUEST,
      );
    }

    let staffModel = new StaffModel({
      firstName: data.firstName,
      lastName: data.lastName,
      code: data.code,
      email: data.email,
      phone: data.phone,
      status: data.status,
      createdBy: {
        id: jwtPayload?.userId,
        firstName: jwtPayload?.firstName,
        lastName: jwtPayload?.lastName,
        email: jwtPayload?.email,
        phone: jwtPayload?.phone,
        staffId: jwtPayload?.staffId,
        staffCode: jwtPayload?.staffCode,
      },
      poCreatorId: data.poCreatorId,
      // purchaserId: data.purchaserId,
      position: await this.positionUsecases.getDetailPosition(
        { id: data.positionId },
        jwtPayload,
      ),
    });

    const portfolio = await this.handlePortfolioStaff(data, jwtPayload);

    if (Object.keys(portfolio).length) {
      staffModel = { ...staffModel, ...portfolio };
    }

    const staff = await this.staffRepository.createStaff(staffModel);

    await this.assignManagerToStaff(staff, data.managers || [], jwtPayload);

    return staff;
  }

  private async handlePortfolioStaff(
    data: CreateStaffDto | UpdateStaffDto,
    jwtPayload: any,
  ) {
    const portfolio = {};
    if (data.sectorIds && data.sectorIds.length) {
      const { results } = await this.sectorUsecases.getSectors(
        { ids: data.sectorIds, getAll: 1 } as GetSectorListDto,
        jwtPayload,
      );

      if (
        !results ||
        !results.length ||
        results.length != data.sectorIds.length
      ) {
        throw new HttpException(
          staffErrorDetails.E_3003(
            'Sectors not found or access denied for this sectors.',
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      portfolio['sectors'] = results;
    }

    if (data.companyIds && data.companyIds.length) {
      const { results } = await this.companyUsecases.getCompanies(
        { ids: data.companyIds, getAll: 1 } as GetCompanyListDto,
        jwtPayload,
      );

      if (
        !results ||
        !results.length ||
        results.length != data.companyIds.length
      ) {
        throw new HttpException(
          staffErrorDetails.E_3003(
            'Companies not found or access denied for this companies.',
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      portfolio['companies'] = results;
    }

    if (data.businessUnitIds && data.businessUnitIds.length) {
      const { results } = await this.businessUnitUsecases.getBusinessUnits(
        { ids: data.businessUnitIds, getAll: 1 } as GetBusinessUnitListDto,
        jwtPayload,
      );

      if (
        !results ||
        !results.length ||
        results.length != data.businessUnitIds.length
      ) {
        throw new HttpException(
          staffErrorDetails.E_3003(
            'Business units not found or access denied for this business units.',
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      portfolio['businessUnits'] = results;
    }

    if (data.departmentIds && data.departmentIds.length) {
      const { results } = await this.departmentUsecases.getDepartments(
        { ids: data.departmentIds, getAll: 1 } as GetDepartmentListDto,
        jwtPayload,
      );

      if (
        !results ||
        !results.length ||
        results.length != data.departmentIds.length
      ) {
        throw new HttpException(
          staffErrorDetails.E_3003(
            'Departments not found or access denied for this departments.',
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      portfolio['departments'] = results;
    }

    if (data.functionUnitIds && data.functionUnitIds.length) {
      const { results } = await this.functionUnitUsecases.getFunctionUnits(
        { ids: data.functionUnitIds, getAll: 1 } as GetFunctionUnitListDto,
        jwtPayload,
      );

      if (
        !results ||
        !results.length ||
        results.length != data.functionUnitIds.length
      ) {
        throw new HttpException(
          staffErrorDetails.E_3003(
            'Function units not found or access denied for this function units.',
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      portfolio['functionUnits'] = results;
    }

    if (data.businessOwnerIds && data.businessOwnerIds.length) {
      const { results } = await this.businessOwnerUsecases.getBusinessOwners(
        { ids: data.businessOwnerIds, getAll: 1 } as GetBusinessOwnerListDto,
        jwtPayload,
      );

      if (
        !results ||
        !results.length ||
        results.length != data.businessOwnerIds.length
      ) {
        throw new HttpException(
          staffErrorDetails.E_3003(
            'Business Owners not found or access denied for this Business owners.',
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      portfolio['businessOwners'] = results;
    }

    if (data.poCreatorId) {
      const poCreator = await this.staffRepository.getDetailStaff(
        plainToInstance(GetDetailStaffDto, {
          staffId: data.poCreatorId,
        }),
        jwtPayload,
      );

      if (!poCreator) {
        throw new HttpException(
          staffErrorDetails.E_3000(),
          HttpStatus.NOT_FOUND,
        );
      }
    }

    if (data.purchaserIds && data.purchaserIds.length) {
      const { results } = await this.staffRepository.getStaffList(
        { ids: data.purchaserIds, getAll: 1 } as GetStaffListDto,
        jwtPayload,
      );

      if (
        !results ||
        !results.length ||
        results.length != data.purchaserIds.length
      ) {
        throw new HttpException(
          staffErrorDetails.E_3003(
            'Sectors not found or access denied for this purchasers.',
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      portfolio['purchasers'] = results;
    }

    // if (data.purchaserId) {
    //   const purchaser = await this.staffRepository.getDetailStaff(
    //     plainToInstance(GetDetailStaffDto, {
    //       staffId: data.purchaserId,
    //     }),
    //     jwtPayload,
    //   );

    //   if (!purchaser) {
    //     throw new HttpException(
    //       staffErrorDetails.E_3000(),
    //       HttpStatus.NOT_FOUND,
    //     );
    //   }
    // }

    return portfolio;
  }

  private async assignManagerToStaff(
    subordinateModel: StaffModel,
    createManagerDtos: CreateManagerDto[],
    jwtPayload: any,
  ) {
    const existManagementLevel =
      await this.staffHierarchyUsecases.getExistManagementLevel(
        subordinateModel.id,
      );

    for (const createManagerDto of createManagerDtos) {
      if (subordinateModel.id === createManagerDto.managerId) {
        throw new HttpException(
          staffErrorDetails.E_3005(),
          HttpStatus.BAD_REQUEST,
        );
      }
      const existLevel = existManagementLevel?.find(
        (item) => item.level === createManagerDto.level,
      );

      if (existLevel) {
        throw new HttpException(
          staffErrorDetails.E_3004(
            `The level ${createManagerDto.level} is already exist.`,
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      const manager = await this.getDetailStaff(
        { staffId: createManagerDto.managerId },
        jwtPayload,
      );

      const staffHierarchyModel = new StaffHierarchyModel({
        level: createManagerDto.level,
        subordinate: subordinateModel,
        manager: manager,
      });

      await this.staffHierarchyUsecases.createStaffHierarchy(
        staffHierarchyModel,
      );
    }
  }

  async getDetailStaff(
    conditions: GetDetailStaffDto,
    jwtPayload: any,
  ): Promise<StaffModel> {
    const staff = await this.staffRepository.getDetailStaff(
      conditions,
      jwtPayload,
    );

    if (!staff) {
      throw new HttpException(staffErrorDetails.E_3000(), HttpStatus.NOT_FOUND);
    }

    if (staff.managerHierarchies && staff.managerHierarchies.length) {
      staff.managers = staff.managerHierarchies.map((item) => {
        const managerModel = new StaffModel({
          id: item.manager.id,
          firstName: item.manager.firstName,
          lastName: item.manager.lastName,
          code: item.manager.code,
          level: item.level,
          email: item.manager.email,
        });

        return managerModel;
      });

      delete staff.managerHierarchies;
    }

    return staff;
  }

  async deleteStaff(
    staffId: string,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ): Promise<void> {
    const staff = await this.getDetailStaff({ staffId }, jwtPayload);

    staff.deletedBy = {
      id: jwtPayload?.userId,
      firstName: jwtPayload?.firstName,
      lastName: jwtPayload?.lastName,
      email: jwtPayload?.email,
      phone: jwtPayload?.phone,
      staffId: jwtPayload?.staffId,
      staffCode: jwtPayload?.staffCode,
    };

    await this.staffRepository.updateStaff(staff);

    await this.staffRepository.deleteStaff(staffId);

    await sendDelete(
      IdentityServiceApiUrlsConst.DELETE_ACCOUNT_BY_STAFF_ID(staff.id),
      { authorization },
    );
  }

  async getStaffs(
    conditions: GetStaffListDto,
    jwtPayload: IAuthUserPayload,
  ): Promise<ResponseDto<StaffModel>> {
    return await this.staffRepository.getStaffList(conditions, jwtPayload);
  }

  async updateStaff(
    staffId: string,
    data: UpdateStaffDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<StaffModel> {
    let staff = await this.getDetailStaff({ staffId }, jwtPayload);

    if (data.poCreatorId && staffId == data.poCreatorId) {
      throw new HttpException(
        staffErrorDetails.E_3006(),
        HttpStatus.BAD_REQUEST,
      );
    }

    // if (data.purchaserId && staffId == data.purchaserId) {
    //   throw new HttpException(
    //     staffErrorDetails.E_3007(),
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    staff.updatedBy = {
      id: jwtPayload?.userId,
      firstName: jwtPayload?.firstName,
      lastName: jwtPayload?.lastName,
      email: jwtPayload?.email,
      phone: jwtPayload?.phone,
      staffId: jwtPayload?.staffId,
      staffCode: jwtPayload?.staffCode,
    };

    const portfolio = await this.handlePortfolioStaff(data, jwtPayload);

    if (Object.keys(portfolio).length) {
      staff = { ...staff, ...portfolio };
    }

    staff.managerHierarchies = [];
    staff.position = await this.positionUsecases.getDetailPosition(
      { id: data.positionId },
      jwtPayload,
    );
    staff = await this.staffRepository.updateStaff(Object.assign(staff, data));

    await this.assignManagerToStaff(staff, data.managers || [], jwtPayload);

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_ACCOUNT(staff.id),
      {
        staffCode: staff.code,
        firstName: staff.firstName,
        lastName: staff.lastName,
        email: staff.email,
        status: staff.status,
      },
      { authorization },
    );

    return staff;
  }

  async getStaffByCodes(
    conditions: GetStaffByCodesDto,
    jwtPayload: any,
  ): Promise<StaffModel[]> {
    return await this.staffRepository.getStaffByCodes(conditions, jwtPayload);
  }

  async getStaffByIds(
    conditions: GetStaffListDto,
    jwtPayload: any,
  ): Promise<StaffModel[]> {
    return await this.staffRepository.getStaffByIds(conditions, jwtPayload);
  }

  async getStaffByEmails(
    conditions: GetStaffByEmailsDto,
    jwtPayload: any,
  ): Promise<StaffModel[]> {
    const staffs = await this.staffRepository.getStaffByEmails(
      conditions,
      jwtPayload,
    );

    const staffEmails = (staffs ?? []).map((item) => item.email);

    const diffEmails = _.difference(conditions.emails, staffEmails);

    if (diffEmails.length) {
      throw new HttpException(
        staffErrorDetails.E_3000(`Emails ${diffEmails.join(', ')} not found`),
        HttpStatus.BAD_REQUEST,
      );
    }

    return staffs;
  }

  async approverByPosition(
    conditions: GetApproverByPositionDto,
    jwtPayload: any,
    authorization: string,
  ) {
    const accountsReponse = await getData(
      IdentityServiceApiUrlsConst.GET_ACCOUNTS_APPROVER(),
      { authorization },
      {
        ...conditions,
      },
    );
    const accounts = accountsReponse?.data?.data || [];
    const staffIds = accounts?.map((item) => item.staffId).filter(Boolean);
    conditions.staffIds = staffIds;

    const staffs = await this.staffRepository.approverByPosition(conditions);

    const staffsPriority = [];
    for (let i = 0; i < staffs.length; i++) {
      const account = accounts.find((item) => item.staffId == staffs[i].id);
      const refIds =
        account?.user?.roles
          ?.flatMap((item) => item?.dataPermissions || [])
          .map((item) => item?.dataRole?.refId)
          .filter(Boolean) || [];

      const matchBusinessUnit = refIds.filter((item) =>
        (conditions.businessUnitIds || []).includes(item),
      )?.length
        ? true
        : false;
      const matchDepartment = refIds.filter((item) =>
        (conditions.departmentIds || []).includes(item),
      )?.length
        ? true
        : false;
      const matchFunctionUnit = refIds.filter((item) =>
        (conditions.functionUnitIds || []).includes(item),
      )?.length
        ? true
        : false;

      let priority = 5;
      if (matchBusinessUnit && matchDepartment && matchFunctionUnit) {
        priority = 1;
      } else if (matchBusinessUnit && matchFunctionUnit) {
        priority = 2;
      } else if (matchBusinessUnit && matchDepartment) {
        priority = 3;
      } else if (matchFunctionUnit && matchDepartment) {
        priority = 4;
      }

      staffsPriority.push({
        ...staffs[i],
        priority,
      });
    }
    staffsPriority.sort((a, b) => a.priority - b.priority);
    return staffsPriority;
  }
}
