import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import * as Excel from 'exceljs';
import _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { ActualSpendingForReportDto } from '../controller/actual-spending/dtos/actual-spending-for-report.dto';
import { ConfirmMultiActualSpendingDto } from '../controller/actual-spending/dtos/confirm-multi-actual-spending.dto';
import { CreateActualSpendingForSapDto } from '../controller/actual-spending/dtos/create-actual-spending-for-sap.dto';
import { CreateActualSpendingListDto } from '../controller/actual-spending/dtos/create-actual-spending-list.dto';
import { CreateActualSpendingDto } from '../controller/actual-spending/dtos/create-actual-spending.dto';
import { GetActualSpendingListDto } from '../controller/actual-spending/dtos/get-list-actual-spending.dto';
import { UpdateActualSpendingDto } from '../controller/actual-spending/dtos/update-actual-spending.dto';
import { ImportActualSpendingDto } from '../controller/import/dtos/import-actual-spending.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EActualType,
  EAcutalEventFor,
  EColumnImportActualSpending,
  EExportActualSpendingCapex,
  EExportActualSpendingOpex,
  EStatusActualSpending,
  ISapEventCreate,
} from '../domain/config/enums/actual-spending.enum';
import { EBudgetType } from '../domain/config/enums/budget.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { importErrorDetails } from '../domain/messages/error-details/import';
import { supplierErrorDetails } from '../domain/messages/error-details/supplier';
import {
  buildErrorDetail,
  errorMessage,
  getErrorMessage,
  TErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { ActualSapResponseModel } from '../domain/model/actual-sap-response.model';
import { ActualSpendingModel } from '../domain/model/actual-spending.model';
import { CurrencyUnitModel } from '../domain/model/currency-unit.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { IActualSpendingRepository } from '../domain/repositories/actual-spending.repository';
import {
  codeToIdMap,
  excelSerialToDate,
  getColumnNumber,
  getValueOrResult,
  handleCurrencyConversion,
  processInBatches,
} from '../utils/common';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendPost } from '../utils/http';
import { BudgetCodeUsecases } from './budget-code.usecases';
import { BusinessOwnerUsecases } from './business-owner.usecases';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CompanyUsecases } from './company.usecases';
import { CostcenterSubaccountUsecases } from './costcenter-subaccount.usecases';
import { CurrencyUnitUsecases } from './currency-unit.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { SapPurchaseOrderUsecases } from './sap-purchase-order.usecases';
import { SupplierUsecases } from './supplier.usecases';

@Injectable()
export class ActualSpendingUsecases {
  constructor(
    private budgetCodeUsecases: BudgetCodeUsecases,
    private companyUsecases: CompanyUsecases,
    private costcenterSubaccountUsecases: CostcenterSubaccountUsecases,
    private businessUnitUsecases: BusinessUnitUsecases,
    private supplierUsecases: SupplierUsecases,
    private currencyUnitUsecases: CurrencyUnitUsecases,
    private fileUsecases: FileUsecases,
    private fileImportHistoryUsecases: FileImportHistoryUsecases,
    private businessOwnerUsecases: BusinessOwnerUsecases,
    @Inject(IActualSpendingRepository)
    private readonly actualSpendingRepository: IActualSpendingRepository,
    private readonly sapPurchaseOrderUsecases: SapPurchaseOrderUsecases,
  ) {}

  async createActualSpendingListByCode(
    createActualSpendingListDto: CreateActualSpendingListDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<ActualSapResponseModel[]> {
    const { actualSpendingList, actualType } = createActualSpendingListDto;
    const {
      dataBudgetCodes,
      dataCompanies,
      dataCostCenters,
      dataBusinessUnits,
      dataSuppliers,
      dataCurrencyUnits,
      dataSapPos,
    } = await this.validateInputActualSpendingByCode(
      actualSpendingList,
      jwtPayload,
      authorization,
    );

    const createActualSpendingDtos: CreateActualSpendingDto[] = [];
    const responseSaps: ActualSapResponseModel[] = [];

    for (const createActualSpendingForSapDto of actualSpendingList) {
      // const period = moment(createActualSpendingForSapDto.period, 'MM/YYYY');

      const createActualSpendingDto: CreateActualSpendingDto = {
        ...createActualSpendingForSapDto,
        // periodMonth: period.month() + 1,
        // periodYear: period.year(),
        // budgetCodeId: createActualSpendingForSapDto.budgetCodeCode
        //   ? dataBudgetCodes[createActualSpendingForSapDto.budgetCodeCode]
        //   : null,
        companyId: createActualSpendingForSapDto.companyCode
          ? dataCompanies[createActualSpendingForSapDto.companyCode]
          : null,
        costCenterId: createActualSpendingForSapDto.costCenterCode
          ? dataCostCenters[createActualSpendingForSapDto.costCenterCode]?.id
          : null,
        buId: createActualSpendingForSapDto.buCode
          ? dataBusinessUnits[createActualSpendingForSapDto.buCode]
          : null,
        supplierId: createActualSpendingForSapDto.supplierCode
          ? dataSuppliers[createActualSpendingForSapDto.supplierCode]?.id
          : null,
        supplierName: !createActualSpendingForSapDto.supplierName
          ? createActualSpendingForSapDto.supplierCode
            ? dataSuppliers[createActualSpendingForSapDto.supplierCode]?.name
            : null
          : createActualSpendingForSapDto.supplierName,
        currencyId: createActualSpendingForSapDto.currencyCode
          ? dataCurrencyUnits[createActualSpendingForSapDto.currencyCode]
          : null,
        localCurrencyId: createActualSpendingForSapDto.localCurrencyCode
          ? dataCurrencyUnits[createActualSpendingForSapDto.localCurrencyCode]
          : null,
        poSapId: createActualSpendingForSapDto.poSapId?.toString()
          ? dataSapPos[createActualSpendingForSapDto.poSapId.toString()]
          : null,
        costCenterName: !createActualSpendingForSapDto.costCenterName
          ? createActualSpendingForSapDto.costCenterCode
            ? dataSuppliers[createActualSpendingForSapDto.costCenterCode]?.name
            : null
          : createActualSpendingForSapDto.costCenterName,
        actualType,

        companyCode: createActualSpendingForSapDto.companyCode,
        costCenterCode: createActualSpendingForSapDto.costCenterCode,
        buCode: createActualSpendingForSapDto.buCode,
        supplierCode: createActualSpendingForSapDto.supplierCode,
        currencyCode: createActualSpendingForSapDto.currencyCode,
        localCurrencyCode: createActualSpendingForSapDto.localCurrencyCode,
      };

      if (createActualSpendingDto.functionalArea) {
        createActualSpendingDto.budgetCodeId = dataBudgetCodes.find(
          (item) =>
            item.code === createActualSpendingDto.functionalArea &&
            item.budgetType === EBudgetType.OPEX,
        )?.id;
      } else if (
        !createActualSpendingDto.functionalArea &&
        createActualSpendingDto.internalOrder
      ) {
        createActualSpendingDto.budgetCodeId = dataBudgetCodes.find(
          (item) =>
            item.internalOrder === createActualSpendingDto.internalOrder &&
            item.budgetType === EBudgetType.CAPEX,
        )?.id;
      } else {
        createActualSpendingDto.budgetCodeId = null;
      }

      const responseSap: ActualSapResponseModel = {
        sapActualId: createActualSpendingForSapDto.sapActualId,
        errorStatus: 'SUCCESSFUL',
        errorDetails: this.buildErrorDetails(
          createActualSpendingDto,
          createActualSpendingForSapDto,
        ),
      };

      responseSap.errorStatus = responseSap.errorDetails.length
        ? 'FAILED'
        : responseSap.errorStatus;

      responseSaps.push(responseSap);

      if (responseSap.errorDetails?.length) {
        createActualSpendingDto.status = EStatusActualSpending.SAP_SYNC_FAIL;
      }

      createActualSpendingDtos.push(createActualSpendingDto);
    }

    if (createActualSpendingDtos.length) {
      try {
        await processInBatches(createActualSpendingDtos, 500, async (batch) => {
          await this.createOrUpdateActualSpendingList(
            batch,
            jwtPayload,
            actualType,
          );
        });
      } catch (error) {
        responseSaps.forEach((item) => {
          item.errorStatus = 'FAILED';
          item.errorDetails = [
            buildErrorDetail('0000', 'Internal Server Error', error.message),
          ];
        });
      }
    }

    return responseSaps;
  }

  async createOrUpdateActualSpendingList(
    createActualSpendingDtos: CreateActualSpendingDto[],
    jwtPayload: any,
    actualType?: EActualType,
  ): Promise<ActualSpendingModel[]> {
    const conditions: any[] = [];

    // Gom nhóm các điều kiện theo loại
    if (actualType === EActualType.INVOICE_PAYMENT_DAILY) {
      conditions.push(
        ...createActualSpendingDtos.map((dto) => ({
          documentNumber: dto.documentNumber,
          postingDate: dto.postingDate,
          entryDate: dto.entryDate,
        })),
      );
    } else if (actualType === EActualType.ASSET_MONTHLY) {
      conditions.push(
        ...createActualSpendingDtos.map((dto) => ({
          assetCode: dto.assetCode,
          postingDate: dto.postingDate,
        })),
      );
    } else if (actualType === EActualType.FUNCTIONALAREA_UPDATE) {
      conditions.push(
        ...createActualSpendingDtos
          .filter((dto) => dto.functionalArea) // Chỉ xử lý các dòng có Functional Area
          .map((dto) => ({
            documentNumber: dto.documentNumber,
            postingDate: dto.postingDate,
          })),
      );
    }

    // Truy vấn tất cả các bản ghi trùng lặp
    const existingRecords = await this.findExistingRecordsByType(
      actualType,
      conditions,
    );

    const dtosToInsert: CreateActualSpendingDto[] = [];
    const dtosToUpdate: UpdateActualSpendingDto[] = [];

    for (const dto of createActualSpendingDtos) {
      const key: string | null = this.generateKey(
        actualType,
        dto.documentNumber,
        dto.entryDate,
        dto.assetCode,
        dto.postingDate,
        dto.sapActualId,
      );

      // Xác định key phù hợp với từng loại
      // if (
      //   actualType === EActualType.INVOICE_PAYMENT_DAILY &&
      //   dto.documentNumber &&
      //   dto.entryDate
      // ) {
      //   key = `${dto.documentNumber}_${dto.postingDate}_${dto.entryDate}`;
      // } else if (actualType === EActualType.ASSET_MONTHLY && dto.assetCode) {
      //   key = `${dto.assetCode}_${dto.postingDate}`;
      // } else if (
      //   actualType === EActualType.FUNCTIONALAREA_UPDATE &&
      //   dto.documentNumber
      // ) {
      //   key = `${dto.documentNumber}_${dto.postingDate}`;
      // }

      const existingRecord = key ? existingRecords.get(key) : null;

      if (existingRecord) {
        // Nếu tồn tại, thêm vào danh sách cập nhật
        dtosToUpdate.push({
          ...dto,
          id: existingRecord.id,
          updatedBy: {
            id: jwtPayload?.userId,
            firstName: jwtPayload?.firstName,
            lastName: jwtPayload?.lastName,
            email: jwtPayload?.email,
            phone: jwtPayload?.phone,
            staffId: jwtPayload?.staffId,
            staffCode: jwtPayload?.staffCode,
          },
        });
      } else {
        dto.createdBy = {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        };
        // Nếu không tồn tại, thêm vào danh sách tạo mới
        if (actualType !== EActualType.FUNCTIONALAREA_UPDATE) {
          dtosToInsert.push(dto);
        }
      }
    }

    // Ghi mới và cập nhật
    const [createdActuals, updatedActuals] = await Promise.all([
      this.actualSpendingRepository.createActualSpendingList(dtosToInsert),
      this.actualSpendingRepository.updateActualSpendingList(dtosToUpdate),
    ]);

    return [...createdActuals, ...updatedActuals];
  }

  private async findExistingRecordsByType(
    actualType: EActualType,
    conditions: any[],
  ): Promise<Map<string, ActualSpendingModel>> {
    let queryResults: ActualSpendingModel[] = [];

    if (actualType === EActualType.INVOICE_PAYMENT_DAILY) {
      queryResults = await this.actualSpendingRepository.findExistingRecords({
        invoiceConditions: conditions,
      });
    } else if (actualType === EActualType.ASSET_MONTHLY) {
      queryResults = await this.actualSpendingRepository.findExistingRecords({
        assetConditions: conditions,
      });
    } else if (actualType === EActualType.FUNCTIONALAREA_UPDATE) {
      queryResults = await this.actualSpendingRepository.findExistingRecords({
        functionalAreaConditions: conditions,
      });
    }

    // Tạo Map để tra cứu nhanh
    return new Map(
      queryResults.map((record) => {
        const key = this.generateKey(
          actualType,
          record.documentNumber,
          record.entryDate,
          record.assetCode,
          record.postingDate,
          record.sapActualId,
        );
        return [key, record];
      }),
    );
  }

  private generateKey(
    actualType: EActualType,
    documentNumber?: string,
    entryDate?: Date | string,
    assetCode?: string,
    postingDate?: Date | string,
    sapActualId?: number,
  ): string | null {
    if (
      actualType === EActualType.INVOICE_PAYMENT_DAILY &&
      documentNumber &&
      entryDate
    ) {
      return `${documentNumber}_${postingDate}_${entryDate}_${sapActualId}`;
    }

    if (actualType === EActualType.ASSET_MONTHLY && assetCode) {
      return `${assetCode}_${postingDate}_${sapActualId}`;
    }

    if (actualType === EActualType.FUNCTIONALAREA_UPDATE && documentNumber) {
      return `${documentNumber}_${postingDate}_${sapActualId}`;
    }

    return null; // Không tạo được key nếu không khớp điều kiện
  }

  private async validateInputActualSpendingByCode(
    createActualSpendingForSapDtos: CreateActualSpendingForSapDto[],
    jwtPayload: any,
    authorization: string,
  ) {
    const functionalAreas = this.extractCodes(
      createActualSpendingForSapDtos,
      'functionalArea',
    );
    const internalOrders = this.extractCodes(
      createActualSpendingForSapDtos,
      'internalOrder',
    );
    const companyCodes = this.extractCodes(
      createActualSpendingForSapDtos,
      'companyCode',
    );
    const costCenterCodes = this.extractCodes(
      createActualSpendingForSapDtos,
      'costCenterCode',
    );
    const buCodes = this.extractCodes(createActualSpendingForSapDtos, 'buCode');
    const supplierCodes = this.extractCodes(
      createActualSpendingForSapDtos,
      'supplierCode',
    );
    const currencyCodes = [
      ...this.extractCodes(createActualSpendingForSapDtos, 'currencyCode'),
      ...this.extractCodes(createActualSpendingForSapDtos, 'localCurrencyCode'),
    ];
    const sapPoIds = this.extractCodes(
      createActualSpendingForSapDtos,
      'poSapId',
    );
    // Fetch all data concurrently
    const [
      budgetCodes,
      companies,
      costCenters,
      businessUnits,
      suppliers,
      currencyUnits,
      sapPoResponse,
    ] = await Promise.all([
      this.budgetCodeUsecases.getBudgetCodeByInternalOrderAndFunctionalArea(
        jwtPayload,
        functionalAreas as string[],
        internalOrders as string[],
      ),
      this.companyUsecases.listByCodes(companyCodes as string[], jwtPayload),
      this.costcenterSubaccountUsecases.getCostCenterByCodes(
        costCenterCodes as string[],
        jwtPayload,
      ),
      this.businessUnitUsecases.listByCodes(buCodes as string[], jwtPayload),
      this.supplierUsecases.listByCodes(supplierCodes as string[], jwtPayload),
      this.currencyUnitUsecases.listByCodes(
        currencyCodes as string[],
        jwtPayload,
      ),
      this.sapPurchaseOrderUsecases.getListSapPurchaseOrderByIds([
        ...new Set(sapPoIds.map((id) => Number(id))),
      ]),
    ]);

    // Organize results in a structured return object
    return {
      dataBudgetCodes: budgetCodes,
      dataCompanies: codeToIdMap(companies),
      dataCostCenters: codeToIdMap(costCenters, 'code', ['id', 'name']),
      dataBusinessUnits: codeToIdMap(businessUnits),
      dataSuppliers: codeToIdMap(suppliers, 'code', ['id', 'name']),
      dataCurrencyUnits: codeToIdMap(currencyUnits, 'currencyCode'),
      dataSapPos: codeToIdMap(sapPoResponse || [], 'id'),
    };
  }

  // Extract and structure all required codes
  private extractCodes(
    dtos: CreateActualSpendingForSapDto[],
    key: keyof CreateActualSpendingForSapDto,
  ) {
    return dtos.map((item) => item[key]).filter(Boolean);
  }

  // Kiểm tra lỗi trả về cho SAP
  private buildErrorDetails(
    createDto: CreateActualSpendingDto,
    originalDto: CreateActualSpendingForSapDto,
  ): TErrorMessage[] {
    const errors: TErrorMessage[] = [];

    // if (!createDto.budgetCodeId) {
    //   errors.push(
    //     errorMessage.E_1015(
    //       `Mã ngân sách ${originalDto.budgetCodeCode} không tìm thấy`,
    //     ),
    //   );
    // }

    if (originalDto.companyCode && !createDto.companyId) {
      errors.push(
        errorMessage.E_1024(
          `Mã công ty ${originalDto.companyCode} không tìm thấy`,
        ),
      );
    }

    if (originalDto.costCenterCode && !createDto.costCenterId) {
      errors.push(
        errorMessage.E_1005(
          `Mã Cost Center ${originalDto.costCenterCode} không tìm thấy`,
        ),
      );
    }

    if (originalDto.buCode && !createDto.buId) {
      errors.push(
        errorMessage.E_1022(`Mã BU ${originalDto.buCode} không tìm thấy`),
      );
    }

    if (originalDto.supplierCode && !createDto.supplierId) {
      errors.push(
        supplierErrorDetails.E_4000(
          `Mã nhà cung cấp ${originalDto.supplierCode} không tìm thấy`,
        ),
      );
    }

    if (originalDto.poSapId && !createDto.poSapId) {
      errors.push(
        errorMessage.E_1090(`Mã PO SAP ${originalDto.poSapId} không tìm thấy`),
      );
    }

    if (!createDto.currencyId) {
      errors.push(
        errorMessage.E_1013(
          `Mã đơn vị tiền tệ ${originalDto.currencyCode} không tìm thấy`,
        ),
      );
    }

    if (originalDto.localCurrencyCode && !createDto.localCurrencyId) {
      errors.push(
        errorMessage.E_1013(
          `Mã đơn vị tiền tệ quy đổi ${originalDto.localCurrencyCode} không tìm thấy`,
        ),
      );
    }

    return errors;
  }

  async getListActualSpending(
    conditions: GetActualSpendingListDto,
    jwtPayload: any,
    authorization: string,
  ) {
    let dataBusinessOwners = {};
    if (conditions.businessOwnerIds?.length) {
      const businessOwners =
        await this.businessOwnerUsecases.getBusinessOwnerByIds(
          conditions.businessOwnerIds,
          jwtPayload,
        );

      dataBusinessOwners =
        businessOwners?.reduce((acc, bo) => {
          bo.budgetCodes?.forEach((bc) => {
            acc[bc.code] = { bosCode: bo.code, bosName: bo.name };
          });
          return acc;
        }, {}) || {};

      conditions.budgetCodeCodes =
        businessOwners
          ?.filter((bo) => bo.budgetCodes?.length)
          ?.map((bo) => bo.budgetCodes)
          ?.flat()
          ?.map((bc) => bc.code) || [];
    }

    const response = await this.actualSpendingRepository.getListActualSpending(
      conditions,
      jwtPayload,
    );

    if (response?.results?.length) {
      if (conditions.budgetCodeType == EBudgetType.CAPEX) {
        response.results.forEach((item) => {
          const bo = item.internalOrder
            ? dataBusinessOwners[item.internalOrder]
            : null;

          if (bo) {
            item.businessOwnerName = bo.bosName;
            item.businessOwnerCode = bo.bosCode;
          }
        });
      } else {
        response.results.forEach((item) => {
          const bo = item.functionalArea
            ? dataBusinessOwners[item.functionalArea]
            : null;

          if (bo) {
            item.businessOwnerName = bo.bosName;
            item.businessOwnerCode = bo.bosCode;
          }
        });
      }

      const sapPoIds = response?.results
        ?.filter((item) => item.poSapId)
        ?.map((item) => Number(item.poSapId));

      if (sapPoIds?.length) {
        try {
          const sapPoResponse =
            await this.sapPurchaseOrderUsecases.getListSapPurchaseOrderByIds(
              sapPoIds,
            );

          const sapPoMaps = codeToIdMap(sapPoResponse || [], 'id', [
            'id',
            'poId',
          ]);

          response.results.forEach((item) => {
            if (item.poSapId) {
              item.poSap = sapPoMaps[item.poSapId];
            }
          });
        } catch (error) {}
      }
    }

    return response;
  }

  async confirmMultiActualSpending(conditions: ConfirmMultiActualSpendingDto) {
    const { actualSpendingIds, diffActualSpendingIds, actualSpendings } =
      await this.getListActualSpendingById(conditions.ids);

    const errors = [];

    if (diffActualSpendingIds.length) {
      diffActualSpendingIds.forEach((id) => {
        errors.push(getErrorMessage(errorMessage.E_1091(`Id ${id} not found`)));
      });
    }

    const acutalSpedingsFail = actualSpendings
      .filter((item) => item.status == EStatusActualSpending.SAP_SYNC_FAIL)
      .filter(Boolean);

    if (acutalSpedingsFail?.length) {
      errors.push(
        getErrorMessage(
          errorMessage.E_1092(
            `Thực chi ${acutalSpedingsFail?.map((item) => item.id).join(', ')} không thể xác nhận đồng bộ thất bại`,
          ),
        ),
      );
    }

    for (const actualSpendingId of actualSpendingIds) {
      try {
        await this.actualSpendingRepository.updateActualSpending({
          id: actualSpendingId,
          status: EStatusActualSpending.CONFIRMED,
        });
      } catch (err) {
        errors.push(err);
      }
    }

    if (errors.length) {
      return { message: 'Some updates failed', errors };
    }

    return { message: 'All updates successful' };
  }

  async getDetailActualSpending(
    actualSpendingId: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<ActualSpendingModel> {
    const actualSpending =
      await this.actualSpendingRepository.getDetailActualSpending(
        actualSpendingId,
        jwtPayload,
      );

    if (!actualSpending) {
      throw new HttpException(
        getErrorMessage(
          errorMessage.E_1091(`Id ${actualSpendingId} not found`),
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      const sapPoResponse =
        await this.sapPurchaseOrderUsecases.getListSapPurchaseOrderByIds([
          Number(actualSpending.poSapId),
        ]);

      actualSpending.poSap = sapPoResponse[0] || null;
    } catch (error) {}

    return actualSpending;
  }

  private async getListActualSpendingById(ids: string[]) {
    const actualSpendings =
      await this.actualSpendingRepository.getListActualSpendingById(ids);

    const actualSpendingIds = actualSpendings?.map((item) => item.id) || [];

    const diffActualSpendingIds = _.difference(ids, actualSpendingIds);

    return {
      actualSpendingIds,
      diffActualSpendingIds,
      actualSpendings,
    };
  }

  async importActualSpending(
    file: Express.Multer.File,
    jwtPayload: any,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );
    if (!fileImport)
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );

    const fileImportHistory =
      await this.fileImportHistoryUsecases.createFileImportHistory(
        new FileImportHistoryModel({
          fileName: fileImport.filename,
          filePath: fileImport.path,
          status: EFileImportStatus.WAITING,
          createdBy: { ...jwtPayload },
          importType: EFileImportType.ACTUAL_SPENDING,
        }),
      );

    try {
      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(Buffer.from(fileImport.buffer));
      const rows =
        workbook.worksheets[0]?.getRows(
          2,
          workbook.worksheets[0].actualRowCount - 1,
        ) ?? [];

      // Extract column values using helper function
      const getColumnValues = (column) =>
        rows
          .map((row) => getValueOrResult(row, column)?.toString())
          .filter(Boolean) || [];

      const [companyCodes, costCenterCodes, buCodes, supplierCodes, poSapIds] =
        [
          EColumnImportActualSpending.COMPANY_CODE,
          EColumnImportActualSpending.COST_CENTER_CODE,
          EColumnImportActualSpending.BU_CODE,
          EColumnImportActualSpending.SUPPLIER_CODE,
          EColumnImportActualSpending.PO_SAP_ID,
        ].map(getColumnValues);

      const currencyCodes = [
        ...getColumnValues(EColumnImportActualSpending.CURRENCY_CODE),
        ...getColumnValues(EColumnImportActualSpending.LOCAL_CURRENCY_CODE),
      ];

      // Fetch data in parallel
      const [
        companies,
        costCenters,
        businessUnits,
        suppliers,
        currencyUnits,
        sapPoResponse,
      ] = await Promise.all([
        this.companyUsecases.listByCodes(companyCodes, jwtPayload),
        this.costcenterSubaccountUsecases.getCostCenterByCodes(
          costCenterCodes,
          jwtPayload,
        ),
        this.businessUnitUsecases.listByCodes(buCodes, jwtPayload),
        this.supplierUsecases.listByCodes(supplierCodes, jwtPayload),
        this.currencyUnitUsecases.listByCodes(currencyCodes, jwtPayload),
        this.sapPurchaseOrderUsecases.getListSapPurchaseOrderByIds([
          ...new Set(poSapIds.map(Number)),
        ]),
      ]);

      // Map data for quick access
      const [
        // dataBudgetCodes,
        dataCompanies,
        dataCostCenters,
        dataBusinessUnits,
        dataSuppliers,
        dataCurrencyUnits,
        dataSapPos,
      ] = [
        companies,
        costCenters,
        businessUnits,
        suppliers,
        currencyUnits,
        sapPoResponse || [],
      ].map((items, index) =>
        codeToIdMap(
          items as any,
          index === 6 ? 'id' : index === 5 ? 'currencyCode' : 'code',
          index === 4 ? ['id', 'name'] : ['id'],
        ),
      );

      const createActualSpendingDtos: CreateActualSpendingDto[] = [];
      const errors: TErrorMessageImport[] = [];
      const currentDate = moment().format('YYYY-MM-DD');

      // Parse date
      const parseDate = (row, column, format = 'YYYY-MM-DD') =>
        moment(
          excelSerialToDate(getValueOrResult(row, column, false, true)),
        ).format(format);

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const rowNumber = row.number;

        const createActualSpendingForSapDto: CreateActualSpendingForSapDto = {
          sapActualId: 268,
          companyCode: getValueOrResult(
            row,
            EColumnImportActualSpending.COMPANY_CODE,
          )?.toString(),
          costCenterCode: getValueOrResult(
            row,
            EColumnImportActualSpending.COST_CENTER_CODE,
          )?.toString(),
          buCode: getValueOrResult(
            row,
            EColumnImportActualSpending.BU_CODE,
          )?.toString(),
          internalOrder: getValueOrResult(
            row,
            EColumnImportActualSpending.INTERNAL_ORDER,
          )?.toString(),
          internalOrderName: getValueOrResult(
            row,
            EColumnImportActualSpending.INTERNAL_ORDER_NAME,
          )?.toString(),
          supplierCode: getValueOrResult(
            row,
            EColumnImportActualSpending.SUPPLIER_CODE,
          )?.toString(),
          supplierName: getValueOrResult(
            row,
            EColumnImportActualSpending.SUPPLIER_NAME,
          )?.toString(),
          poSapId: getValueOrResult(
            row,
            EColumnImportActualSpending.PO_SAP_ID,
            true,
          ),
          docDate: parseDate(row, EColumnImportActualSpending.DOC_DATE),
          postingDate: parseDate(row, EColumnImportActualSpending.POSTING_DATE),
          entryDate:
            parseDate(row, EColumnImportActualSpending.ENTRY_DATE) ||
            currentDate,
          paymentDoc: getValueOrResult(
            row,
            EColumnImportActualSpending.PAYMENT_DOC,
          )?.toString(),
          eInvoiceNumber: getValueOrResult(
            row,
            EColumnImportActualSpending.E_INVOICE_NUMBER,
          )?.toString(),
          documentNumber: getValueOrResult(
            row,
            EColumnImportActualSpending.DOCUMENT_NUMBER,
          )?.toString(),
          inventoryDoc: getValueOrResult(
            row,
            EColumnImportActualSpending.INVENTORY_DOC,
          )?.toString(),
          invoiceNumber: getValueOrResult(
            row,
            EColumnImportActualSpending.INVOICE_NUMBER,
          )?.toString(),
          glAccount: getValueOrResult(
            row,
            EColumnImportActualSpending.GL_ACCOUNT,
          )?.toString(),
          taxCode: getValueOrResult(
            row,
            EColumnImportActualSpending.TAX_CODE,
          )?.toString(),
          taxRate: getValueOrResult(
            row,
            EColumnImportActualSpending.TAX_RATE,
          )?.toString(),
          docAmount: getValueOrResult(
            row,
            EColumnImportActualSpending.DOC_AMOUNT,
            true,
          ),
          currencyCode: getValueOrResult(
            row,
            EColumnImportActualSpending.CURRENCY_CODE,
          )?.toString(),
          localCurrencyAmount: getValueOrResult(
            row,
            EColumnImportActualSpending.LOCAL_CURRENCY_AMOUNT,
            true,
          ),
          localCurrencyCode: getValueOrResult(
            row,
            EColumnImportActualSpending.LOCAL_CURRENCY_CODE,
          )?.toString(),
          exchangeRate: getValueOrResult(
            row,
            EColumnImportActualSpending.EXCHANGE_RATE,
            true,
          ),
          receiverCode: getValueOrResult(
            row,
            EColumnImportActualSpending.RECEIVER_CODE,
          )?.toString(),
          receiverName: getValueOrResult(
            row,
            EColumnImportActualSpending.RECEIVER_NAME,
          )?.toString(),
          profitCenter: getValueOrResult(
            row,
            EColumnImportActualSpending.PROFIT_CENTER,
          )?.toString(),
          profitCenterDescription: getValueOrResult(
            row,
            EColumnImportActualSpending.PROFIT_CENTER_DESCRIPTION,
          )?.toString(),
          profitCenterGroup: getValueOrResult(
            row,
            EColumnImportActualSpending.PROFIT_CENTER_GROUP,
          )?.toString(),
          profitCenterGroupDescription: getValueOrResult(
            row,
            EColumnImportActualSpending.PROFIT_CENTER_GROUP_DESCRIPTION,
          )?.toString(),
          status: EStatusActualSpending.UNCONFIRMED,
          documentType: getValueOrResult(
            row,
            EColumnImportActualSpending.DOCUMENT_TYPE,
          )?.toString(),
          invocieBusinessTransaction: getValueOrResult(
            row,
            EColumnImportActualSpending.INVOICE_BUSINESS_TRANSACTION,
          )?.toString(),
          paymentDate: parseDate(row, EColumnImportActualSpending.PAYMENT_DATE),
          paymentBusinessTransaction: getValueOrResult(
            row,
            EColumnImportActualSpending.PAYMENT_BUSINESS_TRANSACTION,
          )?.toString(),
          payementDocType: getValueOrResult(
            row,
            EColumnImportActualSpending.PAYMENT_DOC_TYPE,
          )?.toString(),
          inventoryDocDate: parseDate(
            row,
            EColumnImportActualSpending.INVENTORY_DOC_DATE,
          ),
          poItem: getValueOrResult(
            row,
            EColumnImportActualSpending.PO_ITEM,
          )?.toString(),
          poDate: parseDate(row, EColumnImportActualSpending.PO_DATE),
          internalOrderType: getValueOrResult(
            row,
            EColumnImportActualSpending.INTERNAL_ORDER_TYPE,
          )?.toString(),
          functionalArea: getValueOrResult(
            row,
            EColumnImportActualSpending.FUNCTIONAL_AREA,
          )?.toString(),
          functionalAreaName: getValueOrResult(
            row,
            EColumnImportActualSpending.FUNCTIONAL_AREA_NAME,
          )?.toString(),
          taxCodeName: getValueOrResult(
            row,
            EColumnImportActualSpending.TAX_CODE_NAME,
          )?.toString(),
          docPaymentAmount: getValueOrResult(
            row,
            EColumnImportActualSpending.DOC_PAYMENT_AMOUNT,
            true,
          ),
          localCurrencyPaymentAmount: getValueOrResult(
            row,
            EColumnImportActualSpending.LOCAL_CURRENCY_PAYMENT_AMOUNT,
            true,
          ),
          debitCreditInd: getValueOrResult(
            row,
            EColumnImportActualSpending.DEBIT_CREDIT_INDICATOR,
          )?.toString(),
          accountType: getValueOrResult(
            row,
            EColumnImportActualSpending.ACCOUNT_TYPE,
          )?.toString(),
          description: getValueOrResult(
            row,
            EColumnImportActualSpending.DESCRIPTION,
          )?.toString(),
          note: getValueOrResult(
            row,
            EColumnImportActualSpending.NOTE,
          )?.toString(),
          assetCode: getValueOrResult(
            row,
            EColumnImportActualSpending.ASSET_CODE,
          )?.toString(),
        };

        const errorRow = await this.validateActualSpending(
          CreateActualSpendingForSapDto,
          createActualSpendingForSapDto,
        );
        if (errorRow?.length) {
          errors.push(
            ...errorRow.map((item) => ({ row: rowNumber, error: item })),
          );
        } else {
          const createActualSpendingDto = {
            ...createActualSpendingForSapDto,
            // periodMonth:
            //   moment(createActualSpendingForSapDto.period, 'MM/YYYY').month() +
            //   1,
            // periodYear: moment(
            //   createActualSpendingForSapDto.period,
            //   'MM/YYYY',
            // ).year(),
            // budgetCodeId: createActualSpendingForSapDto.budgetCodeCode
            //   ? dataBudgetCodes[createActualSpendingForSapDto.budgetCodeCode]
            //   : null,
            companyId: createActualSpendingForSapDto.companyCode
              ? dataCompanies[createActualSpendingForSapDto.companyCode]
              : null,
            costCenterId: createActualSpendingForSapDto.costCenterCode
              ? dataCostCenters[createActualSpendingForSapDto.costCenterCode]
              : null,
            costCenterName: !createActualSpendingForSapDto.costCenterName
              ? createActualSpendingForSapDto.costCenterCode
                ? dataSuppliers[createActualSpendingForSapDto.costCenterCode]
                    ?.name
                : null
              : createActualSpendingForSapDto.costCenterName,
            buId: createActualSpendingForSapDto.buCode
              ? dataBusinessUnits[createActualSpendingForSapDto.buCode]
              : null,
            supplierId: createActualSpendingForSapDto.supplierCode
              ? dataSuppliers[createActualSpendingForSapDto.supplierCode]?.id
              : null,
            supplierName:
              createActualSpendingForSapDto.supplierName ||
              (createActualSpendingForSapDto.supplierCode
                ? dataSuppliers[createActualSpendingForSapDto.supplierCode]
                    ?.name
                : null),
            currencyId: createActualSpendingForSapDto.currencyCode
              ? dataCurrencyUnits[createActualSpendingForSapDto.currencyCode]
              : null,
            localCurrencyId: createActualSpendingForSapDto.localCurrencyCode
              ? dataCurrencyUnits[
                  createActualSpendingForSapDto.localCurrencyCode
                ]
              : null,
            poSapId: createActualSpendingForSapDto.poSapId?.toString()
              ? dataSapPos[createActualSpendingForSapDto.poSapId.toString()]
              : null,
            createdBy: {
              id: jwtPayload?.userId,
              firstName: jwtPayload?.firstName,
              lastName: jwtPayload?.lastName,
              email: jwtPayload?.email,
              phone: jwtPayload?.phone,
              staffId: jwtPayload?.staffId,
              staffCode: jwtPayload?.staffCode,
            },
            companyCode: createActualSpendingForSapDto.companyCode,
            costCenterCode: createActualSpendingForSapDto.costCenterCode,
            buCode: createActualSpendingForSapDto.buCode,
            supplierCode: createActualSpendingForSapDto.supplierCode,
            currencyCode: createActualSpendingForSapDto.currencyCode,
            localCurrencyCode: createActualSpendingForSapDto.localCurrencyCode,
          };

          const errorExists = this.buildErrorDetails(
            createActualSpendingDto,
            createActualSpendingForSapDto,
          );

          if (errorExists?.length) {
            errors.push(
              ...errorExists.map((item) => ({ row: rowNumber, error: item })),
            );
          } else {
            createActualSpendingDtos.push(createActualSpendingDto);
          }
        }
      }

      if (errors.length) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: errors,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );

        throw new HttpException(
          getErrorMessage(errorMessage.E_1041(), {
            totalRow: workbook.worksheets[0].actualRowCount - 1,
            totalRowError: [...new Set(errors.map((item) => item.row))].length,
            errorDetail: errors,
          }),
          HttpStatus.BAD_REQUEST,
        );
      }

      const importBody: ImportActualSpendingDto = {
        createActualSpendingDtos,
        fileImportHistoryId: fileImportHistory.id,
      };
      await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
        importBody: importBody,
        importHeader: {
          authorization,
          'x-api-key': process.env.API_KEY,
        },
        importUrl: PurchaseServiceApiUrlsConst.IMPORT_ACTUAL_SPENDING(),
        updateStatusFileUrl:
          PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
            fileImportHistory.id,
          ),
      });

      return { fileImportHistoryId: fileImportHistory.id };
    } catch (error) {
      const updateFileImportHistory = new FileImportHistoryModel({
        errors: error,
        status: EFileImportStatus.FAIL,
      });
      await this.fileImportHistoryUsecases.updateFileImportHistory(
        fileImportHistory.id,
        updateFileImportHistory,
      );
      throw error;
    }
  }

  private async validateActualSpending<T extends object>(
    dtoClass: new () => T,
    dataRow: any,
  ): Promise<TErrorMessage[] | null> {
    // Tạo instance của DTO từ dữ liệu truyền vào
    const dtoInstance = plainToInstance(dtoClass, dataRow);

    // Validate dữ liệu dựa trên các decorator trong DTO
    const errors = await validate(dtoInstance, {
      skipMissingProperties: false, // Kiểm tra cả những thuộc tính thiếu
      whitelist: true, // Loại bỏ các thuộc tính không tồn tại trong DTO
      forbidNonWhitelisted: true, // Báo lỗi nếu có thuộc tính không có trong DTO
    });

    // Kiểm tra có lỗi không
    if (errors.length > 0) {
      // Chuyển lỗi thành mảng thông báo dễ đọc
      const errorMessages = errors.map((err) => {
        return {
          message: Object.values(err.constraints || {}).join(', '),
          errorCode: 'VALIDATION_ERROR',
        };
      });
      return errorMessages; // Trả về mảng lỗi
    }

    // Nếu không có lỗi, trả về null hoặc dữ liệu hợp lệ
    return null;
  }

  async exportActual(conditions: GetActualSpendingListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const [data, convertCurrency] = await Promise.all(
      [
        this.actualSpendingRepository.getListActualSpending(
          conditions,
          jwtPayload,
        ),
        conditions.currencyId
          ? this.currencyUnitUsecases.getCurrencyUnitDetail(
              { id: conditions.currencyId },
              jwtPayload,
            )
          : null,
      ].filter(Boolean),
    );

    const sourceWorkbook = new Excel.Workbook();
    await sourceWorkbook.xlsx.readFile(
      resolve(
        __dirname,
        conditions.budgetCodeType == EBudgetType.OPEX
          ? '../domain/template/export/template-export-opex-actual.xlsx'
          : '../domain/template/export/template-export-capex-actual.xlsx',
      ),
    );

    const sourceWorksheet = sourceWorkbook.worksheets[0];

    const targetWorkbook = new Excel.Workbook();
    const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

    sourceWorksheet.model['merges'].forEach((merge) =>
      targetWorksheet.mergeCells(merge),
    );
    sourceWorksheet.columns.forEach((sourceColumn, index) => {
      const targetColumn = targetWorksheet.getColumn(index + 1);
      targetColumn.width = sourceColumn.width; // Copy width
    });

    for (let rowNumber = 1; rowNumber <= 1; rowNumber++) {
      const sourceRow = sourceWorksheet.getRow(rowNumber);
      const targetRow = targetWorksheet.getRow(rowNumber);

      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Copy value
        targetCell.style = { ...cell.style }; // Copy full style
        targetCell.border = cell.border; // Copy border
        targetCell.font = cell.font; // Copy font
        targetCell.alignment = cell.alignment; // Copy alignment
        targetCell.numFmt = cell.numFmt; // Copy number format
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet
    }
    if ((data as ResponseDto<ActualSpendingModel>)?.results?.length) {
      const items = this.toReportActualSpending(
        (data as ResponseDto<ActualSpendingModel>)?.results || [],
        conditions.budgetCodeType,
        convertCurrency as CurrencyUnitModel,
      );

      // Lấy danh sách cột từ enum
      const columnCapexMappings = Object.entries(EExportActualSpendingCapex);
      const columnOpexMappings = Object.entries(EExportActualSpendingOpex);

      for (let i = 0; i < items.length; i++) {
        if (conditions.budgetCodeType == EBudgetType.OPEX) {
          for (const [key, value] of columnOpexMappings) {
            const colNumber = getColumnNumber(key);
            const targetCell = targetWorksheet.getCell(`${key}${i + 2}`);
            const sourceCell = sourceWorksheet.getRow(2).getCell(colNumber); // Get format from row 2

            let finalValue = items[i][value]; // Giá trị thực tế
            let numFmt = sourceCell.numFmt; // Format Excel

            // Nếu giá trị rỗng, giữ nguyên ''
            if (
              items[i][value] === '' ||
              items[i][value] === null ||
              items[i][value] === undefined
            ) {
              finalValue = '';
            } else if (sourceCell.numFmt) {
              if (
                sourceCell.numFmt.includes('yyyy') ||
                sourceCell.numFmt.includes('mm') ||
                sourceCell.numFmt.includes('dd')
              ) {
                // Nếu là ngày, giữ đúng kiểu Date
                if (items[i][value] instanceof Date) {
                  finalValue = items[i][value];
                } else {
                  const parsedDate = new Date(items[i][value]);
                  finalValue = isNaN(parsedDate.getTime()) ? '' : parsedDate;
                }
                numFmt = 'dd/mm/yyyy'; // Format ngày
              } else {
                // Nếu là số hoặc tiền tệ, ép kiểu về number nếu có thể
                const numericValue = Number(items[i][value]);
                if (!isNaN(numericValue)) {
                  finalValue = numericValue;
                }
              }
            }

            targetCell.value = finalValue; // Gán giá trị vào ô

            if (numFmt) {
              targetCell.numFmt = numFmt; // Áp dụng định dạng Excel
            }
          }
        }

        if (conditions.budgetCodeType == EBudgetType.CAPEX) {
          for (const [key, value] of columnCapexMappings) {
            const colNumber = getColumnNumber(key);
            const targetCell = targetWorksheet.getCell(`${key}${i + 2}`);
            const sourceCell = sourceWorksheet.getRow(2).getCell(colNumber); // Get format from row 2

            let finalValue = items[i][value]; // Giá trị thực tế
            let numFmt = sourceCell.numFmt; // Format Excel

            // Nếu giá trị rỗng, giữ nguyên ''
            if (
              items[i][value] === '' ||
              items[i][value] === null ||
              items[i][value] === undefined
            ) {
              finalValue = '';
            } else if (sourceCell.numFmt) {
              if (
                sourceCell.numFmt.includes('yyyy') ||
                sourceCell.numFmt.includes('mm') ||
                sourceCell.numFmt.includes('dd')
              ) {
                // Nếu là ngày, giữ đúng kiểu Date
                if (items[i][value] instanceof Date) {
                  finalValue = items[i][value];
                } else {
                  const parsedDate = new Date(items[i][value]);
                  finalValue = isNaN(parsedDate.getTime()) ? '' : parsedDate;
                }
                numFmt = 'dd/mm/yyyy'; // Format ngày
              } else {
                // Nếu là số hoặc tiền tệ, ép kiểu về number nếu có thể
                const numericValue = Number(items[i][value]);
                if (!isNaN(numericValue)) {
                  finalValue = numericValue;
                }
              }
            }

            targetCell.value = finalValue; // Gán giá trị vào ô

            if (numFmt) {
              targetCell.numFmt = numFmt; // Áp dụng định dạng Excel
            }
          }
        }

        // targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }
    }

    const buffer = await targetWorkbook.xlsx.writeBuffer();

    const file = await this.fileUsecases.bufferToMulterFile(
      Buffer.from(buffer),
      conditions.budgetCodeType == EBudgetType.OPEX
        ? 'report-opex-actual.xlsx'
        : 'report-capex-actual.xlsx',
    );

    const uploadedFile = await this.fileUsecases.uploadFile(
      file,
      null,
      exportFileUploadPath,
    );

    return { ...uploadedFile, buffer: null };
  }

  private toReportActualSpending(
    items: ActualSpendingModel[],
    type: EBudgetType,
    convertCurrency?: CurrencyUnitModel,
  ) {
    const datas = [];

    for (const data of items) {
      handleCurrencyConversion(
        data,
        {
          amountField: 'docAmount',
          dateField: 'postingDate',
          currencyField: 'currency',
        },
        convertCurrency,
      );

      const commonData = this.mapCommonFields(data);

      datas.push(commonData);
      // if (type === EBudgetType.CAPEX) {
      //   const dataInterfaceCapex: IExportActualSpendingCapex = {
      //     ...commonData,
      //   };
      //   datas.push(dataInterfaceCapex);
      // }

      // if (type === EBudgetType.OPEX) {
      //   const dataInterfaceOpex: IExportActualSpendingOpex = {
      //     ...commonData,
      //   };
      //   datas.push(dataInterfaceOpex);
      // }
    }

    return datas;
  }

  private mapCommonFields(data: ActualSpendingModel) {
    return {
      companyCode: data.company?.code || '',
      companyName: data.company?.name || '',
      businessUnitCode: data.bu?.code || '',
      businessUnitName: data.bu?.name || '',
      businessOwnerCode: data.businessOwnerCode || '',
      businessOwnerName: data.businessOwnerName || '',
      businessPlace: data.receiverCode || '',
      businessPlaceName: data.receiverName || '',
      profitCenter: data.profitCenter || '',
      profitCenterDescription: data.profitCenterDescription || '',
      profitCenterGroup: data.profitCenterGroup || '',
      profitCenterGroupDescription: data.profitCenterGroupDescription || '',
      entryDate: data.entryDate ? new Date(data.entryDate) : '',
      documentNumber: data.documentNumber || '',
      invoiceNumber: data.invoiceNumber || '',
      eInvoiceNumber: data.eInvoiceNumber || '',
      docDate: data.docDate ? new Date(data.docDate) : '',
      postingDate: data.postingDate ? new Date(data.postingDate) : '',
      documentType: data.documentType || '',
      invocieBusinessTransaction: data.invocieBusinessTransaction || '',
      inventoryDoc: data.inventoryDoc || '',
      inventoryDocDate: data.inventoryDocDate
        ? new Date(data.inventoryDocDate)
        : '',
      poSapId: data.poSap ? data.poSap.poId : '',
      poItem: data.poItem || '',
      poDate: data.poDate ? new Date(data.poDate) : '',
      functionalArea: data.functionalArea || '',
      functionalAreaName: data.functionalAreaName || '',
      costCenterCode: data.costCenter?.code || '',
      costCenterName: data.costCenter?.name || '',
      supplierCode: data.supplierCode || '',
      supplierName: data.supplierName || '',
      glAccount: data.glAccount || '',
      taxCode: data.taxCode || '',
      taxCodeName: data.taxCodeName || '',
      taxRate: Number(data.taxRate || 0),
      docAmount: Number(data.docAmount || 0),
      currencyCode: data.currency?.currencyCode || '',
      localCurrencyAmount: Number(data.localCurrencyAmount || 0),
      localCurrencyCode: data.localCurrency?.currencyCode || '',
      exchangeRate: Number(data.exchangeRate || 1),
      debitCreditInd: data.debitCreditInd || '',
      accountType: data.accountType || '',
      description: data.description || '',
      note: data.note || '',
      status:
        data.status === EStatusActualSpending.CONFIRMED
          ? 'Xác nhận'
          : data.status === EStatusActualSpending.SAP_SYNC_FAIL
            ? 'Đồng bộ thất bại'
            : 'Chưa xác nhận',
      paymentDoc: data.paymentDoc || '',
      paymentDate: data.paymentDate ? new Date(data.paymentDate) : '',
      paymentBusinessTransaction: data.paymentBusinessTransaction || '',
      payementDocType: data.payementDocType || '',
      internalOrder: data.internalOrder || '',
      internalOrderName: data.internalOrderName || '',
      internalOrderType: data.internalOrderType || '',
      docPaymentAmount: Number(data.docPaymentAmount || 0),
      localCurrencyPaymentAmount: Number(data.localCurrencyPaymentAmount || 0),
      assetCode: data.assetCode || '',
    };
  }

  async actualSpendingForReport(conditions: ActualSpendingForReportDto) {
    return await this.actualSpendingRepository.actualSpendingForReport(
      conditions,
    );
  }

  @OnEvent('sap.costcenter.created')
  @OnEvent('sap.company.created')
  @OnEvent('sap.bu.created')
  @OnEvent('sap.supplier.created')
  @OnEvent('sap.currency.created')
  @OnEvent('sap.budgetCode.created')
  async handleCostCenterCreated(payload: ISapEventCreate) {
    let actualSpedings;

    switch (payload.eventFor) {
      case EAcutalEventFor.COST_CENTER:
        actualSpedings =
          await this.actualSpendingRepository.getListActualSpedingForUpdateSyncSap(
            {
              // statuses: [EStatusActualSpending.SAP_SYNC_FAIL],
              costCenterCode: payload.code,
            },
          );
        break;
      case EAcutalEventFor.COMPANY:
        actualSpedings =
          await this.actualSpendingRepository.getListActualSpedingForUpdateSyncSap(
            {
              // statuses: [EStatusActualSpending.SAP_SYNC_FAIL],
              companyCode: payload.code,
            },
          );
        break;
      case EAcutalEventFor.BU:
        actualSpedings =
          await this.actualSpendingRepository.getListActualSpedingForUpdateSyncSap(
            {
              // statuses: [EStatusActualSpending.SAP_SYNC_FAIL],
              buCode: payload.code,
            },
          );
        break;
      case EAcutalEventFor.SUPPLIER:
        actualSpedings =
          await this.actualSpendingRepository.getListActualSpedingForUpdateSyncSap(
            {
              // statuses: [EStatusActualSpending.SAP_SYNC_FAIL],
              supplierCode: payload.code,
            },
          );
        break;
      case EAcutalEventFor.CURRENCY:
        actualSpedings =
          await this.actualSpendingRepository.getListActualSpedingForUpdateSyncSap(
            {
              // statuses: [EStatusActualSpending.SAP_SYNC_FAIL],
              currencyCode: payload.code,
            },
          );
        break;
      case EAcutalEventFor.BUDGET_CODE:
        actualSpedings =
          await this.actualSpendingRepository.getListActualSpedingForUpdateSyncSap(
            {
              // statuses: [EStatusActualSpending.SAP_SYNC_FAIL],
              budgetCodeCode: payload.code,
              budgetType: payload.budgetType,
            },
          );
        break;
      default:
        break;
    }

    if (actualSpedings?.length) {
      const updateActualSpedingDtos: UpdateActualSpendingDto[] = actualSpedings
        .map((item) => {
          let dto = {
            ...item,
            id: item.id,
            docDate: item.docDate?.toString(),
            postingDate: item.postingDate?.toString(),
            entryDate: item.entryDate?.toString(),
            paymentDate: item.paymentDate?.toString(),
            inventoryDocDate: item.inventoryDocDate?.toString(),
            poDate: item.poDate?.toString(),
            updatedBy: item.updatedBy as object,
            createdBy: item.createdBy as object,
            sapActualId: item.sapActualId,
          };
          switch (payload.eventFor) {
            case EAcutalEventFor.COST_CENTER:
              dto = {
                ...dto,
                costCenterId: payload.id,
              };
              return dto;
            case EAcutalEventFor.COMPANY:
              dto = {
                ...dto,
                companyId: payload.id,
              };
              return dto;
            case EAcutalEventFor.BU:
              dto = {
                ...dto,
                buId: payload.id,
              };
              return dto;
            case EAcutalEventFor.SUPPLIER:
              dto = {
                ...dto,
                supplierId: payload.id,
              };
              return dto;
            case EAcutalEventFor.CURRENCY:
              if (dto.currencyCode == payload.code) {
                dto = {
                  ...dto,
                  currencyId: payload.id,
                };
              }

              if (dto.localCurrencyCode == payload.code) {
                dto = {
                  ...dto,
                  localCurrencyId: payload.id,
                };
              }

              return dto;
            case EAcutalEventFor.BUDGET_CODE:
              dto = {
                ...dto,
                budgetCodeId: payload.id,
              };
              return dto;
            default:
              return null;
          }
        })
        .filter(Boolean);

      const responseSaps: ActualSapResponseModel[] = [];

      for (let i = 0; i < updateActualSpedingDtos.length; i++) {
        const responseSap: ActualSapResponseModel = {
          sapActualId: updateActualSpedingDtos[i].sapActualId,
          errorStatus: 'SUCCESSFUL',
          errorDetails: this.buildSapErrorDetails(updateActualSpedingDtos[i]),
        };

        responseSap.errorStatus = responseSap.errorDetails.length
          ? 'FAILED'
          : responseSap.errorStatus;

        responseSaps.push(responseSap);

        if (responseSap.errorDetails?.length) {
          updateActualSpedingDtos[i].status =
            EStatusActualSpending.SAP_SYNC_FAIL;
        } else {
          updateActualSpedingDtos[i].status = EStatusActualSpending.CONFIRMED;
        }
      }

      if (updateActualSpedingDtos?.length) {
        await processInBatches(updateActualSpedingDtos, 500, async (batch) => {
          await this.updateActualSpendingList(batch);
        });
      }
    }
  }

  // Kiểm tra lỗi trả về cho SAP
  private buildSapErrorDetails(dto: UpdateActualSpendingDto): TErrorMessage[] {
    const errors: TErrorMessage[] = [];

    if (dto.companyCode && !dto.companyId) {
      errors.push(
        errorMessage.E_1024(`Mã công ty ${dto.companyCode} không tìm thấy`),
      );
    }

    if (dto.costCenterCode && !dto.costCenterId) {
      errors.push(
        errorMessage.E_1005(
          `Mã Cost Center ${dto.costCenterCode} không tìm thấy`,
        ),
      );
    }

    if (dto.buCode && !dto.buId) {
      errors.push(errorMessage.E_1022(`Mã BU ${dto.buCode} không tìm thấy`));
    }

    if (dto.supplierCode && !dto.supplierId) {
      errors.push(
        supplierErrorDetails.E_4000(
          `Mã nhà cung cấp ${dto.supplierCode} không tìm thấy`,
        ),
      );
    }

    if (dto.poSapId && !dto.poSapId) {
      errors.push(
        errorMessage.E_1090(`Mã PO SAP ${dto.poSapId} không tìm thấy`),
      );
    }

    if (!dto.currencyId) {
      errors.push(
        errorMessage.E_1013(
          `Mã đơn vị tiền tệ ${dto.currencyCode} không tìm thấy`,
        ),
      );
    }

    if (dto.localCurrencyCode && !dto.localCurrencyId) {
      errors.push(
        errorMessage.E_1013(
          `Mã đơn vị tiền tệ quy đổi ${dto.localCurrencyCode} không tìm thấy`,
        ),
      );
    }

    return errors;
  }

  async updateActualSpendingList(
    updateActualSpendingDtos: UpdateActualSpendingDto[],
  ): Promise<ActualSpendingModel[]> {
    const data = await this.actualSpendingRepository.updateActualSpendingList(
      updateActualSpendingDtos,
    );

    return data;
  }
}
