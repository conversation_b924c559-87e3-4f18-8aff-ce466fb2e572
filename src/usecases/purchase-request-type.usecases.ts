import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as _ from 'lodash';
import { CreatePurchaseRequestTypeDto } from '../controller/purchase-request-type/dtos/create-purchase-request-type.dto';
import { GetDetailPurchaseRequestTypeDto } from '../controller/purchase-request-type/dtos/get-detail-purchase-request-type.dto';
import { GetPurchaseRequestTypeListDto } from '../controller/purchase-request-type/dtos/get-purchase-request-type-list.dto';
import { UpdatePurchaseRequestTypeDto } from '../controller/purchase-request-type/dtos/update-purchase-request-type.dto';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EPurchaseRequestStatus } from '../domain/config/enums/purchasing.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { PurchaseRequestTypeErrorDetails } from '../domain/messages/error-details/purchase-request-type';
import { PurchaseRequestTypeModel } from '../domain/model/purchase-request-type.model';
import { IPurchaseRequestTypeRepository } from '../domain/repositories/purchase-request-type.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';

@Injectable()
export class PurchaseRequestTypeUsecases {
  constructor(
    @Inject(IPurchaseRequestTypeRepository)
    private readonly prTypeRepository: IPurchaseRequestTypeRepository,
  ) {}

  async createPrType(
    data: CreatePurchaseRequestTypeDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PurchaseRequestTypeModel> {
    await this.verifyDataDto(data, jwtPayload);
    const purchaseRequestTypeModel = new PurchaseRequestTypeModel(data);
    const prType = await this.prTypeRepository.createPrType(
      purchaseRequestTypeModel,
    );
    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: prType.name,
        refId: prType.id,
        refCode: prType.code,
        type: EDataRoleType.PR_TYPE,
        isEnabled: prType.status === EPurchaseRequestStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization },
    );

    return prType;
  }

  async updatePrType(
    id: string,
    UpdatePurchaseRequestTypeDto: UpdatePurchaseRequestTypeDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PurchaseRequestTypeModel> {
    await this.verifyDataDto(UpdatePurchaseRequestTypeDto, jwtPayload, id);

    const prType = await this.prTypeRepository.updatePrType(
      id,
      UpdatePurchaseRequestTypeDto,
    );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: prType.name,
        refCode: prType.code,
        isEnabled: prType.status === EPurchaseRequestStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    return prType;
  }

  async getPrTypes(
    conditions: GetPurchaseRequestTypeListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PurchaseRequestTypeModel>> {
    return await this.prTypeRepository.getPrTypes(conditions, jwtPayload);
  }

  async deletePrType(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getPrTypeDetail(
      plainToInstance(GetDetailPurchaseRequestTypeDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.prTypeRepository.deletePrType(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistPrTypeByCode(code: string, id?: string): Promise<void> {
    const prType = await this.prTypeRepository.getPrTypeByCode(code, id);

    if (prType) {
      throw new HttpException(
        PurchaseRequestTypeErrorDetails.E_3301(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async verifyDataDto(
    conditions: CreatePurchaseRequestTypeDto | UpdatePurchaseRequestTypeDto,
    jwtPayload: any,
    id?: string,
  ) {
    if (id) {
      await this.getPrTypeDetail(
        plainToInstance(GetDetailPurchaseRequestTypeDto, {
          id: id,
        }),
        jwtPayload,
      );
    }

    await this.checkExistPrTypeByCode(conditions.code, id);
  }

  async getPrTypeDetail(
    conditions: GetDetailPurchaseRequestTypeDto,
    jwtPayload,
  ) {
    const prType = await this.prTypeRepository.getPrTypeDetail(
      conditions,
      jwtPayload,
    );

    if (!prType) {
      throw new HttpException(
        PurchaseRequestTypeErrorDetails.E_3300(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return prType;
  }

  async getPrTypeByIds(ids: string[], jwtPayload: any) {
    const prTypes = await this.prTypeRepository.getPrTypeByIds(ids, jwtPayload);

    if (!prTypes || !prTypes.length) {
      throw new HttpException(
        PurchaseRequestTypeErrorDetails.E_3300(),
        HttpStatus.NOT_FOUND,
      );
    }

    const activePrTypes = prTypes?.filter(
      (item) => item.status === EPurchaseRequestStatus.ACTIVE,
    );

    const activePrTypeIds = activePrTypes.map((item) => item.id);

    const missingPrTypeIds = _.difference(ids, activePrTypeIds);

    if (missingPrTypeIds.length) {
      throw new HttpException(
        PurchaseRequestTypeErrorDetails.E_3300(
          `PR Type ids ${missingPrTypeIds.join(', ')} not found or inactive`,
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return prTypes;
  }
}
