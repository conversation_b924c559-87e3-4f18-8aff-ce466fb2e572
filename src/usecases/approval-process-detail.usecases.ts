import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { ResponseDto } from '../domain/dtos/response.dto';
import { getErrorMessage } from '../domain/messages/error-message';

import { CreateApprovalProcessDetailDto } from '../controller/approval-process-detail/dtos/create-approval-process-detail.dto';
import { GetApprovalProcessDetailListDto } from '../controller/approval-process-detail/dtos/get-approval-process-detail-list.dto';
import { GetDetailApprovalProcessDetailDto } from '../controller/approval-process-detail/dtos/get-detail-approval-process-detail.dto';
import { UpdateApprovalProcessDetailDto } from '../controller/approval-process-detail/dtos/update-approval-process-detail.dto';
import { approvalProcessDetailErrorDetails } from '../domain/messages/error-details/approval-process-detail';
import { ApprovalProcessDetailModel } from '../domain/model/approval-process-detail.model';
import { IApprovalProcessDetailRepository } from '../domain/repositories/approval-process-detail.repository';
import { codeToIdMap } from '../utils/common';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { FunctionUnitUsecases } from './function-unit.usecases';
import { PlantUsecases } from './plant.usecases';
import { PurchaseRequestTypeUsecases } from './purchase-request-type.usecases';
import { SectorUsecases } from './sector.usecases';
import { StaffUsecases } from './staff.usecase';
import { DepartmentUsecases } from './department.usecases';

@Injectable()
export class ApprovalProcessDetailUsecases {
  constructor(
    @Inject(IApprovalProcessDetailRepository)
    private readonly approvalProcessDetailRepository: IApprovalProcessDetailRepository,
    private readonly staffUsecases: StaffUsecases,
    private readonly sectorUsecases: SectorUsecases,
    private readonly functionUnitUsecases: FunctionUnitUsecases,
    private readonly departmentUsecases: DepartmentUsecases,
    private readonly businessUnitUsecases: BusinessUnitUsecases,
    private readonly prTypeUsecases: PurchaseRequestTypeUsecases,
  ) {}

  async createApprovalProcessDetail(
    data: CreateApprovalProcessDetailDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<ApprovalProcessDetailModel> {
    const approvalProcessDetailModel = await this.validateData(
      data,
      jwtPayload,
    );

    const approvalProcessDetail =
      await this.approvalProcessDetailRepository.createApprovalProcessDetail(
        approvalProcessDetailModel,
      );

    return approvalProcessDetail;
  }

  async updateApprovalProcessDetail(
    id: string,
    updateApprovalProcessDetailDto: UpdateApprovalProcessDetailDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<ApprovalProcessDetailModel> {
    const approvalProcessDetailModel = await this.validateData(
      updateApprovalProcessDetailDto,
      jwtPayload,
      id,
    );

    const approvalProcessDetail =
      await this.approvalProcessDetailRepository.updateApprovalProcessDetail(
        id,
        approvalProcessDetailModel,
      );

    return approvalProcessDetail;
  }

  async getApprovalProcessDetails(
    conditions: GetApprovalProcessDetailListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<ApprovalProcessDetailModel>> {
    return await this.approvalProcessDetailRepository.getApprovalProcessDetails(
      conditions,
      jwtPayload,
    );
  }

  async deleteApprovalProcessDetail(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailApprovalProcessDetail(
      plainToInstance(GetDetailApprovalProcessDetailDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.approvalProcessDetailRepository.deleteApprovalProcessDetail(id);
  }

  async getDetailApprovalProcessDetail(
    conditions: GetDetailApprovalProcessDetailDto,
    jwtPayload: any,
  ) {
    const detail =
      await this.approvalProcessDetailRepository.getDetailApprovalProcessDetail(
        conditions,
        jwtPayload,
      );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(approvalProcessDetailErrorDetails.E_6074()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  private async validateData(
    data: CreateApprovalProcessDetailDto | UpdateApprovalProcessDetailDto,
    jwtPayload: any,
    id?: string,
  ): Promise<ApprovalProcessDetailModel> {
    let approvalProcessDetail: ApprovalProcessDetailModel;
    if (id) {
      approvalProcessDetail = await this.getDetailApprovalProcessDetail(
        plainToInstance(GetDetailApprovalProcessDetailDto, {
          id: id,
        }),
        jwtPayload,
      );
    }

    ///Validate staff id
    const staffIds = [
      ...new Set(
        [
          data.prCreatedById,
          data.poCreatedById,
          data.prApprover1Id,
          data.prApprover2Id,
          data.prApprover3Id,
          data.prApprover4Id,
          data.prApprover5Id,
          data.prApprover6Id,
          data.prApprover7Id,
          data.poApprover1Id,
          data.poApprover2Id,
          data.poApprover3Id,
        ].filter(Boolean),
      ),
    ];

    const staffs = await this.staffUsecases.getStaffs(
      {
        statuses: [],
        page: 1,
        limit: 5,
        getAll: 1,
        searchString: '',
        ids: staffIds,
      },
      jwtPayload,
    );

    const dataStaffs = codeToIdMap(staffs.results || [], 'id', ['id', 'code']);

    const prCreatedBy = dataStaffs[data.prCreatedById];
    if (!prCreatedBy) {
      throw new HttpException(
        getErrorMessage(approvalProcessDetailErrorDetails.E_6075()),
        HttpStatus.NOT_FOUND,
      );
    }

    const poCreatedBy = dataStaffs[data.poCreatedById];
    if (!poCreatedBy) {
      throw new HttpException(
        getErrorMessage(approvalProcessDetailErrorDetails.E_6076()),
        HttpStatus.NOT_FOUND,
      );
    }

    const prApprover1 = dataStaffs[data.prApprover1Id];
    if (!prApprover1) {
      throw new HttpException(
        getErrorMessage(approvalProcessDetailErrorDetails.E_6077()),
        HttpStatus.NOT_FOUND,
      );
    }

    if (data.prApprover2Id) {
      const prApprover2 = dataStaffs[data.prApprover2Id];
      if (!prApprover2) {
        throw new HttpException(
          getErrorMessage(approvalProcessDetailErrorDetails.E_6085()),
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      data.prApprover2Id = null;
    }

    if (data.prApprover3Id) {
      const prApprover3 = dataStaffs[data.prApprover3Id];
      if (!prApprover3) {
        throw new HttpException(
          getErrorMessage(approvalProcessDetailErrorDetails.E_6078()),
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      data.prApprover3Id = null;
    }

    if (data.prApprover4Id) {
      const prApprover4 = dataStaffs[data.prApprover4Id];
      if (!prApprover4) {
        throw new HttpException(
          getErrorMessage(approvalProcessDetailErrorDetails.E_6079()),
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      data.prApprover4Id = null;
    }

    if (data.prApprover5Id) {
      const prApprover5 = dataStaffs[data.prApprover5Id];
      if (!prApprover5) {
        throw new HttpException(
          getErrorMessage(approvalProcessDetailErrorDetails.E_6080()),
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      data.prApprover5Id = null;
    }

    if (data.prApprover6Id) {
      const prApprover6 = dataStaffs[data.prApprover6Id];
      if (!prApprover6) {
        throw new HttpException(
          getErrorMessage(approvalProcessDetailErrorDetails.E_6081()),
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      data.prApprover6Id = null;
    }

    if (data.prApprover7Id) {
      const prApprover7 = dataStaffs[data.prApprover7Id];
      if (!prApprover7) {
        throw new HttpException(
          getErrorMessage(approvalProcessDetailErrorDetails.E_6086()),
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      data.prApprover7Id = null;
    }

    const poApprover1 = dataStaffs[data.poApprover1Id];
    if (!poApprover1) {
      throw new HttpException(
        getErrorMessage(approvalProcessDetailErrorDetails.E_6082()),
        HttpStatus.NOT_FOUND,
      );
    }

    if (data.poApprover2Id) {
      const poApprover2 = dataStaffs[data.poApprover2Id];
      if (!poApprover2) {
        throw new HttpException(
          getErrorMessage(approvalProcessDetailErrorDetails.E_6083()),
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      data.poApprover2Id = null;
    }

    if (data.poApprover3Id) {
      const poApprover3 = dataStaffs[data.poApprover3Id];
      if (!poApprover3) {
        throw new HttpException(
          getErrorMessage(approvalProcessDetailErrorDetails.E_6084()),
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      data.poApprover3Id = null;
    }

    const [sector, functionUnit, department, businessUnit, prTypes] =
      await Promise.all([
        this.sectorUsecases.getDetailSector({ id: data.sectorId }, jwtPayload),
        this.functionUnitUsecases.getDetailFunctionUnit(
          { id: data.functionUnitId },
          jwtPayload,
        ),
        this.departmentUsecases.getDetailDepartment(
          { id: data.departmentId },
          jwtPayload,
        ),
        this.businessUnitUsecases.getDetailBusinessUnit(
          { id: data.businessUnitId },
          jwtPayload,
        ),
        this.prTypeUsecases.getPrTypeByIds(data.prTypeIds, jwtPayload),
      ]);

    const approvalProcessDetailModel = new ApprovalProcessDetailModel(data);

    approvalProcessDetailModel.prTypes = prTypes.filter((item) =>
      data.prTypeIds.includes(item.id),
    );

    return approvalProcessDetailModel;
  }
}
