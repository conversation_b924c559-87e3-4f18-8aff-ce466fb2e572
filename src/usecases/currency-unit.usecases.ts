import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as moment from 'moment';
import { resolve } from 'path';
import { CreateCurrencyUnitExchangeDto } from '../controller/currency-unit-exchange/dtos/create-currency-unit-exchange.dto';
import { UpdateCurrencyUnitExchangeDto } from '../controller/currency-unit-exchange/dtos/update-currency-unit-exchange.dto';
import { CreateCurrencyUnitDto } from '../controller/currency-unit/dtos/create-currency-unit.dto';
import { GetCurrencyListByIdsDto } from '../controller/currency-unit/dtos/get-currency-unit-list-by-ids.dto';
import { GetCurrencyUnitListDto } from '../controller/currency-unit/dtos/get-currency-unit-list.dto';
import { GetDetailCurrencyUnitDto } from '../controller/currency-unit/dtos/get-detail-currency-unit.dto';
import { UpdateCurrencyUnitDto } from '../controller/currency-unit/dtos/update-currency-unit.dto';
import { ImportCurrencyUnitExchangeDto } from '../controller/import/dtos/import-currency-exchange.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EAcutalEventFor,
  ISapEventCreate,
} from '../domain/config/enums/actual-spending.enum';
import {
  EColumnImportCurrencyExchange,
  ECurrencyUnitStatus,
} from '../domain/config/enums/currency-unit.enum';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { CurrencyUnitExchangeModel } from '../domain/model/currency-unit-exchange.model';
import { CurrencyUnitModel } from '../domain/model/currency-unit.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { ICurrencyUnitRepository } from '../domain/repositories/currency-unit.repository';
import {
  checkEffectiveTimeOverlaps,
  checkValuesEmptyRowExcel,
  excelSerialToDate,
  getStatusCurrencyUnit,
  getValueOrResult,
  startOfDay,
} from '../utils/common';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { CurrencyUnitExchangeUsecases } from './currency-unit-exchange.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';

@Injectable()
export class CurrencyUnitUsecases {
  constructor(
    @Inject(ICurrencyUnitRepository)
    private readonly currencyUnitRepository: ICurrencyUnitRepository,
    private readonly currencyUnitExchangeUsecases: CurrencyUnitExchangeUsecases,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private eventEmitter: EventEmitter2,
  ) {}

  async createCurrencyUnit(
    data: CreateCurrencyUnitDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<CurrencyUnitModel> {
    const checkCode = await this.currencyUnitRepository.findCurrencyUnitByCode(
      data.currencyCode,
    );

    if (checkCode) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1031()),
        HttpStatus.BAD_REQUEST,
      );
    }

    const currencyData = await this.verifyCurrencyUnitDto(data);

    const currency =
      await this.currencyUnitRepository.createCurrencyUnit(currencyData);

    const currencyExchanges: CurrencyUnitExchangeModel[] = [];
    for (const currencyExchangeData of data.currencyUnitExchanges) {
      //Kiểm tra effectiveStartDate < effectiveEndDate
      const effectiveStartDate = new Date(
        currencyExchangeData.effectiveStartDate,
      );

      let effectiveEndDate = null;
      if (currencyExchangeData.effectiveEndDate) {
        effectiveEndDate = new Date(currencyExchangeData.effectiveEndDate);

        if (effectiveStartDate > effectiveEndDate) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1019()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      const checkDuplicates = data.currencyUnitExchanges.filter(
        (item) =>
          checkEffectiveTimeOverlaps(
            currencyExchangeData.effectiveStartDate,
            item.effectiveStartDate,
            currencyExchangeData.effectiveEndDate,
            item.effectiveEndDate,
          ) && item.exchangeBudget == currencyExchangeData.exchangeBudget,
      );

      if (checkDuplicates && checkDuplicates.length > 1) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1102()),
          HttpStatus.BAD_REQUEST,
        );
      }

      currencyExchanges.push(
        new CurrencyUnitExchangeModel({
          ...currencyExchangeData,
          effectiveStartDate: effectiveStartDate,
          effectiveEndDate: effectiveEndDate,
          currencyUnitId: currency.id,
        }),
      );
    }
    await this.currencyUnitExchangeUsecases.createManyCurrencyExchange(
      currencyExchanges,
    );

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: currency.name,
        refId: currency.id,
        refCode: currency.currencyCode,
        type: EDataRoleType.CURRENCY_UNIT,
        isEnabled: currency.status === ECurrencyUnitStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization },
    );

    this.eventEmitter.emit('sap.currency.created', {
      code: currency.currencyCode,
      id: currency.id,
      eventFor: EAcutalEventFor.CURRENCY,
    } as ISapEventCreate);

    return await this.getCurrencyUnitDetail(
      plainToInstance(GetDetailCurrencyUnitDto, {
        id: currency.id,
      }),
      jwtPayload,
    );
  }

  // async getCurrencyUnitById(id: string): Promise<CurrencyUnitModel> {
  //   return await this.currencyUnitRepository.getCurrencyUnitById(id);
  // }

  async getCurrencyUnits(
    conditions: GetCurrencyUnitListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<CurrencyUnitModel>> {
    return await this.currencyUnitRepository.getCurrencyUnits(
      conditions,
      jwtPayload,
    );
  }

  async deleteCurrencyUnit(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getCurrencyUnitDetail(
      plainToInstance(GetDetailCurrencyUnitDto, {
        id,
      }),
      jwtPayload,
    );

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });

    return await this.currencyUnitRepository.deleteCurrencyUnit(id);
  }

  async updateCurrencyUnit(
    id: string,
    data: UpdateCurrencyUnitDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<CurrencyUnitModel> {
    const detail = await this.getCurrencyUnitDetail(
      plainToInstance(GetDetailCurrencyUnitDto, {
        id,
      }),
      jwtPayload,
    );

    const checkCode = await this.currencyUnitRepository.findCurrencyUnitByCode(
      data.currencyCode,
    );

    if (checkCode && detail.id !== checkCode.id) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1031()),
        HttpStatus.BAD_REQUEST,
      );
    }

    const currencyData = await this.verifyCurrencyUnitDto(data);
    currencyData.id = id;

    const currency =
      await this.currencyUnitRepository.updateCurrencyUnit(currencyData);

    const createCurrencyExchanges: CurrencyUnitExchangeModel[] = [];
    const updateCurrencyExchanges: CurrencyUnitExchangeModel[] = [];
    for (const currencyExchangeData of data.currencyUnitExchanges) {
      //Kiểm tra effectiveStartDate < effectiveEndDate
      const effectiveStartDate = new Date(
        currencyExchangeData.effectiveStartDate,
      );

      let effectiveEndDate = null;
      if (currencyExchangeData.effectiveEndDate) {
        effectiveEndDate = new Date(currencyExchangeData.effectiveEndDate);

        if (effectiveStartDate > effectiveEndDate) {
          throw new HttpException(
            getErrorMessage(errorMessage.E_1019()),
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      const checkDuplicates = data.currencyUnitExchanges.filter(
        (item) =>
          checkEffectiveTimeOverlaps(
            currencyExchangeData.effectiveStartDate,
            item.effectiveStartDate,
            currencyExchangeData.effectiveEndDate,
            item.effectiveEndDate,
          ) && item.exchangeBudget == currencyExchangeData.exchangeBudget,
      );

      if (checkDuplicates && checkDuplicates.length > 1) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1102()),
          HttpStatus.BAD_REQUEST,
        );
      }

      if (currencyExchangeData.id) {
        updateCurrencyExchanges.push(
          new CurrencyUnitExchangeModel({
            id: currencyExchangeData.id,
            ...currencyExchangeData,
            effectiveStartDate: effectiveStartDate,
            effectiveEndDate: effectiveEndDate,
            currencyUnitId: currency.id,
          }),
        );
      } else {
        createCurrencyExchanges.push(
          new CurrencyUnitExchangeModel({
            ...currencyExchangeData,
            effectiveStartDate: effectiveStartDate,
            effectiveEndDate: effectiveEndDate,
            currencyUnitId: currency.id,
          }),
        );
      }
    }

    await this.currencyUnitExchangeUsecases.deleteCurrencyUnitExchangesNotIn(
      updateCurrencyExchanges.map((item) => item.id),
      id,
    );
    await this.currencyUnitExchangeUsecases.createManyCurrencyExchange(
      createCurrencyExchanges,
    );
    await this.currencyUnitExchangeUsecases.updateManyCurrencyUnitExchange(
      updateCurrencyExchanges,
    );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: currency.name,
        refCode: currency.currencyCode,
        isEnabled: currency.status === ECurrencyUnitStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    if (detail.currencyCode != data.currencyCode) {
      this.eventEmitter.emit('sap.currency.created', {
        code: currency.currencyCode,
        id: currency.id,
        eventFor: EAcutalEventFor.CURRENCY,
      } as ISapEventCreate);
    }

    return await this.getCurrencyUnitDetail(
      plainToInstance(GetDetailCurrencyUnitDto, {
        id: id,
      }),
      jwtPayload,
    );
  }

  private async verifyCurrencyUnitDto(
    dto: CreateCurrencyUnitDto | UpdateCurrencyUnitDto,
  ) {
    return new CurrencyUnitModel({
      ...dto,
    });
  }

  async getCurrencyUnitByCode(code: string): Promise<CurrencyUnitModel> {
    return await this.currencyUnitRepository.findCurrencyUnitByCode(code);
  }

  async getCurrencyUnitDetail(
    conditions: GetDetailCurrencyUnitDto,
    jwtPayload: any,
  ) {
    const detail = await this.currencyUnitRepository.getCurrencyUnitDetail(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1013()),
        HttpStatus.NOT_FOUND,
      );
    }
    return detail;
  }

  async listByCodes(codes: string[], jwtPayload: any) {
    return await this.currencyUnitRepository.getCurrencyUnitsByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
    );
  }

  async exportCurrencyUnit(
    conditions: GetCurrencyUnitListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data = await this.currencyUnitRepository.getCurrencyUnits(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-currency-exchange.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toCurrencyUnitModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-currency-exchange.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toCurrencyUnitModel(currencyUnits: CurrencyUnitModel[]) {
    const items = [];

    for (let i = 0; i < currencyUnits.length; i++) {
      if (currencyUnits[i]?.currencyExchanges?.length) {
        for (let y = 0; y < currencyUnits[i]?.currencyExchanges?.length; y++) {
          items.push({
            code: currencyUnits[i].currencyCode || '',
            name: currencyUnits[i].name || '',
            description: currencyUnits[i].description || '',
            status: getStatusCurrencyUnit(currencyUnits[i].status),
            effectiveDate: currencyUnits[i]?.currencyExchanges[y]
              ?.effectiveStartDate
              ? moment(
                  currencyUnits[i]?.currencyExchanges[y]?.effectiveStartDate,
                ).format('DD/MM/YYYY')
              : '',
            exchangeRate:
              currencyUnits[i]?.currencyExchanges[y]?.exchangeRate || '',
          });
        }
      } else {
        items.push({
          code: currencyUnits[i].currencyCode || '',
          name: currencyUnits[i].name || '',
          description: currencyUnits[i].description || '',
          status: getStatusCurrencyUnit(currencyUnits[i].status),
          effectiveDate: '',
          exchangeRate: '',
        });
      }
    }

    return items;
  }

  async importCurrencyExchange(
    file: Express.Multer.File,
    jwtPayload: any,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.EXCHANGE_RATE,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createCurrencyUnitExchangeDtos: CreateCurrencyUnitExchangeDto[] =
          [];
        const updateCurrencyUnitExchangeDtos: UpdateCurrencyUnitExchangeDto[] =
          [];
        const totalData: any = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const currencyCodes = rows
          .map((item) =>
            getValueOrResult(
              item,
              EColumnImportCurrencyExchange.CURRENCY_CODE,
            )?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const currencyUnits =
          await this.currencyUnitRepository.getCurrencyUnitsByCodesWithRole(
            [...new Set(currencyCodes)],
            jwtPayload,
            false,
          );

        let totalRowHasValue = 0;

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportCurrencyExchange.CURRENCY_CODE, // First Cell
            EColumnImportCurrencyExchange.EXCHANGE_BUDGET, // Last Cell
          );

          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          const currencyCode = getValueOrResult(
            row,
            EColumnImportCurrencyExchange.CURRENCY_CODE,
          )?.toString();

          const effectiveStartDate = row.getCell(
            EColumnImportCurrencyExchange.EFFECTIVE_START_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(
                    EColumnImportCurrencyExchange.EFFECTIVE_START_DATE,
                  ).value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian bắt đầu

          const effectiveEndDate = row.getCell(
            EColumnImportCurrencyExchange.EFFECTIVE_END_DATE,
          ).value
            ? moment(
                excelSerialToDate(
                  row.getCell(EColumnImportCurrencyExchange.EFFECTIVE_END_DATE)
                    .value as string | number,
                ),
              ).format('YYYY-MM-DD')
            : null; //Thời gian kết thúc

          const exchangeRate = getValueOrResult(
            row,
            EColumnImportCurrencyExchange.EXCHANGE_RATE,
            true,
          );

          const exchangeBudget =
            getValueOrResult(
              row,
              EColumnImportCurrencyExchange.EXCHANGE_BUDGET,
            )?.toString() == 'true';

          const currencyExchangeObject = {
            id: undefined,
            currencyUnitId: null,
            exchangeRate,
            effectiveStartDate,
            effectiveEndDate,
            exchangeBudget,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            row: i + 1,
          };

          const matchCurrency = currencyUnits.find(
            (item) => item.currencyCode == currencyCode,
          );

          if (!currencyCode) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1055(),
                'Mã đơn vị tiền tệ không được để trống',
              ),
              row: i + 1,
            });
          } else {
            if (matchCurrency) {
              currencyExchangeObject.currencyUnitId = matchCurrency.id;
            } else {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1013(),
                  'Không tìm thấy mã đơn vị tiền tệ',
                ),
                row: i + 1,
              });
            }
          }

          if (!effectiveStartDate || effectiveStartDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1075(),
                'Ngày hiệu lực không hợp lệ',
              ),
              row: i + 1,
            });
          } else {
            currencyExchangeObject.effectiveStartDate = effectiveStartDate;
            const matchExchange = matchCurrency?.currencyExchanges?.find(
              (item) =>
                startOfDay(item.effectiveStartDate).getTime() ==
                startOfDay(effectiveStartDate).getTime(),
            );

            if (matchExchange) {
              currencyExchangeObject.id = matchExchange.id;
            }
          }

          if (effectiveEndDate && effectiveEndDate == 'Invalid date') {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1075(),
                'Ngày hiệu lực không hợp lệ',
              ),
              row: i + 1,
            });
          }

          if (!exchangeRate) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1086(),
                'Tỷ giá không hợp lệ',
              ),
              row: i + 1,
            });
          }

          if (
            effectiveStartDate &&
            effectiveStartDate != 'Invalid date' &&
            effectiveEndDate &&
            effectiveEndDate != 'Invalid date'
          ) {
            if (effectiveStartDate > effectiveEndDate) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1019(),
                  'Ngày hiệu lực không được lớn hơn ngày hết hiệu lực',
                ),
                row: i + 1,
              });
            }
          }

          if (
            effectiveStartDate &&
            effectiveStartDate != 'Invalid date' &&
            effectiveEndDate != 'Invalid date'
          ) {
            totalData.push(currencyExchangeObject);
          }

          if (currencyExchangeObject.id) {
            const currencyExchangeDto = plainToInstance(
              UpdateCurrencyUnitExchangeDto,
              currencyExchangeObject,
            );
            updateCurrencyUnitExchangeDtos.push({
              ...currencyExchangeDto,
              createdAt: undefined,
            });
          } else {
            const currencyExchangeDto = plainToInstance(
              CreateCurrencyUnitExchangeDto,
              currencyExchangeObject,
            );
            createCurrencyUnitExchangeDtos.push(currencyExchangeDto);
          }
        }

        for (let i = 0; i < totalData.length; i++) {
          const data = totalData[i];

          if (data.effectiveEndDate) {
            const checkDuplicates = data.currencyUnitExchanges.filter(
              (item) =>
                item.currencyUnitId == data.currencyUnitId &&
                ((item.effectiveEndDate &&
                  item.effectiveStartDate <= new Date(data.effectiveEndDate) &&
                  item.effectiveEndDate >= new Date(data.effectiveStartDate)) ||
                  (!item.effectiveEndDate &&
                    item.effectiveStartDate >=
                      new Date(data.effectiveStartDate) &&
                    item.effectiveStartDate <=
                      new Date(data.effectiveEndDate))) &&
                item.exchangeBudget == data.exchangeBudget,
            );

            if (checkDuplicates && checkDuplicates.length > 1) {
              errors.push({
                error: getErrorMessage(
                  getErrorMessage(errorMessage.E_1102()),
                  'Tỷ giá quy đổi bị trùng lặp thời gian',
                ),
                row: data.row,
              });
            }
          } else {
            const checkDuplicates = data.currencyUnitExchanges.filter(
              (item) =>
                item.currencyUnitId == data.currencyUnitId &&
                (((item.effectiveStartDate <=
                  new Date(data.effectiveStartDate) ||
                  item.effectiveStartDate >=
                    new Date(data.effectiveStartDate)) &&
                  !item.effectiveEndDate) ||
                  (item.effectiveEndDate &&
                    item.effectiveStartDate <=
                      new Date(data.effectiveStartDate) &&
                    item.effectiveEndDate >=
                      new Date(data.effectiveStartDate))) &&
                item.exchangeBudget == data.exchangeBudget,
            );

            if (checkDuplicates && checkDuplicates.length > 1) {
              errors.push({
                error: getErrorMessage(
                  getErrorMessage(errorMessage.E_1102()),
                  'Tỷ giá quy đổi bị trùng lặp thời gian',
                ),
                row: data.row,
              });
            }
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportCurrencyUnitExchangeDto = {
          dataCurrencyUnitExchanges: createCurrencyUnitExchangeDtos,
          dataUpdateCurrencyUnitExchanges: updateCurrencyUnitExchangeDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_CURRENCY_EXCHANGE(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getCurrencyUnitVND(): Promise<CurrencyUnitModel> {
    return await this.currencyUnitRepository.getCurrencyUnitVND();
  }

  async getListByIds(
    conditions: GetCurrencyListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<CurrencyUnitModel>> {
    return await this.currencyUnitRepository.getListByIds(
      conditions,
      jwtPayload,
    );
  }
}
