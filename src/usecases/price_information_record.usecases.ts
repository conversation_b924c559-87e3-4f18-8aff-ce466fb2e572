import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { GetPriceInformationRecordDto } from '../controller/price-information-record/dtos/get-all-price-information-record.dto';
import {
  PriceInformationRecordDto,
  UpdatePirStatusDto,
  UpdatePriceInformationRecordDto,
} from '../controller/price-information-record/dtos/price-information-record.dto';
import { PriceMaterialDto } from '../controller/purchase-request/dtos/price-material.dto';
import { exportFileUploadPath } from '../domain/config/constant';
import { EPIRColumnSheet } from '../domain/config/enums/pir.enum';
import { Status } from '../domain/config/enums/status.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { PriceInformationRecordModel } from '../domain/model/price_information_record.model';
import { IPriceInformationRecordRepository } from '../domain/repositories/priceInformationRecordRepository.repository';
import { HttpService } from '../infrastructure/http/http.service';
import {
  excelSerialToDate,
  getStatusPir,
  getValueOrResult,
} from '../utils/common';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CurrencyUnitUsecases } from './currency-unit.usecases';
import { FileUsecases } from './file.usecases';
import { MaterialUsecases } from './material.usecases';
import { PlantUsecases } from './plant.usecases';
import { PurchasingDepartmentUsecases } from './purchasing-department.usecases';
import { PurchasingGroupUsecases } from './purchasing-group.usecases';
import { SupplierUsecases } from './supplier.usecases';
import { PriceInformationRecordEntity } from '../infrastructure/entities/price_information_record.entity';
import { BusinessUnitModel } from '../domain/model/business-unit.model';
import { GetPIRListByIdsDto } from '../controller/price-information-record/dtos/get-price-information-record-list-by-ids.dto';

@Injectable()
export class priceInformationRecordUsecases {
  constructor(
    @Inject('IPriceInformationRecordRepository')
    private readonly priceInformationRecordRepository: IPriceInformationRecordRepository,
    private configService: ConfigService,
    private readonly httpService: HttpService,
    private fileUsecases: FileUsecases,

    private readonly supplierUsecases: SupplierUsecases,
    private readonly materialUsecases: MaterialUsecases,
    private readonly plantUsecases: PlantUsecases,
    private readonly purchasingDepartmentUsecases: PurchasingDepartmentUsecases,
    private readonly purchasingGroupUsecases: PurchasingGroupUsecases,
    private readonly currencyUnitUsecases: CurrencyUnitUsecases,
    private readonly businessUnitUsecases: BusinessUnitUsecases,
  ) {}

  async findAll(
    paginationDto: GetPriceInformationRecordDto,
    jwtPayload: any,
    authorization: any,
  ): Promise<ResponseDto<PriceInformationRecordModel>> {
    const data = await this.priceInformationRecordRepository.findAll(
      paginationDto,
      jwtPayload,
    );

    return data;
  }

  async findOne(id: number, authorization): Promise<any> {
    // Tìm dữ liệu PurchaseRequestEntity bằng id
    let item = await this.priceInformationRecordRepository.findOne(id);

    if (!item) {
      throw new NotFoundException(`Purchase request with id ${id} not found`);
    }

    return item;
  }

  async create(
    price: PriceInformationRecordDto,
    authorization,
    jwtPayload: any,
  ): Promise<PriceInformationRecordModel> {
    const resultDetail = await this.callServices(
      price,
      authorization,
      jwtPayload,
    );

    if (!resultDetail.supplier) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5002()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!resultDetail.material) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5003()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (price.plantId && !resultDetail.plant) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5004()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!resultDetail.purchaseOrganization) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5005()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!resultDetail.purchaseGroup) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5006()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!resultDetail.currency) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5007()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (price.businessUnitIds) {
      const businessUnitIds = (resultDetail.businessUnits ?? []).map(
        (item) => item.id,
      );

      const diffbusinessUnitIds = _.difference(
        price.businessUnitIds,
        businessUnitIds,
      );

      if (diffbusinessUnitIds.length) {
        throw new HttpException(
          getErrorMessage(
            errorMessage.E_5106(),
            `BUSINESS_OWNER_IDS_NOT_FOUND_${diffbusinessUnitIds.join(', ')}`,
          ),
          HttpStatus.NOT_FOUND,
        );
      }

      const businessUnitsInactive = resultDetail.businessUnits.filter(
        (item) => item.status == 'IN_ACTIVE',
      );

      if (businessUnitsInactive?.length) {
        throw new HttpException(
          getErrorMessage(
            errorMessage.E_5010(),
            `BUSINESS_OWNER_IDS_IN_ACTIVE_${businessUnitsInactive.map((item) => item.id).join(', ')}`,
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      price.businessUnits = resultDetail.businessUnits;
    } else {
      price.businessUnits = [];
    }

    const deliveryTime = new Date(price.effectiveDate).getTime();
    const expirationTime = new Date(price.expirationDate).getTime();
    const currentTime = moment().startOf('day').toDate().getTime();

    if (deliveryTime < currentTime) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5001()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (expirationTime < deliveryTime) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5009()),
        HttpStatus.BAD_REQUEST,
      );
    }

    return await this.priceInformationRecordRepository.createPriceInformationRecord(
      price,
      authorization,
    );
  }

  async updatePriceInformationRecord(
    id: number,
    price: UpdatePriceInformationRecordDto,
    authorization,
    jwtPayload: any,
  ): Promise<PriceInformationRecordModel> {
    const existingRecord =
      await this.priceInformationRecordRepository.findOne(id);

    if (!existingRecord) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5008()),
        HttpStatus.BAD_REQUEST,
      );
    }

    const resultDetail = await this.callServices(
      price,
      authorization,
      jwtPayload,
    );

    if (!resultDetail.supplier) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5002()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!resultDetail.material) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5003()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (price.plantId && !resultDetail.plant) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5004()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!resultDetail.purchaseOrganization) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5005()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!resultDetail.purchaseGroup) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5006()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!resultDetail.currency) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5007()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (price.businessUnitIds) {
      const businessUnitIds = (resultDetail.businessUnits ?? []).map(
        (item) => item.id,
      );

      const diffbusinessUnitIds = _.difference(
        price.businessUnitIds,
        businessUnitIds,
      );

      if (diffbusinessUnitIds.length) {
        throw new HttpException(
          getErrorMessage(
            errorMessage.E_5106(),
            `BUSINESS_OWNER_IDS_NOT_FOUND_${diffbusinessUnitIds.join(', ')}`,
          ),
          HttpStatus.NOT_FOUND,
        );
      }

      const businessUnitsInactive = resultDetail.businessUnits.filter(
        (item) => item.status == 'IN_ACTIVE',
      );

      if (businessUnitsInactive?.length) {
        throw new HttpException(
          getErrorMessage(
            errorMessage.E_5010(),
            `BUSINESS_OWNER_IDS_IN_ACTIVE_${businessUnitsInactive.map((item) => item.id).join(', ')}`,
          ),
          HttpStatus.BAD_REQUEST,
        );
      }

      price.businessUnits = resultDetail.businessUnits;
    } else {
      price.businessUnits = [];
    }

    const deliveryTime = new Date(price.effectiveDate).getTime();
    const expirationTime = new Date(price.expirationDate).getTime();
    const currentTime = moment().startOf('day').toDate().getTime();

    if (deliveryTime < currentTime) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5001()),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (expirationTime < deliveryTime) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5009()),
        HttpStatus.BAD_REQUEST,
      );
    }

    return await this.priceInformationRecordRepository.updatePriceInformationRecord(
      id,
      price,
    );
  }

  async updateStatus(
    id: number,
    updatePir: UpdatePirStatusDto,
  ): Promise<PriceInformationRecordModel> {
    const existingRecord =
      await this.priceInformationRecordRepository.findOne(id);

    if (!existingRecord) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_5008()),
        HttpStatus.BAD_REQUEST,
      );
    }

    existingRecord.status = updatePir.status;

    return this.priceInformationRecordRepository.updateStatusPriceInformationRecord(
      id,
      existingRecord,
    );
  }

  async autoUpdateStatus(): Promise<void> {
    const activePirs = await this.priceInformationRecordRepository.activePirs();
    const inactivePirs =
      await this.priceInformationRecordRepository.inactivePirs();

    for (const pir of activePirs) {
      pir.status = Status.Active;
      await this.priceInformationRecordRepository.updateStatusPriceInformationRecord(
        pir.id,
        pir,
      );
    }

    for (const pir of inactivePirs) {
      pir.status = Status.Inactive;
      await this.priceInformationRecordRepository.updateStatusPriceInformationRecord(
        pir.id,
        pir,
      );
    }
  }

  async importPriceInformationRecords(
    file: Express.Multer.File,
    authorization,
    jwtPayload: any,
  ): Promise<string> {
    try {
      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(Buffer.from(file.buffer));
      const worksheet = workbook.worksheets[0];
      const rows = worksheet.getRows(2, worksheet.actualRowCount - 1);

      const vendorCodes = [
        ...new Set(
          rows
            .map((item) =>
              getValueOrResult(item, EPIRColumnSheet.vendorCode)?.toString(),
            )
            ?.filter(Boolean),
        ),
      ];
      const materialCodes = [
        ...new Set(
          rows
            .map((item) =>
              getValueOrResult(item, EPIRColumnSheet.materialCode)?.toString(),
            )
            ?.filter(Boolean),
        ),
      ];
      const businessUnitCodes = [
        ...new Set(
          _.flatten(
            rows
              .map((item) =>
                getValueOrResult(item, EPIRColumnSheet.businessUnitCode)
                  ?.toString()
                  ?.split(',')
                  ?.map((item) => item.trim()),
              )
              ?.filter(Boolean),
          ),
        ),
      ];
      const purchaseOrganizationCodes = [
        ...new Set(
          rows
            .map((item) =>
              getValueOrResult(
                item,
                EPIRColumnSheet.purchaseOrganizationCode,
              )?.toString(),
            )
            ?.filter(Boolean),
        ),
      ];
      const purchaseGroupCodes = [
        ...new Set(
          rows
            .map((item) =>
              getValueOrResult(
                item,
                EPIRColumnSheet.purchaseGroupCode,
              )?.toString(),
            )
            ?.filter(Boolean),
        ),
      ];
      const currencyCodes = [
        ...new Set(
          rows
            .map((item) =>
              getValueOrResult(item, EPIRColumnSheet.currencyCode)?.toString(),
            )
            ?.filter(Boolean),
        ),
      ];

      const [
        suppliers,
        materials,
        businessUnits,
        purchaseOrgs,
        purchaseGroups,
        currencys,
      ] = await Promise.all([
        vendorCodes && vendorCodes.length
          ? this.supplierUsecases.listByCodes(vendorCodes, jwtPayload)
          : null,
        materialCodes && materialCodes.length
          ? this.materialUsecases.listByCodes(materialCodes, jwtPayload)
          : null,
        businessUnitCodes && businessUnitCodes.length
          ? this.businessUnitUsecases.listByCodes(businessUnitCodes, jwtPayload)
          : null,
        purchaseOrganizationCodes && purchaseOrganizationCodes.length
          ? this.purchasingDepartmentUsecases.listByCodes(
              purchaseOrganizationCodes,
              jwtPayload,
            )
          : null,
        purchaseGroupCodes && purchaseGroupCodes.length
          ? this.purchasingGroupUsecases.listByCodes(
              purchaseGroupCodes,
              jwtPayload,
            )
          : null,
        currencyCodes && currencyCodes.length
          ? this.currencyUnitUsecases.listByCodes(currencyCodes, jwtPayload)
          : null,
      ]);

      const priceInformationRecordDtos: PriceInformationRecordDto[] = [];
      for (let i = 0; i < rows.length; i++) {
        const vendorCode = getValueOrResult(
          rows[i],
          EPIRColumnSheet.vendorCode,
          false,
        );
        const materialCode = getValueOrResult(
          rows[i],
          EPIRColumnSheet.materialCode,
          false,
        );
        const businessUnitCodes = getValueOrResult(
          rows[i],
          EPIRColumnSheet.businessUnitCode,
          false,
        )
          ?.toString()
          ?.split(',')
          ?.map((item) => item.trim());
        const purchaseOrganizationCode = getValueOrResult(
          rows[i],
          EPIRColumnSheet.purchaseOrganizationCode,
          false,
        );
        const purchaseGroupCode = getValueOrResult(
          rows[i],
          EPIRColumnSheet.purchaseGroupCode,
          false,
        );
        const infoType = getValueOrResult(
          rows[i],
          EPIRColumnSheet.infoType,
          false,
        )
          ?.toString()
          ?.split(',')
          ?.map((item) => item.trim());
        const vendorLeadtime = getValueOrResult(
          rows[i],
          EPIRColumnSheet.vendorLeadTime,
          false,
        );
        const regularPurchaseQuantity = getValueOrResult(
          rows[i],
          EPIRColumnSheet.regularPurchaseQuantity,
          true,
        );
        const minimumOrderQuantity = getValueOrResult(
          rows[i],
          EPIRColumnSheet.minimumOrderQuantity,
          true,
        );
        const upperTolerance = getValueOrResult(
          rows[i],
          EPIRColumnSheet.upperTolerance,
          true,
        );
        const lowerTolerance = getValueOrResult(
          rows[i],
          EPIRColumnSheet.lowerTolerance,
          true,
        );
        const purchasePrice = getValueOrResult(
          rows[i],
          EPIRColumnSheet.purchasePrice,
          true,
        );
        const currencyCode = getValueOrResult(
          rows[i],
          EPIRColumnSheet.currencyCode,
          false,
        );
        const overPurchaseUnit = getValueOrResult(
          rows[i],
          EPIRColumnSheet.overPurchaseUnit,
          false,
        );
        const effectiveDate = getValueOrResult(
          rows[i],
          EPIRColumnSheet.effectiveDate,
          false,
          true,
        );
        const expirationDate = getValueOrResult(
          rows[i],
          EPIRColumnSheet.expirationDate,
          false,
          true,
        );
        const status =
          getValueOrResult(rows[i], EPIRColumnSheet.status, false)
            ?.toString()
            ?.toLowerCase() == 'inactive'
            ? Status.Inactive
            : Status.Active;

        const priceInformationRecordDto: PriceInformationRecordDto = {
          vendorCodeId: '',
          materialCodeId: '',
          purchaseOrganizationId: '',
          vendorLeadtime,
          purchaseGroupId: '',
          regularPurchaseQuantity,
          minimumOrderQuantity,
          upperTolerance,
          lowerTolerance,
          purchasePrice,
          currencyId: '',
          businessUnitIds: [],
          infoType,
          overPurchaseUnit,
          effectiveDate: undefined,
          expirationDate: undefined,
          status: status,
          businessUnits: [],
        };

        if (!vendorCode) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5002(),
              'Nhà cung cấp không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        } else {
          const supplier = (suppliers || []).find(
            (item: any) => item.code === vendorCode,
          );
          if (!supplier) {
            throw new HttpException(
              getErrorMessage(
                errorMessage.E_5002(),
                'Không tìm thấy nhà cung cấp',
              ),
              HttpStatus.BAD_REQUEST,
            );
          }
          priceInformationRecordDto.vendorCodeId = supplier?.id;
        }

        if (!materialCode) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5003(),
              'Vật tư không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        } else {
          const material = (materials || []).find(
            (item: any) => item.code === materialCode,
          );
          if (!material) {
            throw new HttpException(
              getErrorMessage(errorMessage.E_5003(), 'Không tìm thấy vật tư'),
              HttpStatus.BAD_REQUEST,
            );
          }
          priceInformationRecordDto.materialCodeId = material?.id;
        }

        if (businessUnitCodes && businessUnitCodes.length) {
          const businessUnit = (businessUnits || []).filter((item: any) =>
            businessUnitCodes.includes(item.code),
          );
          const checkBusinessUnits = businessUnitCodes.filter(
            (item) =>
              !(businessUnits || []).map((data) => data.code).includes(item),
          );
          if (checkBusinessUnits && checkBusinessUnits.length) {
            throw new HttpException(
              getErrorMessage(
                errorMessage.E_5010(),
                'Không tìm thấy đơn vị kinh doanh',
              ),
              HttpStatus.BAD_REQUEST,
            );
          }
          priceInformationRecordDto.businessUnitIds = businessUnit.map(
            (item: any) => item.id,
          );

          priceInformationRecordDto.businessUnits = businessUnit.map(
            (item) => item,
          );
        }

        if (!purchaseOrganizationCode) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5005(),
              'Bộ phận mua hàng không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        } else {
          const purchaseOrg = (purchaseOrgs || []).find(
            (item: any) => item.code === purchaseOrganizationCode,
          );
          if (!purchaseOrg) {
            throw new HttpException(
              getErrorMessage(
                errorMessage.E_5005(),
                'Không tìm thấy bộ phận mua hàng',
              ),
              HttpStatus.BAD_REQUEST,
            );
          }
          priceInformationRecordDto.purchaseOrganizationId = purchaseOrg?.id;
        }

        if (!purchaseGroupCode) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5006(),
              'Nhóm mua hàng không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        } else {
          const purchaseGroup = (purchaseGroups || []).find(
            (item) => item.code == purchaseGroupCode,
          );
          if (!purchaseGroup) {
            throw new HttpException(
              getErrorMessage(
                errorMessage.E_5006(),
                'Không tìm thấy nhóm mua hàng',
              ),
              HttpStatus.BAD_REQUEST,
            );
          }
          priceInformationRecordDto.purchaseGroupId = purchaseGroup?.id;
        }

        if (!vendorLeadtime) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5011(),
              'Leadtime NCC không đúng định dạng',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!regularPurchaseQuantity) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5012(),
              'Số lượng thường mua hàng không đúng định dạng',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!minimumOrderQuantity) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5013(),
              'Số lượng đặt hàng tối thiểu không đúng định dạng',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!upperTolerance) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5014(),
              'Dung sai trên theo % không đúng định dạng',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!lowerTolerance) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5015(),
              'Dung sai dưới theo % không đúng định dạng',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!purchasePrice) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5016(),
              'Giá tiền mua hàng không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!currencyCode) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5007(),
              'Đơn vị tiền tệ không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        } else {
          const currency = (currencys || []).find(
            (item) => item.currencyCode == currencyCode,
          );
          if (!currency) {
            throw new HttpException(
              getErrorMessage(
                errorMessage.E_5007(),
                'Không tìm thấy đơn vị tiền tệ',
              ),
              HttpStatus.BAD_REQUEST,
            );
          }
          priceInformationRecordDto.currencyId = currency?.id;
        }

        if (!overPurchaseUnit) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5017(),
              'Trên bao nhiêu đơn vị mua không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!effectiveDate) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5018(),
              'Thời gian có hiệu lực không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        } else {
          const date = new Date(excelSerialToDate(effectiveDate));

          if (!date) {
            throw new HttpException(
              getErrorMessage(
                errorMessage.E_5020(),
                'Thời gian có hiệu lực không đúng định dạng',
              ),
              HttpStatus.BAD_REQUEST,
            );
          }
          priceInformationRecordDto.effectiveDate = date;
        }

        if (!expirationDate) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5019(),
              'Thời gian hết hiệu lực không được để trống',
            ),
            HttpStatus.BAD_REQUEST,
          );
        } else {
          const date = new Date(excelSerialToDate(expirationDate));
          if (!date) {
            throw new HttpException(
              getErrorMessage(
                errorMessage.E_5020(),
                'Thời gian có hiệu lực không đúng định dạng',
              ),
              HttpStatus.BAD_REQUEST,
            );
          }
          priceInformationRecordDto.expirationDate = date;
        }

        const deliveryTime = priceInformationRecordDto.effectiveDate.getTime();
        const expirationTime =
          priceInformationRecordDto.expirationDate.getTime();
        const currentTime = moment().startOf('day').toDate().getTime();

        if (deliveryTime < currentTime) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5001(),
              'Thời gian có hiệu lực không được nhỏ hơn ngày hiện tại',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        if (expirationTime < deliveryTime) {
          throw new HttpException(
            getErrorMessage(
              errorMessage.E_5009(),
              'Thời gian hết hiệu lực không được nhỏ hơn Thời gian có hiệu lực',
            ),
            HttpStatus.BAD_REQUEST,
          );
        }

        priceInformationRecordDtos.push(priceInformationRecordDto);
      }

      const data =
        await this.priceInformationRecordRepository.createMultiplePriceInformationRecord(
          priceInformationRecordDtos,
        );

      return 'Price Information Record imported successfully';
    } catch (error) {
      throw error;
    }
    // const records = await this.parseFile(file);

    // const fieldMapping = {
    //   'Mã NCC': 'vendor_code',
    //   'Mã material': 'material_code',
    //   'Mã bộ phận mua hàng': 'purchase_organization',
    //   Plan: 'plant',
    //   'Loại Info record': 'info_type',
    //   'Đơn vị mua hàng': 'purchase_unit',
    //   'Leadtime của NCC': 'vendor_leadtime',
    //   'Mã nhóm thu mua': 'purchase_group',
    //   'Số lượng thường mua hàng': 'regular_purchase_quantity',
    //   'Số lượng đặt hàng tối thiểu': 'minimum_order_quantity',
    //   'Dung sai trên theo %': 'upper_tolerance',
    //   'Dung sai dưới theo %': 'lower_tolerance',
    //   'Giá tiền mua hàng': 'purchase_price',
    //   'Loại tiền': 'currency',
    //   'Trên bao nhiêu đơn vị mua': 'over_purchase_unit',
    //   'Đơn vị tính': 'unit_of_measurement',
    //   'Thời gian có hiệu lực': 'effective_date',
    //   'Thời gian hết hiệu lực': 'expiration_date',
    //   'Trạng thái': 'status',
    // };

    // const results = records.map((record) => {
    //   const translatedRecord = {};
    //   for (const key in record) {
    //     if (fieldMapping[key]) {
    //       translatedRecord[fieldMapping[key]] = record[key];
    //     } else {
    //       throw new BadRequestException(`Field ${key} is not valid.`);
    //     }
    //   }
    //   return translatedRecord;
    // });

    // for (const result of results) {
    //   const effectiveDateStr = result['effective_date'];
    //   const expirationDateStr = result['expiration_date'];

    //   // Nếu giá trị là số, chuyển thành ngày từ serial Excel
    //   const effectiveDate = typeof effectiveDateStr === 'number' ? convertExcelSerialToDate(effectiveDateStr) : parse(String(effectiveDateStr), 'dd/MM/yy', new Date());
    //   const expirationDate = typeof expirationDateStr === 'number' ? convertExcelSerialToDate(expirationDateStr) : parse(String(expirationDateStr), 'dd/MM/yy', new Date());

    //   effectiveDate.setUTCHours(0, 0, 0, 0);
    //   expirationDate.setUTCHours(0, 0, 0, 0);

    //   const deliveryTime = new Date(effectiveDate).getTime();
    //   const currentTime = new Date().getTime();

    //   if (deliveryTime < currentTime) {
    //     throw new HttpException(getErrorMessage(errorMessage.E_5001()), HttpStatus.BAD_REQUEST);
    //   }

    //   if (!effectiveDate || isNaN(effectiveDate.getTime())) {
    //     throw new BadRequestException(`Invalid effective_date: ${effectiveDateStr}`);
    //   }

    //   if (!expirationDate || isNaN(expirationDate.getTime())) {
    //     throw new BadRequestException(`Invalid expiration_date: ${expirationDateStr}`);
    //   }

    //   const importFromExcel: PriceInformationRecordDto = {
    //     vendor_code: result['vendor_code'],
    //     material_code: result['material_code'],
    //     purchase_organization: result['purchase_organization'],
    //     plant: result['plant'],
    //     info_type: result['info_type'],
    //     purchase_unit: result['purchase_unit'],
    //     vendor_leadtime: result['vendor_leadtime'],
    //     purchase_group: result['purchase_group'],
    //     regular_purchase_quantity: result['regular_purchase_quantity'],
    //     minimum_order_quantity: result['minimum_order_quantity'],
    //     upper_tolerance: result['upper_tolerance'],
    //     lower_tolerance: result['lower_tolerance'],
    //     purchase_price: result['purchase_price'],
    //     currency: result['currency'],
    //     over_purchase_unit: result['over_purchase_unit'],
    //     unit_of_measurement: result['unit_of_measurement'],
    //     effective_date: effectiveDate,
    //     expiration_date: expirationDate,
    //     status: result['status'],
    //   };

    //   // Kiểm tra điều kiện
    //   if (!importFromExcel) {
    //     throw new BadRequestException('Missing required fields');
    //   }

    //   // Lưu dữ liệu vào database
    //   await this.priceInformationRecordRepository.createPriceInformationRecord(importFromExcel, authorization);
    // }
  }

  private async callServices(
    price: PriceInformationRecordDto,
    authorization,
    jwtPayload: any,
  ): Promise<any> {
    const [
      supplier,
      material,
      plant,
      purchaseOrganization,
      purchaseGroup,
      currency,
      businessUnits,
    ] = await Promise.all([
      price.vendorCodeId
        ? this.supplierUsecases.getSupplierDetail(
            { id: price.vendorCodeId },
            jwtPayload,
          )
        : null,
      price.materialCodeId
        ? this.materialUsecases.getMaterialDetail(
            { id: price.materialCodeId },
            jwtPayload,
          )
        : null,
      price.plantId
        ? this.plantUsecases.getPlantDetail({ id: price.plantId }, jwtPayload)
        : null,
      price.purchaseOrganizationId
        ? this.purchasingDepartmentUsecases.getPurchasingDepartmentDetail(
            { id: price.purchaseOrganizationId },
            jwtPayload,
          )
        : null,
      price.purchaseGroupId
        ? await this.purchasingGroupUsecases.getPurchasingGroupDetail(
            { id: price.purchaseGroupId },
            jwtPayload,
          )
        : null,
      price.currencyId
        ? this.currencyUnitUsecases.getCurrencyUnitDetail(
            { id: price.currencyId },
            jwtPayload,
          )
        : null,
      price.businessUnitIds && price.businessUnitIds.length
        ? this.businessUnitUsecases.getBusinessUnits(
            {
              ids: price.businessUnitIds,
              getAll: 1,
              page: 0,
              limit: 0,
              searchString: '',
            },
            jwtPayload,
          )
        : null,
    ]);

    return {
      supplier,
      material,
      plant,
      purchaseOrganization,
      purchaseGroup,
      currency,
      businessUnits: businessUnits?.results || [],
    };
  }

  async exportPir(
    paginationDto: GetPriceInformationRecordDto,
    jwtPayload: any,
    authorization: any,
  ) {
    paginationDto.getAll = 1;
    const data = await this.findAll(paginationDto, jwtPayload, authorization);

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template-export/template-export-pir.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      for (let rowNumber = 1; rowNumber <= 1; rowNumber++) {
        const sourceRow = sourceWorksheet.getRow(rowNumber);
        const targetRow = targetWorksheet.getRow(rowNumber);

        sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const targetCell = targetRow.getCell(colNumber);
          targetCell.value = cell.value; // Copy value
          targetCell.style = { ...cell.style }; // Copy full style
          targetCell.border = cell.border; // Copy border
          targetCell.font = cell.font; // Copy font
          targetCell.alignment = cell.alignment; // Copy alignment
          targetCell.numFmt = cell.numFmt; // Copy number format
        });

        targetRow.commit(); // Ghi các thay đổi của row vào sheet
      }

      const items = this.toPirModelExport(data.results);

      for (let i = 0; i < data.results.length; i++) {
        const targetRow = targetWorksheet.getRow(i + 2);
        Object.values(items[i]).forEach((value: any, colIndex) => {
          const targetCell = targetRow.getCell(colIndex + 1);
          const sourceCell = sourceWorksheet.getRow(2).getCell(colIndex + 1); // Lấy format từ dòng 2

          let finalValue = value; // Giá trị thực tế
          let numFmt = sourceCell.numFmt; // Format Excel

          // Nếu giá trị rỗng, giữ nguyên ''
          if (value === '' || value === null || value === undefined) {
            finalValue = '';
          } else if (sourceCell.numFmt) {
            if (
              sourceCell.numFmt.includes('yyyy') ||
              sourceCell.numFmt.includes('mm') ||
              sourceCell.numFmt.includes('dd')
            ) {
              // Nếu là ngày, giữ đúng kiểu Date
              if (value instanceof Date) {
                finalValue = value;
              } else {
                const parsedDate = new Date(value);
                finalValue = isNaN(parsedDate.getTime()) ? '' : parsedDate;
              }
              numFmt = 'dd/mm/yyyy'; // Format ngày
            } else {
              // Nếu là số hoặc tiền tệ, ép kiểu về number nếu có thể
              const numericValue = Number(value);
              if (!isNaN(numericValue)) {
                finalValue = numericValue;
              }
            }
          }

          targetCell.value = finalValue; // Gán giá trị vào ô

          if (numFmt) {
            targetCell.numFmt = numFmt; // Áp dụng định dạng Excel
          }
        });

        targetRow.commit();
      }

      // worksheets.addRows(data.results);
      const buffer = await targetWorkbook.xlsx.writeBuffer();

      // return buffer;

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'export-pir.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private toPirModelExport(pirs: PriceInformationRecordModel[]) {
    const items = [];
    for (let i = 0; i < (pirs || []).length; i++) {
      items.push({
        vendorCode: pirs[i].vendor?.code || '',
        vendorName: pirs[i].vendor?.name || '',
        materialCode: pirs[i].material?.code || '',
        materialName: pirs[i].material?.name || '',
        plantCode: pirs[i].plant?.code || '',
        plantName: pirs[i].plant?.name || '',
        purchaseGroupCode: pirs[i].purchaseGroup?.code || '',
        purchaseGroupName: pirs[i].purchaseGroup?.name || '',
        purchaseUnit: pirs[i].purchaseUnit || '',
        miniumOrderQuantity: pirs[i].minimumOrderQuantity || 0,
        regularPurchaseQuantity: pirs[i].regularPurchaseQuantity || 0,
        upperTolerance: pirs[i].upperTolerance || 0,
        lowerTolerance: pirs[i].lowerTolerance || 0,
        vendorLeadTime: pirs[i].vendorLeadtime || 0,
        inforType: (pirs[i].infoType || []).join(', '),
        purchasePrice: pirs[i].purchasePrice || 0,
        currencyCode: pirs[i].currency?.currencyCode || '',
        overPurchaseUnit: pirs[i].overPurchaseUnit || 0,
        effectiveDate: pirs[i].effectiveDate
          ? new Date(pirs[i].effectiveDate)
          : '',
        expirationDate: pirs[i].expirationDate
          ? new Date(pirs[i].expirationDate)
          : '',
        status: getStatusPir(pirs[i].status),
      });
    }

    return items;
  }

  async priceMaterial(conditions: PriceMaterialDto) {
    return await this.priceInformationRecordRepository.priceMaterial(
      conditions,
    );
  }

  async migrationRelationPIR(jwtPayload: any) {
    const pirs = await this.priceInformationRecordRepository.findAll(
      {
        getAll: 1,
        page: 1,
        limit: 10,
        searchString: '',
      },
      jwtPayload,
    );

    for (let i = 0; i < (pirs.results || []).length; i++) {
      const businessUnitIds = pirs.results[i].businessUnitIds;
      if (!businessUnitIds?.length) {
        continue;
      }

      const businessUnits = await this.businessUnitUsecases.getBusinessUnits(
        {
          ids: businessUnitIds,
          getAll: 1,
          page: 0,
          limit: 0,
          searchString: '',
        },
        jwtPayload,
      );

      const pirBusinessUnits = (businessUnits.results || []).filter((item) =>
        businessUnitIds.includes(item.id),
      );

      if (!pirBusinessUnits?.length) {
        continue;
      }

      await this.priceInformationRecordRepository.migrationRelationPIR(
        pirs.results[i].id,
        pirBusinessUnits,
      );
    }
  }

  async getListByIds(
    conditions: GetPIRListByIdsDto,
    jwtPayload,
  ): Promise<ResponseDto<PriceInformationRecordModel>> {
    return await this.priceInformationRecordRepository.getListByIds(
      conditions,
      jwtPayload,
    );
  }
}
