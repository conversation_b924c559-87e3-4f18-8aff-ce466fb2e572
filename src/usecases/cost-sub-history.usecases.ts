import { Inject, Injectable } from '@nestjs/common';
import { ICostSubHistoryRepository } from '../domain/repositories/cost-sub-history.repository';
import { CostSubHistoryModel } from '../domain/model/cost-sub-history.model';

@Injectable()
export class CostSubHistoryUsecases {
  constructor(
    @Inject(ICostSubHistoryRepository)
    private readonly costSubHistoryRepository: ICostSubHistoryRepository,
  ) {}

  async createCostSubHistory(
    data: CostSubHistoryModel,
  ): Promise<CostSubHistoryModel> {
    return await this.costSubHistoryRepository.createCostSubHistory(data);
  }
}
