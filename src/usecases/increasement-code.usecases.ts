import { Inject, Injectable } from '@nestjs/common';
import { En<PERSON>tyManager } from 'typeorm';
import { ECodeType } from '../domain/config/enums/code-type.enum';
import { IIncreasementCodeRepository } from '../domain/repositories/increasement-code.repository';
import { IncreasementCodeModel } from '../domain/model/increasement-code.model';

@Injectable()
export class IncreasementCodeUsecases {
  constructor(
    @Inject(IIncreasementCodeRepository)
    private readonly increasementCodeRepository: IIncreasementCodeRepository,
  ) {}

  async generateIncreasementCode(codeType: ECodeType) {
    const increasementCode =
      await this.increasementCodeRepository.getIncreasementCodeByType(codeType);

    if (!increasementCode) {
      const createIncreasementCode =
        await this.increasementCodeRepository.createIncreasementCode(
          new IncreasementCodeModel({
            sequencing: 1,
            codeType,
          }),
        );
      return `${codeType}-${createIncreasementCode.sequencing
        .toString()
        .padStart(6, '0')}`;
    } else {
      await this.increasementCodeRepository.updateIncreasementCode({
        sequencing: increasementCode.sequencing++,
        ...increasementCode,
      });
      return `${codeType}-${(increasementCode.sequencing++)
        .toString()
        .padStart(6, '0')}`;
    }
  }
}
