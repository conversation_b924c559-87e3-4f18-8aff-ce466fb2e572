import { Inject, Injectable } from '@nestjs/common';
import { IOwnerDeviceTokenRepository } from '../domain/repositories/owner-device-token.repository';
import { OwnerDeviceTokenEntity } from '../infrastructure/entities/owner-device-token.entity';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { CreateOnwerDeviceTokenDto } from '../controller/owner-device-token/dtos/create-owner-device-token.dto';
import { DeleteOnwerDeviceTokenDto } from '../controller/owner-device-token/dtos/delete-owner-device-token.dto';

@Injectable()
export class OwnerDeviceTokenUsecases {
  constructor(
    @Inject(IOwnerDeviceTokenRepository)
    private readonly ownerDeviceTokenRepository: IOwnerDeviceTokenRepository,
  ) {}

  async createOwnerDeviceToken(
    conditions: CreateOnwerDeviceTokenDto,
  ): Promise<void> {
    if (conditions.ownerId && conditions.platform) {
      await this.ownerDeviceTokenRepository.deleteOnwerDeviceToken(
        conditions.deviceToken,
        conditions.platform,
      );

      const data: OwnerDeviceTokenEntity = {
        ...conditions,
        ownerId: conditions.ownerId || '',
        platform: conditions.platform || EPlatform.E_PURCHASE,
      };

      await this.ownerDeviceTokenRepository.createOwnerDeviceToken(data);
    }
  }

  async deleteOnwerDeviceToken(
    conditions: DeleteOnwerDeviceTokenDto,
  ): Promise<void> {
    if (conditions.platform) {
      await this.ownerDeviceTokenRepository.deleteOnwerDeviceToken(
        conditions.deviceToken,
        conditions.platform || EPlatform.E_PURCHASE,
        conditions.ownerId,
      );
    }
  }

  // async getOwnerDeviceTokens(
  //   ownerIds: string[],
  //   platform: EPlatform,
  // ): Promise<OwnerDeviceTokenEntity[]> {
  //   return await this.ownerDeviceTokenRepository.getOwnerDeviceTokens(
  //     ownerIds,
  //     platform,
  //   );
  // }
}
