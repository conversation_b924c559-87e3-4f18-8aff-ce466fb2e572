import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import { resolve } from 'path';
import { GetApprovalProcessDetailListDto } from '../controller/approval-process-detail/dtos/get-approval-process-detail-list.dto';
import {
  ApprovalLevelDto,
  StatusLevel,
} from '../controller/approve/dtos/approve.dto';
import { GetPurchaseRequestDto } from '../controller/purchase-request/dtos/get-all-purchase-request.dto';
import { GetPRWithBudgetDto } from '../controller/purchase-request/dtos/get-pr-with-budget.dto';
import { PriceMaterialDto } from '../controller/purchase-request/dtos/price-material.dto';
import { PurchaseRequestDetailDto } from '../controller/purchase-request/dtos/purchase-request-detail.dto';
import {
  PurchaseRequestDto,
  UpdatePurchaseRequestDto,
} from '../controller/purchase-request/dtos/purchase-request.dto';
import { exportFileUploadPath } from '../domain/config/constant';
import { EStatusActualSpending } from '../domain/config/enums/actual-spending.enum';
import {
  APPROVER_KEYS,
  EApprover,
  levelMap,
} from '../domain/config/enums/approver.enum';
import {
  EBudgetCreateType,
  EBudgetType,
} from '../domain/config/enums/budget.enum';
import { EConditionType } from '../domain/config/enums/condition-type.enum';
import { EProcessType } from '../domain/config/enums/process-type.enum';
import {
  Priority,
  State,
  Status,
} from '../domain/config/enums/purchase-request.enum';
import { EPurchaseRequestStatus } from '../domain/config/enums/purchasing.enum';
import { EStatus } from '../domain/config/enums/status.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { approvalErrorDetails } from '../domain/messages/error-details/approval';
import { measureErrorDetails } from '../domain/messages/error-details/measure';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessage,
} from '../domain/messages/error-message';
import { ApprovalProcessDetailModel } from '../domain/model/approval-process-detail.model';
import { BudgetModel } from '../domain/model/budget.model';
import { PurchaseRequestModel } from '../domain/model/purchase_request.model';
import { StaffModel } from '../domain/model/staff.model';
import { IActualSpendingRepository } from '../domain/repositories/actual-spending.repository';
import { IPrApprovalFlowRepository } from '../domain/repositories/prApprovalFlowRepository.repository';
import { IPurchaseOrderRepository } from '../domain/repositories/purchaseOrderRepository.repository';
import { IPurchaseRequestRepository } from '../domain/repositories/purchaseRequestRepository.repository';
import { emailTemplateService } from '../infrastructure/config/email-transport-config/email-template.service';
import { emailTransportService } from '../infrastructure/config/email-transport-config/email.service';
import { ApproveType } from '../infrastructure/entities/approval-level.entity';
import { HttpService } from '../infrastructure/http/http.service';
import {
  checkEffectiveTimeOverlaps,
  codeToIdMap,
  comparisonResult,
  convertToGMT7,
  embedIdInUuid,
} from '../utils/common';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendPost } from '../utils/http';
import { ApprovalProcessDetailUsecases } from './approval-process-detail.usecases';
import { BudgetCodeUsecases } from './budget-code.usecases';
import { BudgetUsecases } from './budget.usecases';
import { BusinessUnitUsecases } from './business-unit.usecases';
import { CostcenterSubaccountUsecases } from './costcenter-subaccount.usecases';
import { CurrencyUnitUsecases } from './currency-unit.usecases';
import { DepartmentUsecases } from './department.usecases';
import { FileUsecases } from './file.usecases';
import { FunctionUnitUsecases } from './function-unit.usecases';
import { MaterialGroupUsecases } from './material-group.usecases';
import { MaterialUsecases } from './material.usecases';
import { MeasureUsecases } from './measure.usecases';
import { PlantUsecases } from './plant.usecases';
import { priceInformationRecordUsecases } from './price_information_record.usecases';
import { ProcessTypeUsecases } from './process-type.usecases';
import { ProcessUsecases } from './process.usecases';
import { PurchaseRequestTypeUsecases } from './purchase-request-type.usecases';
import { PurchasingDepartmentUsecases } from './purchasing-department.usecases';
import { PurchasingGroupUsecases } from './purchasing-group.usecases';
import { SectorUsecases } from './sector.usecases';
import { StaffUsecases } from './staff.usecase';
import { SupplierUsecases } from './supplier.usecases';
import { SectorModel } from '../domain/model/sector.model';
import { BusinessUnitModel } from '../domain/model/business-unit.model';
import { PurchaseRequestTypeModel } from '../domain/model/purchase-request-type.model';
import { BudgetCodeModel } from '../domain/model/budget-code.model';
import { CostcenterSubaccountModel } from '../domain/model/costcenter-subaccount.model';
import { PurchasingDepartmentModel } from '../domain/model/purchasing-department.model';
import { PurchasingGroupModel } from '../domain/model/purchasing-group.model';
import { ProcessTypeModel } from '../domain/model/process-type.model';
import { PlantModel } from '../domain/model/plant.model';
import { FunctionUnitModel } from '../domain/model/function-unit.model';
import { DepartmentModel } from '../domain/model/department.model';
import { ActualSpendingModel } from '../domain/model/actual-spending.model';
import { ResendEmailPrDto } from '../controller/purchase-request/dtos/resend-email-pr.dto';
import { WarehouseUsecases } from './warehouse.usecases';
import { warehouseErrorDetails } from '../domain/messages/error-details/7050-warehouse';

@Injectable()
export class purchaseRequestUsecases {
  private transporter;

  constructor(
    @Inject('IPurchaseRequestRepository')
    private readonly purchaseRequestRepository: IPurchaseRequestRepository,
    @Inject('IPurchaseOrderRepository')
    private readonly purchaseOrderRepository: IPurchaseOrderRepository,
    @Inject('IPrApprovalFlowRepository')
    private readonly prApprovalFlowRepository: IPrApprovalFlowRepository,
    private readonly pirUsescases: priceInformationRecordUsecases,
    private configService: ConfigService,
    private _emailTransportService: emailTransportService,
    private _emailTemplateService: emailTemplateService,
    private readonly httpService: HttpService,
    private fileUsecases: FileUsecases,

    private businessUnitUsecases: BusinessUnitUsecases,
    private sectorUsecases: SectorUsecases,
    private staffUsecases: StaffUsecases,
    private purchaseReuqestTypeUsecases: PurchaseRequestTypeUsecases,
    private budgetCodeUsecases: BudgetCodeUsecases,
    private costcenterSubaccountUsecases: CostcenterSubaccountUsecases,
    private purchasingDepartmentUsecases: PurchasingDepartmentUsecases,
    private purchasingGroupUsecases: PurchasingGroupUsecases,
    private currencyUnitUsecases: CurrencyUnitUsecases,
    private processTypeUsecases: ProcessTypeUsecases,
    private plantUsecases: PlantUsecases,
    private functionUnitUsecases: FunctionUnitUsecases,
    private departmentUsecases: DepartmentUsecases,
    private readonly processUsecases: ProcessUsecases,
    private readonly materialUsecases: MaterialUsecases,
    private readonly supplierUsecases: SupplierUsecases,
    private readonly materialGroupUsecases: MaterialGroupUsecases,
    private readonly budgetUsecases: BudgetUsecases,
    private readonly approvalProcessDetailUsecases: ApprovalProcessDetailUsecases,
    @Inject(IActualSpendingRepository)
    private readonly actualSpendingRepository: IActualSpendingRepository,
    private readonly measureUsecases: MeasureUsecases,
    private readonly warehouseUsecases: WarehouseUsecases,
  ) {
    this.transporter = this._emailTransportService.getTransporter();
  }

  async findAll(
    paginationDto: GetPurchaseRequestDto,
    jwtPayload,
    authorization,
    isExport: boolean,
    isNeedDetails: boolean,
  ): Promise<ResponseDto<PurchaseRequestModel>> {
    // if (paginationDto.businessUnitIds && paginationDto.companyIds) {
    //   const businessUnits = await this.businessUnitUsecases.getBusinessUnits(
    //     {
    //       ids: paginationDto.businessUnitIds,
    //       companyIds: paginationDto.companyIds,
    //       getAll: 1,
    //       limit: 5,
    //       page: 1,
    //       searchString: '',
    //     },
    //     jwtPayload,
    //   );
    //   const businessUnitIds =
    //     businessUnits?.results?.map((item) => item.id) || [];
    //   paginationDto.businessUnitIds =
    //     businessUnitIds && businessUnitIds.length ? businessUnitIds : [null];
    // }

    // if (!jwtPayload?.isSuperAdmin) {
    //   const [businessUnits, sectors] = await Promise.all([
    //     this.businessUnitUsecases.getBusinessUnits(
    //       {
    //         getAll: 1,
    //         limit: 5,
    //         page: 1,
    //         searchString: '',
    //       },
    //       jwtPayload,
    //     ),
    //     this.sectorUsecases.getSectors(
    //       {
    //         getAll: 1,
    //         limit: 5,
    //         page: 1,
    //         searchString: '',
    //       },
    //       jwtPayload,
    //     ),
    //   ]);

    //   paginationDto.businessUnitIds = paginationDto.businessUnitIds?.length
    //     ? paginationDto.businessUnitIds.filter((item) =>
    //         (businessUnits?.results || [])
    //           .map((data) => data.id)
    //           .includes(item),
    //       )
    //     : (businessUnits?.results || []).map((data) => data.id);
    //   paginationDto.sectorIds = paginationDto.sectorIds?.length
    //     ? paginationDto.sectorIds.filter((item) =>
    //         (sectors?.results || []).map((data) => data.id).includes(item),
    //       )
    //     : (sectors?.results || []).map((data) => data.id);

    //   if (!paginationDto.businessUnitIds?.length) {
    //     paginationDto.businessUnitIds = [null];
    //   }

    //   if (!paginationDto.sectorIds?.length) {
    //     paginationDto.sectorIds = [null];
    //   }
    // }

    const data = await this.purchaseRequestRepository.findAll(
      paginationDto,
      jwtPayload,
      isNeedDetails,
    );

    if (isNeedDetails) {
      const prDetailIds = [
        ...new Set(
          (data.results.flatMap((item) => item.details) || [])
            .map((item) => item.id)
            .filter(Boolean),
        ),
      ];

      let poDetails = [];
      if (prDetailIds?.length) {
        poDetails =
          await this.purchaseOrderRepository.numberPoCreatedByPrDetailIds(
            prDetailIds,
          );
      }

      const promises = data.results.map(async (item) => {
        let detailsPromises;
        if (isNeedDetails) {
          detailsPromises = item.details.map(async (detail) => {
            return {
              ...detail,
              numberPoCreated: (poDetails || [])
                .filter((item) => item.prDetailId == detail.id)
                .reduce((a, b) => a + Number(b.quantity || 0), 0),
            };
          });
        } else {
          detailsPromises = [];
        }

        const mappedDetails = await Promise.all(detailsPromises);

        return {
          ...item,
          details: mappedDetails,
        };
      });

      // Chờ tất cả các Promise hoàn thành
      const mappedDataArray = await Promise.all(promises);

      // Thay thế dữ liệu cũ bằng dữ liệu mới đã được mapping
      return {
        ...data,
        results: mappedDataArray,
      };
    } else {
      return data;
    }
  }

  async findById(id: number): Promise<PurchaseRequestModel> {
    return await this.purchaseRequestRepository.findOne(id);
  }

  async findOne(
    id: number,
    authorization: string,
  ): Promise<PurchaseRequestModel> {
    // Tìm dữ liệu PurchaseRequestEntity bằng id
    const item = await this.purchaseRequestRepository.findOne(id, true);
    const approvalLevels =
      await this.prApprovalFlowRepository.findApprovalLevels(id, null);

    if (!item) {
      throw new NotFoundException(`Purchase request with id ${id} not found`);
    }

    const sortedLevels = approvalLevels.sort((a, b) => a.level - b.level);
    item.levels = sortedLevels;

    return item;
  }

  async create(
    purchases: PurchaseRequestDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<
    | PurchaseRequestModel
    | { approvalSteps: ApprovalLevelDto[]; staffApprovalWorkflowId: string }
  > {
    ///Validate data and Calculate Remaining Budget
    purchases.details = await this.validatePurchase(
      purchases,
      jwtPayload,
      authorization,
    );

    const purchase = await this.purchaseRequestRepository.createPurchaseRequest(
      purchases,
      jwtPayload,
      authorization,
    );

    const processedItem = await this.handleObjectProcessing(
      purchase?.id,
      authorization,
    );

    let approvalSteps: ApprovalLevelDto[] = [];
    if (!purchases.approvalLevelDtos?.length) {
      ///Handle approval levels
      const dataApprovalLevels = await this.handleProcessAndApprovalLevel(
        purchases,
        jwtPayload,
        authorization,
        purchase?.id,
      );

      if (Array.isArray(dataApprovalLevels)) {
        approvalSteps.push(...dataApprovalLevels);
      } else {
        return dataApprovalLevels;
      }
    } else {
      approvalSteps = purchases.approvalLevelDtos;

      approvalSteps.forEach((level) => {
        level.approveType = ApproveType.PR;
        level.purchaseRequestId = purchase.id;
      });

      const emailChecks = approvalSteps?.map((step) => step.email);
      await this.staffUsecases.getStaffByEmails(
        { emails: emailChecks },
        jwtPayload,
      );
    }

    if (!approvalSteps?.length) {
      throw new HttpException(
        approvalErrorDetails.E_5410(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const approvalLevels =
      await this.prApprovalFlowRepository.createApprovalLevels(approvalSteps);
    purchases.history = approvalLevels.map((level) => {
      const { id, ...rest } = level;
      return rest;
    });

    // lowestStaffApprovalWorkflowLevel.status = 'Pending';
    await this.prApprovalFlowRepository.changeStatus(
      { levels: approvalLevels },
      purchase.id,
    );
    // purchase.approval_steps = approvalSteps;
    purchase.levels = approvalLevels;

    const sortedApprovalLevels = approvalLevels.sort(
      (a, b) => a.level - b.level,
    );

    await this.handleEmailNotification(
      sortedApprovalLevels[0],
      purchase,
      processedItem,
      authorization,
    );

    const refId = embedIdInUuid(purchase.id);
    await this.purchaseRequestRepository.updatePurchaseRequest(purchase.id, {
      refId,
      history: purchases.history?.map((history) => {
        return {
          ...history,
          status: Status.Pending,
        };
      }),
    });

    return purchase;
  }

  private getApprovalLevelByAprovalProcessDetail(
    approvalProcessDetails: ResponseDto<ApprovalProcessDetailModel>,
    approvalLevel: ApprovalLevelDto,
    approver: EApprover,
    purchases: PurchaseRequestDto | UpdatePurchaseRequestDto,
  ) {
    if (!approvalProcessDetails?.results.length) {
      throw new HttpException(
        approvalErrorDetails.E_5411('Không tìm thấy quy trình duyệt chi tiết'),
        HttpStatus.BAD_REQUEST,
      );
    }
    //Only get top 1
    const approvalProcessDetail = approvalProcessDetails.results[0];

    if (approvalProcessDetail.prCreatedById !== purchases.requesterId) {
      throw new HttpException(
        approvalErrorDetails.E_5411(
          'Người yêu cầu không tìm thấy trong quy trình duyệt chi tiết',
        ),
        HttpStatus.BAD_REQUEST,
      );
    }

    const approverKey = APPROVER_KEYS.get(approver);
    if (!approverKey) {
      throw new HttpException(
        approvalErrorDetails.E_5411('Cấp duyệt không hợp lệ'),
        HttpStatus.BAD_REQUEST,
      );
    }

    const approverData = approvalProcessDetail[approverKey] as StaffModel;
    if (!approverData) {
      throw new HttpException(
        approvalErrorDetails.E_5411(
          `Người duyệt cấp ${approver} không tìm thấy trong quy trình duyệt chi tiết`,
        ),
        HttpStatus.BAD_REQUEST,
      );
    }

    // Trả về thông tin duyệt
    return {
      ...approvalLevel,
      email: approverData?.email || '',
      name: `${approverData?.lastName || ''} ${approverData?.firstName || ''}`,
    };
  }

  private traverseAndCheckNodes(
    purchases: PurchaseRequestDto,
    processes: any,
    checkConditionNode: (
      process: any,
      purchases: any,
      authorization: any,
    ) => boolean,
    processMatching: any,
    authorization: any,
    level: number = 1,
  ) {
    // Lọc các node thoả điều kiện tại cấp hiện tại
    const validNodes =
      processes?.filter((node) =>
        checkConditionNode(node, purchases, authorization),
      ) || [];

    if (validNodes.length === 0) {
      return [];
    }

    // Kiểm tra xem có các node con hay không
    validNodes.forEach((parentNode) => {
      if (parentNode.children && parentNode.children.length > 0) {
        // Đệ quy kiểm tra các node con của cấp tiếp theo
        this.traverseAndCheckNodes(
          purchases,
          parentNode.children,
          checkConditionNode,
          processMatching,
          authorization,
          level + 1,
        );
      } else {
        // checkConditionNode(parentNode, purchases);
        processMatching.push(parentNode.id);
      }
    });
    //
    return validNodes;
  }

  checkConditionNode(process: any, purchases: PurchaseRequestDto) {
    const conditionDetails = process?.conditionDetails;

    for (const conditionDetail of conditionDetails) {
      let value, target;
      switch (conditionDetail.type) {
        case EConditionType.SECTOR:
          value = conditionDetail.sectors.map((sector) => sector.id);
          target = purchases.sectorId;
          break;
        case EConditionType.BUSINESS_UNIT:
          value = conditionDetail.businessUnits.map((bu) => bu.id);
          target = purchases.businessUnitId;
          break;
        case EConditionType.PR_TYPE:
          value = conditionDetail.prTypes.map((prType) => prType.id);
          target = purchases.typePrId;
          break;
        case EConditionType.COST_CENTER:
          value = conditionDetail.costCenters.map(
            (costCenter) => costCenter.id,
          );
          target = purchases.costCenterId;
          break;
        case EConditionType.BUDGET_CODE:
          value = conditionDetail.budgetCodes.map(
            (budgetCode) => budgetCode.id,
          );
          target = purchases.budgetCodeId;
          break;
        case EConditionType.VALUE_PR:
          value = purchases.details.reduce(
            (total, detail) => total + Number(detail.totalAmount || 0),
            0,
          );
          target = conditionDetail.valuePR;
          break;
        case EConditionType.BUDGET_OVERRUN:
          value =
            Number(purchases.details[0].remainingBudget) < 0
              ? Math.abs(Number(purchases.details[0].remainingBudget || 0))
              : 0;
          target = Number(conditionDetail.budgetOverrun);
          break;
        case EConditionType.CHECK_BUDGET: {
          value = purchases.isCheckBudget ? 1 : 0;
          target = conditionDetail.comparisonType;
          break;
        }
        case EConditionType.PLANT: {
          value = conditionDetail.plants?.map((plant) => plant.id);
          target = purchases.plantId;
          break;
        }
        case EConditionType.PROCESS_TYPE: {
          value = conditionDetail.processTypes?.map(
            (processType) => processType.id,
          );
          target = purchases.processTypeId;
          break;
        }
        case EConditionType.BUDGET_OVERRUN_RATE: {
          // // Tổng số tiền detail
          // const total_amount_details = purchases?.details?.reduce((total, detail) => total + Number(detail.total_amount || 0), 0) || 0;

          // if (total_amount_details <= purchases.details[0].remaining_budget) continue;
          // // [Ngân sách] (Cộng ngân sách cha (NEW) với tất cả ngân sách con (TĂNG/GIẢM))
          // const total_budget = purchases?.details?.reduce((total, detail) => total + Number(detail.budget || 0), 0) || 0;
          // // Tỷ lệ vượt ngân sách = Tổng số tiền - Ngân sách còn lại / Giá trị ngân sách (bao gồm Ngân sách mới, Ngân sách điều chỉnh tăng, Ngân sách điều chỉnh giảm) * 100
          // const budgetOverrunRate = total_budget > 0 ? (total_amount_details - Number(purchases.details[0].remaining_budget)) / (total_budget * 100) : 0;

          const firstDetail = purchases?.details[0];

          if (Number(firstDetail?.remainingBudget || 0) >= 0) {
            value = 0;
          } else {
            value =
              Number(firstDetail?.budget || 0) > 0
                ? (Math.abs(Number(firstDetail?.remainingBudget || 0)) /
                    Number(firstDetail?.budget || 0)) *
                  100
                : 0;
          }

          target = conditionDetail.budgetOverrunRate;
          break;
        }
        case EConditionType.DEPARTMENT: {
          value = conditionDetail.departments.map(
            (department) => department.id,
          );
          target = purchases.departmentId;
          break;
        }
        case EConditionType.FUNCTION_UNIT:
          value = conditionDetail.functionUnits.map((fu) => fu.id);
          target = purchases.functionUnitId;
          break;
        case EConditionType.FIRST_BUDGET: {
          const firstDetail = purchases?.details[0];
          value = purchases.budgetCodeId ? Number(firstDetail?.budget || 0) : 0;
          target = Number(conditionDetail.firstBudget || 0);
          break;
        }
        default:
          return false;
      }

      if (!comparisonResult(conditionDetail.comparisonType, value, target)) {
        return false;
      }
    }

    return true;
  }

  async update(
    id: number,
    purchases: UpdatePurchaseRequestDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<
    | PurchaseRequestModel
    | { approvalSteps: ApprovalLevelDto[]; staffApprovalWorkflowId: string }
  > {
    ///Validate data and Calculate Remaining Budget
    purchases.details = await this.validatePurchase(
      purchases,
      jwtPayload,
      authorization,
      id,
    );

    const purchaseRequest =
      await this.purchaseRequestRepository.updatePurchaseRequest(id, purchases);

    const processedItem = await this.handleObjectProcessing(id, authorization);
    let approvalSteps: ApprovalLevelDto[] = [];
    if (!purchases.statusPr || purchases.statusPr === Status.Draft) {
      await this.prApprovalFlowRepository.deleteApprovalLevels(id, null);

      ///Handle approval levels
      const dataApprovalLevels = await this.handleProcessAndApprovalLevel(
        purchases,
        jwtPayload,
        authorization,
        id,
      );

      if (Array.isArray(dataApprovalLevels)) {
        approvalSteps.push(...dataApprovalLevels);
      }
    } else {
      approvalSteps = purchases?.approvalLevelDtos?.length
        ? purchases.approvalLevelDtos
        : (await this.prApprovalFlowRepository.findApprovalLevels(id, null)) ||
          [];
    }

    if (!approvalSteps.length) {
      throw new HttpException(
        approvalErrorDetails.E_5410(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const approvalLevels = approvalSteps;
    //Tìm cấp duyệt cho phép chọn nhân viên
    const hasStaffApprovalWorkflow = approvalLevels.find(
      (level) => level.allowSelect,
    );
    //Tìm cấp duyệt là kế toán
    const isAccountantApproved = approvalLevels.find(
      (level) => level.isAccountantApproved,
    );

    let sortedApprovalLevels = approvalLevels?.sort(
      (a, b) => a.level - b.level,
    );

    // If no approval levels exist in purchases
    // Nếu trong luồng duyệt tồn tại cấp duyệt tích "Cho phép điều chỉnh” và không có cấp duyệt nào tích “Kế toán”,
    // thì khi nhấn button “Lưu và gửi” hoặc gửi sẽ hiển thị popup hiển thị các cấp duyệt và cho phép chọn người duyệt
    if (
      !purchases?.approvalLevelDtos?.length &&
      hasStaffApprovalWorkflow &&
      !isAccountantApproved
    ) {
      return {
        approvalSteps: approvalLevels,
        staffApprovalWorkflowId:
          hasStaffApprovalWorkflow.staffApprovalWorkflowId,
      };
    } else {
      approvalLevels.forEach((level) => {
        level.approveType = ApproveType.PR;
        level.purchaseRequestId = id;
      });
      //
      const emailChecks = approvalLevels
        .filter((step) => step.email)
        .map((step) => step.email);
      await this.staffUsecases.getStaffByEmails(
        { emails: emailChecks },
        jwtPayload,
      );

      if (approvalLevels.length > 0) {
        await this.prApprovalFlowRepository.deleteApprovalLevels(id, null);

        const newApprovalLevels =
          await this.prApprovalFlowRepository.createApprovalLevels(
            approvalLevels,
          );

        sortedApprovalLevels = newApprovalLevels.sort(
          (a, b) => a.level - b.level,
        );

        purchases.history = newApprovalLevels.map(({ id, ...rest }) => rest);

        await this.prApprovalFlowRepository.changeStatus(
          { levels: newApprovalLevels },
          purchaseRequest.id,
        );
        purchaseRequest.levels = newApprovalLevels;
      }
    }

    await this.handleEmailNotification(
      sortedApprovalLevels[0],
      purchaseRequest,
      processedItem,
      authorization,
    );

    const refId = embedIdInUuid(purchaseRequest.id);
    await this.purchaseRequestRepository.updatePurchaseRequest(
      purchaseRequest.id,
      {
        refId,
        history: purchases.history?.map((history) => {
          return {
            ...history,
            status: Status.Pending,
          };
        }),
      },
    );

    return purchaseRequest;
  }

  private async handleEmailNotification(
    approvalLevel,
    purchaseRequest: PurchaseRequestModel,
    processedItem: PurchaseRequestModel,
    authorization,
  ) {
    if (approvalLevel?.isSendMail) {
      if (!approvalLevel.email) {
        throw new HttpException(
          approvalErrorDetails.E_5410(`Không tìm thấy nhân viên`),
          HttpStatus.BAD_REQUEST,
        );
      }
      const mailOptions = {
        from: this.configService.get<string>('MAIL_SMTP_USER'),
        to: approvalLevel.email,
        subject: `Phê duyệt PR #${processedItem.id}${processedItem.businessUnit?.code ? ` - ${processedItem.businessUnit.code}` : ''}${processedItem.reason ? ` - ${processedItem.reason}` : ''}`,
        html: this._emailTemplateService.getApprovalPREmailHtml(
          processedItem,
          approvalLevel.email,
          `${this.configService.get<string>('STATUS_PR_EMAIL_URL')}?levelId=${approvalLevel.id}&prId=${purchaseRequest.id}`,
          approvalLevel,
        ),
      };
      if (purchaseRequest.statusPr !== Status.Draft) {
        try {
          if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
            await sendPost(
              QueueServiceApiUrlsConst.SEND_MAIL_QUEUE(),
              mailOptions,
              { authorization },
            );
          } else {
            await this.transporter.sendMail(mailOptions);
          }
        } catch (e) {
          console.log(`SEND EMAIL PR ERROR: ${e}`);
        }
      }
    }
  }

  private async callServices(
    requestData: PurchaseRequestDto,
    authorization,
    jwtPayload: any,
  ): Promise<{
    sector: SectorModel;
    businessUnit: BusinessUnitModel;
    requester: StaffModel;
    typePr: PurchaseRequestTypeModel;
    budgetCode: BudgetCodeModel;
    costCenter: CostcenterSubaccountModel;
    purchaseOrg: PurchasingDepartmentModel;
    purchaseGroup: PurchasingGroupModel;
    purchaser: StaffModel;
    processType: ProcessTypeModel;
    plant: PlantModel;
    functionUnit: FunctionUnitModel;
    department: DepartmentModel;
  }> {
    const [
      sector,
      businessUnit,
      requester,
      typePr,
      budgetCode,
      costCenter,
      purchaseOrg,
      purchaseGroup,
      purchaser,
      processType,
      plant,
      functionUnit,
      department,
    ] = await Promise.all([
      requestData.sectorId
        ? await this.sectorUsecases.getDetailSector(
            { id: requestData.sectorId },
            jwtPayload,
          )
        : null,
      requestData.businessUnitId
        ? await this.businessUnitUsecases.getDetailBusinessUnit(
            { id: requestData.businessUnitId },
            jwtPayload,
          )
        : null,
      requestData.requesterId
        ? await this.staffUsecases.getDetailStaff(
            { staffId: requestData.requesterId },
            jwtPayload,
          )
        : null,
      requestData.typePrId
        ? await this.purchaseReuqestTypeUsecases.getPrTypeDetail(
            { id: requestData.typePrId },
            jwtPayload,
          )
        : null,
      requestData.budgetCodeId
        ? await this.budgetCodeUsecases.getBudgetCodeDetail(
            { id: requestData.budgetCodeId },
            jwtPayload,
          )
        : null,
      requestData.costCenterId
        ? await this.costcenterSubaccountUsecases.getCostcenterSubaccountDetail(
            { id: requestData.costCenterId },
            jwtPayload,
          )
        : null,
      requestData.purchaseOrgId
        ? await this.purchasingDepartmentUsecases.getPurchasingDepartmentDetail(
            { id: requestData.purchaseOrgId },
            jwtPayload,
          )
        : null,
      requestData.purchaseGroupId
        ? await this.purchasingGroupUsecases.getPurchasingGroupDetail(
            { id: requestData.purchaseGroupId },
            jwtPayload,
          )
        : null,
      requestData.purchaserId
        ? await this.staffUsecases.getDetailStaff(
            { staffId: requestData.purchaserId },
            jwtPayload,
          )
        : null,
      requestData.processTypeId
        ? await this.processTypeUsecases.getDetailProcessType({
            id: requestData.processTypeId,
          })
        : null,
      requestData.plantId
        ? await this.plantUsecases.getPlantDetail(
            { id: requestData.plantId },
            jwtPayload,
          )
        : null,
      requestData.functionUnitId
        ? await this.functionUnitUsecases.getDetailFunctionUnit(
            { id: requestData.functionUnitId },
            jwtPayload,
          )
        : null,
      requestData.departmentId
        ? await this.departmentUsecases.getDetailDepartment(
            { id: requestData.departmentId },
            jwtPayload,
          )
        : null,
    ]);

    return {
      sector,
      businessUnit,
      requester,
      typePr,
      budgetCode,
      costCenter,
      purchaseOrg,
      purchaseGroup,
      purchaser,
      processType,
      plant,
      functionUnit,
      department,
    };
  }

  private async handleObjectProcessing(id: number, authorization) {
    const pr = await this.purchaseRequestRepository.findOne(id);
    return pr;
  }

  async findPRWithBudget(
    conditions: GetPRWithBudgetDto,
  ): Promise<PurchaseRequestModel[]> {
    return await this.purchaseRequestRepository.findPRWithBudget(conditions);
  }

  async countPr() {
    return await this.purchaseRequestRepository.countPr();
  }

  async changeStatePr(prIds: number[]) {
    if (prIds?.length) {
      const data = await this.purchaseRequestRepository.findPrWithPo(prIds);

      for (const pr of data) {
        const countPrDetails = pr.details.length;
        const prDetailsHasPoDetails = pr.details.filter(
          (detail) =>
            (detail.poDetails || []).filter((item) => item.purchaseOrder)
              .length,
        ).length;

        if (countPrDetails == prDetailsHasPoDetails) {
          await this.purchaseRequestRepository.updateState(
            pr.id,
            State.Completed,
          );
          continue;
        }

        if (prDetailsHasPoDetails == 0) {
          await this.purchaseRequestRepository.updateState(
            pr.id,
            State.Pending,
          );
          continue;
        }

        await this.purchaseRequestRepository.updateState(
          pr.id,
          State.PartialCompleted,
        );
      }
    }
  }

  async newCalculateRemainingBudget(
    data: PurchaseRequestDetailDto[],
    authorization: string,
    createdAt: Date,
    jwtPayload: any,
  ): Promise<PurchaseRequestDetailDto[]> {
    const budgetCodeIds = (data || [])
      .map((item) => item.budgetCodeId)
      .filter(Boolean);

    const allBudgets: BudgetModel[] = [];
    if (budgetCodeIds?.length) {
      const [opexApiResponse, capexApiResponse] = await Promise.all([
        this.budgetUsecases.getBudgetList(
          {
            budgetCodeIds: budgetCodeIds,
            budgetType: EBudgetType.OPEX,
            limit: 5,
            page: 1,
            getAll: 1,
            isLock: true,
            createTypes: [EBudgetCreateType.NEW],
            createdAt: createdAt.toISOString(),
            searchString: '',
          },
          jwtPayload,
        ),
        this.budgetUsecases.getBudgetList(
          {
            budgetCodeIds: budgetCodeIds,
            budgetType: EBudgetType.CAPEX,
            limit: 5,
            page: 1,
            getAll: 1,
            isLock: true,
            createTypes: [EBudgetCreateType.NEW],
            createdAt: createdAt.toISOString(),
            searchString: '',
          },
          jwtPayload,
        ),
      ]);

      const dataOpexes = opexApiResponse?.results || [];
      const dataCapexes = capexApiResponse?.results || [];
      allBudgets.push(...dataOpexes, ...dataCapexes);
    }
    const currencyUnitResponse =
      await this.currencyUnitUsecases.getCurrencyUnits(
        {
          page: 1,
          limit: 10,
          getAll: 1,
          searchString: '',
        },
        jwtPayload,
      );
    const currencyUnits = currencyUnitResponse?.results || [];
    const currencyVND = (currencyUnits || []).find(
      (item) => item.currencyCode == 'VND',
    );
    if (!currencyVND) {
      throw new HttpException(errorMessage.E_1103(), HttpStatus.BAD_REQUEST);
    }

    const dataCalculated = [];

    for (let i = 0; i < data.length; i++) {
      const matchingBudgets = allBudgets.filter(
        (budget) =>
          budget.budgetCodeId === data[i].budgetCodeId &&
          new Date(budget.effectiveStartDate) <= createdAt &&
          createdAt <= new Date(budget.effectiveEndDate),
      );

      if (matchingBudgets?.length) {
        const firstBudget = matchingBudgets[0];
        const existBudget = data.find(
          (item) => item.budgetId == firstBudget.id,
        );

        /// Filter tỷ giá để quy đổi tiền cho budget
        const matchCurrencyExchangeBudget = (
          (currencyUnits || []).find(
            (item) => item.id == firstBudget.currencyUnitId,
          )?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              firstBudget.effectiveStartDate,
              item.effectiveStartDate,
              firstBudget.effectiveEndDate,
              item.effectiveEndDate,
            ) && item.exchangeBudget,
        );

        const matchCurrencyExchangeFilter = (
          currencyVND?.currencyExchanges || []
        ).find(
          (item) =>
            checkEffectiveTimeOverlaps(
              firstBudget.effectiveStartDate,
              item.effectiveStartDate,
              firstBudget.effectiveEndDate,
              item.effectiveEndDate,
            ) && item.exchangeBudget,
        );

        if (!matchCurrencyExchangeBudget || !matchCurrencyExchangeFilter) {
          throw new HttpException(
            errorMessage.E_1103(),
            HttpStatus.BAD_REQUEST,
          );
        }

        // Gán budget id để lưu vết xem ngân sách dự chi được tính toán từ budget nào
        data[i].budgetId = firstBudget.id;
        data[i].matchingBudget = firstBudget;

        // [Ngân sách] (Cộng ngân sách cha (NEW) với tất cả ngân sách con (TĂNG/GIẢM))
        let totalBudget = 0;
        for (let i = 0; i < matchingBudgets.length; i++) {
          totalBudget +=
            ((matchingBudgets[i].totalValue +
              (matchingBudgets[i]?.children || []).reduce(function (
                sum,
                budget,
              ) {
                if (budget.isLock == true) {
                  if (budget.createType == 'INCREASE') {
                    return sum + (budget.totalValue || 0);
                  }
                  if (budget.createType == 'DECREASE') {
                    return sum - (budget.totalValue || 0);
                  }
                }

                return sum;
              }, 0)) *
              matchCurrencyExchangeBudget.exchangeRate) /
            matchCurrencyExchangeFilter.exchangeRate;
        }

        data[i].budget = totalBudget;

        if (existBudget) {
          data[i].budgetId = existBudget.budgetId;
          data[i].matchingBudget = existBudget.matchingBudget;
          data[i].remainingBudget = existBudget.remainingBudget;
        } else {
          // [Tổng số tiền các item (tương ứng) trong PO đã được duyệt cuối (trạng thái Đã duyệt)]
          const poDetails =
            await this.purchaseOrderRepository.getPoDetailsForRemainingBudget(
              firstBudget.budgetCodeId,
              new Date(firstBudget.effectiveStartDate),
              new Date(firstBudget.effectiveEndDate),
            );

          let totalCostPoDetails = 0;
          for (const item of poDetails) {
            const levels = (item?.purchaseOrder?.levels || []).sort(
              (a, b) => b.level - a.level,
            );
            let lastLevelApproved;
            // Nếu PO chưa được duyệt thì sẽ lấy thời gian updated_at của level là kế toán duyệt
            if (levels[0].status == StatusLevel.Approved) {
              lastLevelApproved = levels[0];
            } else {
              lastLevelApproved = levels.find(
                (item) =>
                  item.isAccountantApproved == true &&
                  item.status == StatusLevel.Approved,
              );
            }

            if (!lastLevelApproved) {
              throw new HttpException(
                approvalErrorDetails.E_5413(`Không tìm thấy cấp duyệt phù hợp`),
                HttpStatus.NOT_FOUND,
              );
            }

            const matchCurrencyExchangeFilter = (
              currencyVND?.currencyExchanges || []
            ).find(
              (item) =>
                checkEffectiveTimeOverlaps(
                  new Date(lastLevelApproved.updatedAt),
                  item.effectiveStartDate,
                  null,
                  item.effectiveEndDate,
                ) && item.exchangeBudget == false,
            );

            if (!matchCurrencyExchangeFilter) {
              throw new HttpException(
                errorMessage.E_1103(),
                HttpStatus.BAD_REQUEST,
              );
            }

            totalCostPoDetails +=
              Number(item.totalAmount || 0) /
              matchCurrencyExchangeFilter.exchangeRate;
          }

          /// Tìm actual spending
          // const actualSpendingResponse =
          //   await this.actualSpendingRepository.actualSpendingForReport({
          //     postingDateFrom: firstBudget.effectiveStartDate.toString(),
          //     postingDateTo: firstBudget.effectiveEndDate.toString(),
          //     statuses: [EStatusActualSpending.CONFIRMED],
          //     budgetCodeCode: firstBudget?.budgetCode?.code || null,
          //   });

          const actualSpendingResponse: ActualSpendingModel[] = [];

          let totalAcutalSpending = 0;
          /// Quy đổi actual spending sang VND
          for (const actualSpending of actualSpendingResponse || []) {
            const matchCurrencyExchangeFilter = (
              currencyVND?.currencyExchanges || []
            ).find(
              (item) =>
                checkEffectiveTimeOverlaps(
                  actualSpending.postingDate,
                  item.effectiveStartDate,
                  null,
                  item.effectiveEndDate,
                ) && item.exchangeBudget == false,
            );

            if (!matchCurrencyExchangeFilter) {
              throw new HttpException(
                errorMessage.E_1103(),
                HttpStatus.BAD_REQUEST,
              );
            }

            let docAmount = 0;
            if (firstBudget.budgetType == EBudgetType.CAPEX) {
              docAmount = Number(actualSpending.docAmount || 0);
            } else {
              const taxCode = (actualSpending.taxCode || '')
                .split('')[0]
                ?.toLowerCase();
              docAmount = ['a', 'c', 'e', 'g'].includes(taxCode)
                ? Number(actualSpending.docAmount || 0)
                : Number(actualSpending.docAmount || 0) *
                  (1 +
                    (isNaN(Number(actualSpending.taxRate || 0))
                      ? 0
                      : Number(actualSpending.taxRate || 0) / 100));
            }

            totalAcutalSpending +=
              (docAmount * Number(actualSpending.exchangeRate || 0)) /
              matchCurrencyExchangeFilter.exchangeRate;
          }

          // [Tổng tiền các item (tương ứng) trong PR/PO đang tạo]
          const filterItems = data.filter(
            (item) => item.budgetCodeId == firstBudget.budgetCodeId,
          );
          const totalCostCurrentItems = filterItems.reduce(
            (sum, item) =>
              sum + (item.estimatedPrice || 0) * (item.quantity || 0),
            0,
          );

          // [Ngân sách dự chi còn lại]  = [Ngân sách] - [Tổng số tiền các item (tương ứng) trong PO đã được duyệt cuối (trạng thái Đã duyệt)] - [Tổng tiền các item (tương ứng) trong PR/PO đang tạo]
          data[i].remainingBudget =
            data[i].budget -
            (totalCostPoDetails > totalAcutalSpending
              ? totalCostPoDetails
              : totalAcutalSpending) -
            totalCostCurrentItems;
        }
      } else {
        data[i].budgetId = null;
        data[i].budget = 0;
        data[i].remainingBudget = 0;
      }

      dataCalculated.push(data[i]);
    }

    return dataCalculated;
  }

  async exportPr(
    paginationDto: GetPurchaseRequestDto,
    jwtPayload,
    authorization,
  ) {
    paginationDto.getAll = 1;
    const data = await this.findAll(
      paginationDto,
      jwtPayload,
      authorization,
      true,
      true,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(__dirname, '../domain/template-export/template-export-pr.xlsx'),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Copy value
        targetCell.style = { ...cell.style }; // Copy full style
        targetCell.border = cell.border; // Copy border
        targetCell.font = cell.font; // Copy font
        targetCell.alignment = cell.alignment; // Copy alignment
        targetCell.numFmt = cell.numFmt; // Copy number format
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toPrModel(data.results);
      for (let i = 0; i < items.length; i++) {
        const targetRow = targetWorksheet.getRow(i + 2);
        Object.values(items[i]).forEach((value: any, colIndex) => {
          const targetCell = targetRow.getCell(colIndex + 1);
          const sourceCell = sourceWorksheet.getRow(2).getCell(colIndex + 1); // Lấy format từ dòng 2

          let finalValue = value; // Giá trị thực tế
          let numFmt = sourceCell.numFmt; // Format Excel

          // Nếu giá trị rỗng, giữ nguyên ''
          if (value === '' || value === null || value === undefined) {
            finalValue = '';
          } else if (sourceCell.numFmt) {
            if (
              sourceCell.numFmt.includes('yyyy') ||
              sourceCell.numFmt.includes('mm') ||
              sourceCell.numFmt.includes('dd')
            ) {
              // Nếu là ngày, giữ đúng kiểu Date
              if (value instanceof Date) {
                finalValue = value;
              } else {
                const parsedDate = new Date(value);
                finalValue = isNaN(parsedDate.getTime()) ? '' : parsedDate;
              }
              numFmt = 'dd/mm/yyyy'; // Format ngày
            } else {
              // Nếu là số hoặc tiền tệ, ép kiểu về number nếu có thể
              const numericValue = Number(value);
              if (!isNaN(numericValue)) {
                finalValue = numericValue;
              }
            }
          }

          targetCell.value = finalValue; // Gán giá trị vào ô

          if (numFmt) {
            targetCell.numFmt = numFmt; // Áp dụng định dạng Excel
          }
        });

        targetRow.commit();
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-pr.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toPrModel(prs: PurchaseRequestModel[]) {
    const items = [];

    for (let i = 0; i < (prs || []).length; i++) {
      const sortedHistory =
        prs[i]?.history
          ?.filter((item) => item.level)
          ?.sort((a, b) => a.level - b.level) || [];
      for (let y = 0; y < (prs[i]?.details || []).length; y++) {
        items.push({
          prCode: prs[i]?.id || '',
          requester:
            `${prs[i]?.requester?.firstName || ''} ${prs[i]?.requester?.lastName || ''}`.trim(),
          createdAt: prs[i]?.createdAt ? new Date(prs[i]?.createdAt) : '',
          sectorCode: prs[i]?.sector?.code || '',
          sectorName: prs[i]?.sector?.name || '',
          functionUnitName: prs[i]?.functionUnit?.name || '',
          businessUnitCode: prs[i]?.businessUnit?.code || '',
          businessUnitName: prs[i]?.businessUnit?.name || '',
          typePr: prs[i]?.typePr?.name || '',
          isCheckBudget: this.getIsCheckBudget(prs[i]?.isCheckBudget || false),
          budgetCode: prs[i]?.details[y]?.budgetCode?.code || '',
          purchaseOrgCode: prs[i]?.purchaseOrg?.code || '',
          purchaseOrgName: prs[i]?.purchaseOrg?.name || '',
          purchaseGroupCode: prs[i]?.purchaseGroup?.code || '',
          purchaseGroupName: prs[i]?.purchaseGroup?.name || '',
          priority: this.getPriorityPr(prs[i]?.priority),
          reason: prs[i]?.reason || '',
          purchaser:
            `${prs[i]?.purchaser?.firstName || ''} ${prs[i]?.purchaser?.lastName || ''}`.trim(),
          status: this.getStatusPr(prs[i].statusPr),
          totalAmount: (prs[i].details || []).reduce(
            (sum, item) => sum + Number(item.totalAmount),
            0,
          ),
          costCenter: prs[i]?.details[y]?.costCenter?.name || '',
          materialCode: prs[i]?.details[y]?.material?.code || '',
          materialName: prs[i]?.details[y]?.materialName || '',
          materialGroup: prs[i]?.details[y]?.materialGroupName || '',
          deliveryTime: prs[i]?.details[y]?.deliveryTime
            ? new Date(prs[i]?.details[y]?.deliveryTime)
            : '',
          warehouse: prs[i]?.details[y]?.warehouse?.code || '',
          estimatedPrice: prs[i]?.details[y]?.estimatedPrice || 0,
          standardQuantity: prs[i]?.details[y]?.standardQuantity || 0,
          inventoryNumber: prs[i]?.details[y]?.inventoryNumber || 0,
          quantity: prs[i]?.details[y]?.quantity || 0,
          unit: prs[i]?.details[y]?.unit || '',
          total: prs[i]?.details[y]?.totalAmount || 0,
          note: prs[i]?.details[y]?.note || '',
          approver1: sortedHistory[0]?.name || '',
          approvedDate1:
            sortedHistory[0]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[0]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
          approver2: sortedHistory[1]?.name || '',
          approvedDate2:
            sortedHistory[1]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[1]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
          approver3: sortedHistory[2]?.name || '',
          approvedDate3:
            sortedHistory[2]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[2]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
          approver4: sortedHistory[3]?.name || '',
          approvedDate4:
            sortedHistory[3]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[3]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
          approver5: sortedHistory[4]?.name || '',
          approvedDate5:
            sortedHistory[4]?.status == 'Approved'
              ? convertToGMT7(sortedHistory[4]?.updatedAt, 'DD/MM/YYYY HH:mm')
              : '',
        });
      }
    }

    return items;
  }

  getIsCheckBudget(isCheck: boolean) {
    switch (isCheck) {
      case true:
        return 'Có';
      case false:
        return 'Không';
      default:
        return '';
    }
  }

  getPriorityPr(priority?: Priority) {
    switch (priority) {
      case Priority.Urgent:
        return 'Khẩn cấp';
      case Priority.Important:
        return 'Quan trọng';
      case Priority.Normal:
        return 'Bình thường';
      default:
        return '';
    }
  }

  getStatusPr(status?: Status) {
    switch (status) {
      case Status.Temporary:
        return 'Lưu tạm';
      case Status.Pending:
        return 'Chờ duyệt';
      case Status.InProgress:
        return 'Đang duyệt';
      case Status.Approved:
        return 'Duyệt';
      case Status.Rejected:
        return 'Từ chối';
      case Status.Rechecked:
        return 'Kiểm tra lại';
      case Status.Cancel:
        return 'Hủy';
      case Status.WaitingProcess:
        return 'Chờ duyệt';
      case Status.Draft:
        return 'Lưu tạm';
      default:
        return '';
    }
  }

  async getPrDetails(ids: number[]) {
    return await this.purchaseRequestRepository.getPrDetails(ids);
  }

  async getPrDetailByIds(ids: number[]) {
    return await this.purchaseRequestRepository.getPrDetailByIds(ids);
  }

  async getPrListByIds(ids: number[]) {
    return await this.purchaseRequestRepository.getPrListByIds(ids);
  }

  async priceMaterial(conditions: PriceMaterialDto[]) {
    const detailPromises = conditions.map(async (detail) => {
      // 1
      const pir = await this.pirUsescases.priceMaterial(detail);
      if (pir) {
        return {
          materialCodeId: detail.materialCodeId,
          estimatedPrice: pir.purchasePrice || 0,
          key: detail.key,
        };
      }

      // 2
      const prDetail =
        await this.purchaseRequestRepository.priceMaterial(detail);
      if (prDetail) {
        return {
          materialCodeId: detail.materialCodeId,
          estimatedPrice: prDetail.estimatedPrice || 0,
          key: detail.key,
        };
      }

      return {
        materialCodeId: detail.materialCodeId,
        estimatedPrice: 0,
        key: detail.key,
      };
    });

    return await Promise.all(detailPromises);
  }

  async validatePurchase(
    purchases: PurchaseRequestDto | UpdatePurchaseRequestDto,
    jwtPayload: any,
    authorization: string,
    id?: number,
  ) {
    let pr: PurchaseRequestModel;
    if (id) {
      const pr = await this.purchaseRequestRepository.findOneById(id);
      if (!pr) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_5110()),
          HttpStatus.NOT_FOUND,
        );
      }
    }

    const throwIfInvalid = (condition: boolean, error: TErrorMessage) => {
      if (condition)
        throw new HttpException(getErrorMessage(error), HttpStatus.BAD_REQUEST);
    };

    const budgetCodeIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.budgetCodeId)
          .filter(Boolean),
      ),
    ];
    const materialCodeIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.materialCodeId)
          .filter(Boolean),
      ),
    ];
    const costCenterIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.costCenterId)
          .filter(Boolean),
      ),
    ];
    const materialGroupIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.materialGroupId)
          .filter(Boolean),
      ),
    ];
    const measureIds = [
      ...new Set(
        (purchases.details || []).map((item) => item.measureId).filter(Boolean),
      ),
    ];
    const warehouseIds = [
      ...new Set(
        (purchases.details || [])
          .map((item) => item.warehouseId)
          .filter(Boolean),
      ),
    ];

    const [
      budgetCodes,
      materialCodes,
      costCenters,
      materialGroups,
      measureCodes,
      warehouses,
    ] = await Promise.all([
      this.budgetCodeUsecases.getListByIds(
        {
          ids: budgetCodeIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.materialUsecases.getListByIds(
        {
          ids: materialCodeIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.costcenterSubaccountUsecases.getListByIds(
        {
          ids: costCenterIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.materialGroupUsecases.getListByIds(
        {
          ids: materialGroupIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.measureUsecases.getListByIds(
        {
          ids: measureIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
      this.warehouseUsecases.getListByIds(
        {
          ids: warehouseIds,
          getAll: 1,
          page: 1,
          limit: 10,
          searchString: '',
        },
        jwtPayload,
      ),
    ]);

    const processDetail = async (
      detail: PurchaseRequestDetailDto,
      checkBudget: boolean,
      dataDetails: {
        budgetCodes: Record<string, any>;
        materialCodes: Record<string, any>;
        costCenters: Record<string, any>;
        materialGroups: Record<string, any>;
        measureCodes: Record<string, any>;
        warehouses: Record<string, any>;
      },
    ) => {
      throwIfInvalid(
        !detail.materialCodeId && !detail.costCenterId && !detail.budgetCodeId,
        errorMessage.E_5023(),
      );

      throwIfInvalid(
        detail.materialCodeId &&
          !dataDetails.materialCodes[detail.materialCodeId],
        errorMessage.E_5003(),
      );
      throwIfInvalid(
        detail.budgetCodeId && !dataDetails.budgetCodes[detail.budgetCodeId],
        errorMessage.E_5024(),
      );
      throwIfInvalid(
        detail.costCenterId && !dataDetails.costCenters[detail.costCenterId],
        errorMessage.E_5025(),
      );
      throwIfInvalid(
        detail.materialGroupId &&
          !dataDetails.materialGroups[detail.materialGroupId],
        errorMessage.E_5026(),
      );
      throwIfInvalid(
        detail.measureId && !dataDetails.measureCodes[detail.measureId],
        measureErrorDetails.E_6050(),
      );
      throwIfInvalid(
        detail.warehouseId && !dataDetails.warehouses[detail.warehouseId],
        warehouseErrorDetails.E_7050(),
      );

      const budgetCheckFlag = checkBudget
        ? 'NO_CHECK_BUDGET'
        : 'YES_CHECK_BUDGET';
      throwIfInvalid(
        dataDetails.materialCodes[detail.materialCodeId]?.checkBudget ===
          budgetCheckFlag,
        errorMessage[checkBudget ? 'E_5101' : 'E_5102'](),
      );

      detail.materialGroupName =
        detail.materialGroupName ||
        dataDetails.materialGroups[detail.materialGroupId]?.name;
      detail.deliveryTime = new Date(convertToGMT7(detail.deliveryTime));
    };

    if (purchases.isCheckBudget) {
      throwIfInvalid(!purchases.budgetCodeId, errorMessage.E_5100());
    }

    const dataBudgetCodes = codeToIdMap(budgetCodes.results, 'id');
    const dataMaterialCodes = codeToIdMap(materialCodes.results, 'id');
    const dataCostCenters = codeToIdMap(costCenters.results, 'id');
    const dataMaterialGroups = codeToIdMap(materialGroups.results, 'id');
    const dataMeasureCodes = codeToIdMap(measureCodes.results, 'id');
    const dataWarehouses = codeToIdMap(warehouses.results, 'id');

    const chunkDetails = _.chunk(purchases.details, 50);
    for (let i = 0; i < (chunkDetails?.length || 0); i++) {
      await Promise.all(
        (chunkDetails[i] || []).map((detail) =>
          processDetail(detail, purchases.isCheckBudget, {
            budgetCodes: dataBudgetCodes,
            materialCodes: dataMaterialCodes,
            costCenters: dataCostCenters,
            materialGroups: dataMaterialGroups,
            measureCodes: dataMeasureCodes,
            warehouses: dataWarehouses,
          }),
        ),
      );
    }

    const result = await this.callServices(
      purchases,
      authorization,
      jwtPayload,
    );

    const validations = [
      { condition: !result.sector, error: errorMessage.E_5105() },
      { condition: !result.businessUnit, error: errorMessage.E_5106() },
      { condition: !result.requester, error: errorMessage.E_5107() },
      {
        condition:
          purchases.purchaserId &&
          !result.requester?.purchasers?.find(
            (item) => item.id === purchases.purchaserId,
          ),
        error: errorMessage.E_5021(),
      },
      { condition: !result.typePr, error: errorMessage.E_5108() },
      {
        condition: purchases.costCenterId && !result.costCenter,
        error: errorMessage.E_5025(),
      },
      {
        condition: purchases.purchaseOrgId && !result.purchaseOrg,
        error: errorMessage.E_5005(),
      },
      {
        condition: purchases.purchaseGroupId && !result.purchaseGroup,
        error: errorMessage.E_5006(),
      },
      {
        condition: purchases.budgetCodeId && !result.budgetCode,
        error: errorMessage.E_5024(),
      },
      {
        condition: result?.typePr?.status !== EPurchaseRequestStatus.ACTIVE,
        error: errorMessage.E_5109(),
      },
      { condition: !result.processType, error: errorMessage.E_5030() },
      {
        condition: purchases.plantId && !result.plant,
        error: errorMessage.E_5031(),
      },
      {
        condition: purchases.functionUnitId && !result.functionUnit,
        error: errorMessage.E_5033(),
      },
      {
        condition: purchases.departmentId && !result.department,
        error: errorMessage.E_5032(),
      },
    ];

    validations.forEach(({ condition, error }) =>
      throwIfInvalid(condition, error),
    );

    if (result.processType?.hasInventoryStandard == true) {
      for (const detail of purchases.details) {
        if (
          (!detail.standardQuantity && detail.standardQuantity != 0) ||
          (!detail.inventoryNumber && detail.inventoryNumber != 0)
        ) {
          throw new HttpException(
            approvalErrorDetails.E_5420(),
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    }

    purchases.details = await this.newCalculateRemainingBudget(
      purchases.details,
      authorization,
      pr?.createdAt ? new Date(pr.createdAt) : new Date(),
      jwtPayload,
    );

    return purchases.details;
  }

  async handleProcessAndApprovalLevel(
    purchases: PurchaseRequestDto | UpdatePurchaseRequestDto,
    jwtPayload: any,
    authorization: string,
    id: number,
  ): Promise<
    | ApprovalLevelDto[]
    | { approvalSteps: ApprovalLevelDto[]; staffApprovalWorkflowId: string }
  > {
    let approvalSteps: ApprovalLevelDto[] = [];

    const [processes, managers, approvalProcessDetails] = await Promise.all([
      this.processUsecases.getProcessList({
        statuses: [EStatus.ACTIVE],
        types: [EProcessType.PR],
        getAll: 1,
        page: 1,
        limit: 10,
        searchString: '',
      }),
      this.staffUsecases.getDetailStaff(
        { staffId: purchases?.requesterId },
        jwtPayload,
      ),
      this.approvalProcessDetailUsecases.getApprovalProcessDetails(
        plainToInstance(GetApprovalProcessDetailListDto, {
          sectorIds: [purchases.sectorId],
          departmentIds: purchases.departmentId
            ? [purchases.departmentId]
            : [null],
          functionUnitIds: purchases.functionUnitId
            ? [purchases.functionUnitId]
            : [null],
          prTypeIds: [purchases.typePrId],
          businessUnitIds: [purchases.businessUnitId],
          prCreatedByIds: purchases.requesterId
            ? [purchases.requesterId]
            : [null],
          limit: 1,
          page: 1,
          searchString: '',
        }),

        jwtPayload,
      ),
    ]);

    if (!processes.results?.length) {
      throw new HttpException(
        approvalErrorDetails.E_5410(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const processMatching = {};
    const managersOfRequester = managers?.managers || [];

    for (const process of processes.results) {
      processMatching[process.id] = [];
      const processDetail = await this.processUsecases.getDetailProcessGraph(
        process.id,
      );
      const node = processDetail;

      this.traverseAndCheckNodes(
        purchases,
        node,
        (node, purchases) => this.checkConditionNode(node, purchases),
        processMatching[process.id],
        authorization,
      );
    }

    if (!Object.keys(processMatching).length) {
      throw new HttpException(
        approvalErrorDetails.E_5410(),
        HttpStatus.BAD_REQUEST,
      );
    }

    for (const [key, value] of Object.entries(processMatching)) {
      if (!value || !(value as string[]).length) {
        throw new HttpException(
          approvalErrorDetails.E_5410(),
          HttpStatus.BAD_REQUEST,
        );
      }
      const processParentDetail =
        await this.processUsecases.getDetailParentProcess(key);

      if (processParentDetail?.parentApprovalWorkflows?.length) {
        //Approval-Level
        for (const parentApprovalWorkflow of processParentDetail.parentApprovalWorkflows) {
          const { processes, staffApprovalWorkflows, sendEmailToCreator } =
            parentApprovalWorkflow;
          const processIds = processes.map((process) => process.id);

          if (
            processIds?.some((processId) =>
              (value as string[]).includes(processId),
            )
          ) {
            // Sort levels in ascending order
            const sortedStaffApprovalWorkflows = staffApprovalWorkflows.sort(
              (a, b) => a.level - b.level,
            );

            const getManagerDetails = (approver: EApprover) => {
              const level = levelMap[approver];
              const manager = managersOfRequester?.find(
                (manager) => manager.level == level,
              );
              if (!manager) {
                throw new HttpException(
                  approvalErrorDetails.E_5411(
                    `Không tìm thấy quản lý cấp ${level} duyệt`,
                  ),
                  HttpStatus.BAD_REQUEST,
                );
              }
              return {
                email: manager?.email || '',
                name: `${manager?.lastName || ''} ${manager?.firstName || ''}`,
                id: manager?.id,
              };
            };

            const getStaffDetails = async (
              id: string,
              errorMessage: TErrorMessage,
            ) => {
              const staff = await this.staffUsecases.getDetailStaff(
                { staffId: id },
                jwtPayload,
              );
              if (!staff) {
                throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
              }
              return {
                email: staff?.email || '',
                name: `${staff?.lastName || ''} ${staff?.firstName || ''}`,
              };
            };

            for (
              let index = 0;
              index < sortedStaffApprovalWorkflows.length;
              index++
            ) {
              const workflow = sortedStaffApprovalWorkflows[index];

              if (!workflow) {
                throw new HttpException(
                  approvalErrorDetails.E_5410(`Không tìm thấy luồng duyệt`),
                  HttpStatus.BAD_REQUEST,
                );
              }

              // sortedStaffApprovalWorkflows.forEach(async (workflow, index) => {

              let approvalLevel: ApprovalLevelDto = {
                role: `Người duyệt cấp ${workflow.level}`,
                isSendMail: workflow.receiveEmail,
                level: workflow.level,
                purchaseRequestId: id,
                status: index === 0 ? 'Pending' : null,
                approveType: ApproveType.PR,
                isSendMailCreator: sendEmailToCreator,
                isAccountantApproved: workflow.accountantApproved,
                allowSelect: workflow.allowSelect,
                staffApprovalWorkflowId: workflow.id,
              };

              switch (workflow.approver) {
                case EApprover.LEVEL_1:
                case EApprover.LEVEL_2:
                case EApprover.LEVEL_3:
                case EApprover.LEVEL_4: {
                  const { email, name } = getManagerDetails(workflow.approver);
                  approvalLevel.email = email;
                  approvalLevel.name = name;

                  break;
                }
                case EApprover.ASSIGN: {
                  const { email, name } = await getStaffDetails(
                    workflow.staffId,
                    approvalErrorDetails.E_5411(
                      'Không tìm thấy người chỉ định duyệt',
                    ),
                  );
                  approvalLevel.email = email;
                  approvalLevel.name = name;
                  break;
                }
                case EApprover.POSITION: {
                  const staffList = await this.staffUsecases.approverByPosition(
                    {
                      statuses: [EStatus.ACTIVE],
                      positionIds: [workflow?.position?.id],
                      businessUnitIds: [purchases.businessUnitId],
                      functionUnitIds: [purchases.functionUnitId],
                      departmentIds: [purchases.departmentId],
                    },
                    jwtPayload,
                    authorization,
                  );

                  if (!staffList?.length) {
                    throw new HttpException(
                      approvalErrorDetails.E_5411('Không tìm thấy người duyệt'),
                      HttpStatus.BAD_REQUEST,
                    );
                  }

                  const staff = staffList[0];

                  approvalLevel.email = staff?.email || '';
                  approvalLevel.name = `${staff?.lastName || ''} ${staff?.firstName || ''}`;
                  break;
                }
                case EApprover.PR_APPROVER_1:
                case EApprover.PR_APPROVER_2:
                case EApprover.PR_APPROVER_3:
                case EApprover.PR_APPROVER_4:
                case EApprover.PR_APPROVER_5:
                case EApprover.PR_APPROVER_6:
                case EApprover.PR_APPROVER_7: {
                  approvalLevel = this.getApprovalLevelByAprovalProcessDetail(
                    approvalProcessDetails,
                    approvalLevel,
                    workflow.approver,
                    purchases,
                  );
                  break;
                }
              }

              approvalSteps.push(approvalLevel);
            }
            //TODO: Chờ KH confirm
            const allowSelectStaffApprovalWorkflow =
              sortedStaffApprovalWorkflows?.find(
                (workflow) => workflow.allowSelect,
              );
            if (
              allowSelectStaffApprovalWorkflow &&
              !sortedStaffApprovalWorkflows?.find(
                (workflow) => workflow.accountantApproved,
              )
            ) {
              return {
                approvalSteps,
                staffApprovalWorkflowId: allowSelectStaffApprovalWorkflow?.id,
              };
            }
            break;
          }
        }
      }
    }

    return approvalSteps;
  }

  async resendEmailPr(data: ResendEmailPrDto, authorization?: string) {
    const prs = await this.purchaseRequestRepository.getPrsForResendEmail(
      data.prIds,
    );

    for (const pr of prs) {
      pr.levels = pr.levels?.sort((a, b) => a.level - b.level) || [];
      await this.handleEmailNotification(pr.levels[0], pr, pr, authorization);
    }

    return { message: 'Successfully!!!' };
  }
}
