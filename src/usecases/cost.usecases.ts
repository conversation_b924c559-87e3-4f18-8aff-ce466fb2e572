import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as moment from 'moment';
import { resolve } from 'path';
import { CreateCostDto } from '../controller/cost/dtos/create-cost.dto';
import { GetCostListDto } from '../controller/cost/dtos/get-cost-list.dto';
import { GetDetailCostDto } from '../controller/cost/dtos/get-detail-cost.dto';
import { UpdateCostDto } from '../controller/cost/dtos/update-cost.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import {
  EColumnImportCost,
  ECostStatus,
} from '../domain/config/enums/cost.enum';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { costErrorDetails } from '../domain/messages/error-details/cost';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { CostModel } from '../domain/model/cost.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { ICostRepository } from '../domain/repositories/cost.repository';
import {
  checkValuesEmptyRowExcel,
  getStatus,
  getStatusCost,
  getValueOrResult,
} from '../utils/common';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { ImportCostDto } from '../controller/import/dtos/import-cost.dto';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';

@Injectable()
export class CostUsecases {
  constructor(
    @Inject(ICostRepository)
    private readonly costRepository: ICostRepository,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
  ) {}

  async createCost(
    data: CreateCostDto,
    authorization: string,
  ): Promise<CostModel> {
    await this.checkExistCostByCode(data.code);

    const costModel = new CostModel(data);

    const cost = await this.costRepository.createCost(costModel);

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: cost.name,
        refId: cost.id,
        refCode: cost.code,
        type: EDataRoleType.COST,
        isEnabled: cost.status === ECostStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization, 'x-api-key': process.env.API_KEY },
    );

    return cost;
  }

  async updateCost(
    id: string,
    updateCostDto: UpdateCostDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<CostModel> {
    await this.checkExistCostByCode(updateCostDto.code, id);

    await this.getDetailCost(
      plainToInstance(GetDetailCostDto, {
        id: id,
      }),
      jwtPayload,
    );

    const cost = await this.costRepository.updateCost(id, updateCostDto);

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: cost.name,
        refCode: cost.code,
        isEnabled: cost.status === ECostStatus.ACTIVE,
      },
      {
        authorization,
        'x-api-key': process.env.API_KEY,
      },
    );

    return cost;
  }

  async getCosts(
    conditions: GetCostListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<CostModel>> {
    return await this.costRepository.getCosts(conditions, jwtPayload);
  }

  async deleteCost(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailCost(
      plainToInstance(GetDetailCostDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.costRepository.deleteCost(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistCostByCode(code: string, id?: string): Promise<void> {
    const cost = await this.costRepository.getCostByCode(code, id);

    if (cost) {
      throw new HttpException(
        getErrorMessage(costErrorDetails.E_6025()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getDetailCost(conditions: GetDetailCostDto, jwtPayload: any) {
    const detail = await this.costRepository.getDetailCost(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(costErrorDetails.E_6024()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async getCostsByCodesWithRole(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ): Promise<CostModel[]> {
    return await this.costRepository.getCostsByCodesWithRole(
      codes,
      jwtPayload,
      isNeedPermission,
    );
  }

  async exportCost(conditions: GetCostListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.costRepository.getCosts(conditions, jwtPayload);

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-cost.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toCostModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-cost.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toCostModel(costs: CostModel[]) {
    const items = [];

    for (let i = 0; i < costs.length; i++) {
      items.push({
        code: costs[i].code || '',
        name: costs[i].name || '',
        groupCost: costs[i].groupCost || '',
        status: getStatusCost(costs[i].status),
      });
    }

    return items;
  }

  async importCost(
    file: Express.Multer.File,
    jwtPayload: any,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.COST,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createCostDtos: CreateCostDto[] = [];
        const updateCostDtos: UpdateCostDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportCost.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const costs = await this.costRepository.getCostsByCodesWithRole(
          [...new Set(codes)],
          jwtPayload,
          false,
        );

        let totalRowHasValue = 0;

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportCost.CODE, // First Cell
            EColumnImportCost.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          const code = getValueOrResult(
            row,
            EColumnImportCost.CODE,
          )?.toString();

          const name = getValueOrResult(
            row,
            EColumnImportCost.NAME,
          )?.toString();

          const groupCost = getValueOrResult(
            row,
            EColumnImportCost.COST_GROUP,
          )?.toString();

          const status = getStatus(
            CostModel,
            getValueOrResult(row, EColumnImportCost.STATUS)?.toString(),
          );

          const costObject = {
            id: undefined,
            name,
            code,
            groupCost,
            status,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };

          if (!code) {
            errors.push({
              error: getErrorMessage(
                costErrorDetails.E_6027(),
                'Mã chi phí không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const checkCode = costs.find((item) => item.code == code);
            if (checkCode) {
              costObject.id = checkCode.id;
            }

            const checkDup = codes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(
                  costErrorDetails.E_6029(),
                  'Cost bị trùng lặp',
                ),
                row: i + 1,
              });
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(
                costErrorDetails.E_6028(),
                'Tên chi phí không được để trống',
              ),
              row: i + 1,
            });
          }

          if (costObject.id) {
            const costDto = plainToInstance(UpdateCostDto, costObject);
            updateCostDtos.push({ ...costDto, createdAt: undefined });
          } else {
            const costDto = plainToInstance(CreateCostDto, costObject);
            createCostDtos.push(costDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportCostDto = {
          dataCosts: createCostDtos,
          dataUpdateCosts: updateCostDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_COST(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
