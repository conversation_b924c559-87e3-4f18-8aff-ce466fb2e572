import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';

import { plainToInstance } from 'class-transformer';
import { CreatePositionDto } from '../controller/position/dtos/create-position.dto';
import { GetDetailPositionDto } from '../controller/position/dtos/get-detail-position.dto';
import { GetPositionListDto } from '../controller/position/dtos/get-position-list.dto';
import { UpdatePositionDto } from '../controller/position/dtos/update-position.dto';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EPositionStatus } from '../domain/config/enums/position.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { PositionModel } from '../domain/model/position.model';
import { IPositionRepository } from '../domain/repositories/position.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { positionErrorDetails } from '../domain/messages/error-details/position';

@Injectable()
export class PositionUsecases {
  constructor(
    @Inject(IPositionRepository)
    private readonly positionRepository: IPositionRepository,
  ) {}
  async createPosition(
    data: CreatePositionDto,
    authorization: string,
  ): Promise<PositionModel> {
    await this.checkExistPositionByCode(data.code);

    const positionModel = new PositionModel({
      code: data.code,
      name: data.name,
      description: data.description,
      status: data.status,
    });

    const position =
      await this.positionRepository.createPosition(positionModel);

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: position.name,
        refId: position.id,
        refCode: position.code,
        type: EDataRoleType.POSITION,
        isEnabled: position.status === EPositionStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization },
    );

    return position;
  }

  async updatePosition(
    id: string,
    updatePositionDto: UpdatePositionDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PositionModel> {
    if (updatePositionDto.code)
      await this.checkExistPositionByCode(updatePositionDto.code, id);

    await this.getDetailPosition(
      plainToInstance(GetDetailPositionDto, {
        id: id,
      }),
      jwtPayload,
    );

    const position = await this.positionRepository.updatePosition(
      id,
      updatePositionDto,
    );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: position.name,
        refCode: position.code,
        isEnabled: position.status === EPositionStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    return position;
  }

  async getPositionById(id: string): Promise<PositionModel> {
    const position = await this.positionRepository.getPositionById(id);

    if (!position) {
      throw new HttpException(
        positionErrorDetails.E_2910(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return position;
  }

  async getPositions(
    conditions: GetPositionListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PositionModel>> {
    return await this.positionRepository.getPositions(conditions, jwtPayload);
  }

  async deletePosition(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailPosition(
      plainToInstance(GetDetailPositionDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.positionRepository.deletePosition(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistPositionByCode(code: string, id?: string): Promise<void> {
    const position = await this.positionRepository.getPositionByCode(code, id);

    if (position) {
      throw new HttpException(
        getErrorMessage(positionErrorDetails.E_2911()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getDetailPosition(conditions: GetDetailPositionDto, jwtPayload: any) {
    const detail = await this.positionRepository.getDetailPosition(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        positionErrorDetails.E_2910(),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }
}
