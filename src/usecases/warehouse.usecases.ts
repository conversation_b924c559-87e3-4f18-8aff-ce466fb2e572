import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import { CreateWarehouseDto } from '../controller/warehouse/dtos/create-warehouse.dto';
import { GetDetailWarehouseDto } from '../controller/warehouse/dtos/get-detail-warehouse.dto';
import { GetWarehouseListDto } from '../controller/warehouse/dtos/get-warehouse-list.dto';
import { UpdateWarehouseDto } from '../controller/warehouse/dtos/update-warehouse.dto';
import { ResponseDto } from '../domain/dtos/response.dto';
import { warehouseErrorDetails } from '../domain/messages/error-details/7050-warehouse';
import { errorMessage } from '../domain/messages/error-message';
import { WarehouseModel } from '../domain/model/warehouse.model';
import { IWarehouseRepository } from '../domain/repositories/warehouse.repository';
import { SectorUsecases } from './sector.usecases';
import { resolve } from 'path';
import { FileUsecases } from './file.usecases';
import { exportFileUploadPath } from '../domain/config/constant';
import { EWarehouseStatus } from '../domain/config/enums/warehouse.enum';

@Injectable()
export class WarehouseUsecases {
  constructor(
    @Inject(IWarehouseRepository)
    private readonly warehouseRepository: IWarehouseRepository,
    private readonly sectorUsecases: SectorUsecases,
    private readonly fileUsecases: FileUsecases,
  ) {}

  async createWarehouse(
    data: CreateWarehouseDto,
    jwtPayload: any,
  ): Promise<WarehouseModel> {
    return await this.createAndUpdateWarehouse(data, jwtPayload);
  }

  async updateWarehouse(
    id: string,
    data: UpdateWarehouseDto,
    jwtPayload: any,
  ): Promise<WarehouseModel> {
    return await this.createAndUpdateWarehouse(data, jwtPayload, id);
  }

  private async createAndUpdateWarehouse(
    data: CreateWarehouseDto | UpdateWarehouseDto,
    jwtPayload: any,
    id?: string,
  ): Promise<WarehouseModel> {
    if (id) {
      await this.getDetailWarehouse({ id }, jwtPayload);
    }

    await this.checkExistWarehouseByCode(data.code, id);

    const setSectorIds = [...new Set(data.sectorIds)];

    const sectors = await this.sectorUsecases.getSectorByIds(
      setSectorIds,
      jwtPayload,
    );

    const sectorIds = sectors?.map((item) => item.id) || [];

    const diffSectorIds = _.difference(setSectorIds, sectorIds);

    if (diffSectorIds.length) {
      throw new HttpException(
        errorMessage.E_1036(`sectorIds ${diffSectorIds.join(', ')} not found`),
        HttpStatus.NOT_FOUND,
      );
    }

    data.sectors = sectors;
    console.log(70, data.sectors);
    if (id) {
      return await this.warehouseRepository.updateWarehouse(id, data);
    }

    return await this.warehouseRepository.createWarehouse(data);
  }

  async getDetailWarehouse(conditions: GetDetailWarehouseDto, jwtPayload: any) {
    const detail = await this.warehouseRepository.getDetailWarehouse(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        warehouseErrorDetails.E_7050(),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async deleteWarehouse(id: string, jwtPayload: any): Promise<void> {
    await this.getDetailWarehouse({ id }, jwtPayload);

    const data = await this.warehouseRepository.validateDeleteWarehouse(id);
    if (
      Number(data.totalPoDetails || 0) > 0 ||
      Number(data.totalPrDetails || 0) > 0
    ) {
      throw new HttpException(
        warehouseErrorDetails.E_7052(),
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.warehouseRepository.deleteWarehouse(id);
  }

  async getWarehouses(
    conditions: GetWarehouseListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<WarehouseModel>> {
    return await this.warehouseRepository.getWarehouses(conditions, jwtPayload);
  }

  async getListByIds(
    conditions: GetWarehouseListDto,
    jwtPayload,
  ): Promise<ResponseDto<WarehouseModel>> {
    return await this.warehouseRepository.getWarehouseByIds(
      conditions,
      jwtPayload,
    );
  }

  async exportWarehouse(conditions: GetWarehouseListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.warehouseRepository.getWarehouses(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-warehouse-export.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];
      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toWarehouseModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }
      const buffer = await targetWorkbook.xlsx.writeBuffer();
      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-warehouse.xlsx',
      );
      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );
      return { ...uploadedFile, buffer: null };
    }
  }

  private async toWarehouseModel(warehouses: WarehouseModel[]) {
    const items = [];
    for (let i = 0; i < warehouses?.length; i++) {
      items.push({
        code: warehouses[i].code || '',
        name: warehouses[i].name || '',
        sectorCodes: (warehouses[i].sectors || [])
          .map((item) => item.code)
          .join(', '),
        sectorNames: (warehouses[i].sectors || [])
          .map((item) => item.name)
          .join(', '),
        description: warehouses[i].description || '',
        status: this.getStatusWarehouse(warehouses[i].status),
      });
    }

    return items;
  }

  private getStatusWarehouse(status?: EWarehouseStatus) {
    switch (status) {
      case EWarehouseStatus.ACTIVE:
        return 'Hoạt động';
      case EWarehouseStatus.IN_ACTIVE:
        return 'Không hoạt động';
      default:
        return '';
    }
  }

  private async checkExistWarehouseByCode(
    code: string,
    id?: string,
  ): Promise<void> {
    const warehouse = await this.warehouseRepository.getWarehouseByCode(
      code,
      id,
    );

    if (warehouse) {
      throw new HttpException(
        warehouseErrorDetails.E_7051('Kho đã tồn tại'),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
