import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { FileExportHistoryModel } from '../domain/model/file-export-history.model';
import { EFileExportStatus } from '../domain/config/enums/file-export-history.enum';
import { GetListFileExportHistoryDto } from '../controller/file-export-history/dtos/get-list-file-export-history.dto';
import { IFileExportHistoryRepository } from '../domain/repositories/file-export-history.repository';
import { fileErrorDetails } from '../domain/messages/error-details/file';

@Injectable()
export class FileExportHistoryUsecases {
  public get fileExportHistoryRepository(): IFileExportHistoryRepository {
    return this._fileExportHistoryRepository;
  }
  constructor(
    @Inject(IFileExportHistoryRepository)
    private readonly _fileExportHistoryRepository: IFileExportHistoryRepository,
  ) {}

  async createFileExportHistory(
    data: FileExportHistoryModel,
  ): Promise<FileExportHistoryModel> {
    return await this.fileExportHistoryRepository.createFileExportHistory(data);
  }

  async updateFileExportHistory(id: string, data: any) {
    const fileExportHistoryData = new FileExportHistoryModel({
      id,
      ...data,
    });

    return await this.fileExportHistoryRepository.updateFileExportHistory(
      fileExportHistoryData,
    );
  }

  async deleteFileExportHistory(id: string): Promise<void> {
    const checkFileExportHistory =
      await this.fileExportHistoryRepository.getFileExportHistoryById(id);

    if (!checkFileExportHistory) {
      throw new HttpException(fileErrorDetails.E_7000(), HttpStatus.NOT_FOUND);
    }

    if (
      checkFileExportHistory.status == EFileExportStatus.IN_PROCESS ||
      checkFileExportHistory.status == EFileExportStatus.WAITING
    ) {
      throw new HttpException(
        fileErrorDetails.E_7001(
          `HISTORY_STATUS_IS_${checkFileExportHistory.status}`,
        ),
        HttpStatus.BAD_REQUEST,
      );
    }
    return await this.fileExportHistoryRepository.deleteFileExportHistory(id);
  }

  async getListFileExportHistory(conditions: GetListFileExportHistoryDto) {
    return await this.fileExportHistoryRepository.getListFileExportHistory(
      conditions,
    );
  }

  async getFileExportHistoryDetail(id: string) {
    const detail =
      await this.fileExportHistoryRepository.getFileExportHistoryById(id);

    return detail;
  }
}
