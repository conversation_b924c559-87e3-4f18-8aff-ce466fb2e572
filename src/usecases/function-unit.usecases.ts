import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as _ from 'lodash';
import { CreateFunctionUnitDto } from '../controller/function-unit/dtos/create-function-unit.dto';
import { GetDetailFunctionUnitDto } from '../controller/function-unit/dtos/get-detail-function-unit.dto';
import { GetFunctionUnitListDto } from '../controller/function-unit/dtos/get-function-unit-list.dto';
import { UpdateFunctionUnitDto } from '../controller/function-unit/dtos/update-function-unit.dto';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import { EFunctionUnitStatus } from '../domain/config/enums/function-unit.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { FunctionUnitModel } from '../domain/model/function-unit.model';
import { IFunctionUnitRepository } from '../domain/repositories/function-unit.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';

@Injectable()
export class FunctionUnitUsecases {
  constructor(
    @Inject(IFunctionUnitRepository)
    private readonly functionUnitRepository: IFunctionUnitRepository,
  ) {}

  async createFunctionUnit(
    data: CreateFunctionUnitDto,
    authorization: string,
  ): Promise<FunctionUnitModel> {
    await this.checkCodeFunctionUnit(data.code);

    const functionUnitModel = new FunctionUnitModel({
      ...data,
    });
    const functionUnit =
      await this.functionUnitRepository.createFunctionUnit(functionUnitModel);

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: functionUnit.name,
        refId: functionUnit.id,
        refCode: functionUnit.code,
        type: EDataRoleType.FUNCTION_UNIT,
        isEnabled: functionUnit.status === EFunctionUnitStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization },
    );

    return functionUnit;
  }

  async getFunctionUnitById(id: string): Promise<FunctionUnitModel> {
    return await this.functionUnitRepository.getFunctionUnitById(id);
  }

  async updateFunctionUnit(
    data: UpdateFunctionUnitDto,
    id: string,
    jwtPayload: any,
    authorization: string,
  ) {
    await this.checkCodeFunctionUnit(data.code, id);
    await this.getDetailFunctionUnit(
      plainToInstance(GetDetailFunctionUnitDto, {
        id: id,
      }),
      jwtPayload,
    );

    const functionUnitData = new FunctionUnitModel({
      id,
      ...data,
    });

    const functionUnit =
      await this.functionUnitRepository.updateFunctionUnit(functionUnitData);

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: functionUnit.name,
        refCode: functionUnit.code,
        isEnabled: functionUnit.status === EFunctionUnitStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    return functionUnit;
  }

  async deleteFunctionUnit(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getDetailFunctionUnit(
      plainToInstance(GetDetailFunctionUnitDto, {
        id: id,
      }),
      jwtPayload,
    );

    await this.functionUnitRepository.deleteFunctionUnit(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async getFunctionUnits(
    data: GetFunctionUnitListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<FunctionUnitModel>> {
    return await this.functionUnitRepository.getFunctionUnits(data, jwtPayload);
  }

  async getDetailFunctionUnit(
    conditions: GetDetailFunctionUnitDto,
    jwtPayload: any,
  ): Promise<FunctionUnitModel> {
    const detail = await this.functionUnitRepository.getDetailFunctionUnit(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1034()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async checkCodeFunctionUnit(code: string, id?: string) {
    const checkCode = await this.functionUnitRepository.getFunctionUnitByCode(
      code,
      id,
    );

    if (checkCode) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1035()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getFunctionUnitByIds(ids: string[], jwtPayload: any) {
    const functionUnits =
      await this.functionUnitRepository.getFunctionUnitsByIds(ids, jwtPayload);

    if (!functionUnits || !functionUnits.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1034()),
        HttpStatus.BAD_REQUEST,
      );
    }

    const activeFunctionUnits = functionUnits?.filter(
      (item) => item.status === EFunctionUnitStatus.ACTIVE,
    );

    const activeFunctionUnitIds = activeFunctionUnits.map((item) => item.id);

    const missingFunctionUnitIds = _.difference(ids, activeFunctionUnitIds);

    if (missingFunctionUnitIds.length) {
      throw new HttpException(
        getErrorMessage(
          errorMessage.E_1034(
            `FunctionUnit ids ${missingFunctionUnitIds.join(', ')} not found or inactive`,
          ),
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return functionUnits;
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.functionUnitRepository.getFunctionUnitsByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }
}
