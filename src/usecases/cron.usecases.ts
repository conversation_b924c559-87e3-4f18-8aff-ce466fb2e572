import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as moment from 'moment';
import { EBudgetStatus } from '../domain/config/enums/budget.enum';
import { ECurrencyUnitStatus } from '../domain/config/enums/currency-unit.enum';
import { BudgetEntity } from '../infrastructure/entities/budget.entity';
import { CurrencyUnitExchangeEntity } from '../infrastructure/entities/currency-unit-exchange.entity';
import { CurrencyUnitEntity } from '../infrastructure/entities/currency-unit.entity';

@Injectable()
export class CronUsecases {
  constructor() {}

  // @Cron('5 0 * * *', {
  //   name: 'cron-update-status-budget',
  //   timeZone: 'Asia/Ho_Chi_Minh',
  // })
  // async cronUpdateStatusBudget() {
  //   const startDate = moment().toISOString();
  //   const endDate = moment().subtract(1, 'days').toISOString();
  //   console.log(20, 'start cron-update-status-budget');
  //   await BudgetEntity.createQueryBuilder('budgets')
  //     .update()
  //     .set({ status: EBudgetStatus.IN_ACTIVE })
  //     .where('status = :status', { status: EBudgetStatus.ACTIVE })
  //     .andWhere(
  //       'effective_start_date > :startDate OR effective_end_date < :endDate',
  //       { startDate, endDate },
  //     )
  //     .execute();

  //   console.log(31, 'cron-update-status-budget SUCESSFULLY');
  // }

  // @Cron('5 0 * * *', {
  //   name: 'cron-update-status-currrency',
  //   timeZone: 'Asia/Ho_Chi_Minh',
  // })
  // async cronUpdateStatusCurrency() {
  //   const startDate = moment().toISOString();
  //   const endDate = moment().subtract(1, 'days').toISOString();
  //   console.log(40, 'start cron-update-status-currrency');
  //   await CurrencyUnitEntity.createQueryBuilder('currencies')
  //     .update()
  //     .set({ status: ECurrencyUnitStatus.IN_ACTIVE })
  //     .where('status = :status', { status: ECurrencyUnitStatus.ACTIVE })
  //     .andWhere(
  //       'effective_start_date > :startDate OR effective_end_date < :endDate',
  //       { startDate, endDate },
  //     )
  //     .execute();

  //   await CurrencyUnitExchangeEntity.createQueryBuilder('currencyExchanges')
  //     .update()
  //     .set({ status: ECurrencyUnitStatus.IN_ACTIVE })
  //     .where('status = :status', { status: ECurrencyUnitStatus.ACTIVE })
  //     .andWhere(
  //       'effective_start_date > :startDate OR effective_end_date < :endDate',
  //       { startDate, endDate },
  //     )
  //     .execute();

  //   console.log(61, 'cron-update-status-currrency SUCESSFULLY');
  // }
}
