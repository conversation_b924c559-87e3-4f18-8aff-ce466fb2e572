import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CountUnReadNotificationDto } from '../controller/notification/dtos/count-un-read-notification.dto';
import {
  CreateMultipleNotificationDto,
  CreateNotificationDto,
} from '../controller/notification/dtos/create-notification.dto';
import { GetDetailNotificationDto } from '../controller/notification/dtos/get-detail-notification.dto';
import { GetNotificationListDto } from '../controller/notification/dtos/get-notification-list.dto';
import { MarkAllAsReadDto } from '../controller/notification/dtos/mark-all-as-read-notification.dto';
import { MarkAsReadDto } from '../controller/notification/dtos/mark-as-read-notification.dto';
import { ENotificationFormType } from '../domain/config/enums/notification-form.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { notificationErrorDetails } from '../domain/messages/error-details/notification';
import { INotificationRepository } from '../domain/repositories/notification.repository';
import { NotificationEntity } from '../infrastructure/entities/notification.entity';
import { NotificationServiceApiUrlsConst } from '../utils/constants/notification-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendPost } from '../utils/http';

@Injectable()
export class NotificationUsecases {
  constructor(
    @Inject(INotificationRepository)
    private readonly notificationRepository: INotificationRepository,
  ) {}

  // async createNotification(
  //   data: CreateNotificationDto,
  //   jwtPayload: any,
  // ): Promise<NotificationEntity> {
  //   const notificationData: NotificationEntity = {
  //     ...data,
  //   };

  //   const notification =
  //     await this.notificationRepository.createNotification(notificationData);

  //   console.log(31, notification);

  //   return await this.getNotificationDetail(
  //     plainToInstance(GetDetailNotificationDto, {
  //       id: notification._id.toString(),
  //       onwerId: jwtPayload?.staffId || '',
  //       platform: jwtPayload?.platform || '',
  //     }),
  //   );
  // }

  async getNotifications(
    conditions: GetNotificationListDto,
  ): Promise<ResponseDto<NotificationEntity>> {
    return await this.notificationRepository.getNotifications(conditions);
  }

  async getNotificationDetail(
    conditions: GetDetailNotificationDto,
  ): Promise<NotificationEntity> {
    const detail =
      await this.notificationRepository.getNotificationDetail(conditions);

    if (!detail) {
      throw new HttpException(
        notificationErrorDetails.E_2350(),
        HttpStatus.NOT_FOUND,
      );
    }
    return detail;
  }

  async markAsRead(conditions: MarkAsReadDto): Promise<void> {
    await this.notificationRepository.markAsRead(conditions);
  }

  async markAllAsRead(conditions: MarkAllAsReadDto): Promise<void> {
    await this.notificationRepository.markAllAsRead(conditions);
  }

  async countUnRead(conditions: CountUnReadNotificationDto): Promise<object> {
    return await this.notificationRepository.countUnRead(conditions);
  }

  async pushNotification(conditions: CreateMultipleNotificationDto) {
    const notifications: CreateNotificationDto[] = [];

    for (const data of conditions.data) {
      notifications.push({
        ...data,
        ownerId: data.ownerId,
        platform: EPlatform.E_PURCHASE, //@TODO: sử dụng platform tương ứng
        notificationForm: data.notificationForm, //@TODO: sử dụng notification form tương ứng
        metaData: {
          ///@TODO: thông tin cần thiết cho FE sử dụng notification fcm
          ///Example
          // detailId: 'detail to id',
        },
        url: '//@TODO: cần url tới detail',
      });
    }

    const data: CreateMultipleNotificationDto = {
      data: notifications,
      callBackUrl: NotificationServiceApiUrlsConst.CREATE_NOTIFICATION(),
    };

    await sendPost(QueueServiceApiUrlsConst.NOTIFICATION_QUEUE(), data);
  }

  async testNotification() {
    const notifications: CreateNotificationDto[] = [];

    notifications.push({
      ownerId: 'thanhtong',
      platform: EPlatform.E_PURCHASE, //@TODO: sử dụng platform tương ứng
      notificationForm: ENotificationFormType.APPROVED_PO, //@TODO: sử dụng notification form tương ứng
      metaData: {
        ///@TODO: thông tin cần thiết cho FE sử dụng notification fcm
        ///Example
        // detailId: 'detail to id',
      },
      url: '//@TODO: cần url tới detail',
      title: 'DUONG THANH TONG',
      body: 'PHAM DOAN ANH DUY & MAI THANH HAI',
    });

    const data: CreateMultipleNotificationDto = {
      data: notifications,
      callBackUrl: NotificationServiceApiUrlsConst.CREATE_NOTIFICATION(),
    };

    await sendPost(QueueServiceApiUrlsConst.NOTIFICATION_QUEUE(), data);
  }
}
