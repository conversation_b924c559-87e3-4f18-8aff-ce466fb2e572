import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as _ from 'lodash';
import { CreatePlantDto } from '../controller/plant/dtos/create-plant.dto';
import { GetDetailPlantDto } from '../controller/plant/dtos/get-detail-plant.dto';
import { GetPlantListDto } from '../controller/plant/dtos/get-plant-list.dto';
import { UpdatePlantDto } from '../controller/plant/dtos/update-plant.dto';
import { GetDetailSectorDto } from '../controller/sector/dtos/get-detail-sector.dto';
import { EPlantStatus } from '../domain/config/enums/plant.enum';
import { ESectorStatus } from '../domain/config/enums/sector.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { plantErrorDetails } from '../domain/messages/error-details/plant';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { PlantModel } from '../domain/model/plant.model';
import { IPlantRepository } from '../domain/repositories/plant.repository';
import { SectorUsecases } from './sector.usecases';

@Injectable()
export class PlantUsecases {
  constructor(
    @Inject(IPlantRepository)
    private readonly plantRepository: IPlantRepository,
    private readonly sectorUsecases: SectorUsecases,
  ) {}

  async createPlant(
    data: CreatePlantDto,
    jwtPayload: any,
  ): Promise<PlantModel> {
    await this.verifyDataDto(data, jwtPayload);

    const plantModel = new PlantModel(data);

    const plant = await this.plantRepository.createPlant(plantModel);

    // await sendPost(
    //   IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
    //   {
    //     description: plant.name,
    //     refId: plant.id,
    //     refCode: plant.code,
    //     type: EDataRoleType.PLANT,
    //     isEnabled: plant.status === EPlantStatus.ACTIVE,
    //     platform: EPlatform.E_PURCHASE,
    //   },
    //   { authorization },
    // );

    return plant;
  }

  async updatePlant(
    id: string,
    updatePlantDto: UpdatePlantDto,
    jwtPayload: any,
  ): Promise<PlantModel> {
    await this.verifyDataDto(updatePlantDto, jwtPayload, id);

    const plant = await this.plantRepository.updatePlant(id, updatePlantDto);

    // await sendPatch(
    //   IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
    //   {
    //     description: plant.name,
    //     refCode: plant.code,
    //     isEnabled: plant.status === EPlantStatus.ACTIVE,
    //   },
    //   {
    //     authorization,
    //   },
    // );

    return plant;
  }

  // async getPlantById(id: string): Promise<PlantModel> {
  //   const plant = await this.plantRepository.getPlantById(id);

  //   if (!plant) {
  //     throw new HttpException(
  //       plantErrorDetails.E_2900(),
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   return plant;
  // }

  async getPlants(
    conditions: GetPlantListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PlantModel>> {
    return await this.plantRepository.getPlants(conditions, jwtPayload);
  }

  async deletePlant(id: string, jwtPayload: any): Promise<void> {
    await this.getPlantDetail(
      plainToInstance(GetDetailPlantDto, {
        id,
      }),
      jwtPayload,
    );

    await this.plantRepository.deletePlant(id);

    // await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
    //   authorization,
    // });
  }

  async checkExistPlantByCode(code: string, id?: string): Promise<void> {
    const plant = await this.plantRepository.getPlantByCode(code, id);

    if (plant) {
      throw new HttpException(
        plantErrorDetails.E_2901(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async verifyDataDto(
    conditions: CreatePlantDto | UpdatePlantDto,
    jwtPayload: any,
    id?: string,
  ) {
    let detail: PlantModel;
    if (id) {
      detail = await this.getPlantDetail(
        plainToInstance(GetDetailPlantDto, {
          id,
        }),
        jwtPayload,
      );
    }

    await this.checkExistPlantByCode(conditions.code, id);

    if (detail?.sectorId != conditions.sectorId) {
      const sector = await this.sectorUsecases.getDetailSector(
        plainToInstance(GetDetailSectorDto, {
          id: conditions.sectorId,
        }),
        jwtPayload,
      );

      if (sector.status == ESectorStatus.IN_ACTIVE) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1027()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  async getPlantDetail(conditions: GetDetailPlantDto, jwtPayload) {
    const plant = await this.plantRepository.getPlantDetail(
      conditions,
      jwtPayload,
    );

    if (!plant) {
      throw new HttpException(
        plantErrorDetails.E_2900(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return plant;
  }

  async getPlantByIds(ids: string[], jwtPayload: any) {
    const plants = await this.plantRepository.getPlantByIds(ids, jwtPayload);

    if (!plants || !plants.length) {
      throw new HttpException(
        plantErrorDetails.E_2900(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const activePlants = plants?.filter(
      (item) => item.status === EPlantStatus.ACTIVE,
    );

    const activePlantIds = activePlants.map((item) => item.id);

    const missingPlantIds = _.difference(ids, activePlantIds);

    if (missingPlantIds.length) {
      throw new HttpException(
        plantErrorDetails.E_2900(
          `Plant ids ${missingPlantIds.join(', ')} not found or inactive`,
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return plants;
  }
}
