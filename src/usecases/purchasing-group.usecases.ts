import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { CreatePurchasingGroupDto } from '../controller/purchasing-group/dtos/create-purchasing-group.dto';
import { GetDetailPurchasingGroupDto } from '../controller/purchasing-group/dtos/get-detail-purchasing-group.dto';
import { GetPurchasingGroupListDto } from '../controller/purchasing-group/dtos/get-purchasing-group-list.dto';
import { UpdatePurchasingGroupDto } from '../controller/purchasing-group/dtos/update-purchasing-group.dto';
import { GetDetailSectorDto } from '../controller/sector/dtos/get-detail-sector.dto';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EPurchasingGroupStatus } from '../domain/config/enums/purchasing.enum';
import { ESectorStatus } from '../domain/config/enums/sector.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { purchasingGroupErrorDetails } from '../domain/messages/error-details/purchasing-group';
import {
  errorMessage,
  getErrorMessage,
} from '../domain/messages/error-message';
import { PurchasingGroupModel } from '../domain/model/purchasing-group.model';
import { IPurchasingGroupRepository } from '../domain/repositories/purchasing-group.repository';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { SectorUsecases } from './sector.usecases';
import * as Excel from 'exceljs';
import { FileUsecases } from './file.usecases';
import { resolve } from 'path';
import { exportFileUploadPath } from '../domain/config/constant';
import { getStatusPurchasingGroup } from '../utils/common';

@Injectable()
export class PurchasingGroupUsecases {
  constructor(
    @Inject(IPurchasingGroupRepository)
    private readonly purchasingGroupRepository: IPurchasingGroupRepository,
    private readonly sectorUsecases: SectorUsecases,
    private readonly fileUsecases: FileUsecases,
  ) {}

  async createPurchasingGroup(
    data: CreatePurchasingGroupDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PurchasingGroupModel> {
    await this.verifyDataDto(data, jwtPayload);

    const purchasingGroupModel = new PurchasingGroupModel(data);

    const purchasingGroup =
      await this.purchasingGroupRepository.createPurchasingGroup(
        purchasingGroupModel,
      );

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: purchasingGroup.name,
        refId: purchasingGroup.id,
        refCode: purchasingGroup.code,
        type: EDataRoleType.PURCHASING_GROUP,
        isEnabled: purchasingGroup.status === EPurchasingGroupStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization },
    );

    return purchasingGroup;
  }

  async updatePurchasingGroup(
    id: string,
    updatePurchasingGroupDto: UpdatePurchasingGroupDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<PurchasingGroupModel> {
    await this.verifyDataDto(updatePurchasingGroupDto, jwtPayload, id);

    const purchasingGroup =
      await this.purchasingGroupRepository.updatePurchasingGroup(
        id,
        updatePurchasingGroupDto,
      );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: purchasingGroup.name,
        refCode: purchasingGroup.code,
        isEnabled: purchasingGroup.status === EPurchasingGroupStatus.ACTIVE,
      },
      {
        authorization,
      },
    );

    return purchasingGroup;
  }

  // async getPurchasingGroupById(id: string): Promise<PurchasingGroupModel> {
  //   const purchasingGroup =
  //     await this.purchasingGroupRepository.getPurchasingGroupById(id);

  //   if (!purchasingGroup) {
  //     throw new HttpException(
  //       purchasingGroupErrorDetails.E_2600(),
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   return purchasingGroup;
  // }

  async getPurchasingGroups(
    conditions: GetPurchasingGroupListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PurchasingGroupModel>> {
    return await this.purchasingGroupRepository.getPurchasingGroups(
      conditions,
      jwtPayload,
    );
  }

  async deletePurchasingGroup(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getPurchasingGroupDetail(
      plainToInstance(GetDetailPurchasingGroupDto, {
        id,
      }),
      jwtPayload,
    );

    await this.purchasingGroupRepository.deletePurchasingGroup(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
    });
  }

  async checkExistPurchasingGroupByCode(
    code: string,
    id?: string,
  ): Promise<PurchasingGroupModel> {
    const purchasingGroup =
      await this.purchasingGroupRepository.getPurchasingGroupByCode(
        code,
        {
          isSuperAdmin: true, //jwtPayload?.isSuperAdmin: check exist by super admin
        },
        id,
      );

    if (purchasingGroup) {
      throw new HttpException(
        purchasingGroupErrorDetails.E_2601(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return purchasingGroup;
  }

  async verifyDataDto(
    conditions: CreatePurchasingGroupDto | UpdatePurchasingGroupDto,
    jwtPayload: any,
    id?: string,
  ) {
    let detail: PurchasingGroupModel;
    if (id) {
      detail = await this.getPurchasingGroupDetail(
        plainToInstance(GetDetailPurchasingGroupDto, {
          id,
        }),
        jwtPayload,
      );
    }

    await this.checkExistPurchasingGroupByCode(conditions.code, id);

    if (detail?.sectorId != conditions.sectorId) {
      const sector = await this.sectorUsecases.getDetailSector(
        plainToInstance(GetDetailSectorDto, {
          id: conditions.sectorId,
        }),
        jwtPayload,
      );

      if (sector.status == ESectorStatus.IN_ACTIVE) {
        throw new HttpException(
          getErrorMessage(errorMessage.E_1027()),
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  async getPurchasingGroupDetail(
    conditions: GetDetailPurchasingGroupDto,
    jwtPayload,
  ) {
    const purchasingGroup =
      await this.purchasingGroupRepository.getPurchasingGroupDetail(
        conditions,
        jwtPayload,
      );

    if (!purchasingGroup) {
      throw new HttpException(
        purchasingGroupErrorDetails.E_2600(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return purchasingGroup;
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.purchasingGroupRepository.getPurchasingGroupByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }

  async exportPurchasingGroup(
    conditions: GetPurchasingGroupListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data = await this.purchasingGroupRepository.getPurchasingGroups(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-purchasing-group.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toPurchasingGroupModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-purchasing-group.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toPurchasingGroupModel(
    purchasingGroups: PurchasingGroupModel[],
  ) {
    const items = [];

    for (let i = 0; i < purchasingGroups.length; i++) {
      items.push({
        code: purchasingGroups[i].code || '',
        name: purchasingGroups[i].name || '',
        description: purchasingGroups[i].description || '',
        status: getStatusPurchasingGroup(purchasingGroups[i].status),
      });
    }

    return items;
  }
}
