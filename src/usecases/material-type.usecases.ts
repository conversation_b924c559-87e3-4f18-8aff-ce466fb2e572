import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as moment from 'moment';
import { resolve } from 'path';
import { ImportMaterialTypeDto } from '../controller/import/dtos/import-material-type.dto';
import { CreateMaterialTypeDto } from '../controller/material-type/dtos/create-material-type.dto';
import { GetDetailMaterialTypeDto } from '../controller/material-type/dtos/get-detail-material-type.dto';
import { GetMaterialTypeListDto } from '../controller/material-type/dtos/get-material-type-list.dto';
import { UpdateMaterialTypeDto } from '../controller/material-type/dtos/update-material-type.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import {
  EColumnImportMaterialType,
  EMaterialTypeStatus,
} from '../domain/config/enums/material.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { importErrorDetails } from '../domain/messages/error-details/import';
import { materialTypeErrorDetails } from '../domain/messages/error-details/material-type';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { MaterialTypeModel } from '../domain/model/material-type.model';
import { IMaterialTypeRepository } from '../domain/repositories/material-type.repository';
import {
  checkValuesEmptyRowExcel,
  getStatus,
  getStatusMaterialType,
  getValueOrResult,
} from '../utils/common';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';

@Injectable()
export class MaterialTypeUsecases {
  constructor(
    @Inject(IMaterialTypeRepository)
    private readonly materialTypeRepository: IMaterialTypeRepository,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
  ) {}

  async createMaterialType(
    data: CreateMaterialTypeDto,
    authorization: string,
  ): Promise<MaterialTypeModel> {
    await this.checkExistMaterialTypeByCode(data.code);

    const materialTypeModel = new MaterialTypeModel(data);

    const materialType =
      await this.materialTypeRepository.createMaterialType(materialTypeModel);

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: materialType.name,
        refId: materialType.id,
        refCode: materialType.code,
        type: EDataRoleType.MATERIAL_TYPE,
        isEnabled: materialType.status === EMaterialTypeStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization, 'x-api-key': process.env.API_KEY },
    );

    return materialType;
  }

  async updateMaterialType(
    id: string,
    updateMaterialTypeDto: UpdateMaterialTypeDto,
    jwtPayload: any,
    authorization: string,
  ): Promise<MaterialTypeModel> {
    if (updateMaterialTypeDto.code)
      await this.checkExistMaterialTypeByCode(updateMaterialTypeDto.code, id);

    await this.getMaterialTypeDetail(
      plainToInstance(GetDetailMaterialTypeDto, {
        id,
      }),
      jwtPayload,
    );

    const materialType = await this.materialTypeRepository.updateMaterialType(
      id,
      updateMaterialTypeDto,
    );

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: materialType.name,
        refCode: materialType.code,
        isEnabled: materialType.status === EMaterialTypeStatus.ACTIVE,
      },
      {
        authorization,
        'x-api-key': process.env.API_KEY,
      },
    );

    return materialType;
  }

  // async getMaterialTypeById(id: string): Promise<MaterialTypeModel> {
  //   const materialType =
  //     await this.materialTypeRepository.getMaterialTypeById(id);

  //   if (!materialType) {
  //     throw new HttpException(
  //       materialTypeErrorDetails.E_2000(),
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   return materialType;
  // }

  async getMaterialTypes(
    conditions: GetMaterialTypeListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<MaterialTypeModel>> {
    return await this.materialTypeRepository.getMaterialTypes(
      conditions,
      jwtPayload,
    );
  }

  async deleteMaterialType(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    await this.getMaterialTypeDetail(
      plainToInstance(GetDetailMaterialTypeDto, {
        id,
      }),
      jwtPayload,
    );

    await this.materialTypeRepository.deleteMaterialType(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
      'x-api-key': process.env.API_KEY,
    });
  }

  async checkExistMaterialTypeByCode(
    code: string,
    id?: string,
  ): Promise<MaterialTypeModel> {
    const materialType =
      await this.materialTypeRepository.getMaterialTypeByCode(code, id);

    if (materialType) {
      throw new HttpException(
        materialTypeErrorDetails.E_2001(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return materialType;
  }

  async getMaterialTypeDetail(
    conditions: GetDetailMaterialTypeDto,
    jwtPayload: any,
  ) {
    const detail = await this.materialTypeRepository.getMaterialTypeDetail(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        materialTypeErrorDetails.E_2000(),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async getMaterialTypeDetailByCode(code: string) {
    const materialType =
      await this.materialTypeRepository.getMaterialTypeByCode(code);

    if (!materialType) {
      throw new HttpException(
        materialTypeErrorDetails.E_2000(),
        HttpStatus.NOT_FOUND,
      );
    }

    return materialType;
  }

  async exportMaterialType(
    conditions: GetMaterialTypeListDto,
    jwtPayload: any,
  ) {
    conditions.getAll = 1;
    const data = await this.materialTypeRepository.getMaterialTypes(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-material-type.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toMaterialTypeModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-material-type.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toMaterialTypeModel(materialTypes: MaterialTypeModel[]) {
    const items = [];

    for (let i = 0; i < materialTypes.length; i++) {
      items.push({
        code: materialTypes[i].code || '',
        name: materialTypes[i].name || '',
        description: materialTypes[i].description || '',
        status: getStatusMaterialType(materialTypes[i].status),
      });
    }

    return items;
  }

  async importMaterialType(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.MATERIAL_TYPE,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createMaterialTypeDtos: CreateMaterialTypeDto[] = [];
        const updateMaterialTypeDtos: UpdateMaterialTypeDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportMaterialType.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const materialTypes =
          await this.materialTypeRepository.getMaterialTypeByCodesWithRole(
            [...new Set(codes)],
            jwtPayload,
            false,
          );

        let totalRowHasValue = 0;

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportMaterialType.CODE, // First Cell
            EColumnImportMaterialType.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          ///Data từng row trong file excel
          const code = getValueOrResult(
            row,
            EColumnImportMaterialType.CODE,
          )?.toString();
          const name = getValueOrResult(
            row,
            EColumnImportMaterialType.NAME,
          )?.toString();
          const description = getValueOrResult(
            row,
            EColumnImportMaterialType.DESCRIPTION,
          )?.toString();
          const status = getStatus(
            MaterialTypeModel,
            getValueOrResult(row, EColumnImportMaterialType.STATUS)?.toString(),
          ); //Trạng thái

          const materialTypeObject = {
            id: undefined,
            code,
            name,
            description,
            status,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };

          if (!code) {
            errors.push({
              error: getErrorMessage(
                materialTypeErrorDetails.E_2003(),
                'Mã loại vật tư không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const checkCode = materialTypes.find((item) => item.code == code);

            const checkDup = codes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(
                  materialTypeErrorDetails.E_2005(),
                  'Mã loại vật tư bị trùng lặp',
                ),
                row: i + 1,
              });
            }

            if (checkCode) {
              materialTypeObject.id = checkCode.id;
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(
                materialTypeErrorDetails.E_2004(),
                'Tên loại vật tư không được để trống',
              ),
              row: i + 1,
            });
          }

          if (materialTypeObject.id) {
            const materialTypeDto = plainToInstance(UpdateMaterialTypeDto, {
              ...materialTypeObject,
              createdAt: undefined,
            });
            updateMaterialTypeDtos.push(materialTypeDto);
          } else {
            const materialTypeDto = plainToInstance(
              CreateMaterialTypeDto,
              materialTypeObject,
            );
            createMaterialTypeDtos.push(materialTypeDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportMaterialTypeDto = {
          dataMaterialTypes: createMaterialTypeDtos,
          dataUpdateMaterialTypes: updateMaterialTypeDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_MATERIAL_TYPE(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async listByCodes(
    codes: string[],
    jwtPayload: any,
    isNeedPermission: boolean = true,
  ) {
    return await this.materialTypeRepository.getMaterialTypeByCodesWithRole(
      [...new Set(codes)],
      jwtPayload,
      isNeedPermission,
    );
  }
}
