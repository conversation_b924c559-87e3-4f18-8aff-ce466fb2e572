import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as Excel from 'exceljs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { CreateDepartmentDto } from '../controller/department/dtos/create-department.dto';
import { GetDepartmentListDto } from '../controller/department/dtos/get-department-list.dto';
import { GetDetailDepartmentDto } from '../controller/department/dtos/get-detail-department.dto';
import { UpdateDepartmentDto } from '../controller/department/dtos/update-department.dto';
import { ImportDepartmentDto } from '../controller/import/dtos/import-department.dto';
import {
  exportFileUploadPath,
  fileImportPath,
} from '../domain/config/constant';
import { EDataRoleType } from '../domain/config/enums/data-role-type.enum';
import {
  EColumnImportDepartment,
  EDepartmentStatus,
} from '../domain/config/enums/department.enum';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { importErrorDetails } from '../domain/messages/error-details/import';
import {
  errorMessage,
  getErrorMessage,
  TErrorMessageImport,
} from '../domain/messages/error-message';
import { DepartmentModel } from '../domain/model/department.model';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { IDepartmentRepository } from '../domain/repositories/department.repository';
import {
  checkValuesEmptyRowExcel,
  getStatus,
  getStatusDepartment,
  getValueOrResult,
} from '../utils/common';
import { IdentityServiceApiUrlsConst } from '../utils/constants/identity-service-api-url.const';
import { PurchaseServiceApiUrlsConst } from '../utils/constants/purchase-service-api-url.const';
import { QueueServiceApiUrlsConst } from '../utils/constants/queue-service-api-url.const';
import { sendDelete, sendPatch, sendPost } from '../utils/http';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';

@Injectable()
export class DepartmentUsecases {
  constructor(
    @Inject(IDepartmentRepository)
    private readonly departmentRepository: IDepartmentRepository,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
  ) {}

  async createDepartment(
    data: CreateDepartmentDto,
    authorization: string,
  ): Promise<DepartmentModel> {
    await this.checkCodeDepartment(data.code);

    const departmentModel = new DepartmentModel({
      ...data,
    });
    const department =
      await this.departmentRepository.createDepartment(departmentModel);

    await sendPost(
      IdentityServiceApiUrlsConst.CREATE_DATA_ROLE(),
      {
        description: department.name,
        refId: department.id,
        refCode: department.code,
        type: EDataRoleType.DEPARTMENT,
        isEnabled: department.status === EDepartmentStatus.ACTIVE,
        platform: EPlatform.E_PURCHASE,
      },
      { authorization, 'x-api-key': process.env.API_KEY },
    );

    return department;
  }

  async getDepartmentById(id: string): Promise<DepartmentModel> {
    return await this.departmentRepository.getDepartmentById(id);
  }

  async updateDepartment(
    data: UpdateDepartmentDto,
    id: string,
    jwtPayload: any,
    authorization: string,
  ) {
    await this.checkCodeDepartment(data.code, id);

    await this.getDetailDepartment(
      plainToInstance(GetDetailDepartmentDto, {
        id: id,
      }),
      jwtPayload,
    );

    const departmentUnitData = new DepartmentModel({
      id,
      ...data,
    });

    const department =
      await this.departmentRepository.updateDepartment(departmentUnitData);

    await sendPatch(
      IdentityServiceApiUrlsConst.UPDATE_DATA_ROLE(id),
      {
        description: department.name,
        refCode: department.code,
        isEnabled: department.status === EDepartmentStatus.ACTIVE,
      },
      {
        authorization,
        'x-api-key': process.env.API_KEY,
      },
    );

    return department;
  }

  async deleteDepartment(
    id: string,
    jwtPayload: any,
    authorization: string,
  ): Promise<void> {
    const checkDepartment = await this.getDetailDepartment(
      plainToInstance(GetDetailDepartmentDto, {
        id: id,
      }),
      jwtPayload,
    );

    if (!checkDepartment) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1050()),
        HttpStatus.NOT_FOUND,
      );
    }

    await this.departmentRepository.deleteDepartment(id);

    await sendDelete(IdentityServiceApiUrlsConst.DELETE_DATA_ROLE(id), {
      authorization,
      'x-api-key': process.env.API_KEY,
    });
  }

  async getDepartments(
    data: GetDepartmentListDto,
    jwtPayload: any,
  ): Promise<ResponseDto<DepartmentModel>> {
    return await this.departmentRepository.getDepartments(data, jwtPayload);
  }

  async getDetailDepartment(
    conditions: GetDetailDepartmentDto,
    jwtPayload: any,
  ): Promise<DepartmentModel> {
    const detail = await this.departmentRepository.getDetailDepartment(
      conditions,
      jwtPayload,
    );

    if (!detail) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1050()),
        HttpStatus.NOT_FOUND,
      );
    }

    return detail;
  }

  async getDepartmentByCode(code: string): Promise<DepartmentModel> {
    return await this.departmentRepository.getDepartmentByCode(code);
  }

  async checkCodeDepartment(code: string, id?: string) {
    const checkCode = await this.departmentRepository.getDepartmentByCode(
      code,
      id,
    );

    if (checkCode) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1032()),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getDepartmentByIds(ids: string[], jwtPayload: any) {
    const departments = await this.departmentRepository.getDepartmentByIds(
      ids,
      jwtPayload,
    );

    if (!departments || !departments.length) {
      throw new HttpException(
        getErrorMessage(errorMessage.E_1050()),
        HttpStatus.NOT_FOUND,
      );
    }

    const activeDepartments = departments?.filter(
      (item) => item.status === EDepartmentStatus.ACTIVE,
    );

    const activeDepartmentIds = activeDepartments.map((item) => item.id);

    const missingDepartmentIds = _.difference(ids, activeDepartmentIds);

    if (missingDepartmentIds.length) {
      throw new HttpException(
        getErrorMessage(
          errorMessage.E_1050(
            `Department ids ${missingDepartmentIds.join(', ')} not found or inactive`,
          ),
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return departments;
  }

  async exportDepartment(conditions: GetDepartmentListDto, jwtPayload: any) {
    conditions.getAll = 1;
    const data = await this.departmentRepository.getDepartments(
      conditions,
      jwtPayload,
    );

    if (data?.results?.length) {
      const sourceWorkbook = new Excel.Workbook();
      await sourceWorkbook.xlsx.readFile(
        resolve(
          __dirname,
          '../domain/template/export/template-export-department.xlsx',
        ),
      );
      const sourceWorksheet = sourceWorkbook.worksheets[0];

      const targetWorkbook = new Excel.Workbook();
      const targetWorksheet = targetWorkbook.addWorksheet(sourceWorksheet.name);

      sourceWorksheet.model['merges'].forEach((merge) =>
        targetWorksheet.mergeCells(merge),
      );
      sourceWorksheet.columns.forEach((sourceColumn, index) => {
        const targetColumn = targetWorksheet.getColumn(index + 1);
        targetColumn.width = sourceColumn.width; // Copy width
      });

      const sourceRow = sourceWorksheet.getRow(1);
      const targetRow = targetWorksheet.getRow(1);
      sourceRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const targetCell = targetRow.getCell(colNumber);
        targetCell.value = cell.value; // Sao chép giá trị ô
        targetCell.style = cell.style; // Sao chép style nếu cần
        targetCell.border = cell.border; // Sao chép border nếu cần
        targetCell.font = cell.font; // Sao chép font nếu cần
      });

      targetRow.commit(); // Ghi các thay đổi của row vào sheet

      const items = await this.toDepartmentModel(data.results);
      for (let i = 0; i < items.length; i++) {
        targetWorksheet.getRow(i + 2).values = Object.values(items[i]);
      }

      const buffer = await targetWorkbook.xlsx.writeBuffer();

      //@TODO: Qua UAT sẽ trả link mọi người thấy khúc này bỏ qua dùm em
      const file = await this.fileUsecases.bufferToMulterFile(
        Buffer.from(buffer),
        'template-export-department.xlsx',
      );

      const uploadedFile = await this.fileUsecases.uploadFile(
        file,
        null,
        exportFileUploadPath,
      );

      return { ...uploadedFile, buffer: null };
    }
  }

  private async toDepartmentModel(departments: DepartmentModel[]) {
    const items = [];

    for (let i = 0; i < departments.length; i++) {
      items.push({
        code: departments[i].code || '',
        name: departments[i].name || '',
        description: departments[i].description || '',
        status: getStatusDepartment(departments[i].status),
      });
    }

    return items;
  }

  async importDepartment(
    file: Express.Multer.File,
    jwtPayload: IAuthUserPayload,
    authorization: string,
  ) {
    ///Upload file import
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.DEPARTMENT,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer));

        const createDepartmentDtos: CreateDepartmentDto[] = [];
        const updateDepartmentDtos: UpdateDepartmentDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0]?.rowCount || 1,
          ) ?? [];

        const codes = rows
          .map((item) =>
            getValueOrResult(item, EColumnImportDepartment.CODE)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const departments =
          await this.departmentRepository.getDepartmentsByCodesWithRole(
            [...new Set(codes)],
            jwtPayload,
            false,
          );

        let totalRowHasValue = 0;

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (row.number < 2) {
            continue;
          }

          const isNotEmpty = await checkValuesEmptyRowExcel(
            row,
            EColumnImportDepartment.CODE, // First Cell
            EColumnImportDepartment.STATUS, // Last Cell
          );
          if (!isNotEmpty) {
            continue;
          }
          totalRowHasValue++;

          ///Data từng row trong file excel
          const code = getValueOrResult(
            row,
            EColumnImportDepartment.CODE,
          )?.toString();
          const name = getValueOrResult(
            row,
            EColumnImportDepartment.NAME,
          )?.toString();
          const description = getValueOrResult(
            row,
            EColumnImportDepartment.DESCRIPTION,
          )?.toString();
          const status = getStatus(
            DepartmentModel,
            getValueOrResult(row, EColumnImportDepartment.STATUS)?.toString(),
          ); //Trạng thái

          const departmentObject = {
            id: undefined,
            code,
            name,
            description,
            status,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
          };

          if (!code) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1092(),
                'Mã phòng ban không được để trống',
              ),
              row: i + 1,
            });
          } else {
            const checkCode = departments.find((item) => item.code == code);

            const checkDup = codes.filter((item) => item == code);
            if (checkDup.length > 1) {
              errors.push({
                error: getErrorMessage(
                  errorMessage.E_1094(),
                  'Mã phòng ban bị trùng lặp',
                ),
                row: i + 1,
              });
            }

            if (checkCode) {
              departmentObject.id = checkCode.id;
            }
          }

          if (!name) {
            errors.push({
              error: getErrorMessage(
                errorMessage.E_1093(),
                'Tên phòng ban không được để trống',
              ),
              row: i + 1,
            });
          }

          if (departmentObject.id) {
            const departmentDto = plainToInstance(UpdateDepartmentDto, {
              ...departmentObject,
              createdAt: undefined,
            });
            updateDepartmentDtos.push(departmentDto);
          } else {
            const departmentDto = plainToInstance(
              CreateDepartmentDto,
              departmentObject,
            );
            createDepartmentDtos.push(departmentDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            getErrorMessage(errorMessage.E_1041(), {
              totalRow: totalRowHasValue,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportDepartmentDto = {
          dataDepartments: createDepartmentDtos,
          dataUpdateDepartments: updateDepartmentDtos,
          fileImportHistoryId: fileImportHistory.id,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          importUrl: PurchaseServiceApiUrlsConst.IMPORT_DEPARTMENT(),
          updateStatusFileUrl:
            PurchaseServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
