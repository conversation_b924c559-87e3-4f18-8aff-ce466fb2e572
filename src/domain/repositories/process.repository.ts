import { CreateProcessDto } from '../../controller/process/dtos/create-process.dto';
import { GetProcessListDto } from '../../controller/process/dtos/get-process-list.dto';
import { UpdateProcessDto } from '../../controller/process/dtos/update-process.dto';
import { ResponseDto } from '../dtos/response.dto';
import { ProcessModel } from '../model/process.model';
export abstract class IProcessRepository {
  createProcess: (data: CreateProcessDto) => Promise<ProcessModel>;
  updateProcess: (id: string, data: UpdateProcessDto) => Promise<ProcessModel>;
  deleteProcess: (ids: string[]) => Promise<void>;
  getProcessById: (id: string) => Promise<ProcessModel>;
  getProcessList: (
    conditions: GetProcessListDto,
  ) => Promise<ResponseDto<ProcessModel>>;
  getDetailProcessGraph: (id: string) => Promise<any>;
  getProcessByIds: (ids: string[]) => Promise<ProcessModel[]>;
  getDetailParentProcess: (id: string) => Promise<ProcessModel>;
  getDetailProcessById: (id: string) => Promise<ProcessModel>;
}
