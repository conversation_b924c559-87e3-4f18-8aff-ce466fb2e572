import { CreateInventoryStandardDto } from '../../controller/inventory-standard/dtos/create-inventory-standard.dto';
import { GetDetailInventoryStandardDto } from '../../controller/inventory-standard/dtos/get-detail-inventory-standard.dto';
import { GetInventoryStandardListDto } from '../../controller/inventory-standard/dtos/get-inventory-standard-list.dto';
import { UpdateInventoryStandardDto } from '../../controller/inventory-standard/dtos/update-inventory-standard.dto';
import { ResponseDto } from '../dtos/response.dto';
import { InventoryStandardModel } from '../model/inventory-standard.model';

export abstract class IInventoryStandardRepository {
  createInventoryStandard: (
    data: InventoryStandardModel,
  ) => Promise<InventoryStandardModel>;
  updateInventoryStandard: (
    data: InventoryStandardModel,
  ) => Promise<InventoryStandardModel>;
  deleteInventoryStandard: (id: string) => Promise<void>;
  getInventoryStandards: (
    conditions: GetInventoryStandardListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<InventoryStandardModel>>;
  getInventoryStandardDetail: (
    conditions: GetDetailInventoryStandardDto,
    jwtPayload: any,
  ) => Promise<InventoryStandardModel>;
  checkDuplicatePathInventoryStandard: (
    conditions: CreateInventoryStandardDto | UpdateInventoryStandardDto,
    id?: string,
  ) => Promise<InventoryStandardModel[]>;
  getInventoryStandardByPath: (path: string) => Promise<InventoryStandardModel>;
  getParamsCheckkDuplicatesInventoryStandard: (
    materialId: string,
    sectorId: string,
    companyId?: string,
    businessUnitId?: string,
    departmentId?: string,
  ) => Promise<string[]>;
}
