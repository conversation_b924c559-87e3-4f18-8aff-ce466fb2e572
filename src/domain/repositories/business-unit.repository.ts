import { GetBusinessUnitListDto } from '../../controller/business-unit/dtos/get-business-unit-list.dto';
import { GetDetailBusinessUnitDto } from '../../controller/business-unit/dtos/get-detail-business-unit.dto';
import { ResponseDto } from '../dtos/response.dto';
import { BusinessUnitModel } from '../model/business-unit.model';

export abstract class IBusinessUnitRepository {
  createBusinessUnit: (data: BusinessUnitModel) => Promise<BusinessUnitModel>;
  updateBusinessUnit: (data: BusinessUnitModel) => Promise<BusinessUnitModel>;
  getBusinessUnits: (
    conditions: GetBusinessUnitListDto,
    jwtPayload,
  ) => Promise<ResponseDto<BusinessUnitModel>>;
  deleteBusinessUnit: (id: string) => Promise<void>;
  getBusinessUnitById: (id: string) => Promise<BusinessUnitModel>;
  getBusinessUnitByCode: (
    code: string,
    id?: string,
  ) => Promise<BusinessUnitModel>;
  getDetailBusinessUnit: (
    conditions: GetDetailBusinessUnitDto,
    jwtPayload: any,
  ) => Promise<BusinessUnitModel>;
  ///For import excel
  getBusinessUnitsByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<BusinessUnitModel[]>;
  getBuByIds: (ids: string[], jwtPayload: any) => Promise<BusinessUnitModel[]>;
}
