import { GetListPurchaseOrderDto } from '../../controller/purchase-order/dtos/get-list-purchase-order.dto';
import { ResponseDto } from '../dtos/response.dto';
import { PurchaseOrderModel } from '../model/purchase_order.model';

export abstract class IPurchaseOrderRepository {
  getListPurchaseOrder: (
    conditions: GetListPurchaseOrderDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<PurchaseOrderModel>>;
  findOne: (id: number) => Promise<PurchaseOrderModel>;
}
