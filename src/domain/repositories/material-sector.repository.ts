import { MaterialSectorModel } from '../model/material-sector.model';

export abstract class IMaterialSectorRepository {
  createMaterialSectors: (
    conditions: MaterialSectorModel[],
  ) => Promise<MaterialSectorModel[]>;
  updateMaterialSectors: (
    conditions: MaterialSectorModel[],
  ) => Promise<MaterialSectorModel[]>;
  deleteMaterialSectors: (materialId: string) => Promise<void>;

  getMaterialSectorByMaterialId: (
    materialId: string,
  ) => Promise<MaterialSectorModel[]>;
}
