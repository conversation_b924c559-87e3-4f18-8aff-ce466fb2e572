import { GetBudgetListDto } from '../../controller/budget/dtos/get-budget-list.dto';
import { GetDetailBudgetDto } from '../../controller/budget/dtos/get-detail-budget.dto';
import { ReportBudgetDto } from '../../controller/budget/dtos/report-budget.dto';
import { EBudgetType } from '../config/enums/budget.enum';
import { ResponseDto } from '../dtos/response.dto';
import {
  IGroupBudgetByBudgetCodeAndCostCenter,
  IGroupBudgetByBusinessOwnerAndCostCenter,
} from '../interface/budget.interface';
import { BudgetModel } from '../model/budget.model';

export abstract class IBudgetRepository {
  createBudget: (data: BudgetModel) => Promise<BudgetModel>;
  updateBudget: (id: string, data: BudgetModel) => Promise<BudgetModel>;
  getBudgetById: (id: string) => Promise<BudgetModel>;
  getBudgetList: (
    conditions: GetBudgetListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<BudgetModel>>;
  getBudgetByIdAndBudgetType: (
    id: string,
    budgetType: EBudgetType,
  ) => Promise<BudgetModel>;
  lockBudget: (ids: string[]) => Promise<void>;
  unlockBudget: (ids: string[]) => Promise<void>;
  getBudgetByCode: (code: string) => Promise<BudgetModel>;
  getBudgetDetail: (
    conditions: GetDetailBudgetDto,
    jwtPayload: any,
  ) => Promise<BudgetModel>;
  ///For import excel
  getBudgetsByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    budgetType: EBudgetType,
  ) => Promise<BudgetModel[]>;
  getBudgetByIds: (ids: string[], jwtPayload: any) => Promise<BudgetModel[]>;
  getBudgetsForImportAdjustBudgetWithRole: (
    budgetCodes: string[],
    costcenterCodes: string[],
    effectiveStartDates: string[],
    jwtPayload: any,
    budgetType: EBudgetType,
  ) => Promise<BudgetModel[]>;
  checkDuplicateBudget: (data: BudgetModel) => Promise<BudgetModel[]>;
  reportBudgetDetail: (
    conditions: ReportBudgetDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<IGroupBudgetByBudgetCodeAndCostCenter>>;
  reportBudgetOverview: (
    conditions: ReportBudgetDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<IGroupBudgetByBusinessOwnerAndCostCenter>>;
}
