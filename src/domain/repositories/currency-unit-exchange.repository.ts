import { CurrencyUnitExchangeModel } from '../model/currency-unit-exchange.model';

export abstract class ICurrencyUnitExchangeRepository {
  createManyCurrencyUnitExchange: (
    data: CurrencyUnitExchangeModel[],
  ) => Promise<CurrencyUnitExchangeModel[]>;
  updateManyCurrencyUnitExchange: (
    data: CurrencyUnitExchangeModel[],
  ) => Promise<CurrencyUnitExchangeModel[]>;
  deleteCurrencyUnitExchangesNotIn: (
    ids: string[],
    currencyId: string,
  ) => Promise<void>;
}
