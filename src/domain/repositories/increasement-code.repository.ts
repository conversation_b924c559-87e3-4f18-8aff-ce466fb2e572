import { <PERSON><PERSON><PERSON><PERSON>ana<PERSON> } from 'typeorm';
import { ECodeType } from '../config/enums/code-type.enum';
import { IncreasementCodeModel } from '../model/increasement-code.model';

export abstract class IIncreasementCodeRepository {
  getIncreasementCodeByType: (
    codeType: ECodeType,
  ) => Promise<IncreasementCodeModel | null>;

  createIncreasementCode: (
    increasementCode: IncreasementCodeModel,
  ) => Promise<IncreasementCodeModel>;

  updateIncreasementCode: (
    increasementCode: IncreasementCodeModel,
  ) => Promise<IncreasementCodeModel>;
}
