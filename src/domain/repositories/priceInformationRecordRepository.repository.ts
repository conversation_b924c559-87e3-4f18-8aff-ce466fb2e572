import { GetPriceInformationRecordDto } from '../../controller/price-information-record/dtos/get-all-price-information-record.dto';
import { GetPIRListByIdsDto } from '../../controller/price-information-record/dtos/get-price-information-record-list-by-ids.dto';
import {
  PriceInformationRecordDto,
  UpdatePirStatusDto,
} from '../../controller/price-information-record/dtos/price-information-record.dto';
import { PriceMaterialDto } from '../../controller/purchase-request/dtos/price-material.dto';
import { ResponseDto } from '../dtos/response.dto';
import { BusinessUnitModel } from '../model/business-unit.model';
import { PriceInformationRecordModel } from '../model/price_information_record.model';

export interface IPriceInformationRecordRepository {
  findAll(
    paginationDto: GetPriceInformationRecordDto,
    jwtPayload: any,
  ): Promise<ResponseDto<PriceInformationRecordModel>>;
  findOne(id: number): Promise<PriceInformationRecordModel>;
  createPriceInformationRecord(
    price: PriceInformationRecordDto,
    authorization,
  ): Promise<PriceInformationRecordModel>;
  updatePriceInformationRecord(
    id: number,
    price: PriceInformationRecordDto,
  ): Promise<PriceInformationRecordModel>;
  updateStatusPriceInformationRecord(
    id: number,
    updateStatus: UpdatePirStatusDto,
  ): Promise<PriceInformationRecordModel>;
  activePirs(): Promise<any>;
  inactivePirs(): Promise<any>;
  createMultiplePriceInformationRecord(
    prices: PriceInformationRecordDto[],
  ): Promise<PriceInformationRecordModel[]>;
  priceMaterial(
    conditions: PriceMaterialDto,
  ): Promise<PriceInformationRecordModel>;
  migrationRelationPIR(
    id: number,
    businessUnits: BusinessUnitModel[],
  ): Promise<void>;
  getListByIds: (
    conditions: GetPIRListByIdsDto,
    jwtPayload,
  ) => Promise<ResponseDto<PriceInformationRecordModel>>;
}
