import {
  ApprovalLevelDto,
  ApproveDto,
  ConditionDto,
} from '../../controller/approve/dtos/approve.dto';
import { ApprovalNode } from '../../controller/approve/dtos/condition-structure';
import { ApproveType } from '../../infrastructure/entities/approval-level.entity';
import { ApprovalLevelModel } from '../model/approval-level.model';

export interface IPrApprovalFlowRepository {
  // findActive(): Promise<any>;
  // findPrActive(): Promise<any>;
  findOne(id: number): Promise<ApprovalLevelModel>;
  findOneByLevel(
    purchaseId: number,
    level: number,
    approveType: ApproveType,
  ): Promise<any>;
  // create(
  //   rootNode: ApprovalNode,
  //   approval: ApproveDto,
  //   jwtPayload,
  //   authorization,
  // ): Promise<any>;
  // checkConditions(conditions: ConditionDto[], prData: any): Promise<boolean>;
  changeStatus(flow, id: number): Promise<any>;
  changeStatusPurchaseOrder(flow, id: number): Promise<any>;
  changeStatusHistory(
    data: { status: string; reason: string },
    id: number,
  ): Promise<any>;
  updateApprovalLevel(status: string, id: number): Promise<void>;
  // update(
  //   id: number,
  //   rootNode: ApprovalNode,
  //   approval: ApproveDto,
  //   jwtPayload,
  // ): Promise<any>;
  createApprovalLevels(approvalLevelDtos: ApprovalLevelDto[]): Promise<any>;
  deleteApprovalLevels(prId: number, poId: number): Promise<void>;
  findApprovalLevels(
    prId: number,
    poId: number,
    greaterThanLevel?: number,
  ): Promise<ApprovalLevelModel[]>;
}
