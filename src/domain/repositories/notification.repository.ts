import { CountUnReadNotificationDto } from '../../controller/notification/dtos/count-un-read-notification.dto';
import { GetDetailNotificationDto } from '../../controller/notification/dtos/get-detail-notification.dto';
import { GetNotificationListDto } from '../../controller/notification/dtos/get-notification-list.dto';
import { MarkAllAsReadDto } from '../../controller/notification/dtos/mark-all-as-read-notification.dto';
import { MarkAsReadDto } from '../../controller/notification/dtos/mark-as-read-notification.dto';
import { NotificationEntity } from '../../infrastructure/entities/notification.entity';
import { ResponseDto } from '../dtos/response.dto';

export abstract class INotificationRepository {
  createNotification: (data: NotificationEntity) => Promise<NotificationEntity>;
  getNotifications: (
    conditions: GetNotificationListDto,
  ) => Promise<ResponseDto<NotificationEntity>>;
  getNotificationDetail: (
    conditions: GetDetailNotificationDto,
  ) => Promise<NotificationEntity>;
  markAsRead: (conditions: MarkAsReadDto) => Promise<void>;
  markAllAsRead: (conditions: MarkAllAsReadDto) => Promise<void>;
  countUnRead: (conditions: CountUnReadNotificationDto) => Promise<object>;
}
