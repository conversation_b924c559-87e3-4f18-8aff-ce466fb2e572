import { GetPurchaseOrderDto } from '../../controller/purchase-order/dtos/get-all-purchase-order.dto';
import { GetPoDetailReportBudgetDetailDto } from '../../controller/purchase-order/dtos/get-po-detail-report-budget.dto';
import { GetPOWithBudgetDto } from '../../controller/purchase-order/dtos/get-po-with-budget.dto';
import { PurchaseOrderDto } from '../../controller/purchase-order/dtos/purchase-order.dto';
import {
  EDisplayStatus,
  State,
  Status,
} from '../config/enums/purchase-order.enum';
import { ResponseDto } from '../dtos/response.dto';
import { PurchaseOrderModel } from '../model/purchase_order.model';
import { PurchaseOrderDetailModel } from '../model/purchase_order_detail.model';

export interface IPurchaseOrderRepository {
  findAll(
    paginationDto: GetPurchaseOrderDto,
    jwtPayload,
    isImport: boolean,
  ): Promise<ResponseDto<PurchaseOrderModel>>;
  numberPoCreated(
    id: number,
    poId?: number,
  ): Promise<PurchaseOrderDetailModel[]>;
  findOne(id: number): Promise<PurchaseOrderModel>;
  findOneStatusProcess(status: Status): Promise<PurchaseOrderModel[]>;
  createPurchaseOrder(data: PurchaseOrderDto): Promise<PurchaseOrderModel>;
  updateStatus(
    id: number,
    status: Status,
    displayStatusPo: EDisplayStatus,
  ): Promise<any>;
  updateState(id: number, state: State): Promise<any>;
  updatePurchaseOrder(
    id: number,
    updatePurchaseRequestDto: Partial<PurchaseOrderDto>,
  ): Promise<PurchaseOrderModel>;
  findPOWithBudget(
    conditions: GetPOWithBudgetDto,
  ): Promise<PurchaseOrderModel[]>;
  materialPurchaseOrder(paginationDto: GetPurchaseOrderDto): Promise<any>;
  getPoDetailsForRemainingBudget(
    budgetCodeId: string,
    effectiveStartDate: Date,
    effectiveEndDate: Date,
  ): Promise<PurchaseOrderDetailModel[]>;
  findPoWithIds(ids: number[]): Promise<PurchaseOrderModel[]>;
  findPoReportBudget(
    conditions: GetPoDetailReportBudgetDetailDto,
    authorization,
  ): Promise<PurchaseOrderDetailModel[]>;
  findAdjustBudgetIdInPoDetail(
    adjustBudgetIds: string[],
  ): Promise<PurchaseOrderDetailModel[]>;
  numberPoCreatedByPrDetailIds(
    ids: number[],
    poId?: number,
  ): Promise<PurchaseOrderDetailModel[]>;
  findOneById(id: number): Promise<PurchaseOrderModel>;
  getPosForResendEmail(ids: number[]): Promise<PurchaseOrderModel[]>;
}
