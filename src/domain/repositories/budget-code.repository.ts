import { GetBudgetCodeListByIdsDto } from '../../controller/budget-code/dtos/get-budget-code-list-by-ids.dto';
import { GetBudgetCodeListDto } from '../../controller/budget-code/dtos/get-budget-code-list.dto';
import { GetDetailBudgetCodeDto } from '../../controller/budget-code/dtos/get-detail-budget-code.dto';
import { ResponseDto } from '../dtos/response.dto';
import { BudgetCodeModel } from '../model/budget-code.model';

export abstract class IBudgetCodeRepository {
  createBudgetCode: (data: BudgetCodeModel) => Promise<BudgetCodeModel>;
  updateBudgetCode: (data: BudgetCodeModel) => Promise<BudgetCodeModel>;
  getBudgetCodes: (
    conditions: GetBudgetCodeListDto,
    jwtPayload,
  ) => Promise<ResponseDto<BudgetCodeModel>>;
  deleteBudgetCode: (id: string) => Promise<void>;
  getBudgetCodeById: (id: string) => Promise<BudgetCodeModel>;
  getBudgetCodeByCode: (code: string, id?: string) => Promise<BudgetCodeModel>;
  getBudgetCodeDetail: (
    conditions: GetDetailBudgetCodeDto,
    jwtPayload,
  ) => Promise<BudgetCodeModel>;
  ///For import excel
  getBudgetCodesByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<BudgetCodeModel[]>;
  getBudgetCodeByIds: (
    ids: string[],
    jwtPayload: any,
  ) => Promise<BudgetCodeModel[]>;
  getListByIds: (
    conditions: GetBudgetCodeListByIdsDto,
    jwtPayload,
  ) => Promise<ResponseDto<BudgetCodeModel>>;
  getBudgetCodeByInternalOrderAndFunctionalArea: (
    jwtPayload: any,
    functionalAreas?: string[],
    internalOrders?: string[],
  ) => Promise<BudgetCodeModel[]>;
}
