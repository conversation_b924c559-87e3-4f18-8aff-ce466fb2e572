import { GetApprovalProcessDetailListDto } from '../../controller/approval-process-detail/dtos/get-approval-process-detail-list.dto';
import { GetDetailApprovalProcessDetailDto } from '../../controller/approval-process-detail/dtos/get-detail-approval-process-detail.dto';
import { UpdateApprovalProcessDetailDto } from '../../controller/approval-process-detail/dtos/update-approval-process-detail.dto';
import { ResponseDto } from '../dtos/response.dto';
import { ApprovalProcessDetailModel } from '../model/approval-process-detail.model';

export abstract class IApprovalProcessDetailRepository {
  createApprovalProcessDetail: (
    data: ApprovalProcessDetailModel,
  ) => Promise<ApprovalProcessDetailModel>;
  updateApprovalProcessDetail: (
    id: string,
    updateApprovalProcessDetailDto: ApprovalProcessDetailModel,
  ) => Promise<ApprovalProcessDetailModel>;
  deleteApprovalProcessDetail: (id: string) => Promise<void>;
  getDetailApprovalProcessDetail: (
    conditions: GetDetailApprovalProcessDetailDto,
    jwtPayload: any,
  ) => Promise<ApprovalProcessDetailModel>;
  getApprovalProcessDetails: (
    conditions: GetApprovalProcessDetailListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<ApprovalProcessDetailModel>>;
}
