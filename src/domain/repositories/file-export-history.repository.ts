import { GetListFileExportHistoryDto } from '../../controller/file-export-history/dtos/get-list-file-export-history.dto';
import { ResponseDto } from '../dtos/response.dto';
import { FileExportHistoryModel } from '../model/file-export-history.model';

export abstract class IFileExportHistoryRepository {
  createFileExportHistory: (
    data: FileExportHistoryModel,
  ) => Promise<FileExportHistoryModel>;
  getListFileExportHistory: (
    conditions: GetListFileExportHistoryDto,
  ) => Promise<ResponseDto<FileExportHistoryModel>>;
  deleteFileExportHistory: (id: string) => Promise<void>;
  updateFileExportHistory: (
    data: FileExportHistoryModel,
  ) => Promise<FileExportHistoryModel>;
  getFileExportHistoryById: (id: string) => Promise<FileExportHistoryModel>;
}
