import { SupplierSectorModel } from '../model/supplier-sector.model';

export abstract class ISupplierSectorRepository {
  createSupplierSectors: (
    conditions: SupplierSectorModel[],
  ) => Promise<SupplierSectorModel[]>;
  updateSupplierSectors: (
    conditions: SupplierSectorModel[],
  ) => Promise<SupplierSectorModel[]>;
  deleteSupplierSectors: (supplierId: string) => Promise<void>;

  getSupplierSectorBySupplierId: (
    supplierId: string,
  ) => Promise<SupplierSectorModel[]>;
}
