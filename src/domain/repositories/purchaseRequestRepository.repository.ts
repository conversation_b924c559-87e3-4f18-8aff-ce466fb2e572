import { GetPurchaseRequestDto } from '../../controller/purchase-request/dtos/get-all-purchase-request.dto';
import { GetPRWithBudgetDto } from '../../controller/purchase-request/dtos/get-pr-with-budget.dto';
import { PriceMaterialDto } from '../../controller/purchase-request/dtos/price-material.dto';
import { PurchaseRequestDto } from '../../controller/purchase-request/dtos/purchase-request.dto';
import { State, Status } from '../config/enums/purchase-request.enum';
import { ResponseDto } from '../dtos/response.dto';
import { PurchaseRequestModel } from '../model/purchase_request.model';
import { PurchaseRequestDetailModel } from '../model/purchase_request_detail.model';

export interface IPurchaseRequestRepository {
  findAll(
    paginationDto: GetPurchaseRequestDto,
    jwtPayload,
    isNeedDetails: boolean,
  ): Promise<ResponseDto<PurchaseRequestModel>>;
  findOne(id: number, isNeedPo?: boolean): Promise<PurchaseRequestModel>;
  findOneStatusProcess(status: Status): Promise<PurchaseRequestModel[]>;
  createPurchaseRequest(
    data: PurchaseRequestDto,
    jwtPayload,
    authorization,
  ): Promise<PurchaseRequestModel>;
  updatePurchaseRequest(
    id: number,
    updatePurchaseRequestDto: Partial<PurchaseRequestDto>,
  ): Promise<PurchaseRequestModel>;
  updateStatus(id: number, status: Status): Promise<any>;
  updateState(id: number, state: State): Promise<any>;
  approvePurchaseRequest(data): Promise<PurchaseRequestModel>;
  checkIsPO(prReferenceIds?: number[]): Promise<void>;
  findPRWithBudget(
    conditions: GetPRWithBudgetDto,
  ): Promise<PurchaseRequestModel[]>;
  materialPurchase(
    paginationDto: GetPurchaseRequestDto,
  ): Promise<ResponseDto<PurchaseRequestModel>>;
  countPr(): Promise<any>;
  findPrWithPo(prIds: number[]): Promise<PurchaseRequestModel[]>;
  getPrDetails(ids: number[]): Promise<PurchaseRequestDetailModel[]>;
  getPrDetailByIds(ids: number[]): Promise<PurchaseRequestDetailModel[]>;
  priceMaterial(
    conditions: PriceMaterialDto,
  ): Promise<PurchaseRequestDetailModel>;
  getPrListByIds(ids: number[]): Promise<PurchaseRequestModel[]>;
  findOneById(id: number): Promise<PurchaseRequestModel>;
  getPrsForResendEmail(ids: number[]): Promise<PurchaseRequestModel[]>;
}
