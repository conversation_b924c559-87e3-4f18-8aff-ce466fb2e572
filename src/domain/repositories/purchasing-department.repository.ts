import { GetDetailPurchasingDepartmentDto } from '../../controller/purchasing-department/dtos/get-detail-purchasing-department.dto';
import { GetPurchasingDepartmentListDto } from '../../controller/purchasing-department/dtos/get-purchasing-department-list.dto';
import { UpdatePurchasingDepartmentDto } from '../../controller/purchasing-department/dtos/update-purchasing-department.dto';
import { ResponseDto } from '../dtos/response.dto';
import { PurchasingDepartmentModel } from '../model/purchasing-department.model';

export abstract class IPurchasingDepartmentRepository {
  createPurchasingDepartment: (
    data: PurchasingDepartmentModel,
  ) => Promise<PurchasingDepartmentModel>;
  updatePurchasingDepartment: (
    id: string,
    updatePurchasingDepartmentDto: UpdatePurchasingDepartmentDto,
  ) => Promise<PurchasingDepartmentModel>;
  deletePurchasingDepartment: (id: string) => Promise<void>;
  // getPurchasingDepartmentById: (
  //   id: string,
  // ) => Promise<PurchasingDepartmentModel>;
  getPurchasingDepartmentByCode: (
    code: string,
    id?: string,
  ) => Promise<PurchasingDepartmentModel>;
  getPurchasingDepartments: (
    conditions: GetPurchasingDepartmentListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<PurchasingDepartmentModel>>;
  getPurchasingDepartmentDetail: (
    conditions: GetDetailPurchasingDepartmentDto,
    jwtPayload: any,
  ) => Promise<PurchasingDepartmentModel>;
  getPurchasingDepartmentByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<PurchasingDepartmentModel[]>;
}
