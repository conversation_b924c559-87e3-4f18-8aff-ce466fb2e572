import { CreateApprovalWorkflowDto } from '../../controller/process/dtos/create-approval-workflow.dto';
import { ApprovalWorkflowModel } from '../model/approval-workflow.model';

export abstract class IApprovalWorkflowRepository {
  createApprovalWorkflow: (
    createApprovalWorkflowDto: CreateApprovalWorkflowDto,
  ) => Promise<ApprovalWorkflowModel>;
  deleteApprovalWorkflowByParentProcessId: (
    parentProcessId: string,
  ) => Promise<void>;
  getApprovalWorkflowByParentProcessId: (
    parentProcessId: string,
  ) => Promise<ApprovalWorkflowModel[]>;
}
