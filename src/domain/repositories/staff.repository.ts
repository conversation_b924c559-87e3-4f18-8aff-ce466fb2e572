import { GetApproverByPositionDto } from '../../controller/staff/dtos/get-approver-by-position.dto';
import { GetDetailStaffDto } from '../../controller/staff/dtos/get-detail-staff.dto';
import { GetStaffByCodesDto } from '../../controller/staff/dtos/get-staff-by-codes.dto';
import { GetStaffByEmailsDto } from '../../controller/staff/dtos/get-staff-by-emails.dto';
import { GetStaffListDto } from '../../controller/staff/dtos/get-staff-list.dto';
import { ResponseDto } from '../dtos/response.dto';
import { StaffModel } from '../model/staff.model';

export abstract class IStaffRepository {
  createStaff: (data: StaffModel) => Promise<StaffModel>;
  updateStaff: (staff: StaffModel) => Promise<StaffModel>;
  deleteStaff: (id: string) => Promise<void>;
  getStaffByCode: (code: string) => Promise<StaffModel>;
  getStaffByEmail: (email: string) => Promise<StaffModel>;
  getStaffList: (
    conditions: GetStaffListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<StaffModel>>;
  getDetailStaff: (
    conditions: GetDetailStaffDto,
    jwtPayload: any,
  ) => Promise<StaffModel>;
  getStaffByCodes: (
    conditions: GetStaffByCodesDto,
    jwtPayload: any,
  ) => Promise<StaffModel[]>;
  getStaffByIds: (
    conditions: GetStaffListDto,
    jwtPayload: any,
  ) => Promise<StaffModel[]>;
  getStaffByEmails: (
    conditions: GetStaffByEmailsDto,
    jwtPayload: any,
  ) => Promise<StaffModel[]>;
  approverByPosition: (
    conditions: GetApproverByPositionDto,
  ) => Promise<StaffModel[]>;
}
