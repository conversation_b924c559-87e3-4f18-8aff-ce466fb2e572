import { CreateSapPurchaseOrderDto } from '../../controller/sap-purchase-order/dtos/create-sap-purchase-order.dto';
import { SapPurchaseOrderModel } from '../model/sap_purchase_order.model';

export interface ISapPurchaseOrderRepository {
  createSapPurchaseOrders(
    createSapPurchaseOrderDtos: CreateSapPurchaseOrderDto[],
  ): Promise<SapPurchaseOrderModel[]>;
  updateSapPurchaseOrders(
    models: SapPurchaseOrderModel[],
  ): Promise<SapPurchaseOrderModel[]>;
  getListSapPurchaseOrderByIds(ids: number[]): Promise<SapPurchaseOrderModel[]>;
  deleteSapPurchaseOrdersById(ids: number[]): Promise<void>;
}
