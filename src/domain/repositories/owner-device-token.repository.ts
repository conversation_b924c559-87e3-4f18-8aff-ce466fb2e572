import { OwnerDeviceTokenEntity } from '../../infrastructure/entities/owner-device-token.entity';
import { EPlatform } from '../config/enums/platform.enum';

export abstract class IOwnerDeviceTokenRepository {
  createOwnerDeviceToken: (data: OwnerDeviceTokenEntity) => Promise<void>;
  deleteOnwerDeviceToken: (
    deviceToken: string,
    platform: EPlatform,
    ownerId?: string,
  ) => Promise<void>;
  getOwnerDeviceTokens: (
    ownerIds: string[],
    platform: EPlatform,
  ) => Promise<OwnerDeviceTokenEntity[]>;
}
