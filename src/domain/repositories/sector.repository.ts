import { GetDetailSectorDto } from '../../controller/sector/dtos/get-detail-sector.dto';
import { GetSectorListDto } from '../../controller/sector/dtos/get-sector-list.dto';
import { UpdateSectorDto } from '../../controller/sector/dtos/update-sector.dto';
import { ResponseDto } from '../dtos/response.dto';
import { SectorModel } from '../model/sector.model';

export abstract class ISectorRepository {
  createSector: (data: SectorModel) => Promise<SectorModel>;
  updateSector: (
    id: string,
    updateSectorDto: UpdateSectorDto,
  ) => Promise<SectorModel>;
  deleteSector: (id: string) => Promise<void>;
  getSectorById: (id: string) => Promise<SectorModel>;
  getSectorByCode: (code: string, id?: string) => Promise<SectorModel>;
  getSectors: (
    conditions: GetSectorListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<SectorModel>>;
  getDetailSector: (
    conditions: GetDetailSectorDto,
    jwtPayload: any,
  ) => Promise<SectorModel>;
  ///For import excel
  getSectorsByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<SectorModel[]>;
  getSectorByIds: (ids: string[], jwtPayload: any) => Promise<SectorModel[]>;
}
