import { CreateSapPurchaseOrderItemDto } from '../../controller/sap-purchase-order/dtos/create-sap-purchase-order-item.dto';
import { SapPurchaseOrderItemModel } from '../model/sap_purchase_order_item.model';

export interface ISapPurchaseOrderItemRepository {
  createSapPurchaseOrderItem(
    data: CreateSapPurchaseOrderItemDto[],
  ): Promise<SapPurchaseOrderItemModel[]>;
  getSapPOItemsByPoDetailId(
    poDetailId: number,
  ): Promise<SapPurchaseOrderItemModel[]>;
  deleteSapPOItemsBySapPurchaseOrderId(
    sapPurchaseOrderIds: number[],
  ): Promise<void>;
}
