import { GetDetailMaterialGroupDto } from '../../controller/material-group/dtos/get-detail-material-group.dto';
import { GetMaterialGroupListByIdsDto } from '../../controller/material-group/dtos/get-material-group-list-by-ids.dto';
import { GetMaterialGroupListDto } from '../../controller/material-group/dtos/get-material-group-list.dto';
import { UpdateMaterialGroupDto } from '../../controller/material-group/dtos/update-material-group.model';
import { ResponseDto } from '../dtos/response.dto';
import { MaterialGroupModel } from '../model/material-group.model';

export abstract class IMaterialGroupRepository {
  createMaterialGroup: (
    data: MaterialGroupModel,
  ) => Promise<MaterialGroupModel>;
  updateMaterialGroup: (
    id: string,
    updateMaterialGroupDto: UpdateMaterialGroupDto,
  ) => Promise<MaterialGroupModel>;
  deleteMaterialGroup: (id: string) => Promise<void>;
  // getMaterialGroupById: (id: string) => Promise<MaterialGroupModel>;
  getMaterialGroupByCode: (
    code: string,
    id?: string,
  ) => Promise<MaterialGroupModel>;
  getMaterialGroups: (
    conditions: GetMaterialGroupListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<MaterialGroupModel>>;
  getMaterialGroupDetail: (
    conditions: GetDetailMaterialGroupDto,
    jwtPayload: any,
  ) => Promise<MaterialGroupModel>;
  getMaterialGroupByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<MaterialGroupModel[]>;
  getListByIds: (
    conditions: GetMaterialGroupListByIdsDto,
    jwtPayload,
  ) => Promise<ResponseDto<MaterialGroupModel>>;
}
