import { CreateProcessConditionDto } from '../../controller/process/dtos/create-process-condition.dto';
import { ProcessConditionModel } from '../model/process-condition.model';

export abstract class IProcessConditionRepository {
  createProcessCondition: (
    createProcessConditionDto: CreateProcessConditionDto,
  ) => Promise<ProcessConditionModel>;
  deleteProcessConditionByProcessId: (processId: string) => Promise<void>;
}
