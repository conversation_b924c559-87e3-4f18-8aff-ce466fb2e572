import { GetListPurchaseRequestDto } from '../../controller/purchase-request/dtos/get-list-purchase-request.dto';
import { ResponseDto } from '../dtos/response.dto';
import { PurchaseRequestModel } from '../model/purchase_request.model';

export abstract class IPurchaseRequestRepository {
  getListPurchaseRequest: (
    conditions: GetListPurchaseRequestDto,
    jwtPayload: any,
    isNeedDetails: boolean,
  ) => Promise<ResponseDto<PurchaseRequestModel>>;
  findOne: (id: number) => Promise<PurchaseRequestModel>;
}
