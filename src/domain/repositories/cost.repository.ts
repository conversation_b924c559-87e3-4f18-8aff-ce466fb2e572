import { GetCostListDto } from '../../controller/cost/dtos/get-cost-list.dto';
import { GetDetailCostDto } from '../../controller/cost/dtos/get-detail-cost.dto';
import { UpdateCostDto } from '../../controller/cost/dtos/update-cost.dto';
import { ResponseDto } from '../dtos/response.dto';
import { CostModel } from '../model/cost.model';

export abstract class ICostRepository {
  createCost: (data: CostModel) => Promise<CostModel>;
  updateCost: (id: string, updateCostDto: UpdateCostDto) => Promise<CostModel>;
  deleteCost: (id: string) => Promise<void>;
  getDetailCost: (
    conditions: GetDetailCostDto,
    jwtPayload: any,
  ) => Promise<CostModel>;
  getCosts: (
    conditions: GetCostListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<CostModel>>;
  getCostByCode: (code: string, id?: string) => Promise<CostModel>;
  ///For import excel
  getCostsByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<CostModel[]>;
}
