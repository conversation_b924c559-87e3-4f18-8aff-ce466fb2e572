import { GetCurrencyListByIdsDto } from '../../controller/currency-unit/dtos/get-currency-unit-list-by-ids.dto';
import { GetCurrencyUnitListDto } from '../../controller/currency-unit/dtos/get-currency-unit-list.dto';
import { GetDetailCurrencyUnitDto } from '../../controller/currency-unit/dtos/get-detail-currency-unit.dto';
import { ResponseDto } from '../dtos/response.dto';
import { CurrencyUnitModel } from '../model/currency-unit.model';

export abstract class ICurrencyUnitRepository {
  createCurrencyUnit: (data: CurrencyUnitModel) => Promise<CurrencyUnitModel>;
  updateCurrencyUnit: (data: CurrencyUnitModel) => Promise<CurrencyUnitModel>;
  getCurrencyUnits: (
    conditions: GetCurrencyUnitListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<CurrencyUnitModel>>;
  deleteCurrencyUnit: (id: string) => Promise<void>;
  // getCurrencyUnitById: (id: string) => Promise<CurrencyUnitModel>;
  findCurrencyUnitByCode: (code: string) => Promise<CurrencyUnitModel>;
  ///For import excel
  getCurrencyUnitsByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<CurrencyUnitModel[]>;
  getCurrencyUnitDetail: (
    conditions: GetDetailCurrencyUnitDto,
    jwtPayload: any,
  ) => Promise<CurrencyUnitModel>;
  getCurrencyUnitVND: () => Promise<CurrencyUnitModel>;
  getListByIds: (
    conditions: GetCurrencyListByIdsDto,
    jwtPayload,
  ) => Promise<ResponseDto<CurrencyUnitModel>>;
}
