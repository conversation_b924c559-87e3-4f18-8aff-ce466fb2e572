import { GetDetailMaterialDto } from '../../controller/material/dtos/get-detail-material.dto';
import { GetMaterialListByIdsDto } from '../../controller/material/dtos/get-material-list-by-ids.dto';
import { GetMaterialListDto } from '../../controller/material/dtos/get-material-list.dto';
import { ResponseDto } from '../dtos/response.dto';
import { MaterialModel } from '../model/material.model';

export abstract class IMaterialRepository {
  createMaterial: (data: MaterialModel) => Promise<MaterialModel>;
  updateMaterial: (
    id: string,
    updateMaterialDto: MaterialModel,
  ) => Promise<MaterialModel>;
  deleteMaterial: (id: string) => Promise<void>;
  // getMaterialById: (id: string) => Promise<MaterialModel>;
  getMaterialByCode: (code: string, id?: string) => Promise<MaterialModel>;
  getMaterials: (
    conditions: GetMaterialListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<MaterialModel>>;
  getMaterialDetail: (
    conditions: GetDetailMaterialDto,
    jwtPayload: any,
  ) => Promise<MaterialModel>;
  getMaterialByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<MaterialModel[]>;
  getListByIds: (
    conditions: GetMaterialListByIdsDto,
    jwtPayload,
  ) => Promise<ResponseDto<MaterialModel>>;
}
