import { GetCostCenterListByIdsDto } from '../../controller/costcenter-subaccount/dtos/get-cost-center-list-by-ids.dto';
import { GetCostcenterSubaccountListDto } from '../../controller/costcenter-subaccount/dtos/get-costcenter-subaccount-list.dto';
import { GetDetailCostcenterSubaccountDto } from '../../controller/costcenter-subaccount/dtos/get-detail-costcenter-subaccount.dto';
import { ResponseDto } from '../dtos/response.dto';
import { CostcenterSubaccountModel } from '../model/costcenter-subaccount.model';

export abstract class ICostcenterSubaccountRepository {
  createCostcenterSubaccount: (
    data: CostcenterSubaccountModel,
  ) => Promise<CostcenterSubaccountModel>;
  updateCostcenterSubaccount: (
    data: CostcenterSubaccountModel,
  ) => Promise<CostcenterSubaccountModel>;
  getCostcenterSubaccounts: (
    conditions: GetCostcenterSubaccountListDto,
    jwtPayload,
  ) => Promise<ResponseDto<CostcenterSubaccountModel>>;
  deleteCostcenterSubaccount: (id: string) => Promise<void>;
  getCostcenterSubaccountById: (
    id: string,
  ) => Promise<CostcenterSubaccountModel>;
  getCostcenterSubaccountByCode: (
    code: string,
    id?: string,
  ) => Promise<CostcenterSubaccountModel>;
  getHistory: (id: string) => Promise<CostcenterSubaccountModel>;
  getCostcenterSubaccountDetail: (
    conditions: GetDetailCostcenterSubaccountDto,
    jwtPayload,
  ) => Promise<CostcenterSubaccountModel>;
  ///For import excel
  getCostcenterSubaccountsByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<CostcenterSubaccountModel[]>;
  getCostCenterByIds: (
    ids: string[],
    jwtPayload: any,
  ) => Promise<CostcenterSubaccountModel[]>;
  checkDuplicateCostCenter: (
    code: string,
    effectiveStartDate: Date,
    effectiveEndDate?: Date,
  ) => Promise<CostcenterSubaccountModel>;
  getListByIds: (
    conditions: GetCostCenterListByIdsDto,
    jwtPayload,
  ) => Promise<ResponseDto<CostcenterSubaccountModel>>;
}
