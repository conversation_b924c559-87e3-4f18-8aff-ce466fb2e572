import { GetDetailPurchaseRequestTypeDto } from '../../controller/purchase-request-type/dtos/get-detail-purchase-request-type.dto';
import { GetPurchaseRequestTypeListDto } from '../../controller/purchase-request-type/dtos/get-purchase-request-type-list.dto';
import { UpdatePurchaseRequestTypeDto } from '../../controller/purchase-request-type/dtos/update-purchase-request-type.dto';
import { ResponseDto } from '../dtos/response.dto';
import { PurchaseRequestTypeModel } from '../model/purchase-request-type.model';

export abstract class IPurchaseRequestTypeRepository {
  createPrType: (
    data: PurchaseRequestTypeModel,
  ) => Promise<PurchaseRequestTypeModel>;
  updatePrType: (
    id: string,
    updatePrTypeDto: UpdatePurchaseRequestTypeDto,
  ) => Promise<PurchaseRequestTypeModel>;
  deletePrType: (id: string) => Promise<void>;
  getPrTypeByCode: (
    code: string,
    id?: string,
  ) => Promise<PurchaseRequestTypeModel>;
  getPrTypes: (
    conditions: GetPurchaseRequestTypeListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<PurchaseRequestTypeModel>>;
  getPrTypeDetail: (
    conditions: GetDetailPurchaseRequestTypeDto,
    jwtPayload: any,
  ) => Promise<PurchaseRequestTypeModel>;
  getPrTypeByIds: (
    ids: string[],
    jwtPayload: any,
  ) => Promise<PurchaseRequestTypeModel[]>;
}
