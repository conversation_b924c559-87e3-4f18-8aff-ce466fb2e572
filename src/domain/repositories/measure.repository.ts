import { GetDetailMeasureDto } from '../../controller/measure/dtos/get-detail-cost.dto';
import { GetMeasureListByIdsDto } from '../../controller/measure/dtos/get-measure-list-by-ids.dto';
import { GetMeasureListDto } from '../../controller/measure/dtos/get-measure-list.dto';
import { UpdateMeasureDto } from '../../controller/measure/dtos/update-measure.dto';
import { ResponseDto } from '../dtos/response.dto';
import { MeasureModel } from '../model/measure.model';

export abstract class IMeasureRepository {
  createMeasure: (data: MeasureModel) => Promise<MeasureModel>;
  updateMeasure: (
    id: string,
    updateMeasureDto: UpdateMeasureDto,
  ) => Promise<MeasureModel>;
  deleteMeasure: (id: string) => Promise<void>;
  getDetailMeasure: (
    conditions: GetDetailMeasureDto,
    jwtPayload: any,
  ) => Promise<MeasureModel>;
  getMeasures: (
    conditions: GetMeasureListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<MeasureModel>>;
  getMeasureByCode: (code: string, id?: string) => Promise<MeasureModel>;
  ///For import excel
  getMeasuresByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<MeasureModel[]>;
  getListByIds: (
    conditions: GetMeasureListByIdsDto,
    jwtPayload,
  ) => Promise<ResponseDto<MeasureModel>>;
}
