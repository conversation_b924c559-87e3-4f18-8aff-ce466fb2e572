import { CreateSolomonPurchaseOrderItemDto } from '../../controller/solomon-purchase-order/dtos/create-solomon-purchase-order-item.dto';
import { SolomonPurchaseOrderItemModel } from '../model/solomon-purchase-order-item.model';

export abstract class ISolomonPurchaseOrderItemRepository {
  createSolomonPurchaseOrderItem: (
    data: CreateSolomonPurchaseOrderItemDto[],
  ) => Promise<SolomonPurchaseOrderItemModel[]>;
  getSolomonPOItemsByPoDetailId: (
    poDetailId: number,
  ) => Promise<SolomonPurchaseOrderItemModel[]>;
  deleteSolomonPOItemsBySolomonPurchaseOrderId: (
    sapPurchaseOrderIds: string[],
  ) => Promise<void>;
}
