import { CreateProcessTypeDto } from '../../controller/process-type/dtos/create-process-type.dto';
import { GetDetailProcessTypeDto } from '../../controller/process-type/dtos/get-detail-process-type.dto';
import { GetProcessTypeListDto } from '../../controller/process-type/dtos/get-process-type-list.dto';
import { UpdateProcessTypeDto } from '../../controller/process-type/dtos/update-process-type.dto';
import { ResponseDto } from '../dtos/response.dto';
import { ProcessTypeModel } from '../model/process-type.model';

export abstract class IProcessTypeRepository {
  createProcessType: (
    createProcessTypeDto: CreateProcessTypeDto,
  ) => Promise<ProcessTypeModel>;
  updateProcessType: (
    id: string,
    updateProcessTypeDto: UpdateProcessTypeDto,
  ) => Promise<ProcessTypeModel>;
  deleteProcessType: (id: string) => Promise<void>;
  getProcessTypeById: (id: string) => Promise<ProcessTypeModel>;
  getProcessTypeByCode: (
    code: string,
    id?: string,
  ) => Promise<ProcessTypeModel>;
  getProcessTypes: (
    conditions: GetProcessTypeListDto,
  ) => Promise<ResponseDto<ProcessTypeModel>>;
  getDetailProcessType: (
    conditions: GetDetailProcessTypeDto,
  ) => Promise<ProcessTypeModel>;
  ///For import excel
  getProcessTypesByCodesWithRole: (
    codes: string[],
    isNeedPermission?: boolean,
  ) => Promise<ProcessTypeModel[]>;
  getProcessTypeByIds: (ids: string[]) => Promise<ProcessTypeModel[]>;
}
