import { GetCompanyListDto } from '../../controller/company/dtos/get-company-list.dto';
import { GetDetailCompanyDto } from '../../controller/company/dtos/get-detail-company.dto';
import { UpdateCompanyDto } from '../../controller/company/dtos/update-company.dto';
import { ResponseDto } from '../dtos/response.dto';
import { CompanyModel } from '../model/company.model';

export abstract class ICompanyRepository {
  createCompany: (data: CompanyModel) => Promise<CompanyModel>;
  updateCompany: (
    id: string,
    updateCompanyDto: UpdateCompanyDto,
  ) => Promise<CompanyModel>;
  deleteCompany: (id: string) => Promise<void>;
  getCompanyById: (id: string) => Promise<CompanyModel>;
  getCompanyByCode: (code: string, id?: string) => Promise<CompanyModel>;
  getCompanies: (
    conditions: GetCompanyListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<CompanyModel>>;
  getDetailCompany: (
    conditions: GetDetailCompanyDto,
    jwtPayload: any,
  ) => Promise<CompanyModel>;
  ///For import excel
  getCompaniesByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<CompanyModel[]>;
  getCompanyByIds: (ids: string[], jwtPayload: any) => Promise<CompanyModel[]>;
  getCompanyByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<CompanyModel[]>;
}
