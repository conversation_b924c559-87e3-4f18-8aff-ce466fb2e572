import { GetDetailFunctionUnitDto } from '../../controller/function-unit/dtos/get-detail-function-unit.dto';
import { GetFunctionUnitListDto } from '../../controller/function-unit/dtos/get-function-unit-list.dto';
import { ResponseDto } from '../dtos/response.dto';
import { FunctionUnitModel } from '../model/function-unit.model';

export abstract class IFunctionUnitRepository {
  createFunctionUnit: (data: FunctionUnitModel) => Promise<FunctionUnitModel>;
  updateFunctionUnit: (data: FunctionUnitModel) => Promise<FunctionUnitModel>;
  getFunctionUnits: (
    conditions: GetFunctionUnitListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<FunctionUnitModel>>;
  deleteFunctionUnit: (id: string) => Promise<void>;
  getFunctionUnitById: (id: string) => Promise<FunctionUnitModel>;
  getFunctionUnitByCode: (
    code: string,
    id?: string,
  ) => Promise<FunctionUnitModel>;
  getDetailFunctionUnit: (
    conditions: GetDetailFunctionUnitDto,
    jwtPayload: any,
  ) => Promise<FunctionUnitModel>;
  getFunctionUnitsByIds: (
    ids: string[],
    jwtPayload: any,
  ) => Promise<FunctionUnitModel[]>;
  getFunctionUnitsByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<FunctionUnitModel[]>;
}
