import { GetDetailSupplierDto } from '../../controller/supplier/dtos/get-detail-supplier.dto';
import { GetSupplierListByIdsDto } from '../../controller/supplier/dtos/get-supplier-list-by-ids.dto';
import { GetSupplierListDto } from '../../controller/supplier/dtos/get-supplier-list.dto';
import { UpdateSupplierDto } from '../../controller/supplier/dtos/update-supplier.dto';
import { ResponseDto } from '../dtos/response.dto';
import { SupplierModel } from '../model/supplier.model';

export abstract class ISupplierRepository {
  createSupplier: (data: SupplierModel) => Promise<SupplierModel>;
  updateSupplier: (
    id: string,
    updateSupplierDto: UpdateSupplierDto,
  ) => Promise<SupplierModel>;
  deleteSupplier: (id: string) => Promise<void>;
  // getSupplierById: (id: string) => Promise<SupplierModel>;
  getSupplierByCode: (code: string, id?: string) => Promise<SupplierModel>;
  getSuppliers: (
    conditions: GetSupplierListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<SupplierModel>>;
  getSupplierDetail: (
    conditions: GetDetailSupplierDto,
    jwtPayload: any,
  ) => Promise<SupplierModel>;
  getSupplierByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<SupplierModel[]>;
  getListByIds: (
    conditions: GetSupplierListByIdsDto,
    jwtPayload,
  ) => Promise<ResponseDto<SupplierModel>>;
}
