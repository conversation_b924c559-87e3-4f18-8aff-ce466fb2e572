import { GetDetailMaterialTypeDto } from '../../controller/material-type/dtos/get-detail-material-type.dto';
import { GetMaterialTypeListDto } from '../../controller/material-type/dtos/get-material-type-list.dto';
import { UpdateMaterialTypeDto } from '../../controller/material-type/dtos/update-material-type.dto';
import { ResponseDto } from '../dtos/response.dto';
import { MaterialTypeModel } from '../model/material-type.model';

export abstract class IMaterialTypeRepository {
  createMaterialType: (data: MaterialTypeModel) => Promise<MaterialTypeModel>;
  updateMaterialType: (
    id: string,
    updateMaterialTypeDto: UpdateMaterialTypeDto,
  ) => Promise<MaterialTypeModel>;
  deleteMaterialType: (id: string) => Promise<void>;
  // getMaterialTypeById: (id: string) => Promise<MaterialTypeModel>;
  getMaterialTypeByCode: (
    code: string,
    id?: string,
  ) => Promise<MaterialTypeModel>;
  getMaterialTypes: (
    conditions: GetMaterialTypeListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<MaterialTypeModel>>;
  getMaterialTypeDetail: (
    conditions: GetDetailMaterialTypeDto,
    jwtPayload: any,
  ) => Promise<MaterialTypeModel>;
  getMaterialTypeByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<MaterialTypeModel[]>;
}
