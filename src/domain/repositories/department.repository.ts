import { GetDepartmentListDto } from '../../controller/department/dtos/get-department-list.dto';
import { GetDetailDepartmentDto } from '../../controller/department/dtos/get-detail-department.dto';
import { ResponseDto } from '../dtos/response.dto';
import { DepartmentModel } from '../model/department.model';

export abstract class IDepartmentRepository {
  createDepartment: (data: DepartmentModel) => Promise<DepartmentModel>;
  updateDepartment: (data: DepartmentModel) => Promise<DepartmentModel>;
  getDepartments: (
    conditions: GetDepartmentListDto,
    jwtPayload,
  ) => Promise<ResponseDto<DepartmentModel>>;
  deleteDepartment: (id: string) => Promise<void>;
  getDepartmentById: (id: string) => Promise<DepartmentModel>;
  getDepartmentByCode: (code: string, id?: string) => Promise<DepartmentModel>;
  getDetailDepartment: (
    conditions: GetDetailDepartmentDto,
    jwtPayload,
  ) => Promise<DepartmentModel>;
  ///For import excel
  getDepartmentsByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<DepartmentModel[]>;
  getDepartmentByIds: (
    ids: string[],
    jwtPayload: any,
  ) => Promise<DepartmentModel[]>;
}
