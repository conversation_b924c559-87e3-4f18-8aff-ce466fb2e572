import { GetDetailPlantDto } from '../../controller/plant/dtos/get-detail-plant.dto';
import { GetPlantListDto } from '../../controller/plant/dtos/get-plant-list.dto';
import { UpdatePlantDto } from '../../controller/plant/dtos/update-plant.dto';
import { ResponseDto } from '../dtos/response.dto';
import { PlantModel } from '../model/plant.model';

export abstract class IPlantRepository {
  createPlant: (data: PlantModel) => Promise<PlantModel>;
  updatePlant: (
    id: string,
    updatePlantDto: UpdatePlantDto,
  ) => Promise<PlantModel>;
  deletePlant: (id: string) => Promise<void>;
  // getPlantById: (id: string) => Promise<PlantModel>;
  getPlantByCode: (code: string, id?: string) => Promise<PlantModel>;
  getPlants: (
    conditions: GetPlantListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<PlantModel>>;
  getPlantDetail: (
    conditions: GetDetailPlantDto,
    jwtPayload: any,
  ) => Promise<PlantModel>;
  getPlantByIds: (ids: string[], jwtPayload: any) => Promise<PlantModel[]>;
}
