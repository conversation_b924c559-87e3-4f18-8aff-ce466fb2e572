import { CreateWarehouseDto } from '../../controller/warehouse/dtos/create-warehouse.dto';
import { GetDetailWarehouseDto } from '../../controller/warehouse/dtos/get-detail-warehouse.dto';
import { GetWarehouseListDto } from '../../controller/warehouse/dtos/get-warehouse-list.dto';
import { UpdateWarehouseDto } from '../../controller/warehouse/dtos/update-warehouse.dto';
import { ResponseDto } from '../dtos/response.dto';
import { WarehouseModel } from '../model/warehouse.model';

export abstract class IWarehouseRepository {
  createWarehouse: (data: CreateWarehouseDto) => Promise<WarehouseModel>;
  updateWarehouse: (
    id: string,
    updateWarehouseDto: UpdateWarehouseDto,
  ) => Promise<WarehouseModel>;
  deleteWarehouse: (id: string) => Promise<void>;
  getWarehouseById: (id: string) => Promise<WarehouseModel>;
  getWarehouseByCode: (code: string, id?: string) => Promise<WarehouseModel>;
  getWarehouses: (
    conditions: GetWarehouseListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<WarehouseModel>>;
  getDetailWarehouse: (
    conditions: GetDetailWarehouseDto,
    jwtPayload: any,
  ) => Promise<WarehouseModel>;
  getWarehouseByIds: (
    conditions: GetWarehouseListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<WarehouseModel>>;
  validateDeleteWarehouse: (id: string) => Promise<WarehouseModel>;
  getWarehousesByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<WarehouseModel[]>;
}
