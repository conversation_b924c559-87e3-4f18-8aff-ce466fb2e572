import { BudgetInvestmentModel } from '../model/budget-investment.model';

export abstract class IBudgetInvestmentRepository {
  createManyBudgetInvestment: (
    data: BudgetInvestmentModel[],
  ) => Promise<BudgetInvestmentModel[]>;
  updateManyBudgetInvestment: (
    data: BudgetInvestmentModel[],
  ) => Promise<BudgetInvestmentModel[]>;
  deleteBudgetInvestmentsNotIn: (
    ids: string[],
    budgetCapexId: string,
  ) => Promise<void>;
}
