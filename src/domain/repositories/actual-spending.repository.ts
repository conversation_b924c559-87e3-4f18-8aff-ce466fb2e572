import { ActualSpendingForReportDto } from '../../controller/actual-spending/dtos/actual-spending-for-report.dto';
import { CreateActualSpendingDto } from '../../controller/actual-spending/dtos/create-actual-spending.dto';
import { GetActualSpendingListForSyncSapDto } from '../../controller/actual-spending/dtos/get-list-actual-spending-for-sync-sap.dto';
import { GetActualSpendingListDto } from '../../controller/actual-spending/dtos/get-list-actual-spending.dto';
import { UpdateActualSpendingDto } from '../../controller/actual-spending/dtos/update-actual-spending.dto';
import { ResponseDto } from '../dtos/response.dto';
import { ActualSpendingModel } from '../model/actual-spending.model';

export abstract class IActualSpendingRepository {
  createActualSpendingList: (
    createActualSpendingDtos: CreateActualSpendingDto[],
  ) => Promise<ActualSpendingModel[]>;
  getDetailActualSpending: (
    actualSpendingId: string,
    jwtPayload: any,
  ) => Promise<ActualSpendingModel>;
  getListActualSpending: (
    conditions: GetActualSpendingListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<ActualSpendingModel>>;
  updateActualSpending: (
    updateActualSpendingDto: UpdateActualSpendingDto,
  ) => Promise<ActualSpendingModel>;
  getListActualSpendingById: (ids: string[]) => Promise<ActualSpendingModel[]>;
  actualSpendingForReport: (
    conditions: ActualSpendingForReportDto,
  ) => Promise<ActualSpendingModel[]>;
  updateActualSpendingList: (
    updateActualSpendingDtos: UpdateActualSpendingDto[],
  ) => Promise<ActualSpendingModel[]>;
  findExistingRecords: (dtos: any) => Promise<ActualSpendingModel[]>;
  getListActualSpedingForUpdateSyncSap: (
    conditions: GetActualSpendingListForSyncSapDto,
  ) => Promise<ActualSpendingModel[]>;
}
