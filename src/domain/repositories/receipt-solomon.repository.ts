import { CreateReceiptSolomonDto } from '../../controller/receipt-solomon/dtos/create-receipt-solomon.dto';
import { GetListReceiptDto } from '../../controller/receipt-solomon/dtos/get-list-receipt.dto';
import { GetUuidDto } from '../dtos/get-uuid.dto';
import { ResponseDto } from '../dtos/response.dto';
import { ReceiptSolomonModel } from '../model/receipt-solomon.model';

export abstract class IReceiptSolomonRepository {
  createReceipt: (
    data: CreateReceiptSolomonDto[],
  ) => Promise<ReceiptSolomonModel[]>;
  getReceipts: (
    param: GetListReceiptDto,
  ) => Promise<ResponseDto<ReceiptSolomonModel>>;
  getDetailReceipt: (param: GetUuidDto) => Promise<ReceiptSolomonModel>;
}
