import { GetDetailPositionDto } from '../../controller/position/dtos/get-detail-position.dto';
import { GetPositionListDto } from '../../controller/position/dtos/get-position-list.dto';
import { UpdatePositionDto } from '../../controller/position/dtos/update-position.dto';
import { ResponseDto } from '../dtos/response.dto';
import { PositionModel } from '../model/position.model';

export abstract class IPositionRepository {
  createPosition: (data: PositionModel) => Promise<PositionModel>;
  updatePosition: (
    id: string,
    updatePositionDto: UpdatePositionDto,
  ) => Promise<PositionModel>;
  deletePosition: (id: string) => Promise<void>;
  getPositionById: (id: string) => Promise<PositionModel>;
  getPositionByCode: (code: string, id?: string) => Promise<PositionModel>;
  getPositions: (
    conditions: GetPositionListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<PositionModel>>;
  getDetailPosition: (
    conditions: GetDetailPositionDto,
    jwtPayload: any,
  ) => Promise<PositionModel>;
}
