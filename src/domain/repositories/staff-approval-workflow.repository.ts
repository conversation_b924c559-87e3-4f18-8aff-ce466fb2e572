import { CreateStaffApprovalWorkflowDto } from '../../controller/process/dtos/create-staff-approval-workflow.dto';
import { StaffApprovalWorkflowModel } from '../model/staff-approval-workflow.model';
import { StaffModel } from '../model/staff.model';

export abstract class IStaffApprovalWorkflowRepository {
  createStaffApprovalWorkflow: (
    data: CreateStaffApprovalWorkflowDto,
  ) => Promise<StaffApprovalWorkflowModel>;
  deleteStaffApprovalWorkflow: (approvalWorkflowIds: string[]) => Promise<void>;
  getSelectedStaffs: (staffApprovalWorkflowId: string) => Promise<StaffModel[]>;
}
