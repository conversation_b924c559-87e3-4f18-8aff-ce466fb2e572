import { GetDetailProfitCenterDto } from '../../controller/profit-center/dtos/get-detail-profit-center.dto';
import { GetProfitCenterListDto } from '../../controller/profit-center/dtos/get-profit-center-list.dto';
import { ResponseDto } from '../dtos/response.dto';
import { ProfitCenterModel } from '../model/profit-center.model';

export abstract class IProfitCenterRepository {
  createProfitCenter: (data: ProfitCenterModel) => Promise<ProfitCenterModel>;
  updateProfitCenter: (data: ProfitCenterModel) => Promise<ProfitCenterModel>;
  getProfitCenters: (
    conditions: GetProfitCenterListDto,
  ) => Promise<ResponseDto<ProfitCenterModel>>;
  deleteProfitCenter: (id: string) => Promise<void>;
  getProfitCenterByCode: (
    code: string,
    id?: string,
  ) => Promise<ProfitCenterModel>;
  getDetailProfitCenter: (
    conditions: GetDetailProfitCenterDto,
  ) => Promise<ProfitCenterModel>;
  getProfitCenterByCodes: (codes: string[]) => Promise<ProfitCenterModel[]>;
}
