import { GetBusinessOwnerListDto } from '../../controller/business-owner/dtos/get-business-owner-list.dto';
import { GetDetailBusinessOwnerDto } from '../../controller/business-owner/dtos/get-detail-business-owner.dto';
import { UpdateBusinessOwnerDto } from '../../controller/business-owner/dtos/update-business-owner.dto';
import { ResponseDto } from '../dtos/response.dto';
import { BusinessOwnerModel } from '../model/business-owner.model';

export abstract class IBusinessOwnerRepository {
  createBusinessOwner: (
    data: BusinessOwnerModel,
  ) => Promise<BusinessOwnerModel>;
  updateBusinessOwner: (
    id: string,
    updateBusinessOwnerDto: UpdateBusinessOwnerDto,
  ) => Promise<BusinessOwnerModel>;
  deleteBusinessOwner: (id: string) => Promise<void>;
  getBusinessOwnerById: (id: string) => Promise<BusinessOwnerModel>;
  getBusinessOwnerByCode: (
    code: string,
    id?: string,
  ) => Promise<BusinessOwnerModel>;
  getBusinessOwners: (
    conditions: GetBusinessOwnerListDto,
    jwtPayload,
  ) => Promise<ResponseDto<BusinessOwnerModel>>;
  getBusinessOwnerByCodes: (codes: string[]) => Promise<BusinessOwnerModel[]>;
  getBusinessOwnerByIds: (
    ids: string[],
    jwtPayload: any,
  ) => Promise<BusinessOwnerModel[]>;
  getDetailBusinessOwner: (
    conditions: GetDetailBusinessOwnerDto,
    jwtPayload,
  ) => Promise<BusinessOwnerModel>;
  ///For import excel
  getBusinessOwnersByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<BusinessOwnerModel[]>;
}
