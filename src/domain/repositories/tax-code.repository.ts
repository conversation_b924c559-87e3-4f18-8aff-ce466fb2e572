import { CreateTaxCodeDto } from '../../controller/tax-code/dtos/create-tax-code.dto';
import { GetDetailTaxCodeDto } from '../../controller/tax-code/dtos/get-detail-tax-code.dto';
import { GetTaxCodeListDto } from '../../controller/tax-code/dtos/get-tax-code-list.dto';
import { UpdateTaxCodeDto } from '../../controller/tax-code/dtos/update-tax-code.dto';
import { ResponseDto } from '../dtos/response.dto';
import { TaxCodeModel } from '../model/tax-code.model';

export abstract class ITaxCodeRepository {
  createTaxCode: (data: CreateTaxCodeDto) => Promise<TaxCodeModel>;
  updateTaxCode: (data: UpdateTaxCodeDto) => Promise<TaxCodeModel>;
  getTaxCodes: (
    conditions: GetTaxCodeListDto,
    jwtPayload,
  ) => Promise<ResponseDto<TaxCodeModel>>;
  deleteTaxCode: (id: string) => Promise<void>;
  getTaxCodeByCode: (code: string, id?: string) => Promise<TaxCodeModel>;
  getTaxCodeDetail: (
    conditions: GetDetailTaxCodeDto,
    jwtPayload,
  ) => Promise<TaxCodeModel>;
  getTaxCodesByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<TaxCodeModel[]>;
}
