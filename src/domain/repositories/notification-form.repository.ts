import { GetDetailNotificationFormDto } from '../../controller/notification-form/dtos/get-detail-notification-form.dto';
import { GetNotificationFormListDto } from '../../controller/notification-form/dtos/get-notification-form-list.dto';
import { NotificationForm } from '../../infrastructure/entities/notification-form.entity';
import { ENotificationFormType } from '../config/enums/notification-form.enum';
import { ResponseDto } from '../dtos/response.dto';

export abstract class INotificationFormRepository {
  createNotificationForm: (data: NotificationForm) => Promise<NotificationForm>;
  updateNotificationForm: (data: NotificationForm) => Promise<void>;
  getNotificationForms: (
    conditions: GetNotificationFormListDto,
  ) => Promise<ResponseDto<NotificationForm>>;
  getNotificationFormDetail: (
    conditions: GetDetailNotificationFormDto,
  ) => Promise<NotificationForm>;
  deleteNotificationForm: (id: string) => Promise<void>;
  getNotificationFormByType: (
    type: ENotificationFormType,
  ) => Promise<NotificationForm>;
}
