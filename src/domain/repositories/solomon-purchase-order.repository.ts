import { CreateSolomonPurchaseOrderDto } from '../../controller/solomon-purchase-order/dtos/create-solomon-purchase-order.dto';
import { SolomonPurchaseOrderModel } from '../model/solomon-purchase-order.model';

export abstract class ISolomonPurchaseOrderRepository {
  getListSolomonPurchaseOrderByIds: (
    ids: string[],
  ) => Promise<SolomonPurchaseOrderModel[]>;
  createSolomonPurchaseOrder: (
    createSolomonPurchaseOrderDto: CreateSolomonPurchaseOrderDto,
  ) => Promise<SolomonPurchaseOrderModel>;
  updateSolomonPurchaseOrder: (
    model: SolomonPurchaseOrderModel,
  ) => Promise<SolomonPurchaseOrderModel>;
  deleteSolomonPurchaseOrdersById: (ids: string[]) => Promise<void>;
}
