import { GetDetailPurchaseOrderTypeDto } from '../../controller/purchase-order-type/dtos/get-detail-purchase-order-type.dto';
import { GetPurchaseOrderTypeListDto } from '../../controller/purchase-order-type/dtos/get-purchase-order-type-list.dto';
import { UpdatePurchaseOrderTypeDto } from '../../controller/purchase-order-type/dtos/update-purchase-order-type.dto';
import { ResponseDto } from '../dtos/response.dto';
import { PurchaseOrderTypeModel } from '../model/purchase-order-type.model';

export abstract class IPurchaseOrderTypeRepository {
  createPurchaseOrderType: (
    data: PurchaseOrderTypeModel,
  ) => Promise<PurchaseOrderTypeModel>;
  updatePurchaseOrderType: (
    id: string,
    updatePurchaseOrderTypeDto: UpdatePurchaseOrderTypeDto,
  ) => Promise<PurchaseOrderTypeModel>;
  deletePurchaseOrderType: (id: string) => Promise<void>;
  getPurchaseOrderTypeByCode: (
    code: string,
    id?: string,
  ) => Promise<PurchaseOrderTypeModel>;
  getPurchaseOrderTypes: (
    conditions: GetPurchaseOrderTypeListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<PurchaseOrderTypeModel>>;
  getPurchaseOrderTypeDetail: (
    conditions: GetDetailPurchaseOrderTypeDto,
    jwtPayload: any,
  ) => Promise<PurchaseOrderTypeModel>;
  getPoTypeByIds: (
    ids: string[],
    jwtPayload: any,
  ) => Promise<PurchaseOrderTypeModel[]>;
}
