import { GetDetailPurchasingGroupDto } from '../../controller/purchasing-group/dtos/get-detail-purchasing-group.dto';
import { GetPurchasingGroupListDto } from '../../controller/purchasing-group/dtos/get-purchasing-group-list.dto';
import { UpdatePurchasingGroupDto } from '../../controller/purchasing-group/dtos/update-purchasing-group.dto';
import { ResponseDto } from '../dtos/response.dto';
import { PurchasingGroupModel } from '../model/purchasing-group.model';

export abstract class IPurchasingGroupRepository {
  createPurchasingGroup: (
    data: PurchasingGroupModel,
  ) => Promise<PurchasingGroupModel>;
  updatePurchasingGroup: (
    id: string,
    updatePurchasingGroupDto: UpdatePurchasingGroupDto,
  ) => Promise<PurchasingGroupModel>;
  deletePurchasingGroup: (id: string) => Promise<void>;
  // getPurchasingGroupById: (id: string) => Promise<PurchasingGroupModel>;
  getPurchasingGroupByCode: (
    code: string,
    jwtPayload: any,
    id?: string,
  ) => Promise<PurchasingGroupModel>;
  getPurchasingGroups: (
    conditions: GetPurchasingGroupListDto,
    jwtPayload: any,
  ) => Promise<ResponseDto<PurchasingGroupModel>>;
  getPurchasingGroupDetail: (
    conditions: GetDetailPurchasingGroupDto,
    jwtPayload: any,
  ) => Promise<PurchasingGroupModel>;
  getPurchasingGroupByCodesWithRole: (
    codes: string[],
    jwtPayload: any,
    isNeedPermission?: boolean,
  ) => Promise<PurchasingGroupModel[]>;
}
