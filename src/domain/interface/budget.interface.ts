import { BudgetCodeModel } from '../model/budget-code.model';
import { BudgetModel } from '../model/budget.model';
import { BusinessOwnerModel } from '../model/business-owner.model';
import { CostModel } from '../model/cost.model';
import { CostcenterSubaccountModel } from '../model/costcenter-subaccount.model';

export interface IImportBudget {
  createType?: string;
  budgetType?: string;
  currencyUnitCode?: string;
  budgetCodeCode?: string;
  costcenterSubaccountCode?: string;
  adjustBudgetCode?: string;
  note?: string;
  effectiveStartDate?: string;
  effectiveEndDate?: string;
  status?: string;
  totalValue?: number;
  operationInvestment?: string;
}

export interface IGroupBudgetByBudgetCodeAndCostCenter {
  budgetCodeId: string;
  costcenterSubaccountId: string;
  budgets: BudgetModel[];
}

export interface IReponseReportBudgetDetail {
  budgetCode: BudgetCodeModel;
  costcenterSubaccount: CostcenterSubaccountModel;
  budgets: BudgetModel[];
  /// BOTH CAPEX AND OPEX
  // Trả về tổng giá trị các dòng ngân sách có
  // Mã ngân sách và Cost Center bằng dòng trên lưới,
  // & Thời gian bắt đầu và kết thúc giao với Tháng bắt đầu và Tháng kết thúc ở điều kiện lọc
  // & Loại khởi tạo = Ngân sách mới
  beginBudget: number; //Logic lấy tỷ giá theo ngân sách
  // (1) Lấy tổng giá trị dòng ngân sách có
  // Mã ngân sách và Cost Center bằng dòng trên lưới,
  // & Thời gian bắt đầu và kết thúc giao với Tháng bắt đầu và Tháng kết thúc ở điều kiện lọc
  // & Loại khởi tạo = Tăng ngân sách
  // & Phát sinh từ PO
  // (2) Lấy tổng giá trị dòng ngân sách có
  // Mã ngân sách và Cost Center bằng dòng trên lưới,
  // & Thời gian bắt đầu và kết thúc giao với Tháng bắt đầu và Tháng kết thúc ở điều kiện lọc
  // & Loại khởi tạo = Giảm ngân sách
  // & Phát sinh từ PO
  // Trả về (1) - (2)
  addedBudget: number; //Logic lấy tỷ giá theo ngân sách
  // (1) Lấy tổng giá trị dòng ngân sách có
  // Mã ngân sách và Cost Center bằng dòng trên lưới,
  // & Thời gian bắt đầu và kết thúc giao với Tháng bắt đầu và Tháng kết thúc ở điều kiện lọc
  // & Loại khởi tạo = Tăng ngân sách
  // & Không phát sinh từ PO
  // (2) Lấy tổng giá trị dòng ngân sách có
  // Mã ngân sách và Cost Center bằng dòng trên lưới,
  // & Thời gian bắt đầu và kết thúc giao với Tháng bắt đầu và Tháng kết thúc ở điều kiện lọc
  // & Loại khởi tạo = Giảm ngân sách
  // & Không phát sinh từ PO
  // Trả về (1) - (2)
  adjustBudget: number; //Logic lấy tỷ giá theo ngân sách
  // Begin Budget + Added Budget + Adjust Budget
  totalBudget: number;
  // Lấy tổng “Thành tiền bao gồm VAT” các dòng PO detail có
  // Mã ngân sách = Mã ngân sách dòng đang xét
  // & Cost Center = Cost Center dòng đang xét
  // & Trạng thái PO là duyệt (cuối)
  // & Ngày tạo PO nằm trong 1 trong các kỳ đã chọn
  approvedBudget: number; //Logic lấy tỷ giá theo ngày duyệt PO
  // Total budget - Approved Budget
  availableBudget: number;
  /// CAPEX
  // Lấy tổng “Thành tiền bao gồm VAT” các dòng PO detail có
  // Mã ngân sách = Mã ngân sách dòng đang xét
  // & Cost Center = Cost Center dòng đang xét
  // & Trạng thái PO là duyệt (cuối)
  // Hoặc PO đã được kế toán duyệt
  // & Ngày tạo PO nằm trong 1 trong các kỳ đã chọn
  poAmount?: number; //Logic lấy tỷ giá theo ngày duyệt PO
  // Approved Budget / Total budget * 100%
  usedBudget?: number;
  // Lấy tổng “Số tiền thanh toán” từ bảng Actual có
  // Mã ngân sách = Mã ngân sách dòng đang xét
  // & Cost Center = Cost Center dòng đang xét
  // & Loại giao dịch = A (sẽ update khi có tài liệu chính thức từ SAP)
  // & Kỳ thuộc trong các Kỳ đã lọc
  totalPayment: number; //Báo cáo dữ liệu thanh toán thực tế
  // Total payment / Approved Budget * 100%
  payment?: number;
  // Approved Budget - Total payment
  commitment?: number;
  /// OPEX
  // Lấy tổng “Số tiền thanh toán” từ bảng Actual có
  // Mã ngân sách = Mã ngân sách dòng đang xét
  // & Cost Center = Cost Center dòng đang xét
  // & Loại giao dịch = A (sẽ update khi có tài liệu chính thức từ SAP)
  // & Kỳ thuộc trong các Kỳ đã lọc
  // & PO = NULL
  actualPaymentPrPoFromE?: number; //Báo cáo dữ liệu thanh toán thực tế
  // Lấy tổng “Số tiền thanh toán” từ bảng Actual có
  // Mã ngân sách = Mã ngân sách dòng đang xét
  // & Cost Center = Cost Center dòng đang xét
  // & Loại giao dịch = A (sẽ update khi có tài liệu chính thức từ SAP)
  // & Kỳ thuộc trong các Kỳ đã lọc
  // & PO <> NULL
  actualPaymentPrPoExcludingE?: number; //Báo cáo dữ liệu thanh toán thực tế
  // Lấy tổng “Số tiền điều chỉnh” từ bảng Actual có
  // Mã ngân sách = Mã ngân sách dòng đang xét
  // & Cost Center = Cost Center dòng đang xét
  // & Loại giao dịch = RECLASSIFY (sẽ update khi có tài liệu chính thức từ SAP)
  // & Kỳ thuộc trong các Kỳ đã lọc
  reclassifyAmount?: number;
  actual?: number;
  advance?: number;
}

export interface IGroupBudgetByBusinessOwnerAndCostCenter {
  businessOwnerId: string;
  division?: string; // Ghi chú 2 trong cost center
  costId?: string;
  budgets: BudgetModel[];
}

export interface IReponseReportBudgetOverview {
  businessOwner: BusinessOwnerModel;
  division?: string;
  cost?: CostModel;
  budgets: BudgetModel[];
  /// CAPEX
  beginBudget?: number;
  addedBudget?: number;
  adjustBudget?: number;
  totalBudget?: number;
  approvedBudget?: number;
  totalPayment?: number;
  commitment?: number;
  endBalance?: number;
  availableBudget?: number;
  note?: string;
  /// OPEX
  budget?: number;
  actual?: number;
  advance?: number;
}
