import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsDateString,
  IsInt,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { removeUnicode } from '../../utils/common';

export class SearchDto {
  @ApiPropertyOptional({ type: String, description: 'Nội dung muốn tìm kiếm' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) =>
    value ? '%' + removeUnicode(value.trim()) + '%' : '',
  )
  searchString?: string;
}

export class CommonDto extends SearchDto {
  @ApiPropertyOptional({
    type: Number,
    default: 1,
    example: 1,
    description: 'Trang kết quả (phải là số nguyên dương)',
  })
  @Min(1)
  @IsInt()
  @Transform(({ value }) => +value)
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    type: Number,
    example: 10,
    description: '<PERSON><PERSON> dòng trên 1 trang (phải là số nguyên dương)',
  })
  @Min(1)
  @IsInt()
  @Transform(({ value }) => +value)
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({
    type: Number,
    default: 1,
    example: 1,
    description: 'Get all',
  })
  @IsOptional()
  @IsInt()
  @Transform(({ value }) => +value)
  getAll?: number = 0;

  @ApiPropertyOptional({
    type: Number,
    default: 1,
    example: 1,
    description: 'Return Empty Result',
  })
  @IsOptional()
  @Min(1)
  @IsInt()
  @Transform(({ value }) => +value)
  isEmptyResult?: number;

  // @ApiPropertyOptional({ enum: EOrderBy, default: EOrderBy.DESC })
  // @IsEnum(EOrderBy)
  // @IsOptional()
  // order?: EOrderBy = EOrderBy.DESC;

  // @ApiPropertyOptional({
  //   type: String,
  //   example: 'created_at',
  //   description: 'ColumnName Order',
  // })
  // @IsOptional()
  // @IsString({ message: 'VALIDATE.ORDER_BY.MUST_BE_STRING' })
  // orderBy?: string = 'created_at';
}

export class PaginationDto extends CommonDto {
  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2023-12-31T17:00:00.000Z',
    description: 'Ngày bắt đầu (khởi tạo) đối tượng cần tìm kiếm',
  })
  @IsDateString()
  @IsOptional()
  from?: string;

  @ApiPropertyOptional({
    type: String,
    format: 'date',
    example: '2024-12-31T16:59:59.999Z',
    description: 'Ngày kết thúc (khởi tạo) đối tượng cần tìm kiếm',
  })
  @IsDateString()
  @IsOptional()
  to?: string;
}
