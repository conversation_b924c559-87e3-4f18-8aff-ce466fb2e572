import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateIf,
} from 'class-validator';

export class GetUuidDto {
  @ApiProperty({ type: String, required: false, description: 'Code' })
  @IsOptional()
  @IsNotEmpty({ message: 'VALIDATE.CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.CODE.MUST_BE_STRING' })
  code?: string;

  @ApiProperty({ type: String, required: false, description: 'UUID' })
  @ValidateIf((data) => !data.code)
  @IsNotEmpty({ message: 'VALIDATE.ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.ID.MUST_BE_UUID' })
  id?: string;
}
