import { HttpException, HttpStatus } from '@nestjs/common';
import {
  existsSync,
  mkdirSync,
  readFileSync,
  unlinkSync,
  writeFileSync,
} from 'fs';
import { diskStorage } from 'multer';
import { extname } from 'path';

// Multer configuration
export const multerConfig = {
  dest: './uploads/',
};

export const storage = (path?: string) =>
  diskStorage({
    // Destination storage path details
    destination: (req: any, file: any, cb: any) => {
      const uploadPath = path || multerConfig.dest;
      // Create folder if doesn't exist
      if (!existsSync(uploadPath)) {
        mkdirSync(uploadPath);
      }
      cb(null, uploadPath);
    },
    // File modification details
    filename: (req: any, file: any, cb: any) => {
      // Calling the callback passing the random name generated with the original extension name
      cb(null, `${Date.now()}-${file.originalname}`);
    },
  });

// Multer upload options
export const multerOptions = {
  // Enable file size limits
  //   limits: {
  //     fileSize: +process.env.MAX_FILE_SIZE,
  //   },
  // Check the mimetypes to allow for upload
  fileFilter: (req: any, file: any, cb: any) => {
    // if (file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
    // Allow storage of file
    cb(null, true);
    // } else {
    //   // Reject file
    //   cb(
    //     new HttpException(
    //       `Unsupported file type ${extname(file.originalname)}`,
    //       HttpStatus.BAD_REQUEST,
    //     ),
    //     false,
    //   );
    // }
  },
  // Storage properties
  storage: storage(),
};

// export const upload = async (file: Express.Multer.File, path?: string) => {
//   const uploadPath = path || multerConfig.dest;
//   if (!existsSync(uploadPath)) {
//     mkdirSync(uploadPath);
//   }
//   const fileName = `${Date.now()}-${file.originalname}`;

//   writeFileSync(uploadPath + `/${fileName}`, file.buffer);

//   readFileSync(uploadPath + `/${fileName}`);

//   if (existsSync(uploadPath + `/${fileName}`)) {
//     return {
//       ...file,
//       destination: uploadPath,
//       filename: fileName,
//       path: uploadPath + `/${fileName}`,
//       buffer: undefined,
//     } as Express.Multer.File;
//   } else {
//     return null;
//   }
// };

// export const uploadFile = async (file: Express.Multer.File, path?: string) => {
//   return await upload(file, path);
// };

// export const uploadFiles = async (
//   files: Express.Multer.File[],
//   path?: string,
// ) => {
//   const filesData: Express.Multer.File[] = [];

//   for (const file of files) {
//     const uploaded = await upload(file, path);
//     if (uploaded) {
//       filesData.push(uploaded);
//     }
//   }

//   return filesData;
// };

// export const deletePath = async (path: string) => {
//   if (existsSync(path)) {
//     unlinkSync(path);
//   }
// };

// export const deleteFile = async (path: string) => {
//   await deletePath(path);
// };

// export const deleteFiles = async (files: Express.Multer.File[]) => {
//   for (const file of files) {
//     await deletePath(file.path);
//   }
// };
