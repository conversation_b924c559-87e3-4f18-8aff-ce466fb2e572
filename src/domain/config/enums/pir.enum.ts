export enum EPIRColumnSheet {
  vendorCode = 'A', //Mã NCC
  materialCode = 'B', //Mã material
  businessUnitCode = 'C', //Đơn vị kinh doanh
  purchaseOrganizationCode = 'D', //Mã bộ phận mua hàng
  purchaseGroupCode = 'E', //Mã nhóm thu mua
  infoType = 'F', //Loại thông tin
  vendorLeadTime = 'G', //Leadtime của NCC
  regularPurchaseQuantity = 'H', //Số lượng thường mua hàng
  minimumOrderQuantity = 'I', //Số lượng đặt hàng tối thiểu
  upperTolerance = 'J', //Dung sai trên theo %
  lowerTolerance = 'K', //Dung sai dưới theo %
  purchasePrice = 'L', //Giá tiền mua hàng
  currencyCode = 'M', //Loại tiền
  overPurchaseUnit = 'N', //Trên bao nhiêu đơn vị mua
  effectiveDate = 'O', //Thời gian có hiệu lực
  expirationDate = 'P', //Thời gian hết hiệu lực
  status = 'Q', //Trạng thái
}
