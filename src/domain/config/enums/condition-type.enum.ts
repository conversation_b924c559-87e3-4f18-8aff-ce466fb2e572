export enum EConditionType {
  SECTOR = 'SECTOR',
  COMPANY = 'COMPANY',
  BUSINESS_UNIT = 'BUSINESS_UNIT',
  DEPARTMENT = 'DEPARTMENT',
  PR_TYPE = 'PR_TYPE',
  PO_TYPE = 'PO_TYPE',
  COST_CENTER = 'COST_CENTER',
  BUDGET_CODE = 'BUDGET_CODE',
  VALUE_PR = 'VALUE_PR',
  VALUE_PO = 'VALUE_PO',
  VALUE_BUDGET = 'VALUE_BUDGET',
  BUDGET_OVERRUN = 'BUDGET_OVERRUN',
  CHECK_BUDGET = 'CHECK_BUDGET',
  DIFFERENCE_AMOUNT = 'DIFFERENCE_AMOUNT',
  BUDGET_OVERRUN_RATE = 'BUDGET_OVERRUN_RATE',
  PROCESS_TYPE = 'PROCESS_TYPE',
  PLANT = 'PLANT',
  FUNCTION_UNIT = 'FUNCTION_UNIT',
  DIFFERENCE_AMOUNT_ALL_ITEMS = 'DIFFERENCE_AMOUNT_ALL_ITEMS',
  FIRST_BUDGET = 'FIRST_BUDGET',
}
