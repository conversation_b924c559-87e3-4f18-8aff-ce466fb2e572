import { EBudgetType } from './budget.enum';

export enum EStatusActualSpending {
  CONFIRMED = 'CONFIRMED',
  UNCONFIRMED = 'UNCONFIRMED',
  SAP_SYNC_FAIL = 'SAP_SYNC_FAIL',
}

export interface ISapEventCreate {
  code: string;
  id: string;
  eventFor: EAcutalEventFor;
  budgetType?: EBudgetType;
}

export enum EColumnImportActualSpending {
  COMPANY_CODE = 'A',
  BU_CODE = 'B',
  RECEIVER_CODE = 'C',
  RECEIVER_NAME = 'D',
  PROFIT_CENTER = 'E',
  PROFIT_CENTER_DESCRIPTION = 'F',
  PROFIT_CENTER_GROUP = 'G',
  PROFIT_CENTER_GROUP_DESCRIPTION = 'H',
  ENTRY_DATE = 'I',
  DOCUMENT_NUMBER = 'J',
  INVOICE_NUMBER = 'K',
  E_INVOICE_NUMBER = 'L',
  DOC_DATE = 'M',
  POSTING_DATE = 'N',
  DOCUMENT_TYPE = 'O',
  INVOICE_BUSINESS_TRANSACTION = 'P',
  PAYMENT_DOC = 'Q',
  PAYMENT_DATE = 'R',
  PAYMENT_BUSINESS_TRANSACTION = 'S',
  PAYMENT_DOC_TYPE = 'T',
  INVENTORY_DOC = 'U',
  INVENTORY_DOC_DATE = 'V',
  PO_SAP_ID = 'W',
  PO_ITEM = 'X',
  PO_DATE = 'Y',
  INTERNAL_ORDER = 'Z',
  INTERNAL_ORDER_NAME = 'AA',
  INTERNAL_ORDER_TYPE = 'AB',
  COST_CENTER_CODE = 'AC',
  FUNCTIONAL_AREA = 'AD',
  FUNCTIONAL_AREA_NAME = 'AE',
  SUPPLIER_CODE = 'AF',
  SUPPLIER_NAME = 'AG',
  GL_ACCOUNT = 'AH',
  TAX_CODE = 'AI',
  TAX_CODE_NAME = 'AJ',
  TAX_RATE = 'AK',
  DOC_AMOUNT = 'AL',
  DOC_PAYMENT_AMOUNT = 'AM',
  CURRENCY_CODE = 'AN',
  LOCAL_CURRENCY_AMOUNT = 'AO',
  LOCAL_CURRENCY_PAYMENT_AMOUNT = 'AP',
  LOCAL_CURRENCY_CODE = 'AQ',
  EXCHANGE_RATE = 'AR',
  DEBIT_CREDIT_INDICATOR = 'AS',
  ACCOUNT_TYPE = 'AT',
  DESCRIPTION = 'AU',
  NOTE = 'AV',
  ASSET_CODE = 'AW',
}

export enum EExportActualSpendingOpex {
  /// Key là column, Value là giá trị của field trong object
  A = 'companyCode',
  B = 'companyName',
  C = 'businessUnitCode',
  D = 'businessUnitName',
  E = 'businessOwnerCode',
  F = 'businessOwnerName',
  G = 'businessPlace',
  H = 'businessPlaceName',
  I = 'profitCenter',
  J = 'profitCenterDescription',
  K = 'profitCenterGroup',
  L = 'profitCenterGroupDescription',
  M = 'entryDate',
  N = 'documentNumber',
  O = 'invoiceNumber',
  P = 'eInvoiceNumber',
  Q = 'docDate',
  R = 'postingDate',
  S = 'documentType',
  T = 'invocieBusinessTransaction',
  U = 'inventoryDoc',
  V = 'inventoryDocDate',
  W = 'poSapId',
  X = 'poItem',
  Y = 'poDate',
  Z = 'functionalArea',
  AA = 'functionalAreaName',
  AB = 'costCenterCode',
  AC = 'costCenterName',
  AD = 'supplierCode',
  AE = 'supplierName',
  AF = 'glAccount',
  AG = 'taxCode',
  AH = 'taxCodeName',
  AI = 'taxRate',
  AJ = 'docAmount',
  AK = 'currencyCode',
  AL = 'localCurrencyAmount',
  AM = 'localCurrencyCode',
  AN = 'exchangeRate',
  AO = 'debitCreditInd',
  AP = 'accountType',
  AQ = 'description',
  AR = 'note',
  AS = 'status',
  AT = 'assetCode',
}

export enum EExportActualSpendingCapex {
  /// Key là column, Value là giá trị của field trong object
  A = 'companyCode',
  B = 'companyName',
  C = 'businessUnitCode',
  D = 'businessUnitName',
  E = 'businessOwnerCode',
  F = 'businessOwnerName',
  G = 'businessPlace',
  H = 'businessPlaceName',
  I = 'profitCenter',
  J = 'profitCenterDescription',
  K = 'profitCenterGroup',
  L = 'profitCenterGroupDescription',
  M = 'entryDate',
  N = 'documentNumber',
  O = 'invoiceNumber',
  P = 'eInvoiceNumber',
  Q = 'docDate',
  R = 'postingDate',
  S = 'documentType',
  T = 'invocieBusinessTransaction',
  U = 'paymentDoc',
  V = 'paymentDate',
  W = 'paymentBusinessTransaction',
  X = 'payementDocType',
  Y = 'inventoryDoc',
  Z = 'inventoryDocDate',
  AA = 'poSapId',
  AB = 'poItem',
  AC = 'poDate',
  AD = 'internalOrder',
  AE = 'internalOrderName',
  AF = 'internalOrderType',
  AG = 'costCenterCode',
  AH = 'costCenterName',
  AI = 'supplierCode',
  AJ = 'supplierName',
  AK = 'glAccount',
  AL = 'taxCode',
  AM = 'taxCodeName',
  AN = 'taxRate',
  AO = 'docAmount',
  AP = 'docPaymentAmount',
  AQ = 'currencyCode',
  AR = 'localCurrencyAmount',
  AS = 'localCurrencyPaymentAmount',
  AT = 'localCurrencyCode',
  AU = 'exchangeRate',
  AV = 'debitCreditInd',
  AW = 'accountType',
  AX = 'description',
  AY = 'note',
  AZ = 'status',
  BA = 'assetCode',
}

export enum EActualType {
  INVOICE_PAYMENT_DAILY = 'INVOICE_PAYMENT_DAILY',
  ASSET_MONTHLY = 'ASSET_MONTHLY',
  FUNCTIONALAREA_UPDATE = 'FUNCTIONALAREA_UPDATE',
}

export enum EAcutalEventFor {
  COST_CENTER = 'COST_CENTER',
  COMPANY = 'COMPANY',
  BU = 'BU',
  SUPPLIER = 'SUPPLIER',
  CURRENCY = 'CURRENCY',
  BUDGET_CODE = 'BUDGET_CODE',
}
