export enum Status {
  Temporary = 'Temporary', // Saved but not sent
  Pending = 'Pending', // Already sent for approval
  InProgress = 'In-Progress', // Approval levels are in progress
  Approved = 'Approved', // Data has been approved
  Rejected = 'Rejected', // Data has been rejected
  Rechecked = 'Rechecked', // Data has been returned to creator (for rechecking)
  Cancel = 'Cancel', // Data has been final approved but has been canceled,
  WaitingProcess = 'Waiting to process',
  Draft = 'Draft',
  Closed = 'Closed',
}

export enum State {
  Pending = 'Pending',
  Completed = 'Completed',
  PartialCompleted = 'Partial-Completed',
}

export enum Priority {
  Urgent = 'Urgent',
  Important = 'Important',
  Normal = 'Normal',
}
