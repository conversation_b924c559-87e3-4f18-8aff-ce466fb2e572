export enum EPurchasingGroupStatus {
  ACTIVE = 'ACTIVE', //<PERSON><PERSON>ch hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EPurchasingDepartmentStatus {
  ACTIVE = 'ACTIVE', //<PERSON><PERSON>ch hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EPurchaseRequestStatus {
  ACTIVE = 'ACTIVE', //K<PERSON>ch hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EPurchaseOrderStatus {
  ACTIVE = 'ACTIVE', //K<PERSON>ch hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EPurchaseOrderTypeForm {
  NORMAL = 'NORMAL', //Bình thường
  BUY_TOOLS = 'BUY_TOOLS', //Mua công cụ
}

export enum EPurchaseRequestTypeForm {
  NORMAL = 'NORMAL', //<PERSON><PERSON><PERSON> thường
  BUY_TOOLS = 'BUY_TOOLS', //<PERSON><PERSON> công cụ
}
