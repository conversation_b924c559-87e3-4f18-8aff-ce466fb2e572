export enum Status {
  Temporary = 'Temporary', // Saved but not sent
  Pending = 'Pending', // Already sent for approval
  InProgress = 'In-Progress', // Approval levels are in progress
  Approved = 'Approved', // Data has been approved
  Rejected = 'Rejected', // Data has been rejected
  Rechecked = 'Rechecked', // Data has been returned to creator (for rechecking)
  Cancel = 'Cancel', // Data has been final approved but has been canceled
  WaitingProcess = 'Waiting to process',
  Draft = 'Draft',
  Closed = 'Closed',
}

export enum EDisplayStatus {
  Draft = 'Draft', // Lưu tạm
  Pending = 'Pending',
  InProgress = 'In-Progress', // Chờ duyệt (khi user nhấn <PERSON>ửi)
  WaitingAccountantProcess = 'Waiting accountant to process', // Chờ kế toán duyệt (khi cấp 1 nhấn duyệt và trong quy trình duyệt có cấp kế toán)
  WaitingManagerProcess = 'Waiting manager to process', // Chờ trưởng khối duyệt (khi cấp 1 nhấn duyệt và trong quy trình không có kế toán, hoặc khi cấp 2 nhấn duyệt và trong quy trình có kế toán)
  Approved = 'Approved', // Duyệt (khi cấp cuối nhấn duyệt)
  Cancel = 'Cancel', // Hủy
  Rejected = 'Rejected', // Từ chối
  Closed = 'Closed',
}

export enum State {
  Pending = 'Pending',
  Completed = 'Completed',
  PartialCompleted = 'Partial-Completed',
}

export enum Priority {
  Urgent = 'Urgent',
  Important = 'Important',
  Normal = 'Normal',
}

export enum EPaymentMethod {
  OFFER = 'OFFER', //'Chào giá'
  COMPETITIVE_QUOTATION = 'COMPETITIVE_QUOTATION', //Báo giá cạnh tranh'
  OPEN_BIDDING = 'OPEN_BIDDING', //Đấu thầu mở rộng'
  SELECTIVE_BIDDING = 'SELECTIVE_BIDDING', //Đấu thầu có chọn lọc'
  DIRECT_APPOINTMENT = 'DIRECT_APPOINTMENT', //Chỉ định thầu'
}
