export enum EMaterialTypeStatus {
  ACTIVE = 'ACTIVE', //<PERSON><PERSON>ch hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EMaterialGroupStatus {
  ACTIVE = 'ACTIVE', //Kích hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EMaterialStatus {
  ACTIVE = 'ACTIVE', //Kích hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EMaterialThroughPurchasing {
  YES_THROUGH_PURCHASING = 'YES_THROUGH_PURCHASING', //Có qua thu mua
  NO_THROUGH_PURCHASING = 'NO_THROUGH_PURCHASING', //Không qua thu mua
}

export enum EMaterialCheckBudget {
  YES_CHECK_BUDGET = 'YES_CHECK_BUDGET', //<PERSON><PERSON> kiểm tra ngân sách
  NO_CHECK_BUDGET = 'NO_CHECK_BUDGET', //Không kiểm tra ngân sách
  OPTIONAL = 'OPTIONAL', //Tùy chọn
}

export enum EColumnImportMaterial {
  CODE = 'A',
  CODE_SAP = 'B',
  NAME = 'C',
  SECTOR_CODE = 'D',
  COMPANY_CODE = 'E',
  BUSINESS_UNIT_CODE = 'F',
  FUNCTION_UNIT_CODE = 'G',
  MATERIAL_TYPE_CODE = 'H',
  MATERIAL_GROUP_CODE = 'I',
  PURCHASING_GROUP_CODE = 'J',
  PURCHASING_DEPARTMENT_CODE = 'K',
  UNIT = 'L',
  DESCRIPTION = 'M',
  STATUS = 'N',
  DELETED = 'O',
}

export enum EColumnImportMaterialGroup {
  CODE = 'A',
  NAME = 'B',
  PROCESS_TYPE_CODE = 'C',
  BUSINESS_OWNER_CODE = 'D',
  DESCRIPTION = 'E',
  STATUS = 'F',
}

export enum EColumnImportMaterialType {
  CODE = 'A',
  NAME = 'B',
  DESCRIPTION = 'C',
  STATUS = 'D',
}
