import { ApprovalProcessDetailModel } from '../../model/approval-process-detail.model';

export enum EApprover {
  LEVEL_1 = 'LEVEL_1',
  LEVEL_2 = 'LEVEL_2',
  LEVEL_3 = 'LEVEL_3',
  LEVEL_4 = 'LEVEL_4',
  ASSIGN = 'ASSIGN',
  POSITION = 'POSITION',
  PR_APPROVER_1 = 'PR_APPROVER_1',
  PR_APPROVER_2 = 'PR_APPROVER_2',
  PR_APPROVER_3 = 'PR_APPROVER_3',
  PR_APPROVER_4 = 'PR_APPROVER_4',
  PR_APPROVER_5 = 'PR_APPROVER_5',
  PR_APPROVER_6 = 'PR_APPROVER_6',
  PR_APPROVER_7 = 'PR_APPROVER_7',
  PO_APPROVER_1 = 'PO_APPROVER_1',
  PO_APPROVER_2 = 'PO_APPROVER_2',
  PO_APPROVER_3 = 'PO_APPROVER_3',
}

// Dùng Map để lookup nhanh hơn
export const APPROVER_KEYS = new Map<
  EApprover,
  keyof ApprovalProcessDetailModel
>([
  [EApprover.PR_APPROVER_1, 'prApprover1'],
  [EApprover.PR_APPROVER_2, 'prApprover2'],
  [EApprover.PR_APPROVER_3, 'prApprover3'],
  [EApprover.PR_APPROVER_4, 'prApprover4'],
  [EApprover.PR_APPROVER_5, 'prApprover5'],
  [EApprover.PR_APPROVER_6, 'prApprover6'],
  [EApprover.PR_APPROVER_7, 'prApprover7'],
]);

// Dùng Map để lookup nhanh hơn
export const PO_APPROVER_KEYS = new Map<
  EApprover,
  keyof ApprovalProcessDetailModel
>([
  [EApprover.PR_APPROVER_1, 'prApprover1'],
  [EApprover.PR_APPROVER_2, 'prApprover2'],
  [EApprover.PR_APPROVER_3, 'prApprover3'],
  [EApprover.PR_APPROVER_4, 'prApprover4'],
  [EApprover.PR_APPROVER_5, 'prApprover5'],
  [EApprover.PR_APPROVER_6, 'prApprover6'],
  [EApprover.PR_APPROVER_7, 'prApprover7'],
  [EApprover.PO_APPROVER_1, 'poApprover1'],
  [EApprover.PO_APPROVER_2, 'poApprover2'],
  [EApprover.PO_APPROVER_3, 'poApprover3'],
]);

export const levelMap = {
  [EApprover.LEVEL_1]: 1,
  [EApprover.LEVEL_2]: 2,
  [EApprover.LEVEL_3]: 3,
  [EApprover.LEVEL_4]: 4,
};
