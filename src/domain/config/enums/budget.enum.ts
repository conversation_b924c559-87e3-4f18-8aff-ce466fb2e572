export enum EBudgetStatus {
  ACTIVE = 'ACTIVE', //<PERSON><PERSON><PERSON> hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EBudgetType {
  OPEX = 'OPEX',
  CAPEX = 'CAPEX',
}

export enum EBudgetCreateType {
  NEW = 'NEW', //Ngân sách mới
  INCREASE = 'INCREASE', //Tăng ngân sách
  DECREASE = 'DECREASE', //Giảm ngân sách
}

export enum EColumnImportBudgetOpex {
  CURRENCY_UNIT_CODE = 'A',
  BUDGET_CODE_CODE = 'B',
  COSTCENTER_SUBACCOUNT_CODE = 'C',
  OPERATIONS = 'D',
  TOTAL_VALUE = 'E',
  FORM = 'F',
  EFFECTIVE_START_DATE = 'G',
  EFFECTIVE_END_DATE = 'H',
  NOTE = 'I',
  IS_LOCK = 'J',
}

export enum EColumnImportBudgetCapex {
  CURRENCY_UNIT_CODE = 'A',
  COSTCENTER_SUBACCOUNT_CODE = 'B',
  CLASSIFY = 'C',
  INVESTMENT = 'D',
  QUANTITY = 'E',
  PRICE = 'F',
  TRANSPORTATION_COSTS = 'G',
  TOTAL_VALUE = 'H',
  START_DATE = 'I',
  EXPECTED_ACCEPTANCE_TIME = 'J',
  USE_TIME = 'K',
  PRIORITY = 'L',
  INVESTMENT_PURPOSE = 'M',
  NOTE = 'N',
  BUDGET_CODE_CODE = 'O',
  NOTE2 = 'P',
  KEY_PROJECT = 'Q',
  EFFECTIVE_START_DATE = 'R',
  EFFECTIVE_END_DATE = 'S',
  IS_LOCK = 'T',
}

export enum EColumnImportAdjustBudget {
  CREATE_TYPE = 'A',
  BUDGET_CODE_CODE = 'B',
  COSTCENTER_SUBACCOUNT_CODE = 'C',
  EFFECTIVE_START_DATE = 'D',
  EFFECTIVE_END_DATE = 'E',
  OPERATION_INVESTMENT = 'F',
  TOTAL_VALUE = 'G',
  CURRENCY_CODE = 'H',
  NOTE = 'I',
  IS_LOCK = 'J',
}

export enum EFilterCurrencyReportBudget {
  VND = 'VND',
  ORIGINAL = 'ORIGINAL',
}

export enum EColumnImportBudgetTransfer {
  CURRENCY_CODE = 'A',
  BUDGET_CODE_CODE_1 = 'B',
  COST_CENTER_CODE_1 = 'C',
  BUDGET_CODE_CODE_2 = 'D',
  COST_CENTER_CODE_2 = 'E',
  OPERATION_INVESTMENT_1 = 'F',
  OPERATION_INVESTMENT_2 = 'G',
  EFFECTIVE_START_DATE = 'H',
  EFFECTIVE_END_DATE = 'I',
  TOTAL_VALUE = 'J',
  NOTE = 'L',
}
