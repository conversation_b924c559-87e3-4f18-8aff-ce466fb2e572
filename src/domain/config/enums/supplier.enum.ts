export enum ESupplierType {
  POTENTIAL = 'POTENTIAL',
  OFFICIAL = 'OFFICIAL',
  WALK_IN = 'WALK_IN',
}

export enum ESupplierStatus {
  ACTIVE = 'ACTIVE', //<PERSON><PERSON>ch hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EColumnImportSupplier {
  CODE = 'B',
  CODE_SAP = 'C',
  NAME = 'D',
  SECTOR_CODE = 'E',
  TYPE = 'F',
  PHONE = 'G',
  FAX = 'H',
  ADDRESS = 'I',
  BUSINESS_LICENSE_NUMBER = 'J',
  CONTACT_PERSON = 'K',
  TRANSACTION_CURRENCY = 'L',
  PAYMENT_METHOD = 'M',
  NOTE = 'N',
  STATUS = 'O',
  DELETED = 'P',
}
