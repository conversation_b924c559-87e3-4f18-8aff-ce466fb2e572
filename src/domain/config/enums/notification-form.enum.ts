export enum ENotificationFormStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum ENotificationFormType {
  ///PR
  REQUEST_APPROVE_PR = 'REQUEST_APPROVE_PR', //Yêu cầu duyệt PR
  APPROVED_PR = 'APPROVED_PR', //PR đã được duyệt
  REJECTED_PR = 'REJECTED_PR', //PR bị từ chối
  REQUEST_EDIT_PR = 'REQUEST_EDIT_PR', //Yêu cầu điều chỉnh PR
  CANCELLED_PR = 'CANCELLED_PR', //PR đã bị hủy
  ///PO
  REQUEST_APPROVE_PO = 'REQUEST_APPROVE_PO', //Yêu cầu duyệt POO
  APPROVED_PO = 'APPROVED_PO', //PO đã được duyệt
  REJECTED_PO = 'REJECTED_PO', //PO bị từ chối
  REQUEST_EDIT_PO = 'REQUEST_EDIT_PO', //Yêu cầu điều chỉnh PO
  CANCELLED_PO = 'CANCELLED_PO', //PO đã bị hủy
}

export const getNameOfNotificationFormType = (type: ENotificationFormType) => {
  switch (type) {
    case ENotificationFormType.REQUEST_APPROVE_PR:
      return 'Yêu cầu duyệt PR';
    case ENotificationFormType.APPROVED_PR:
      return 'Thông báo PR đã được duyệt';
    case ENotificationFormType.REJECTED_PR:
      return 'Thông báo PR bị từ chối';
    case ENotificationFormType.REQUEST_EDIT_PR:
      return 'Yêu cầu điều chỉnh PR';
    case ENotificationFormType.CANCELLED_PR:
      return 'Thông báo PR đã bị hủy';
    case ENotificationFormType.REQUEST_APPROVE_PO:
      return 'Yêu cầu duyệt PO';
    case ENotificationFormType.APPROVED_PO:
      return 'Thông báo PO đã được duyệt';
    case ENotificationFormType.REJECTED_PO:
      return 'Thông báo PO bị từ chối';
    case ENotificationFormType.REQUEST_EDIT_PO:
      return 'Yêu cầu điều chỉnh PO';
    case ENotificationFormType.CANCELLED_PO:
      return 'Thông báo PO đã bị hủy';
    default:
      return '';
  }
};
