export enum EInventoryStandardStatus {
  ACTIVE = 'ACTIVE', //<PERSON><PERSON><PERSON> hoạt
  IN_ACTIVE = 'IN_ACTIVE', //Vô hiệu hóa
}

export enum EInventoryStandardColumnName {
  MATERIAL_CODE = 'Mã vật tư',
  MATERIAL_NAME = 'Tên vật tư',
  UNIT = 'Đơn vị tính',
  SECTOR = 'Mảng',
  COMPANY = 'Công ty',
  BUSINESS_UNIT = 'Đơn vị kinh doanh',
  DEPARTMENT = 'Phòng ban',
  STANDARD_QUANTITY = 'SL Định mức',
  INVENTORY_QUANTITY = 'SL Tồn kho',
}

export enum EInventoryStandardKeyName {
  MATERIAL_CODE = 'materialCode',
  MATERIAL_NAME = 'materialName',
  UNIT = 'unit',
  SECTOR = 'sector',
  COMPANY = 'company',
  BUSINESS_UNIT = 'businessUnit',
  DEPARTMENT = 'department',
  STANDARD_QUANTITY = 'standardQuantity',
  INVENTORY_QUANTITY = 'inventoryQuantity',
}

export const colunmnHeaderInventoryStandard = [
  {
    header: EInventoryStandardColumnName.MATERIAL_CODE,
    key: EInventoryStandardKeyName.MATERIAL_CODE,
  },
  {
    header: EInventoryStandardColumnName.MATERIAL_NAME,
    key: EInventoryStandardKeyName.MATERIAL_NAME,
  },
  {
    header: EInventoryStandardColumnName.UNIT,
    key: EInventoryStandardKeyName.UNIT,
  },
  {
    header: EInventoryStandardColumnName.SECTOR,
    key: EInventoryStandardKeyName.SECTOR,
  },
  {
    header: EInventoryStandardColumnName.COMPANY,
    key: EInventoryStandardKeyName.COMPANY,
  },
  {
    header: EInventoryStandardColumnName.BUSINESS_UNIT,
    key: EInventoryStandardKeyName.BUSINESS_UNIT,
  },
  {
    header: EInventoryStandardColumnName.DEPARTMENT,
    key: EInventoryStandardKeyName.DEPARTMENT,
  },
  {
    header: EInventoryStandardColumnName.STANDARD_QUANTITY,
    key: EInventoryStandardKeyName.STANDARD_QUANTITY,
  },
  {
    header: EInventoryStandardColumnName.INVENTORY_QUANTITY,
    key: EInventoryStandardKeyName.INVENTORY_QUANTITY,
  },
];

export enum EColumnImportInventoryStandard {
  MATERIAL_CODE = 'A',
  SECTOR_CODE = 'B',
  COMPANY_CODE = 'C',
  BUSINESS_UNIT_CODE = 'D',
  DEPARTMENT_CODE = 'E',
  STANDARD_QUANTITY = 'F',
  INVENTORY_QUANTITY = 'G',
  STATUS = 'H',
}
