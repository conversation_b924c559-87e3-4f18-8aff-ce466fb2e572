import { BaseModel } from './base.model';

export class BudgetInvestmentModel extends BaseModel {
  investment: string; //Hạng mục đầu tư

  quantity: number; //Số lượng

  price?: number; //Đơn giá

  transportationCosts?: number; //Tổng chi phí vận chuyển

  budgetCapexId: string; //Ngân sách Capex
  constructor(partial: Partial<BudgetInvestmentModel>) {
    super();
    Object.assign(this, partial);
  }
}
