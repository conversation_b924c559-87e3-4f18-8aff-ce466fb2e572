import { ECurrencyUnitStatus } from '../config/enums/currency-unit.enum';
import { BaseModel } from './base.model';
import { BudgetModel } from './budget.model';
import { CurrencyUnitExchangeModel } from './currency-unit-exchange.model';
import { PriceInformationRecordModel } from './price_information_record.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { SapPurchaseOrderItemModel } from './sap_purchase_order_item.model';

export class CurrencyUnitModel extends BaseModel {
  name: string; //Tiền tệ

  currencyCode: string; //Mã tiền tệ

  description?: string; //Mô tả

  status: ECurrencyUnitStatus; //Trạng thái

  searchValue?: string;

  currencyExchanges?: CurrencyUnitExchangeModel[];

  budgets?: BudgetModel[];

  purchaseOrders?: PurchaseOrderModel[];

  purchaseOrderDetails?: PurchaseOrderDetailModel[];

  sapPurchaseOrderItems?: SapPurchaseOrderItemModel[];
  pirs?: PriceInformationRecordModel[];
  constructor(partial: Partial<CurrencyUnitModel>) {
    super();
    Object.assign(this, partial);
  }
}
