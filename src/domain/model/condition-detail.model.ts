import { EComparisonType } from '../config/enums/comparision-type.enum';
import { EConditionType } from '../config/enums/condition-type.enum';
import { BaseModel } from './base.model';
import { BudgetCodeModel } from './budget-code.model';
import { BusinessUnitModel } from './business-unit.model';
import { CompanyModel } from './company.model';
import { ConditionModel } from './condition.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { DepartmentModel } from './department.model';
import { FunctionUnitModel } from './function-unit.model';
import { PlantModel } from './plant.model';
import { ProcessTypeModel } from './process-type.model';
import { PurchaseOrderTypeModel } from './purchase-order-type.model';
import { PurchaseRequestTypeModel } from './purchase-request-type.model';
import { SectorModel } from './sector.model';

export class ConditionDetailModel extends BaseModel {
  type: EConditionType;
  comparisonType: EComparisonType;

  sectors?: SectorModel[];
  companies?: CompanyModel[];
  businessUnits?: BusinessUnitModel[];
  departments?: DepartmentModel[];
  costCenters?: CostcenterSubaccountModel[];
  budgetCodes?: BudgetCodeModel[];
  prTypes?: PurchaseRequestTypeModel[];
  poTypes?: PurchaseOrderTypeModel[];
  processTypes?: ProcessTypeModel[];
  plants?: PlantModel[];
  functionUnits?: FunctionUnitModel[];

  valuePR?: number;
  valuePO?: number;
  valueBudget?: number;
  budgetOverrun?: number;
  differenceAmount?: number;
  budgetOverrunRate?: number;
  differenceAmountAllItems?: number;

  conditions?: ConditionModel;
  conditionsId?: string;

  constructor(partial: Partial<ConditionDetailModel>) {
    super();
    Object.assign(this, partial);
  }
}
