import { EAccountAssignment } from '../config/enums/account-assignment.enum';
import { BaseApproveModel } from './base-approve.model';
import { BusinessUnitModel } from './business-unit.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { CurrencyUnitModel } from './currency-unit.model';
import { MaterialGroupModel } from './material-group.model';
import { MaterialModel } from './material.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { SapPurchaseOrderModel } from './sap_purchase_order.model';
import { TaxCodeModel } from './tax-code.model';

export class SapPurchaseOrderItemModel extends BaseApproveModel {
  eprId?: number; // gen rule SAP
  materialId?: string; // Material id
  materialCode?: string;
  materialName?: string; //Mô tả sản phẩm cần mua đối với mua PO dich vụ không theo mã
  quantity?: number;
  unit?: string; //ĐVT
  deliveryDate?: Date;
  price?: number;
  accountAssignment?: EAccountAssignment; //AAG - Loại đối tượng nhận hàng
  glAccount?: string; //G/L Account Number
  costCenterId?: string;
  costCenterCode?: string;
  asset?: string; //Tài sản
  internalOrder?: string;
  wbs?: string;
  functionalArea?: string;
  materialGroupId?: string;
  materialGroupName?: string;
  materialGroupCode?: string;
  currencyId?: string;
  currencyCode?: string;
  buId?: string; //BU Id-Plant SAP
  buCode?: string; //Plant SAP
  sapPurchaseOrder?: SapPurchaseOrderModel;
  sapPurchaseOrderId?: number;
  purchaseOrderDetailId?: number;
  taxCodeId?: string;
  taxCodeCode?: string;

  messageType?: string;
  message?: string;

  material?: MaterialModel;
  costCenter?: CostcenterSubaccountModel;
  materialGroup?: MaterialGroupModel;
  currency?: CurrencyUnitModel;
  businessUnit?: BusinessUnitModel;
  purchaseOrderDetail?: PurchaseOrderDetailModel;
  taxCode?: TaxCodeModel;
  constructor(partial: Partial<SapPurchaseOrderItemModel>) {
    super();
    Object.assign(this, partial);
  }
}
