import { BaseModel } from './base.model';
import { BusinessUnitModel } from './business-unit.model';
import { DepartmentModel } from './department.model';
import { FunctionUnitModel } from './function-unit.model';
import { PurchaseRequestTypeModel } from './purchase-request-type.model';
import { SectorModel } from './sector.model';
import { StaffModel } from './staff.model';

export class ApprovalProcessDetailModel extends BaseModel {
  //Tên
  name: string;
  //Ngành
  sectorId: string;
  sector?: SectorModel;
  //Khối chức năng
  functionUnitId: string;
  functionUnit?: FunctionUnitModel;
  //Plant
  departmentId: string;
  department?: DepartmentModel;
  //Đơn vị kinh doanh
  businessUnitId: string;
  businessUnit?: BusinessUnitModel;
  //Người tạo PR
  prCreatedById: string;
  prCreatedBy?: StaffModel;
  //Người tạo PO
  poCreatedById: string;
  poCreatedBy?: StaffModel;
  //Người duyệt PR 1
  prApprover1Id: string;
  prApprover1?: StaffModel;
  //Người duyệt PR 2
  prApprover2Id?: string;
  prApprover2?: StaffModel;
  //Người duyệt PR 3
  prApprover3Id?: string;
  prApprover3?: StaffModel;
  //Người duyệt PR 4
  prApprover4Id?: string;
  prApprover4?: StaffModel;
  //Người duyệt PR 5
  prApprover5Id?: string;
  prApprover5?: StaffModel;
  //Người duyệt PR 6
  prApprover6Id?: string;
  prApprover6?: StaffModel;
  //Người duyệt PR 7
  prApprover7Id?: string;
  prApprover7?: StaffModel;
  //Người duyệt PO 1
  poApprover1Id: string;
  poApprover1?: StaffModel;
  //Người duyệt PO 2
  poApprover2Id?: string;
  poApprover2?: StaffModel;
  //Người duyệt PO 3
  poApprover3Id?: string;
  poApprover3?: StaffModel;
  //Loại PR
  prTypes?: PurchaseRequestTypeModel[];
  constructor(partial: Partial<ApprovalProcessDetailModel>) {
    super();
    Object.assign(this, partial);
  }
}
