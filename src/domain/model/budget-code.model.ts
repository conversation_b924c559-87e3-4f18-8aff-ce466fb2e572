import { EBudgetCodeStatus } from '../config/enums/budget-code.enum';
import { EBudgetType } from '../config/enums/budget.enum';
import { BaseModel } from './base.model';
import { BudgetModel } from './budget.model';
import { BusinessOwnerModel } from './business-owner.model';
import { CostModel } from './cost.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { PurchaseRequestModel } from './purchase_request.model';
import { PurchaseRequestDetailModel } from './purchase_request_detail.model';

export class BudgetCodeModel extends BaseModel {
  name: string; //Tên
  code: string; //Mã
  description?: string;
  status: EBudgetCodeStatus;
  searchValue?: string;
  budgets?: BudgetModel[];
  businessOwnerId?: string;
  businessOwner?: BusinessOwnerModel;

  budgetType: EBudgetType;

  costId?: string;
  cost?: CostModel;

  internalOrder?: string;

  purchaseRequests?: PurchaseRequestModel[];

  purchaseRequestDetails?: PurchaseRequestDetailModel[];

  purchaseOrders?: PurchaseOrderModel[];

  purchaseOrderDetails?: PurchaseOrderDetailModel[];
  constructor(partial: Partial<BudgetCodeModel>) {
    super();
    Object.assign(this, partial);
  }
}
