import { BudgetEntity } from '../../infrastructure/entities/budget.entity';
import { BaseModel } from './base.model';
import { BudgetInvestmentModel } from './budget-investment.model';

export class BudgetCapexModel extends BaseModel {
  useTime?: string; //Thời gian sử dụng

  startDate?: Date; //Thời điểm bắt đầu

  expectedAcceptanceTime?: Date; //Thời điểm nghiệm thu dự kiến

  classify?: string; //Phân loại

  priority?: string; //Mức độ ưu tiên

  investmentPurpose?: string; //Mục đích đầu tư

  files?: string[]; //File đính kèm

  note2?: string;

  keyProject?: string;

  budget?: BudgetEntity;

  budgetInvestments?: BudgetInvestmentModel[];

  constructor(partial: Partial<BudgetCapexModel>) {
    super();
    Object.assign(this, partial);
  }
}
