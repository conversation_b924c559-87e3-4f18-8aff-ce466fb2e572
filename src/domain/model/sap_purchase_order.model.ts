import { BaseApproveModel } from './base-approve.model';
import { CompanyModel } from './company.model';
import { PurchaseOrderTypeModel } from './purchase-order-type.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchasingDepartmentModel } from './purchasing-department.model';
import { PurchasingGroupModel } from './purchasing-group.model';
import { SapPurchaseOrderItemModel } from './sap_purchase_order_item.model';
import { SupplierModel } from './supplier.model';

export class SapPurchaseOrderModel extends BaseApproveModel {
  poId?: number; // PO EP id - chua can link po
  companyId?: string; // Company id in BU Id
  companyCode?: string;
  poTypeId?: string; //PO Type
  poTypeCode?: string;
  purchasingDepartmentId?: string; //Purchasing organization
  purchasingDepartmentCode?: string;
  purchasingGroupId?: string; //Purchasing Group
  purchasingGroupCode?: string;
  poCreatedAt?: Date; //Ngày tạo PO
  exchangeRate?: number; //Exchange rage
  currencyCode?: string;
  supplierId?: string;
  supplierCode?: string;
  supplierInfo?: string | object;

  items?: SapPurchaseOrderItemModel[];

  messageType?: string;
  message?: string;
  ebeln?: string;
  status?: string;

  purchaseOrder?: PurchaseOrderModel;
  company?: CompanyModel;
  poType?: PurchaseOrderTypeModel;
  purchasingDepartment?: PurchasingDepartmentModel;
  purchasingGroup?: PurchasingGroupModel;
  supplier?: SupplierModel;

  constructor(partial: Partial<SapPurchaseOrderModel>) {
    super();
    Object.assign(this, partial);
  }
}
