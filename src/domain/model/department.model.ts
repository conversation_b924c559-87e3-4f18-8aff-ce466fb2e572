import { EDepartmentStatus } from '../config/enums/department.enum';
import { BaseModel } from './base.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { InventoryStandardModel } from './inventory-standard.model';
import { MaterialModel } from './material.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';

export class DepartmentModel extends BaseModel {
  code: string;
  name: string;
  description?: string;
  status: EDepartmentStatus;
  searchValue: string;
  costcenterSubaccounts?: CostcenterSubaccountModel[];
  inventoryStandards?: InventoryStandardModel[];
  materials?: MaterialModel[];
  purchaseRequests?: PurchaseRequestModel[];
  purchaseOrders?: PurchaseOrderModel[];
  constructor(partial: Partial<DepartmentModel>) {
    super();
    Object.assign(this, partial);
  }
}
