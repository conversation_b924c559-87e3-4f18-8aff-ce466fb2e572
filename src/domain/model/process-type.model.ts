import { EStatus } from '../config/enums/status.enum';
import { BaseModel } from './base.model';
import { ConditionDetailModel } from './condition-detail.model';
import { MaterialGroupModel } from './material-group.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';

export class ProcessTypeModel extends BaseModel {
  code: string;
  name: string;
  hasInventoryStandard: boolean;
  description?: string;
  searchValue?: string;
  status?: EStatus; //Trạng thái
  materialGroups?: MaterialGroupModel[];
  conditionDetails?: ConditionDetailModel[];
  purchaseRequests?: PurchaseRequestModel[];
  purchaseOrders?: PurchaseOrderModel[];
  constructor(partial: Partial<ProcessTypeModel>) {
    super();
    Object.assign(this, partial);
  }
}
