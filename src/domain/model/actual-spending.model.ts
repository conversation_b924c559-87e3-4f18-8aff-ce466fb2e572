import {
  EActualType,
  EStatusActualSpending,
} from '../config/enums/actual-spending.enum';
import { BaseModel } from './base.model';
import { BudgetCodeModel } from './budget-code.model';
import { BusinessUnitModel } from './business-unit.model';
import { CompanyModel } from './company.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { CurrencyUnitModel } from './currency-unit.model';
import { SupplierModel } from './supplier.model';

export class ActualSpendingModel extends BaseModel {
  // Mã thực chi sap
  sapActualId: number;
  // Mã ngân sách
  budgetCodeId?: string;
  budgetCode?: BudgetCodeModel;

  // Mã công ty
  companyId: string;
  company?: CompanyModel;

  // Cost Center
  costCenterId: string;
  costCenter?: CostcenterSubaccountModel;

  // BU
  buId: string;
  bu?: BusinessUnitModel;

  // Kỳ
  // periodMonth: number;
  // periodYear: number;
  // period?: string;

  // Năm tài chính
  // fiscalYear: number;

  // Internal Order
  internalOrder: string;

  // Internal Oder Name
  internalOrderName: string;

  // ID Nhà cung cấp
  supplierId: string;
  supplier?: SupplierModel;

  // Tên nhà cung cấp
  supplierName: string;

  // PO SAP
  poSapId: number; // approval-engine
  poSap?: any;

  // Ngày chứng từ
  docDate: Date;

  // Ngày ghi có
  postingDate: Date;

  // Ngày nhập liệu
  entryDate: Date;

  // Mã chứng từ thanh toán
  paymentDoc: string;

  // Mã E-Invoice
  eInvoiceNumber: string;

  // Mã chứng từ FI
  documentNumber: string;

  // Mã số Invoice
  invoiceNumber: string;

  // Loại giao dịch
  // transactionType: string;

  // GL Account
  glAccount: string;

  // Tax Code
  taxCode: string;

  // Tax Rate
  taxRate: number;

  // Số tiền invoice
  docAmount: number;

  // Đơn vị tiền tệ
  currencyId: string;
  currency?: CurrencyUnitModel;

  // Số tiền quy đổi
  localCurrencyAmount: number;

  // Đơn vị tiền tệ quy đổi
  localCurrencyId: string;
  localCurrency?: CurrencyUnitModel;

  // Tỷ giá quy đổi
  exchangeRate: number;

  // Mã nơi nhận
  receiverCode: string;

  // Tên nơi nhận
  receiverName: string;

  // Profit Center
  profitCenter: string;

  // Diễn giải Profit Center
  profitCenterDescription: string;

  // Nhóm Profit Center
  profitCenterGroup: string;

  // Diễn giải nhóm Profit Center
  profitCenterGroupDescription: string;

  // Tình trạng
  status: EStatusActualSpending;

  // Mã số chứng từ nhập kho
  inventoryDoc?: string;

  documentType: string;

  // Invoice Business transaction
  invocieBusinessTransaction: string;

  paymentDate: Date;

  paymentBusinessTransaction: string;

  payementDocType: string;

  inventoryDocDate: Date;

  poItem: string;

  poDate: Date;

  internalOrderType: string;

  costCenterName: string;

  functionalArea: string;

  functionalAreaName: string;

  taxCodeName: string;

  docPaymentAmount: number;

  localCurrencyPaymentAmount: number;

  debitCreditInd: string;

  accountType: string;

  description: string;

  note: string;

  businessOwnerCode?: string;

  businessOwnerName?: string;
  assetCode?: string;

  actualType?: EActualType;

  companyCode?: string;

  costCenterCode?: string;

  buCode?: string;

  supplierCode?: string;

  currencyCode?: string;

  localCurrencyCode?: string;

  constructor(partial: Partial<ActualSpendingModel>) {
    super();
    Object.assign(this, partial);
  }
}
