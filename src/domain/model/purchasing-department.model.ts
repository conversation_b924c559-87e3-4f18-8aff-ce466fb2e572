import { EPurchasingDepartmentStatus } from '../config/enums/purchasing.enum';
import { BaseModel } from './base.model';
import { MaterialModel } from './material.model';
import { PriceInformationRecordModel } from './price_information_record.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';
import { SapPurchaseOrderModel } from './sap_purchase_order.model';
import { SectorModel } from './sector.model';

export class PurchasingDepartmentModel extends BaseModel {
  code: string;

  name: string;

  description?: string;

  searchValue?: string;

  sectorId: string; //Nhóm vật tư

  status: EPurchasingDepartmentStatus; //Trạng thái

  sector?: SectorModel;
  materials?: MaterialModel[];

  purchaseRequests?: PurchaseRequestModel[];
  purchaseOrders?: PurchaseOrderModel[];
  sapPurchaseOrders?: SapPurchaseOrderModel[];
  pirs?: PriceInformationRecordModel[];
  constructor(partial: Partial<PurchasingDepartmentModel>) {
    super();
    Object.assign(this, partial);
  }
}
