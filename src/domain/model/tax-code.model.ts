import { BaseModel } from './base.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';

export class TaxCodeModel extends BaseModel {
  code: string; //Mã thuế (2 kí tự)

  description?: string; //Mô tả

  taxRate: number; //Tỷ lệ thuế

  purchaseOrderDetails?: PurchaseOrderDetailModel[];

  constructor(partial: Partial<TaxCodeModel>) {
    super();
    Object.assign(this, partial);
  }
}
