import { EMaterialTypeStatus } from '../config/enums/material.enum';
import { ESectorStatus } from '../config/enums/sector.enum';
import { BaseModel } from './base.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { MaterialModel } from './material.model';

export class MaterialTypeModel extends BaseModel {
  code: string;
  name: string;
  description?: string;
  searchValue: string;
  status?: EMaterialTypeStatus; //Trạng thái
  materials?: MaterialModel[];
  constructor(partial: Partial<MaterialTypeModel>) {
    super();
    Object.assign(this, partial);
  }
}
