import { ESectorStatus } from '../config/enums/sector.enum';
import { BaseModel } from './base.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { InventoryStandardModel } from './inventory-standard.model';
import { MaterialModel } from './material.model';
import { PlantModel } from './plant.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';
import { PurchasingDepartmentModel } from './purchasing-department.model';
import { PurchasingGroupModel } from './purchasing-group.model';
import { SupplierSectorModel } from './supplier-sector.model';

export class SectorModel extends BaseModel {
  code: string;
  name: string;
  description?: string;
  searchValue: string;
  status?: ESectorStatus; //Trạng thái
  costcenterSubaccounts?: CostcenterSubaccountModel[];
  purchasingGroups?: PurchasingGroupModel[];
  purchasingDepartments?: PurchasingDepartmentModel[];
  plants?: PlantModel[];
  materials?: MaterialModel[];
  inventoryStandards?: InventoryStandardModel[];
  supplierSectors?: SupplierSectorModel[];
  purchaseRequests?: PurchaseRequestModel[];
  purchaseOrders?: PurchaseOrderModel[];
  constructor(partial: Partial<SectorModel>) {
    super();
    Object.assign(this, partial);
  }
}
