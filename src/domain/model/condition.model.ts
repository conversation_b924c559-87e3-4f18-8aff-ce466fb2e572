import { BaseModel } from './base.model';
import { ConditionDetailModel } from './condition-detail.model';
import { ProcessConditionModel } from './process-condition.model';

export class ConditionModel extends BaseModel {
  conditionDetails?: ConditionDetailModel[];

  processCondition?: ProcessConditionModel;
  processConditionId?: string;

  constructor(partial: Partial<ConditionModel>) {
    super();
    Object.assign(this, partial);
  }
}
