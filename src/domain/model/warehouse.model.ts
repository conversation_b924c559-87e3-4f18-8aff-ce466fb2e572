import { EWarehouseStatus } from '../config/enums/warehouse.enum';
import { BaseModel } from './base.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { PurchaseRequestDetailModel } from './purchase_request_detail.model';
import { SectorModel } from './sector.model';

export class WarehouseModel extends BaseModel {
  code: string;

  name: string;

  sectors?: SectorModel[];

  description?: string;

  status?: EWarehouseStatus; //Trạng thái

  purchaseRequestDetails?: PurchaseRequestDetailModel[];

  purchaseOrderDetails?: PurchaseOrderDetailModel[];

  totalPrDetails?: number;
  totalPoDetails?: number;

  constructor(partial: Partial<WarehouseModel>) {
    super();
    Object.assign(this, partial);
  }
}
