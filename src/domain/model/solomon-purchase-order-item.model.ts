import { BaseModel } from './base.model';
import { BudgetCodeModel } from './budget-code.model';
import { MaterialModel } from './material.model';
import { MeasureModel } from './measure.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { SolomonPurchaseOrderModel } from './solomon-purchase-order.model';
import { TaxCodeModel } from './tax-code.model';
import { WarehouseModel } from './warehouse.model';

export class SolomonPurchaseOrderItemModel extends BaseModel {
  solomonPurchaseOrderId?: string;

  solomonPurchaseOrder?: SolomonPurchaseOrderModel;

  materialId?: string; //Mã vật tư

  material?: MaterialModel;

  materialCode?: string;

  materialName?: string; //Mô tả sản phẩm cần mua đối với mua PO dich vụ không theo mã

  warehouseId?: string; //Mã kho

  warehouse?: WarehouseModel;

  warehouseCode?: string;

  description?: string; //Mô tả trong dòng item (note)

  quantity?: number; //Số lượng mua

  measureId?: string; //Mã đơn vị tính

  measure?: MeasureModel;

  measureCode?: string; //Mã đơn vị tính (Lưu ý: Chuyển đổi sang đơn vị của hệ thống Solomon)

  unitPrice?: number; //Đơn giá

  totalAmountVat?: number; //Tổng tiền quy đổi * VAT

  unitWeight?: number; //Mặc định = 0

  extWeight?: number; //Mặc định = 0

  deliveryTime?: Date; //Ngày giao hàng

  taxCodeId?: string; //Mã thuế

  taxCode?: TaxCodeModel;

  taxCodeCode?: string;

  cnvFact?: number; //Mặc định = 1

  budgetCodeCode?: string; //Mã ngân sách

  budgetCodeId?: string; //Mã ngân sách

  budgetCode?: BudgetCodeModel;

  purchaseOrderDetail?: PurchaseOrderDetailModel;

  purchaseOrderDetailId?: number;

  messageType?: string; //Response from SOLOMON

  message?: string;

  constructor(partial: Partial<SolomonPurchaseOrderItemModel>) {
    super();
    Object.assign(this, partial);
  }
}
