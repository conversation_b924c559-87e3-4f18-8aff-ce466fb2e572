import { EBusinessUnitStatus } from '../config/enums/business-unit.enum';
import { EFunctionUnitStatus } from '../config/enums/function-unit.enum';
import { EProfitCenterStatus } from '../config/enums/profit-center.enum';
import { BaseModel } from './base.model';
import { MaterialModel } from './material.model';

export class ProfitCenterModel extends BaseModel {
  code: string;
  description?: string;
  status: EProfitCenterStatus;
  materials?: MaterialModel[];
  constructor(partial: Partial<ProfitCenterModel>) {
    super();
    Object.assign(this, partial);
  }
}
