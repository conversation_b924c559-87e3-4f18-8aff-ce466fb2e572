import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';

export class HistoryApproveModel {
  id?: number;

  level?: number;

  role?: string;

  userId?: string;

  email?: string;

  status?: string;

  name?: string;

  reason?: string;

  createdAt?: Date;

  updatedAt?: Date;

  purchaseRequest?: PurchaseRequestModel;

  purchaseOrder?: PurchaseOrderModel;
  constructor(partial: Partial<HistoryApproveModel>) {
    Object.assign(this, partial);
  }
}
