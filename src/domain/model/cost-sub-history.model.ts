import { BaseModel } from './base.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';

export class CostSubHistoryModel extends BaseModel {
  oldData: string | object;
  newData: string | object;
  userInfo: string | object;
  costcenterSubaccountId: string; //Id cost center / sub account
  costcenterSubaccount?: CostcenterSubaccountModel;
  constructor(partial: Partial<CostSubHistoryModel>) {
    super();
    Object.assign(this, partial);
  }
}
