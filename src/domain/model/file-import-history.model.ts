import { OmitType } from '@nestjs/swagger';
import {
  EFileImportStatus,
  EFileImportType,
} from '../config/enums/file-import.enum';
import { BaseModel } from './base.model';

export class FileImportHistoryModel extends OmitType(BaseModel, ['createdBy']) {
  fileName: string;
  createdBy: string | object;
  filePath: string;
  status: EFileImportStatus;
  importType: EFileImportType;
  errors?: string | object[];
  constructor(partial: Partial<FileImportHistoryModel>) {
    super();
    Object.assign(this, partial);
  }
}
