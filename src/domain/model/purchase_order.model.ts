import {
  EDisplayStatus,
  EPaymentMethod,
  Priority,
  State,
  Status,
} from '../config/enums/purchase-order.enum';
import { ApprovalLevelModel } from './approval-level.model';
import { BudgetCodeModel } from './budget-code.model';
import { BusinessUnitModel } from './business-unit.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { CurrencyUnitModel } from './currency-unit.model';
import { DepartmentModel } from './department.model';
import { FunctionUnitModel } from './function-unit.model';
import { HistoryApproveModel } from './history-approve.model';
import { PlantModel } from './plant.model';
import { ProcessTypeModel } from './process-type.model';
import { PurchaseOrderTypeModel } from './purchase-order-type.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { PurchasingDepartmentModel } from './purchasing-department.model';
import { PurchasingGroupModel } from './purchasing-group.model';
import { SapPurchaseOrderModel } from './sap_purchase_order.model';
import { SectorModel } from './sector.model';
import { SolomonPurchaseOrderModel } from './solomon-purchase-order.model';
import { StaffModel } from './staff.model';

export class PurchaseOrderModel {
  id: number;

  sectorId: string;

  businessUnitId: string;

  isCheckBudget: boolean;

  requesterId?: string;

  typePoId: string;

  statusPo: Status;

  statePo: State;

  costCenterId?: string;

  budgetCodeId?: string;

  purchaseOrgId?: string;

  purchaseGroupId?: string;

  priority: Priority;

  reason?: string;

  total?: number;

  totalPriceVat?: number;

  purchaserId?: string;

  attachments: string[];

  refId?: string;

  accountGl?: string;

  isCreatedSap?: boolean;

  isCreatedSolomon?: boolean;

  details?: PurchaseOrderDetailModel[];

  levels?: ApprovalLevelModel[];

  history?: HistoryApproveModel[];

  createdAt: Date;

  updatedAt: Date;

  paymentMethod: EPaymentMethod;

  isAccountantApproved?: boolean;

  currencyId?: string;

  exchangeRate?: number;

  sapPos?: SapPurchaseOrderModel[];

  displayStatusPo: EDisplayStatus;

  processTypeId?: string;

  plantId?: string;

  functionUnitId?: string;

  departmentId?: string;

  sector?: SectorModel;
  businessUnit?: BusinessUnitModel;
  requester?: StaffModel;
  typePo?: PurchaseOrderTypeModel;
  costCenter?: CostcenterSubaccountModel;
  budgetCode?: BudgetCodeModel;
  purchaseOrg?: PurchasingDepartmentModel;
  purchaseGroup?: PurchasingGroupModel;
  purchaser?: StaffModel;
  currency?: CurrencyUnitModel;
  processType?: ProcessTypeModel;
  plant?: PlantModel;
  functionUnit?: FunctionUnitModel;
  department?: DepartmentModel;

  historyPr?: HistoryApproveModel[];

  solomonPo?: SolomonPurchaseOrderModel;
  constructor(partial: Partial<PurchaseOrderModel>) {
    Object.assign(this, partial);
  }
}
