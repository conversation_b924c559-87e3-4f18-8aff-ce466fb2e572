import { EProcessType } from '../config/enums/process-type.enum';
import { EStatus } from '../config/enums/status.enum';
import { ApprovalWorkflowModel } from './approval-workflow.model';
import { BaseModel } from './base.model';
import { ConditionDetailModel } from './condition-detail.model';
import { ProcessConditionModel } from './process-condition.model';

export class ProcessModel extends BaseModel {
  name: string;
  description: string;
  searchValue: string;
  parentId: string;
  allowInherit: boolean;
  path: string;
  parent?: ProcessModel | null;
  children?: ProcessModel[];

  processConditions?: ProcessConditionModel[];

  status: EStatus;
  type: EProcessType;

  //Foreign Key
  conditions?: ConditionDetailModel[];
  nlevel?: number;

  approvalWorkflows?: ApprovalWorkflowModel[];

  parentApprovalWorkflows?: ApprovalWorkflowModel[];

  constructor(partial: Partial<ProcessModel>) {
    super();
    Object.assign(this, partial);
  }
}
