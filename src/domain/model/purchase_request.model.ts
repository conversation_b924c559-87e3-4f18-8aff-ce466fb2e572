import { Priority, State, Status } from '../config/enums/purchase-request.enum';
import { ApprovalLevelModel } from './approval-level.model';
import { BudgetCodeModel } from './budget-code.model';
import { BusinessUnitModel } from './business-unit.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { DepartmentModel } from './department.model';
import { FunctionUnitModel } from './function-unit.model';
import { HistoryApproveModel } from './history-approve.model';
import { PlantModel } from './plant.model';
import { ProcessTypeModel } from './process-type.model';
import { PurchaseRequestTypeModel } from './purchase-request-type.model';
import { PurchaseRequestDetailModel } from './purchase_request_detail.model';
import { PurchasingDepartmentModel } from './purchasing-department.model';
import { PurchasingGroupModel } from './purchasing-group.model';
import { SectorModel } from './sector.model';
import { StaffModel } from './staff.model';

export class PurchaseRequestModel {
  id: number;

  sectorId: string;

  businessUnitId: string;

  isCheckBudget: boolean;

  requesterId?: string;

  typePrId: string;

  statusPr: Status;

  statePr: State;

  budgetCodeId?: string;

  costCenterId?: string;

  purchaseOrgId?: string;

  purchaseGroupId?: string;

  priority: Priority;

  reason?: string;

  isPo?: boolean;

  attachments: string[];

  refId?: string;

  accountGl?: string;

  details?: PurchaseRequestDetailModel[];

  levels?: ApprovalLevelModel[];

  history?: HistoryApproveModel[];

  createdAt?: Date;

  updatedAt?: Date;

  purchaserId?: string;

  isAccountantApproved?: boolean;

  processTypeId?: string;

  plantId?: string;

  functionUnitId?: string;

  departmentId?: string;

  sector?: SectorModel;
  businessUnit?: BusinessUnitModel;
  requester?: StaffModel;
  typePr?: PurchaseRequestTypeModel;
  budgetCode?: BudgetCodeModel;
  costCenter?: CostcenterSubaccountModel;
  purchaseOrg?: PurchasingDepartmentModel;
  purchaseGroup?: PurchasingGroupModel;
  purchaser?: StaffModel;
  processType?: ProcessTypeModel;
  plant?: PlantModel;
  functionUnit?: FunctionUnitModel;
  department?: DepartmentModel;
  constructor(partial: Partial<PurchaseRequestModel>) {
    Object.assign(this, partial);
  }
}
