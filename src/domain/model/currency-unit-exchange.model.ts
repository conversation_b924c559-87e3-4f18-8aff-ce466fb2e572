import { BaseModel } from './base.model';
import { CurrencyUnitModel } from './currency-unit.model';

export class CurrencyUnitExchangeModel extends BaseModel {
  currencyUnitId: string;

  exchangeRate: number; //Quy đổi VND

  effectiveStartDate: Date; //Thời gian có hiệu lực

  effectiveEndDate?: Date; //Thời gian hết hiệu lực

  currencyUnit?: CurrencyUnitModel;

  exchangeBudget: boolean; //Tỷ giá ngân sách
  constructor(partial: Partial<CurrencyUnitExchangeModel>) {
    super();
    Object.assign(this, partial);
  }
}
