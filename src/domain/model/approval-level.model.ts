import { ApproveType } from '../../infrastructure/entities/approval-level.entity';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';

export class ApprovalLevelModel {
  id: number;

  level: number;

  role: string;

  userId: string;

  email: string;

  isSendMail: boolean;

  isSendMailCreator: boolean;

  isAccountantApproved: boolean;

  allowSelect: boolean;

  staffApprovalWorkflowId: string;

  status: string;

  name: string;

  createdAt: Date;

  updatedAt: Date;

  approveType: ApproveType;

  purchaseRequest?: PurchaseRequestModel;

  purchaseRequestId: number;

  purchaseOrder?: PurchaseOrderModel;

  purchaseOrderId: number;
  constructor(partial: Partial<ApprovalLevelModel>) {
    Object.assign(this, partial);
  }
}
