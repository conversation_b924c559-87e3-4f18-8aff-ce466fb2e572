import { CostcenterSubaccountEntity } from '../../infrastructure/entities/costcenter-subaccount.entity';
import { ECompanyStatus } from '../config/enums/company.enum';
import { BaseModel } from './base.model';
import { BusinessUnitModel } from './business-unit.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { InventoryStandardModel } from './inventory-standard.model';
import { MaterialModel } from './material.model';
import { SapPurchaseOrderModel } from './sap_purchase_order.model';

export class CompanyModel extends BaseModel {
  code: string;
  name: string;
  description?: string;
  searchValue: string;
  status?: ECompanyStatus; //Trạng thái
  costcenterSubaccounts?: CostcenterSubaccountModel[];
  inventoryStandards?: InventoryStandardModel[];
  materials?: MaterialModel[];
  businessUnits?: BusinessUnitModel[];
  sapPurchaseOrders?: SapPurchaseOrderModel[];
  constructor(partial: Partial<CompanyModel>) {
    super();
    Object.assign(this, partial);
  }
}
