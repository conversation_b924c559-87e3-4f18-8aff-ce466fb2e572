import { EApprover } from '../config/enums/approver.enum';
import { EReturnRuleApprove } from '../config/enums/return-rule-approve.enum';
import { ApprovalWorkflowModel } from './approval-workflow.model';
import { BaseModel } from './base.model';
import { PositionModel } from './position.model';
import { StaffModel } from './staff.model';

export class StaffApprovalWorkflowModel extends BaseModel {
  receiveEmail: boolean;
  level: number;
  name: string;
  staff?: StaffModel;
  staffId?: string;
  approvalWorkflowId?: string;
  approvalWorkflow?: ApprovalWorkflowModel;
  position?: PositionModel;
  positionId?: string;
  approver: EApprover;
  returnRule: EReturnRuleApprove;
  accountantApproved: boolean;
  allowSelect: boolean;
  allowEdit: boolean;
  selectedStaffs?: StaffModel[];

  constructor(partial: Partial<StaffApprovalWorkflowModel>) {
    super();
    Object.assign(this, partial);
  }
}
