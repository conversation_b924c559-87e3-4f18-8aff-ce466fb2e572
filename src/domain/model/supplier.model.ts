import { ESupplierStatus, ESupplierType } from '../config/enums/supplier.enum';
import { BaseModel } from './base.model';
import { PriceInformationRecordModel } from './price_information_record.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { SapPurchaseOrderModel } from './sap_purchase_order.model';
import { SupplierSectorModel } from './supplier-sector.model';

export class SupplierModel extends BaseModel {
  code: string;

  name: string;

  description: string;

  type: ESupplierType; //Trạng thái

  address: string;

  phone: string;

  fax: string;

  businessLicenseNumber: string;

  taxCode: string;

  contactPerson: string;

  transactionCurrency: string;

  paymentMethod: string;

  note: string;

  searchValue: string;

  industries?: SupplierSectorModel[];

  purchaseOrderDetails?: PurchaseOrderDetailModel[];

  sapPurchaseOrders?: SapPurchaseOrderModel[];
  pirs?: PriceInformationRecordModel[];
  constructor(partial: Partial<SupplierModel>) {
    super();
    Object.assign(this, partial);
  }
}
