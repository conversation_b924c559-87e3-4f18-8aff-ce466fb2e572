import { BaseModel } from './base.model';
import { BudgetCodeModel } from './budget-code.model';
import { PurchaseOrderModel } from './purchase_order.model';

export class ReceiptSolomonModel extends BaseModel {
  poId?: number; // Mã PO

  purchaseOrder?: PurchaseOrderModel;

  receiptCreatedAt?: Date; //Thời gian phát sinh receipt

  receiptCode?: string; // Mã số receipt

  totalAmount?: number;

  budgetCodeCode?: string;

  // Mã ngân sách
  budgetCodeId?: string;

  budgetCode?: BudgetCodeModel;

  constructor(partial: Partial<ReceiptSolomonModel>) {
    super();
    Object.assign(this, partial);
  }
}
