import { EMaterialGroupStatus } from '../config/enums/material.enum';
import { BaseModel } from './base.model';
import { BusinessOwnerModel } from './business-owner.model';
import { MaterialModel } from './material.model';
import { ProcessTypeModel } from './process-type.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { PurchaseRequestDetailModel } from './purchase_request_detail.model';
import { SapPurchaseOrderItemModel } from './sap_purchase_order_item.model';

export class MaterialGroupModel extends BaseModel {
  code: string;
  name: string;
  description?: string;
  searchValue: string;
  status?: EMaterialGroupStatus; //Trạng thái
  materials?: MaterialModel[];
  processTypes?: ProcessTypeModel[];
  businessOwners?: BusinessOwnerModel[];

  purchaseRequestDetails?: PurchaseRequestDetailModel[];

  purchaseOrderDetails?: PurchaseOrderDetailModel[];

  sapPurchaseOrderItems?: SapPurchaseOrderItemModel[];
  constructor(partial: Partial<MaterialGroupModel>) {
    super();
    Object.assign(this, partial);
  }
}
