import { EBusinessOwnerStatus } from '../config/enums/business-owner.enum';
import { BaseModel } from './base.model';
import { BudgetCodeModel } from './budget-code.model';

export class BusinessOwnerModel extends BaseModel {
  code: string;
  name: string;
  description?: string;
  searchValue: string;
  budgetCodes?: BudgetCodeModel[];
  status?: EBusinessOwnerStatus;
  constructor(partial: Partial<BusinessOwnerModel>) {
    super();
    Object.assign(this, partial);
  }
}
