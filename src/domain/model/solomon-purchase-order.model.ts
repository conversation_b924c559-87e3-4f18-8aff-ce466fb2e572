import { BaseModel } from './base.model';
import { BudgetCodeModel } from './budget-code.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { SolomonPurchaseOrderItemModel } from './solomon-purchase-order-item.model';
import { SupplierModel } from './supplier.model';

export class SolomonPurchaseOrderModel extends BaseModel {
  poId: number; //Mã PO

  purchaseOrder?: PurchaseOrderModel;

  supplierId?: string; //Mã nhà cung cấp

  supplier?: SupplierModel;

  supplierCode?: string; //Mã nhà cung cấp

  supplierInfo?: string | object;

  poCreatedDate?: Date; //Ngày tạo PO

  budgetCodeCode?: string; //Mã ngân sách

  budgetCodeId?: string; //Mã ngân sách

  budgetCode?: BudgetCodeModel;

  //Chi tiết đơn hàng
  items?: SolomonPurchaseOrderItemModel[];

  messageType?: string; //Response from SOLOMON

  message?: string;

  status?: string;

  constructor(partial: Partial<SolomonPurchaseOrderModel>) {
    super();
    Object.assign(this, partial);
  }
}
