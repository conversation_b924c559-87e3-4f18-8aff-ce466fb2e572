import { CompanyEntity } from '../../infrastructure/entities/company.entity';
import { EBusinessUnitStatus } from '../config/enums/business-unit.enum';
import { BaseModel } from './base.model';
import { CompanyModel } from './company.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { InventoryStandardModel } from './inventory-standard.model';
import { MaterialModel } from './material.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';
import { SapPurchaseOrderItemModel } from './sap_purchase_order_item.model';

export class BusinessUnitModel extends BaseModel {
  code: string;
  name: string;
  description?: string;
  status: EBusinessUnitStatus;
  searchValue: string;
  costcenterSubaccounts?: CostcenterSubaccountModel[];
  inventoryStandards?: InventoryStandardModel[];
  materials?: MaterialModel[];
  company?: CompanyModel;

  purchaseRequests?: PurchaseRequestModel[];

  purchaseOrders?: PurchaseOrderModel[];

  sapPurchaseOrderItems?: SapPurchaseOrderItemModel[];
  constructor(partial: Partial<BusinessUnitModel>) {
    super();
    Object.assign(this, partial);
  }
}
