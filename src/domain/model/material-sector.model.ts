import { EMaterialStatus } from '../config/enums/material.enum';
import { BaseModel } from './base.model';
import { MaterialModel } from './material.model';
import { SectorModel } from './sector.model';

export class MaterialSectorModel extends BaseModel {
  codeSAP: string;

  status: EMaterialStatus;

  material?: MaterialModel;

  sector?: SectorModel;

  constructor(partial: Partial<MaterialSectorModel>) {
    super();
    Object.assign(this, partial);
  }
}
