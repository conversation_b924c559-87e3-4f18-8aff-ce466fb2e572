import { EAccountAssignment } from '../config/enums/account-assignment.enum';
import { BudgetCodeModel } from './budget-code.model';
import { BudgetModel } from './budget.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { CurrencyUnitModel } from './currency-unit.model';
import { MaterialGroupModel } from './material-group.model';
import { MaterialModel } from './material.model';
import { MeasureModel } from './measure.model';
import { PriceInformationRecordModel } from './price_information_record.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestDetailModel } from './purchase_request_detail.model';
import { SapPurchaseOrderItemModel } from './sap_purchase_order_item.model';
import { SupplierModel } from './supplier.model';
import { TaxCodeModel } from './tax-code.model';
import { WarehouseModel } from './warehouse.model';

export class PurchaseOrderDetailModel {
  id: number;

  budgetCodeId?: string;

  costCenterId?: string;

  materialCodeId?: string;

  materialName?: string;

  materialGroupId?: string;

  materialGroupName?: string; //text

  unit?: string;

  note?: string;

  deliveryTime: Date;

  budget?: number;

  remainingBudget?: number;

  remainingActualBudget?: number;

  unitPrice?: number;

  vat?: number;

  totalAmount?: number;

  totalAmountVat?: number; //Tổng tiền quy đổi * VAT

  prReference?: number;

  pirId?: number;

  supplierId?: string;

  supplierInfo?: string | object;

  estimatedPrice?: number;

  purchasePrice?: number;

  standardQuantity?: number;

  inventoryNumber?: number;

  quantityOfferedPurchase?: number;

  quantity?: number; //Số lượng mua

  sapCreatedQuantity?: number;

  createdAt?: Date;

  updatedAt?: Date;
  deletedAt?: Date;

  purchaseOrder?: PurchaseOrderModel;

  accountGl?: string;

  accountAssignment: EAccountAssignment;

  property?: string; //Tài sản - asset

  internalOrder?: string;

  wbs?: string;

  prDetailId?: number;

  requestedQuantity?: number; // Số lượng yêu cầu: Nếu tạo từ PR item Mặc định load = SL yêu cầu (trong PR item) - SL đã tạo PO

  currencyId?: string;

  exchangeRate?: number;

  totalConvertedAmount?: number; //Tổng tiền * Tỷ giá

  prDetail?: PurchaseRequestDetailModel;

  // Lưu vết khi tính toán remaining budget

  budgetId?: string;
  adjustBudgetId?: string;

  sapPurchaseOrderItems?: SapPurchaseOrderItemModel[];

  budgetCode?: BudgetCodeModel;
  costCenter?: CostcenterSubaccountModel;
  material?: MaterialModel;
  materialGroup?: MaterialGroupModel;
  pir?: PriceInformationRecordModel;
  supplier?: SupplierModel;
  currency?: CurrencyUnitModel;
  budgetData?: BudgetModel;
  adjustBudget?: BudgetModel;
  measureId?: string;
  measure?: MeasureModel;
  taxCode?: TaxCodeModel;

  warehouseId?: string;
  warehouse?: WarehouseModel;
  constructor(partial: Partial<PurchaseOrderDetailModel>) {
    Object.assign(this, partial);
  }
}
