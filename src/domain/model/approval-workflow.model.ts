import { BaseModel } from './base.model';
import { ProcessModel } from './process.model';
import { StaffApprovalWorkflowModel } from './staff-approval-workflow.model';

export class ApprovalWorkflowModel extends BaseModel {
  sendEmailToCreator: boolean;
  processes?: ProcessModel[];
  staffApprovalWorkflows?: StaffApprovalWorkflowModel[];
  parentProcess?: ProcessModel;
  parentProcessId?: string;
  name: string;

  constructor(partial: Partial<ApprovalWorkflowModel>) {
    super();
    Object.assign(this, partial);
  }
}
