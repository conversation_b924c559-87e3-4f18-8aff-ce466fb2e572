import { OmitType } from '@nestjs/swagger';
import {
  EFileExportStatus,
  EFileExportType,
} from '../config/enums/file-export-history.enum';
import { BaseModel } from './base.model';

export class FileExportHistoryModel extends OmitType(BaseModel, ['createdBy']) {
  fileName: string;
  createdBy: string | object;
  filePath: string;
  status: EFileExportStatus;
  exportType: EFileExportType;
  errors?: string | object[];
  constructor(partial: Partial<FileExportHistoryModel>) {
    super();
    Object.assign(this, partial);
  }
}
