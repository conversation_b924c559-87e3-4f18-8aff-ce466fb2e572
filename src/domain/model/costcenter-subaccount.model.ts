import { CostSubHistoryEntity } from '../../infrastructure/entities/cost-sub-history.entity';
import { ECostcenterSubaccountStatus } from '../config/enums/costcenter-subaccount.enum';
import { BaseModel } from './base.model';
import { BudgetModel } from './budget.model';
import { BusinessUnitModel } from './business-unit.model';
import { CompanyModel } from './company.model';
import { CostSubHistoryModel } from './cost-sub-history.model';
import { DepartmentModel } from './department.model';
import { FunctionUnitModel } from './function-unit.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { PurchaseRequestModel } from './purchase_request.model';
import { PurchaseRequestDetailModel } from './purchase_request_detail.model';
import { SapPurchaseOrderItemModel } from './sap_purchase_order_item.model';
import { SectorModel } from './sector.model';

export class CostcenterSubaccountModel extends BaseModel {
  name: string;
  code: string;
  description?: string;
  status: ECostcenterSubaccountStatus;
  searchValue?: string;
  sectorId: string;
  companyId?: string;
  businessUnitId?: string;
  departmentId?: string;
  budgets?: BudgetModel[];
  sector?: SectorModel;
  company?: CompanyModel;
  businessUnit?: BusinessUnitModel;
  department?: DepartmentModel;
  histories?: CostSubHistoryModel[];
  note1?: string; //Ghi chú 1
  note2?: string; //Ghi chú 2
  note3?: string; //Ghi chú 3
  note4?: string; //Ghi chú 4
  note5?: string; //Ghi chú 5
  note6?: string; //Ghi chú 6
  effectiveStartDate: Date; //Thời gian có hiệu lực
  effectiveEndDate?: Date; //Thời gian hết hiệu lực

  purchaseRequests?: PurchaseRequestModel[];

  purchaseRequestDetails?: PurchaseRequestDetailModel[];

  purchaseOrders?: PurchaseOrderModel[];

  purchaseOrderDetails?: PurchaseOrderDetailModel[];

  sapPurchaseOrderItems?: SapPurchaseOrderItemModel[];

  functionUnits?: FunctionUnitModel[];
  constructor(partial: Partial<CostcenterSubaccountModel>) {
    super();
    Object.assign(this, partial);
  }
}
