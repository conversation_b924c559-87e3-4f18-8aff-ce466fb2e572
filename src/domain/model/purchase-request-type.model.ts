import {
  EPurchaseRequestStatus,
  EPurchaseRequestTypeForm,
} from '../config/enums/purchasing.enum';
import { BaseModel } from './base.model';
import { PurchaseRequestModel } from './purchase_request.model';

export class PurchaseRequestTypeModel extends BaseModel {
  name: string;
  description?: string;
  searchValue?: string;
  code: string;
  status: EPurchaseRequestStatus; //Trạng thái
  form?: EPurchaseRequestTypeForm; //Hình thức
  purchaseRequests?: PurchaseRequestModel[];
  constructor(partial: Partial<PurchaseRequestTypeModel>) {
    super();
    Object.assign(this, partial);
  }
}
