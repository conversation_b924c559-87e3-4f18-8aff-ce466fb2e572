import { BaseModel } from './base.model';
import { ConditionDetailModel } from './condition-detail.model';
import { ConditionModel } from './condition.model';
import { ProcessModel } from './process.model';

export class ProcessConditionModel extends BaseModel {
  processId?: string;
  process?: ProcessModel;

  conditionId?: string;
  condition?: ConditionModel;

  conditionDetails?: ConditionDetailModel[];
  constructor(partial: Partial<ProcessConditionModel>) {
    super();
    Object.assign(this, partial);
  }
}
