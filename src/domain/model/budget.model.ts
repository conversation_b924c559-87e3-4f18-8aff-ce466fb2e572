import {
  EBudgetCreateType,
  EBudgetStatus,
  EBudgetType,
} from '../config/enums/budget.enum';
import { BaseModel } from './base.model';
import { BudgetCapexModel } from './budget-capex.model';
import { BudgetCodeModel } from './budget-code.model';
import { BudgetOpexModel } from './budget-opex.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { CurrencyUnitModel } from './currency-unit.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { PurchaseRequestDetailModel } from './purchase_request_detail.model';

export class BudgetModel extends BaseModel {
  code: string; //Mã code - Opex: OP-123456 (unique) với prefix là OP, Capex: CA-123456 (unique) với prefix là CA - và 123456 là số nhảy tăng dần từ 000001

  createType: EBudgetCreateType; //Loại khởi tạo

  adjustBudgetId?: string; //Ngân sách điều chỉnh

  budgetType: EBudgetType; //Loại ngân sách

  currencyUnitId: string; //Đơn vị tiền tệ

  budgetCodeId: string; //Mã ngân sách

  costcenterSubaccountId: string; //Costcenter/Subaccount

  note?: string; //Ghi chú

  effectiveStartDate: Date; //Thời gian có hiệu lực

  effectiveEndDate: Date; //Thời gian hết hiệu lực

  budgetOpexId?: string; //Chi tiết ngân sách Opex

  budgetCapexId?: string; //Chi tiết ngân sách Capex

  totalValue: number; //Tổng giá trị

  isLock: boolean; //Khóa ngân sách

  budgetOpex?: BudgetOpexModel;

  budgetCapex?: BudgetCapexModel;

  children?: BudgetModel[]; //Những ngân sách tạo ra để điều chỉnh ngân sách này

  parent?: BudgetModel; //Ngân sách được điều chỉnh

  currencyUnit?: CurrencyUnitModel;

  budgetCode?: BudgetCodeModel;

  costcenterSubaccount?: CostcenterSubaccountModel;

  searchValue?: string;

  note2?: string;

  purchaseRequestDetailBudgets?: PurchaseRequestDetailModel[];

  purchaseRequestDetailAdjustBudgets?: PurchaseRequestDetailModel[];

  purchaseOrderDetailBudgets?: PurchaseOrderDetailModel[];

  purchaseOrderDetailAdjustBudgets?: PurchaseOrderDetailModel[];
  // FOR IMPORT
  rowNumber?: number;
  constructor(partial: Partial<BudgetModel>) {
    super();
    Object.assign(this, partial);
  }
}
