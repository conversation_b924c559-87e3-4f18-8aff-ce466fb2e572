import { Status } from '../config/enums/status.enum';
import { BusinessUnitModel } from './business-unit.model';
import { CurrencyUnitModel } from './currency-unit.model';
import { MaterialModel } from './material.model';
import { PlantModel } from './plant.model';
import { PurchasingDepartmentModel } from './purchasing-department.model';
import { PurchasingGroupModel } from './purchasing-group.model';
import { SupplierModel } from './supplier.model';

export class PriceInformationRecordModel {
  id: number;

  vendorCodeId: string;

  materialCodeId: string;

  purchaseOrganizationId: string;

  plantId?: string;

  infoType: string[];

  businessUnitIds: string[];

  purchaseUnit?: string;

  vendorLeadtime?: string;

  purchaseGroupId?: string;

  regularPurchaseQuantity: number;

  minimumOrderQuantity: number;

  upperTolerance: number;

  lowerTolerance: number;

  purchasePrice: number;

  currencyId: string;

  overPurchaseUnit: string;

  unitOfMeasurement?: string;

  effectiveDate: Date;

  expirationDate: Date;

  status: Status;

  createdAt: Date;

  updatedAt: Date;

  material?: MaterialModel;
  purchaseOrganization?: PurchasingDepartmentModel;
  plant?: PlantModel;
  currency?: CurrencyUnitModel;
  vendor?: SupplierModel;
  purchaseGroup?: PurchasingGroupModel;
  businessUnits?: BusinessUnitModel[];

  constructor(partial: Partial<PriceInformationRecordModel>) {
    Object.assign(this, partial);
  }
}
