import { EBusinessUnitStatus } from '../config/enums/business-unit.enum';
import { EFunctionUnitStatus } from '../config/enums/function-unit.enum';
import { BaseModel } from './base.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';

export class FunctionUnitModel extends BaseModel {
  code: string;
  name: string;
  description?: string;
  status: EFunctionUnitStatus;
  searchValue: string;
  purchaseRequests?: PurchaseRequestModel[];
  purchaseOrders?: PurchaseOrderModel[];
  constructor(partial: Partial<FunctionUnitModel>) {
    super();
    Object.assign(this, partial);
  }
}
