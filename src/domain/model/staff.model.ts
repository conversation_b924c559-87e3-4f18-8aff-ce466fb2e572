import { EStatus } from '../config/enums/status.enum';
import { BaseModel } from './base.model';
import { BusinessOwnerModel } from './business-owner.model';
import { BusinessUnitModel } from './business-unit.model';
import { CompanyModel } from './company.model';
import { DepartmentModel } from './department.model';
import { FunctionUnitModel } from './function-unit.model';
import { PositionModel } from './position.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';
import { SectorModel } from './sector.model';
import { StaffApprovalWorkflowModel } from './staff-approval-workflow.model';
import { StaffHierarchyModel } from './staff-hierarchy.model';

export class StaffModel extends BaseModel {
  firstName: string;
  lastName: string;
  code: string;
  phone: string;
  email: string;
  status: EStatus;
  poCreatorId?: string;
  purchaserId?: string;

  sectors?: SectorModel[];
  companies?: CompanyModel[];
  departments?: DepartmentModel[];
  businessUnits?: BusinessUnitModel[];
  functionUnits?: FunctionUnitModel[];
  businessOwners?: BusinessOwnerModel[];
  position?: PositionModel;

  subordinateHierarchies?: StaffHierarchyModel[];
  managerHierarchies?: StaffHierarchyModel[];

  managers?: StaffModel[];

  level?: number; //Level Management

  purchaser?: StaffModel;
  purchasers?: StaffModel[];
  poCreator?: StaffModel;

  staffApprovalWorkflows?: StaffApprovalWorkflowModel[];

  purchaseRequestRequesters?: PurchaseRequestModel[];
  purchaseRequestPurchasers?: PurchaseRequestModel[];
  purchaseOrderRequesters?: PurchaseOrderModel[];
  purchaseOrderPurchasers?: PurchaseOrderModel[];
  constructor(partial: Partial<StaffModel>) {
    super();
    Object.assign(this, partial);
  }
}
