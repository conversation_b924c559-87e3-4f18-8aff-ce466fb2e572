import {
  EPurchaseOrderStatus,
  EPurchaseOrderTypeForm,
} from '../config/enums/purchasing.enum';
import { BaseModel } from './base.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { SapPurchaseOrderModel } from './sap_purchase_order.model';

export class PurchaseOrderTypeModel extends BaseModel {
  name: string;
  description?: string;
  searchValue?: string;
  code: string;
  status: EPurchaseOrderStatus; //Trạng thái
  form?: EPurchaseOrderTypeForm; //Hình thức
  purchaseOrders?: PurchaseOrderModel[];
  sapPurchaseOrders?: SapPurchaseOrderModel[];
  constructor(partial: Partial<PurchaseOrderTypeModel>) {
    super();
    Object.assign(this, partial);
  }
}
