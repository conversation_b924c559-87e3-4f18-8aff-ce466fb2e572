import { PurchaseOrderDetailEntity } from '../../infrastructure/entities/purchase_order_detail.entity';
import { PurchaseRequestEntity } from '../../infrastructure/entities/purchase_request.entity';
import { BudgetCodeModel } from './budget-code.model';
import { BudgetModel } from './budget.model';
import { CostcenterSubaccountModel } from './costcenter-subaccount.model';
import { MaterialGroupModel } from './material-group.model';
import { MaterialModel } from './material.model';
import { MeasureModel } from './measure.model';
import { WarehouseModel } from './warehouse.model';

export class PurchaseRequestDetailModel {
  id: number;

  budgetCodeId?: string;

  costCenterId?: string;

  materialCodeId: string;

  materialName: string;

  materialGroupId?: string; //uuid

  materialGroupName?: string; //text

  quantity: number;

  unit?: string;

  unitPrice?: number;

  note?: string;

  deliveryTime: Date;

  totalAmount: number;

  budget?: number;

  remainingBudget?: number; //<PERSON>ân sách dự chi còn lại => <PERSON><PERSON> sách còn lại

  remainingActualBudget?: number; //Ngân sách thực chi còn lại

  estimatedPrice: number;

  standardQuantity?: number;

  inventoryNumber?: number;

  createdAt: Date;

  updatedAt: Date;
  deletedAt?: Date;

  purchaseRequest?: PurchaseRequestEntity;

  poDetails?: PurchaseOrderDetailEntity[];

  // Lưu vết khi tính toán remaining budget

  budgetId?: string;

  adjustBudgetId?: string;
  budgetCode?: BudgetCodeModel;
  costCenter?: CostcenterSubaccountModel;
  material?: MaterialModel;
  materialGroup?: MaterialGroupModel;
  budgetData?: BudgetModel;
  adjustBudget?: BudgetModel;
  measureId?: string;
  measure?: MeasureModel;

  numberPoCreated?: number;

  warehouseId?: string;
  warehouse?: WarehouseModel;
  constructor(partial: Partial<PurchaseRequestDetailModel>) {
    Object.assign(this, partial);
  }
}
