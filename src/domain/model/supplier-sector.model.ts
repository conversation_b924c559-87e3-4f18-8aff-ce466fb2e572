import { ESupplierSectorStatus } from '../config/enums/supplier-sector.enum';
import { ESupplierStatus, ESupplierType } from '../config/enums/supplier.enum';
import { BaseModel } from './base.model';
import { SectorModel } from './sector.model';
import { SupplierModel } from './supplier.model';

export class SupplierSectorModel extends BaseModel {
  codeSAP: string;

  status: ESupplierSectorStatus;

  supplier?: SupplierModel;

  sector?: SectorModel;

  constructor(partial: Partial<SupplierSectorModel>) {
    super();
    Object.assign(this, partial);
  }
}
