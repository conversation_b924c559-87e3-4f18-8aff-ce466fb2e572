import { BusinessUnitEntity } from '../../infrastructure/entities/business-unit.entity';
import { CompanyEntity } from '../../infrastructure/entities/company.entity';
import { DepartmentEntity } from '../../infrastructure/entities/department.entity';
import { MaterialEntity } from '../../infrastructure/entities/material.entity';
import { SectorEntity } from '../../infrastructure/entities/sector.entity';
import { EInventoryStandardStatus } from '../config/enums/inventory-standard.enum';
import { BaseModel } from './base.model';
import { BusinessUnitModel } from './business-unit.model';
import { CompanyModel } from './company.model';
import { DepartmentModel } from './department.model';
import { MaterialModel } from './material.model';
import { SectorModel } from './sector.model';

export class InventoryStandardModel extends BaseModel {
  materialId: string; //Vật tư

  unit?: string; //Đơn vị tính

  sectorId: string; //Vật tư

  companyId?: string; //V<PERSON><PERSON> tư

  businessUnitId?: string; //Vật tư

  departmentId?: string; //Vật tư

  standardQuantity: number; //Số lượng định mức

  inventoryQuantity: number; //Số lượng tồn kho

  status: EInventoryStandardStatus;

  path?: string;

  material?: MaterialModel;

  sector?: SectorModel;

  company?: CompanyModel;

  businessUnit?: BusinessUnitModel;

  department?: DepartmentModel;
  constructor(partial: Partial<InventoryStandardModel>) {
    super();
    Object.assign(this, partial);
  }
}
