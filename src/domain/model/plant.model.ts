import { EPlantStatus } from '../config/enums/plant.enum';
import { BaseModel } from './base.model';
import { ConditionDetailModel } from './condition-detail.model';
import { PriceInformationRecordModel } from './price_information_record.model';
import { PurchaseOrderModel } from './purchase_order.model';
import { PurchaseRequestModel } from './purchase_request.model';
import { SectorModel } from './sector.model';

export class PlantModel extends BaseModel {
  code: string;

  name: string;

  description?: string;

  searchValue?: string;

  sectorId: string; //Nhóm vật tư

  status: EPlantStatus; //Trạng thái

  sector?: SectorModel;

  conditionDetails?: ConditionDetailModel[];
  purchaseRequests?: PurchaseRequestModel[];
  purchaseOrders?: PurchaseOrderModel[];
  pirs?: PriceInformationRecordModel[];
  constructor(partial: Partial<PlantModel>) {
    super();
    Object.assign(this, partial);
  }
}
