import {
  EMaterialCheckBudget,
  EMaterialThroughPurchasing,
} from '../config/enums/material.enum';
import { BaseModel } from './base.model';
import { BusinessUnitModel } from './business-unit.model';
import { CompanyModel } from './company.model';
import { DepartmentModel } from './department.model';
import { InventoryStandardModel } from './inventory-standard.model';
import { MaterialGroupModel } from './material-group.model';
import { MaterialSectorModel } from './material-sector.model';
import { MaterialTypeModel } from './material-type.model';
import { PriceInformationRecordModel } from './price_information_record.model';
import { MeasureModel } from './measure.model';
import { ProfitCenterModel } from './profit-center.model';
import { PurchaseOrderDetailModel } from './purchase_order_detail.model';
import { PurchaseRequestDetailModel } from './purchase_request_detail.model';
import { PurchasingDepartmentModel } from './purchasing-department.model';
import { PurchasingGroupModel } from './purchasing-group.model';
import { SapPurchaseOrderItemModel } from './sap_purchase_order_item.model';
import { FunctionUnitModel } from './function-unit.model';

export class MaterialModel extends BaseModel {
  code: string; //Mã Vật tư

  name?: string; //Tên Vật tư

  materialTypeId: string; //Loại vật tư

  unit?: string; //Đơn vị tính

  materialGroupId: string; //Nhóm vật tư

  description: string; //Mô tả

  purchasingDepartment?: PurchasingDepartmentModel;
  purchasingDepartmentId: string;
  purchasingGroup?: PurchasingGroupModel;
  purchasingGroupId: string;

  createdBy?: string | object;
  throughPurchasing?: EMaterialThroughPurchasing;
  checkBudget?: EMaterialCheckBudget;

  materialType?: MaterialTypeModel;
  materialGroup?: MaterialGroupModel;
  industries?: MaterialSectorModel[];
  inventoryStandards?: InventoryStandardModel[];

  profitCenter?: ProfitCenterModel;

  measureId?: string;

  company?: CompanyModel;
  businessUnits?: BusinessUnitModel[];
  functionUnits?: FunctionUnitModel[];
  department?: DepartmentModel;

  purchaseRequestDetails?: PurchaseRequestDetailModel[];

  purchaseOrderDetails?: PurchaseOrderDetailModel[];

  sapPurchaseOrderItems?: SapPurchaseOrderItemModel[];

  pirs?: PriceInformationRecordModel[];
  measure?: MeasureModel;

  constructor(partial: Partial<MaterialModel>) {
    super();
    Object.assign(this, partial);
  }
}
