import { TErrorMessage, buildErrorDetail } from '../error-message';

export const plantErrorDetails = {
  E_2900(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2900', 'Plant not found', detail);
    return e;
  },

  E_2901(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2901', 'Plant code already exist', detail);
    return e;
  },

  E_2902(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2902', 'Plant inactive', detail);
    return e;
  },
};
