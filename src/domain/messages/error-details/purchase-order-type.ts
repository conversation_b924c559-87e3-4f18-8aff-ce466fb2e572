import { TErrorMessage, buildErrorDetail } from '../error-message';

export const PurchaseOrderTypeErrorDetails = {
  E_3400(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_3400',
      'Purchase Order Type not found',
      detail,
    );
    return e;
  },

  E_3401(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_3401',
      'Purchase Order Type code already exist',
      detail,
    );
    return e;
  },

  E_3402(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_3402',
      'Purchase Order Type inactive',
      detail,
    );
    return e;
  },
};
