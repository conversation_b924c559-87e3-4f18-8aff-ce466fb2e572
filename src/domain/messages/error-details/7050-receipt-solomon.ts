import { buildErrorDetail, TErrorMessage } from '../error-message';

export const receiptSolomonErrorDetails = {
  E_7050(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7050', 'PO_NOT_FOUND', detail);
    return e;
  },
  E_7051(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7051', 'BUDGET_CODE_NOT_FOUND', detail);
    return e;
  },
  E_7052(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7052', 'RECEIPT_NOT_FOUND', detail);
    return e;
  },
};
