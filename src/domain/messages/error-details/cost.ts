import { TErrorMessage } from '../error-message';

export const costErrorDetails = {
  E_6024() {
    const error: TErrorMessage = {
      message: 'COST_NOT_FOUND',
      errorCode: 'E_6024',
    };

    return error;
  },
  E_6025() {
    const error: TErrorMessage = {
      message: 'COST_CODE_EXISTED',
      errorCode: 'E_6025',
    };

    return error;
  },

  E_6026() {
    const error: TErrorMessage = {
      message: 'COST_INACTIVE',
      errorCode: 'E_6026',
    };

    return error;
  },
  E_6027() {
    const error: TErrorMessage = {
      message: 'COST_CODE_IS_REQUIRED',
      errorCode: 'E_6027',
    };

    return error;
  },
  E_6028() {
    const error: TErrorMessage = {
      message: 'COST_NAME_IS_REQUIRED',
      errorCode: 'E_6028',
    };

    return error;
  },
  E_6029() {
    const error: TErrorMessage = {
      message: 'COST_CODE_IS_DUPLICATED',
      errorCode: 'E_6029',
    };

    return error;
  },
};
