import { buildErrorDetail, TErrorMessage } from '../error-message';

export const approvalErrorDetails = {
  E_5410(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '5410',
      'NO_MATCHING_APPROVAL_FLOW_FOUND',
      detail,
    );
    return e;
  },
  E_5411(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('5411', 'NO_APPROVER_FOUND', detail);
    return e;
  },
  E_5412(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '5412',
      'APPROVER_IS_NOT_ON_THE_APPROVED_LIST',
      detail,
    );
    return e;
  },
  E_5413(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('5413', 'APPROVER_LEVEL_NOT_FOUND', detail);
    return e;
  },
  E_5414(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('5414', 'APPROVE_FAIL', detail);
    return e;
  },

  E_5415(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('5415', 'CANCEL_FAIL', detail);

    return e;
  },
  E_5416(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('5416', 'CREATE_ADJUST_BUDGET_FAIL', detail);
    return e;
  },
  E_5417(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('5417', 'REJECT_FAIL', detail);
    return e;
  },
  E_5418(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('5418', 'INTERNAL_ORDER_UPDATES_ONLY', detail);
    return e;
  },
  E_5419(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '5419',
      'PURCHASE_ORDER_NOT_INCLUDED_DETAIL',
      detail,
    );
    return e;
  },
  E_5420(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '5420',
      'Số lượng định mức và tồn kho là bắt buộc',
      detail,
    );
    return e;
  },
};
