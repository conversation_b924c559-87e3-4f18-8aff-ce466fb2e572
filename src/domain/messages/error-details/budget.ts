import { TErrorMessage, buildErrorDetail } from '../error-message';

export const budgetErrorDetails = {
  E_6000(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_6000', 'Duplicate investments', detail);
    return e;
  },
  E_6001(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_6001', 'Investments is required', detail);
    return e;
  },
  E_6002(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_6002', 'Duplicate budget', detail);
    return e;
  },
  E_6003(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_6003', 'Budget Code does not match', detail);
    return e;
  },
};
