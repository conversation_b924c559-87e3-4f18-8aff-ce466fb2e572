import { TErrorMessage, buildErrorDetail } from '../error-message';

export const materialGroupErrorDetails = {
  E_2200(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2200', 'MATERIAL_GROUP_NOT_FOUND', detail);
    return e;
  },

  E_2201(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2201', 'MATERIAL_GROUP_CODE_EXISTED', detail);
    return e;
  },

  E_2202(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_22012', 'Material group inactive', detail);
    return e;
  },
  E_2203(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2203', 'CODE_IS_REQUIRED', detail);
    return e;
  },
  E_2204(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2204', 'NAME_IS_REQUIRED', detail);
    return e;
  },
  E_2205(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2205', 'CODE_IS_DUPLICATED', detail);
    return e;
  },
};
