import { TErrorMessage, buildErrorDetail } from '../error-message';

export const PurchaseRequestTypeErrorDetails = {
  E_3300(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_3300',
      'Purchase request type not found',
      detail,
    );
    return e;
  },

  E_3301(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_3301',
      'Purchase request type code already exist',
      detail,
    );
    return e;
  },

  E_3302(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_3302',
      'Purchase request type inactive',
      detail,
    );
    return e;
  },
};
