import { TErrorMessage, buildErrorDetail } from '../error-message';

export const processTypeErrorDetails = {
  E_2700(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2200', 'PROCESS_TYPE_NOT_FOUND', detail);
    return e;
  },

  E_2701(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2201', 'PROCESS_TYPE_CODE_EXISTED', detail);
    return e;
  },

  E_2702(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_22012', 'PROCESS_TYPE_INACTIVE', detail);
    return e;
  },
};
