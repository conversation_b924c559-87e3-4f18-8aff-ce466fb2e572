import { TErrorMessage, buildErrorDetail } from '../error-message';

export const purchasingGroupErrorDetails = {
  E_2600(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2600', 'Purchasing group not found', detail);
    return e;
  },

  E_2601(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_2601',
      'Purchasing group code already exist',
      detail,
    );
    return e;
  },

  E_2602(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2601', 'Purchasing group inactive', detail);
    return e;
  },
};
