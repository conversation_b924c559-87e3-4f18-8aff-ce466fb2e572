import { TErrorMessage, buildErrorDetail } from '../error-message';

export const fileErrorDetails = {
  E_7000(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7000', 'History not found', detail);
    return e;
  },

  E_7001(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7001', 'History status is invalid', detail);
    return e;
  },

  E_7002(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7002', 'File is required', detail);
    return e;
  },

  E_7003(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7003', 'File mimetype invalid', detail);
    return e;
  },

  E_7004(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7004', 'File max size invalid', detail);
    return e;
  },
};
