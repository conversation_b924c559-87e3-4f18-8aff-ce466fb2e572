import { TErrorMessage, buildErrorDetail } from '../error-message';

export const notificationFormErrorDetails = {
  E_2300(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2300', 'Notification form not found', detail);
    return e;
  },

  E_2301(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2302', 'Notification form inactive', detail);
    return e;
  },
};
