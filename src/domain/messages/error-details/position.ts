import { TErrorMessage, buildErrorDetail } from '../error-message';

export const positionErrorDetails = {
  E_2910(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2910', 'Position not found', detail);
    return e;
  },

  E_2911(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2911', 'Position code already exist', detail);
    return e;
  },

  E_2912(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2912', 'Position inactive', detail);
    return e;
  },
};
