import { TErrorMessage, buildErrorDetail } from '../error-message';

export const inventoryStandardErrorDetails = {
  E_6500(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_6500',
      'Inventory standard not found',
      detail,
    );
    return e;
  },
  E_6501(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_6501',
      'Inventory standard duplicated',
      detail,
    );
    return e;
  },

  E_6502(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_6502',
      'Empty inventory standard to export',
      detail,
    );
    return e;
  },

  E_6503(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_6503',
      'Export inventory standard fail',
      detail,
    );
    return e;
  },
  E_6504(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_6504', 'MATERIAL_CODE_IS_REQUIRED', detail);
    return e;
  },
  E_6505(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_6505', 'SECTOR_CODE_IS_REQUIRED', detail);
    return e;
  },
};
