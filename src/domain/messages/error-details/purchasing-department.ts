import { TErrorMessage, buildErrorDetail } from '../error-message';

export const purchasingDepartmentErrorDetails = {
  E_2800(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_2800',
      'Purchasing department not found',
      detail,
    );
    return e;
  },

  E_2801(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_2801',
      'Purchasing department code already exist',
      detail,
    );
    return e;
  },

  E_2802(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_2801',
      'Purchasing department inactive',
      detail,
    );
    return e;
  },
};
