import { TErrorMessage } from '../error-message';

export const approvalProcessDetailErrorDetails = {
  E_6074() {
    const error: TErrorMessage = {
      message: 'APPROVAL_PROCESS_DETAIL_NOT_FOUND',
      errorCode: 'E_6074',
    };

    return error;
  },
  E_6075() {
    const error: TErrorMessage = {
      message: 'PR_CREATED_BY_NOT_FOUND',
      errorCode: 'E_6075',
    };

    return error;
  },
  E_6076() {
    const error: TErrorMessage = {
      message: 'PO_CREATED_BY_NOT_FOUND',
      errorCode: 'E_6076',
    };

    return error;
  },
  E_6077() {
    const error: TErrorMessage = {
      message: 'PR_APPROVER_1_NOT_FOUND',
      errorCode: 'E_6077',
    };

    return error;
  },
  E_6078() {
    const error: TErrorMessage = {
      message: 'PR_APPROVER_3_NOT_FOUND',
      errorCode: 'E_6078',
    };

    return error;
  },
  E_6079() {
    const error: TErrorMessage = {
      message: 'PR_APPROVER_4_NOT_FOUND',
      errorCode: 'E_6079',
    };

    return error;
  },
  E_6080() {
    const error: TErrorMessage = {
      message: 'PR_APPROVER_5_NOT_FOUND',
      errorCode: 'E_6080',
    };

    return error;
  },
  E_6081() {
    const error: TErrorMessage = {
      message: 'PR_APPROVER_6_NOT_FOUND',
      errorCode: 'E_6081',
    };

    return error;
  },
  E_6082() {
    const error: TErrorMessage = {
      message: 'PO_APPROVER_1_NOT_FOUND',
      errorCode: 'E_6082',
    };

    return error;
  },
  E_6083() {
    const error: TErrorMessage = {
      message: 'PO_APPROVER_2_NOT_FOUND',
      errorCode: 'E_6083',
    };

    return error;
  },
  E_6084() {
    const error: TErrorMessage = {
      message: 'PO_APPROVER_3_NOT_FOUND',
      errorCode: 'E_6084',
    };

    return error;
  },
  E_6085() {
    const error: TErrorMessage = {
      message: 'PR_APPROVER_2_NOT_FOUND',
      errorCode: 'E_6085',
    };

    return error;
  },
  E_6086() {
    const error: TErrorMessage = {
      message: 'PR_APPROVER_7_NOT_FOUND',
      errorCode: 'E_6086',
    };

    return error;
  },
};
