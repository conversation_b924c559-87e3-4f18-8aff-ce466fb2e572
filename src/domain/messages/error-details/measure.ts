import { TErrorMessage } from '../error-message';

export const measureErrorDetails = {
  E_6050() {
    const error: TErrorMessage = {
      message: 'MEASURE_NOT_FOUND',
      errorCode: 'E_6050',
    };

    return error;
  },
  E_6051() {
    const error: TErrorMessage = {
      message: 'MEASURE_CODE_EXISTED',
      errorCode: 'E_6051',
    };

    return error;
  },
  E_6052() {
    const error: TErrorMessage = {
      message: 'MEASURE_CODE_IS_REQUIRED',
      errorCode: 'E_6052',
    };

    return error;
  },
  E_6053() {
    const error: TErrorMessage = {
      message: 'MEASURE_CODE_IS_DUPLICATED',
      errorCode: 'E_6053',
    };

    return error;
  },
  E_6054() {
    const error: TErrorMessage = {
      message: 'MEASURE_NAME_IS_REQUIRED',
      errorCode: 'E_6054',
    };

    return error;
  },
  E_6055() {
    const error: TErrorMessage = {
      message: 'DUPLICATE_SYSTEM_IN_CODE_CONVERSION',
      errorCode: 'E_6055',
    };

    return error;
  },
};
