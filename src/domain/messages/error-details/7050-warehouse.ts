import { buildErrorDetail, TErrorMessage } from '../error-message';

export const warehouseErrorDetails = {
  E_7050(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7050', 'Warehouse not found', detail);
    return e;
  },
  E_7051(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7051', 'Warehouse already exist', detail);
    return e;
  },
  E_7052(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_7052', 'Warehouse already used', detail);
    return e;
  },
};
