import { TErrorMessage, buildErrorDetail } from '../error-message';

export const profitCenterErrorDetails = {
  E_2550(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2550', 'Profit center not found', detail);
    return e;
  },

  E_2551(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_2551',
      'Profit center code already exist',
      detail,
    );
    return e;
  },

  E_2552(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2502', 'Profit center inactive', detail);
    return e;
  },
};
