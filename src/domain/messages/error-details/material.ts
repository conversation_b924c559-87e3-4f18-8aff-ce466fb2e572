import { TErrorMessage, buildErrorDetail } from '../error-message';

export const materialErrorDetails = {
  E_2500(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2500', 'Material not found', detail);
    return e;
  },

  E_2501(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2501', 'Material code already exist', detail);
    return e;
  },

  E_2502(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2502', 'Material inactive', detail);
    return e;
  },
  E_2503(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2503', 'Code is required', detail);
    return e;
  },
  E_2504(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2504', 'Type is required', detail);
    return e;
  },
  E_2505(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2505', 'Group is required', detail);
    return e;
  },
  E_2506(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2506', 'Code SAP is require', detail);
    return e;
  },
  E_2507(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2507', 'Sector is require', detail);
    return e;
  },
};
