import { TErrorMessage, buildErrorDetail } from '../error-message';

export const staffErrorDetails = {
  E_3000(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('3000', 'Staff not found', detail);
    return e;
  },
  E_3001(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '3001',
      `The staff's email already exists`,
      detail,
    );
    return e;
  },
  E_3002(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '3002',
      `The staff's code already exists`,
      detail,
    );
    return e;
  },
  E_3003(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '3003',
      `Category not found or access denied for this category.`,
      detail,
    );
    return e;
  },
  E_3004(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '3004',
      `This management level already exists.`,
      detail,
    );
    return e;
  },
  E_3005(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '3005',
      `Cannot assign the manager as the staff is currently being edited.`,
      detail,
    );
    return e;
  },

  E_3006(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '3006',
      `Cannot assign the PO creator as the staff is currently being edited.`,
      detail,
    );
    return e;
  },
  E_3007(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '3007',
      `Cannot assign the purchaser as the staff is currently being edited.`,
      detail,
    );
    return e;
  },
};
