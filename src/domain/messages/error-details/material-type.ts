import { TErrorMessage, buildErrorDetail } from '../error-message';

export const materialTypeErrorDetails = {
  E_2000(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2000', 'Material type not found', detail);
    return e;
  },

  E_2001(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_2001',
      'Material type code already exist',
      detail,
    );
    return e;
  },

  E_2002(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2002', 'Material type inactive', detail);
    return e;
  },
  E_2003(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2003', 'CODE_IS_REQUIRED', detail);
    return e;
  },
  E_2004(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2004', 'NAME_IS_REQUIRED', detail);
    return e;
  },
  E_2005(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_2005', 'CODE_IS_DUPLICATED', detail);
    return e;
  },
};
