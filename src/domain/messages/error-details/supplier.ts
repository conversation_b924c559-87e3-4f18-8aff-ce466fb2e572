import { TErrorMessage, buildErrorDetail } from '../error-message';

export const supplierErrorDetails = {
  E_4000(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_4000', 'SUPPLIER_NOT_FOUND', detail);
    return e;
  },
  E_4001(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_4001', 'Supplier code already exist', detail);
    return e;
  },
  E_4002(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_4002', 'Supplier inactive', detail);
    return e;
  },
  E_4003(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_4003', 'Code is require', detail);
    return e;
  },

  E_4004(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_4004', 'Name is require', detail);
    return e;
  },
  E_4005(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_4005', 'Code SAP is require', detail);
    return e;
  },
  E_4006(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_4006', 'Sector is require', detail);
    return e;
  },
  E_4007(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_4007', 'Type is require', detail);
    return e;
  },
};
