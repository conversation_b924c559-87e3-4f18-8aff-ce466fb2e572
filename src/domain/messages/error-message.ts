export type TErrorMessage = {
  message: string;
  errorCode: string;
  detail?: any;
};

export type TErrorMessageImport = {
  error: TErrorMessage;
  row: number;
};

export const errorMessage = {
  E_1000() {
    const error: TErrorMessage = {
      message: 'BUSINESS_OWNER_IDS_DUPLICATE',
      errorCode: 'E_1000',
    };

    return error;
  },
  E_1001(detail: string = null) {
    const e = buildErrorDetail('E_1001', 'BUGET_CODE_NOT_FOUND', detail);
    return e;
  },
  E_1002() {
    const error: TErrorMessage = {
      message: 'BUGET_CODE_ALREADY_REGISTERED',
      errorCode: 'E_1002',
    };

    return error;
  },
  E_1003() {
    const error: TErrorMessage = {
      message: 'BUSINESS_OWNER_IDS_NOT_FOUND',
      errorCode: 'E_1003',
    };

    return error;
  },
  E_1004() {
    const error: TErrorMessage = {
      message: 'BUGET_CODE_IDS_IN_ACTIVE',
      errorCode: 'E_1004',
    };

    return error;
  },
  E_1005(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_1005', 'COST_CENTER_NOT_FOUND', detail);
    return e;
  },
  E_1006() {
    const error: TErrorMessage = {
      message: 'BUDGET_NOT_FOUND',
      errorCode: 'E_1006',
    };

    return error;
  },
  E_1007() {
    const error: TErrorMessage = {
      message: 'BUDGET_IS_LOCKED',
      errorCode: 'E_1007',
    };

    return error;
  },
  E_1008() {
    const error: TErrorMessage = {
      message: 'BUDGET_TYPE_INVALID',
      errorCode: 'E_1008',
    };

    return error;
  },
  E_1009() {
    const error: TErrorMessage = {
      message: 'ADJUST_BUDGET_NOT_FOUND',
      errorCode: 'E_1009',
    };

    return error;
  },
  E_1010() {
    const error: TErrorMessage = {
      message: 'ADJUST_BUDGET_HAS_INVALID_CREATE_TYPE',
      errorCode: 'E_1010',
    };

    return error;
  },
  E_1011() {
    const error: TErrorMessage = {
      message: 'ADJUST_BUDGET_HAS_INVALID_BUDGET_TYPE',
      errorCode: 'E_1011',
    };

    return error;
  },
  E_1012() {
    const error: TErrorMessage = {
      message: 'ADJUST_BUDGET_IN_ACTIVE',
      errorCode: 'E_1012',
    };

    return error;
  },
  E_1013(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_1013', 'CURRENCY_UNIT_NOT_FOUND', detail);
    return e;
  },
  E_1014() {
    const error: TErrorMessage = {
      message: 'CURRENCY_UNIT_IN_ACTIVE',
      errorCode: 'E_1014',
    };

    return error;
  },
  E_1015(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_1015', 'BUDGET_CODE_NOT_FOUND', detail);
    return e;
  },
  E_1016() {
    const error: TErrorMessage = {
      message: 'BUDGET_CODE_IN_ACTIVE',
      errorCode: 'E_1016',
    };

    return error;
  },
  E_1018() {
    const error: TErrorMessage = {
      message: 'COST_CENTER_SUB_ACCOUNT_IN_ACTIVE',
      errorCode: 'E_1018',
    };

    return error;
  },
  E_1019() {
    const error: TErrorMessage = {
      message: 'INVALID_EFFECTIVE_DATE',
      errorCode: 'E_1019',
    };

    return error;
  },
  E_1020(detail: string = null) {
    const e = buildErrorDetail('E_1020', 'BUSINESS_OWNER_NOT_FOUND', detail);
    return e;
  },
  E_1021() {
    const error: TErrorMessage = {
      message: 'BUSINESS_OWNER_CODE_EXISTED',
      errorCode: 'E_1021',
    };

    return error;
  },
  E_1022(detail: string = null) {
    const e = buildErrorDetail('E_1022', 'BUSINESS_UNIT_NOT_FOUND', detail);
    return e;
  },
  E_1024(detail: string = null) {
    const e: TErrorMessage = buildErrorDetail(
      'E_1024',
      'COMPANY_NOT_FOUND',
      detail,
    );
    return e;
  },
  E_1025() {
    const error: TErrorMessage = {
      message: 'COMPANY_CODE_EXISTED',
      errorCode: 'E_1025',
    };

    return error;
  },
  E_1026() {
    const error: TErrorMessage = {
      message: 'COST_CENTER_ALREADY_REGISTERED',
      errorCode: 'E_1026',
    };

    return error;
  },
  E_1027() {
    const error: TErrorMessage = {
      message: 'SECTOR_IN_ACTIVE',
      errorCode: 'E_1027',
    };

    return error;
  },
  E_1028() {
    const error: TErrorMessage = {
      message: 'COMPANY_IN_ACTIVE',
      errorCode: 'E_1028',
    };

    return error;
  },
  E_1029() {
    const error: TErrorMessage = {
      message: 'BUSINESS_UNIT_DISABLED',
      errorCode: 'E_1029',
    };

    return error;
  },
  E_1030() {
    const error: TErrorMessage = {
      message: 'DEPARTMENT_DISABLED',
      errorCode: 'E_1030',
    };

    return error;
  },
  E_1031() {
    const error: TErrorMessage = {
      message: 'CURRENCY_CODE_ALREADY_REGISTERED',
      errorCode: 'E_1031',
    };

    return error;
  },
  E_1032() {
    const error: TErrorMessage = {
      message: 'DEPARTMENT_CODE_ALREADY_REGISTERED',
      errorCode: 'E_1032',
    };

    return error;
  },
  E_1034(detail: string = null) {
    const e = buildErrorDetail('E_1034', 'FUNCTION_UNIT_NOT_FOUND', detail);
    return e;
  },
  E_1035() {
    const error: TErrorMessage = {
      message: 'FUNCTION_UNIT_CODE_ALREADY_REGISTERED',
      errorCode: 'E_1035',
    };

    return error;
  },
  E_1036(detail: string = null) {
    const e = buildErrorDetail('E_1036', 'SECTOR_NOT_FOUND', detail);
    return e;
  },
  E_1037() {
    const error: TErrorMessage = {
      message: 'SECTOR_CODE_EXISTED',
      errorCode: 'E_1037',
    };

    return error;
  },
  E_1038() {
    const error: TErrorMessage = {
      message: 'BUDGET_CODE_IS_REQUIRED',
      errorCode: 'E_1038',
    };

    return error;
  },
  E_1039() {
    const error: TErrorMessage = {
      message: 'BUSINESS_OWNERS_IS_REQUIRED',
      errorCode: 'E_1039',
    };

    return error;
  },
  E_1040() {
    const error: TErrorMessage = {
      message: 'BUDGET_CODE_STATUS_IS_REQUIRED',
      errorCode: 'E_1040',
    };

    return error;
  },
  E_1041() {
    const error: TErrorMessage = {
      message: 'IMPORT_FAILED',
      errorCode: 'E_1041',
    };

    return error;
  },
  E_1042() {
    const error: TErrorMessage = {
      message: 'BUDGET_CODE_NAME_IS_REQUIRED',
      errorCode: 'E_1042',
    };

    return error;
  },
  E_1043() {
    const error: TErrorMessage = {
      message: 'COST_CENTER_CODE_IS_REQUIRED',
      errorCode: 'E_1043',
    };

    return error;
  },
  E_1044() {
    const error: TErrorMessage = {
      message: 'COST_CENTER_NAME_IS_REQUIRED',
      errorCode: 'E_1044',
    };

    return error;
  },
  E_1045() {
    const error: TErrorMessage = {
      message: 'COST_CENTER_STATUS_IS_REQUIRED',
      errorCode: 'E_1045',
    };

    return error;
  },
  E_1046() {
    const error: TErrorMessage = {
      message: 'SECTOR_CODE_IS_REQUIRED',
      errorCode: 'E_1046',
    };

    return error;
  },
  E_1047() {
    const error: TErrorMessage = {
      message: 'COMPANY_CODE_IS_REQUIRED',
      errorCode: 'E_1047',
    };

    return error;
  },
  E_1048() {
    const error: TErrorMessage = {
      message: 'BUSINESS_UNIT_CODE_IS_REQUIRED',
      errorCode: 'E_1048',
    };

    return error;
  },
  E_1049() {
    const error: TErrorMessage = {
      message: 'DEPARTMENT_CODE_IS_REQUIRED',
      errorCode: 'E_1049',
    };

    return error;
  },
  E_1050(detail: string = null): TErrorMessage {
    const e: TErrorMessage = buildErrorDetail(
      'E_1050',
      'DEPARTMENT_NOT_FOUND',
      detail,
    );
    return e;
  },
  E_1051() {
    const error: TErrorMessage = {
      message: 'BUDGET_CODE_STATUS_INVALID',
      errorCode: 'E_1051',
    };

    return error;
  },
  E_1052() {
    const error: TErrorMessage = {
      message: 'COST_CENTER_STATUS_INVALID',
      errorCode: 'E_1052',
    };

    return error;
  },
  E_1053() {
    const error: TErrorMessage = {
      message: 'BUDGET_CREATE_TYPE_IS_REQUIRED',
      errorCode: 'E_1053',
    };

    return error;
  },
  E_1054() {
    const error: TErrorMessage = {
      message: 'BUDGET_TYPE_IS_REQUIRED',
      errorCode: 'E_1054',
    };

    return error;
  },
  E_1055() {
    const error: TErrorMessage = {
      message: 'CURRENCY_UNIT_IS_REQUIRED',
      errorCode: 'E_1055',
    };

    return error;
  },
  E_1056() {
    const error: TErrorMessage = {
      message: 'BUDGET_CODE_IS_REQUIRED',
      errorCode: 'E_1056',
    };

    return error;
  },
  E_1057() {
    const error: TErrorMessage = {
      message: 'COST_CENTER_IS_REQUIRED',
      errorCode: 'E_1057',
    };

    return error;
  },
  E_1058() {
    const error: TErrorMessage = {
      message: 'EFFECTIVE_START_DATE_IS_REQUIRED',
      errorCode: 'E_1058',
    };

    return error;
  },
  E_1059() {
    const error: TErrorMessage = {
      message: 'EFFECTIVE_END_DATE_IS_REQUIRED',
      errorCode: 'E_1059',
    };

    return error;
  },
  E_1060() {
    const error: TErrorMessage = {
      message: 'TOTAL_VALUE_IS_REQUIRED',
      errorCode: 'E_1060',
    };

    return error;
  },
  E_1061() {
    const error: TErrorMessage = {
      message: 'ADJUST_BUDGET_IS_REQUIRED',
      errorCode: 'E_1061',
    };

    return error;
  },
  E_1062() {
    const error: TErrorMessage = {
      message: 'BUDGET_CREATE_TYPE_IS_INVALID',
      errorCode: 'E_1062',
    };

    return error;
  },

  E_1063() {
    const error: TErrorMessage = {
      message: 'LEVEL_IS_REQUIRED',
      errorCode: 'E_1063',
    };

    return error;
  },
  E_1064() {
    const error: TErrorMessage = {
      message: 'BUDGET_TYPE_IS_INVALID',
      errorCode: 'E_1064',
    };

    return error;
  },
  E_1065() {
    const error: TErrorMessage = {
      message: 'START_DATE_IS_WRONG_FORMAT',
      errorCode: 'E_1065',
    };

    return error;
  },
  E_1066() {
    const error: TErrorMessage = {
      message: 'CLASSIFY_IS_REQUIRED',
      errorCode: 'E_1066',
    };

    return error;
  },
  E_1067() {
    const error: TErrorMessage = {
      message: 'INVESTMENT_IS_REQUIRED',
      errorCode: 'E_1067',
    };

    return error;
  },
  E_1068() {
    const error: TErrorMessage = {
      message: 'QUANTITY_IS_REQUIRED',
      errorCode: 'E_1068',
    };

    return error;
  },
  E_1069() {
    const error: TErrorMessage = {
      message: 'PRICE_IS_WRONG_FORMAT',
      errorCode: 'E_1069',
    };

    return error;
  },
  E_1070() {
    const error: TErrorMessage = {
      message: 'TRANSPORTATION_COST_IS_WRONG_FORMAT',
      errorCode: 'E_1070',
    };

    return error;
  },
  E_1071() {
    const error: TErrorMessage = {
      message: 'COST_CENTER_CODE_IS_DUPLICATED',
      errorCode: 'E_1071',
    };

    return error;
  },
  E_1072() {
    const error: TErrorMessage = {
      message: 'BUDGET_CODE_CODE_IS_DUPLICATED',
      errorCode: 'E_1072',
    };

    return error;
  },
  E_1073() {
    const error: TErrorMessage = {
      message: 'START_DATE_WRONG_FORMAT',
      errorCode: 'E_1073',
    };

    return error;
  },
  E_1074() {
    const error: TErrorMessage = {
      message: 'EXPECTED_ACCEPTACE_TIME_WRONG_FORMAT',
      errorCode: 'E_1074',
    };

    return error;
  },
  E_1075() {
    const error: TErrorMessage = {
      message: 'EFFECTIVE_START_DATE_WRONG_FORMAT',
      errorCode: 'E_1075',
    };

    return error;
  },
  E_1076() {
    const error: TErrorMessage = {
      message: 'EFFECTIVE_END_DATE_WRONG_FORMAT',
      errorCode: 'E_1076',
    };

    return error;
  },
  E_1077() {
    const error: TErrorMessage = {
      message: 'FILES_ATTACH_NOT_FOUND',
      errorCode: 'E_1077',
    };

    return error;
  },
  E_1078() {
    const error: TErrorMessage = {
      message: 'SECTOR_IDS_DUPLICATE',
      errorCode: 'E_1078',
    };

    return error;
  },
  E_1079() {
    const error: TErrorMessage = {
      message: 'ADJUST_BUDGET_MUST_BE_LOCKED',
      errorCode: 'E_1079',
    };

    return error;
  },
  E_1080() {
    const error: TErrorMessage = {
      message: 'BUSINESS_UNIT_ALREADY_REGISTERED',
      errorCode: 'E_1080',
    };

    return error;
  },
  E_10081() {
    const error: TErrorMessage = {
      message: 'BUDGET_IS_NOT_LOCK',
      errorCode: 'E_10081',
    };

    return error;
  },
  E_1082() {
    const error: TErrorMessage = {
      message: 'BUSINESS_CODE_ALREADY_REGISTERED_IN_PO/PR',
      errorCode: 'E_1082',
    };

    return error;
  },
  E_1083() {
    const error: TErrorMessage = {
      message: 'BUDGET_CODE_TYPE_IS_REQUIRED',
      errorCode: 'E_1083',
    };

    return error;
  },
  E_1084() {
    const error: TErrorMessage = {
      message: 'INTERNAL_ORDER_IS_REQUIRED',
      errorCode: 'E_1084',
    };

    return error;
  },
  E_1085() {
    const error: TErrorMessage = {
      message: 'COST_IS_REQUIRED',
      errorCode: 'E_1085',
    };

    return error;
  },
  E_1086() {
    const error: TErrorMessage = {
      message: 'EXCHANGE_RATE_IS_REQUIRED',
      errorCode: 'E_1086',
    };

    return error;
  },
  E_1087() {
    const error: TErrorMessage = {
      message: 'BUSINESS_OWNER_CODE_IS_REQUIRED',
      errorCode: 'E_1087',
    };

    return error;
  },
  E_1088() {
    const error: TErrorMessage = {
      message: 'BUSINESS_OWNER_NAME_IS_REQUIRED',
      errorCode: 'E_1088',
    };

    return error;
  },
  E_1089() {
    const error: TErrorMessage = {
      message: 'BUSINESS_OWNER_NAME_CODE_IS_DUPLICATED',
      errorCode: 'E_1089',
    };

    return error;
  },
  E_1090(detail: string = null) {
    const e = buildErrorDetail(
      'E_1090',
      'PURCHASE_ORDER_SAP_NOT_FOUND',
      detail,
    );
    return e;
  },
  E_1091(detail: string = null) {
    const e = buildErrorDetail('E_1091', 'ACTUAL_SPENDING_NOT_FOUND', detail);
    return e;
  },

  E_1092(detail: string = null) {
    const e = buildErrorDetail('E_1092', 'DEPARTMENT_CODE_IS_REQUIRED', detail);
    return e;
  },

  E_1093(detail: string = null) {
    const e = buildErrorDetail('E_1093', 'DEPARTMENT_NAME_IS_REQUIRED', detail);
    return e;
  },

  E_1094(detail: string = null) {
    const e = buildErrorDetail(
      'E_1094',
      'DEPARTMENT_CODE_IS_DUPLICATED',
      detail,
    );
    return e;
  },
  E_1095(detail: string = null) {
    const e = buildErrorDetail('E_1095', 'COMPANY_CODE_IS_REQUIRED', detail);
    return e;
  },

  E_1096(detail: string = null) {
    const e = buildErrorDetail('E_1096', 'COMPANY_NAME_IS_REQUIRED', detail);
    return e;
  },

  E_1097(detail: string = null) {
    const e = buildErrorDetail('E_1097', 'COMPANY_CODE_IS_DUPLICATED', detail);
    return e;
  },
  E_1098(detail: string = null) {
    const e = buildErrorDetail(
      'E_1098',
      'BUSINESS_UNIT_CODE_IS_REQUIRED',
      detail,
    );
    return e;
  },

  E_1099(detail: string = null) {
    const e = buildErrorDetail(
      'E_1099',
      'BUSINESS_UNIT_NAME_IS_REQUIRED',
      detail,
    );
    return e;
  },

  E_1100(detail: string = null) {
    const e = buildErrorDetail(
      'E_1100',
      'BUSINESS_UNIT_CODE_IS_DUPLICATED',
      detail,
    );
    return e;
  },
  E_1101(detail: string = null) {
    const e = buildErrorDetail('E_1101', 'COST_CENTER_ALREADY_EXIST', detail);
    return e;
  },
  E_1102(detail: string = null) {
    const e = buildErrorDetail(
      'E_1102',
      'CURRENCY_EXCHANGE_IS_DUPLICATED',
      detail,
    );
    return e;
  },
  E_1103(detail: string = null) {
    const e = buildErrorDetail(
      'E_1103',
      'CURRENCY_EXCHANGE_IS_NOT_AVAILABLE',
      detail,
    );
    return e;
  },
  E_5100() {
    const error: TErrorMessage = {
      message: 'Kiểm tra lại budget_code!',
      errorCode: 'E_5100',
    };

    return error;
  },
  E_5101() {
    const error: TErrorMessage = {
      message: 'Tồn tại vật tư không kiểm tra ngân sách!',
      errorCode: 'E_5101',
    };

    return error;
  },
  E_5102() {
    const error: TErrorMessage = {
      message: 'Tồn tại vật tư có kiểm tra ngân sách!',
      errorCode: 'E_5102',
    };

    return error;
  },
  E_5103() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy ngân sách!',
      errorCode: 'E_5103',
    };

    return error;
  },
  E_5104() {
    const error: TErrorMessage = {
      message: 'Ngân sách không tồn tại!',
      errorCode: 'E_5104',
    };

    return error;
  },
  E_5105() {
    const error: TErrorMessage = {
      message: 'Mã ngành không tồn tại!',
      errorCode: 'E_5105',
    };

    return error;
  },
  E_5106() {
    const error: TErrorMessage = {
      message: 'Mã đơn vị kinh doanh không tồn tại!',
      errorCode: 'E_5106',
    };

    return error;
  },
  E_5107() {
    const error: TErrorMessage = {
      message: 'Người yêu cầu không tồn tại!',
      errorCode: 'E_5107',
    };

    return error;
  },
  E_5108() {
    const error: TErrorMessage = {
      message: 'Loại PR không tồn tại!',
      errorCode: 'E_5108',
    };

    return error;
  },
  E_5109() {
    const error: TErrorMessage = {
      message: 'Trạng thái của PR không tồn tại!',
      errorCode: 'E_5109',
    };

    return error;
  },
  E_5110() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy PR!',
      errorCode: 'E_5110',
    };

    return error;
  },
  E_5111() {
    const error: TErrorMessage = {
      message: 'Thời gian giao hàng còn ít hơn 5 ngày!',
      errorCode: 'E_5111',
    };

    return error;
  },
  E_5208() {
    const error: TErrorMessage = {
      message: 'Loại PO không tồn tại!',
      errorCode: 'E_5208',
    };

    return error;
  },
  E_5209() {
    const error: TErrorMessage = {
      message: 'Trạng thái của PO không tồn tại!',
      errorCode: 'E_5209',
    };

    return error;
  },
  E_5210() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy PO!',
      errorCode: 'E_5210',
    };

    return error;
  },
  E_5211() {
    const error: TErrorMessage = {
      message: 'Kiểm tra lại các trường bắt buộc trong chi tiết!',
      errorCode: 'E_5211',
    };

    return error;
  },
  E_5212() {
    const error: TErrorMessage = {
      message: 'Vật tư mua vượt ngân sách!',
      errorCode: 'E_5212',
    };

    return error;
  },
  E_5333() {
    const error: TErrorMessage = {
      message: 'Bạn không có quyền để access vào PR',
      errorCode: 'E_5333',
    };

    return error;
  },
  E_5001() {
    const error: TErrorMessage = {
      message: 'Ngày hiệu lực < ngày hiện tại. Vui lòng kiểm tra lại!',
      errorCode: 'E_5001',
    };

    return error;
  },
  E_5002() {
    const error: TErrorMessage = {
      message: 'Nhà cung cấp không được để trống',
      errorCode: 'E_5002',
    };

    return error;
  },
  E_5003() {
    const error: TErrorMessage = {
      message: 'Mã vật tư không tồn tại',
      errorCode: 'E_5003',
    };

    return error;
  },
  E_5004() {
    const error: TErrorMessage = {
      message: 'Mã trại không tồn tại',
      errorCode: 'E_5004',
    };

    return error;
  },
  E_5005() {
    const error: TErrorMessage = {
      message: 'Bộ phận mua hàng không tồn tại',
      errorCode: 'E_5005',
    };

    return error;
  },
  E_5006() {
    const error: TErrorMessage = {
      message: 'Nhóm mua hàng không tồn tại',
      errorCode: 'E_5006',
    };

    return error;
  },
  E_5007() {
    const error: TErrorMessage = {
      message: 'Đơn vị tiền tệ không tồn tại',
      errorCode: 'E_5007',
    };

    return error;
  },
  E_5008() {
    const error: TErrorMessage = {
      message: 'Hồ sơ giá không tồn tại!',
      errorCode: 'E_5008',
    };

    return error;
  },
  E_5009() {
    const error: TErrorMessage = {
      message:
        'Thời gian hết hiệu lực < Thời gian có hiệu lực. Vui lòng kiểm tra lại!',
      errorCode: 'E_5009',
    };
    return error;
  },
  E_5010() {
    const error: TErrorMessage = {
      message: 'Mã đơn vị kinh doanh không hoạt động!',
      errorCode: 'E_5010',
    };

    return error;
  },

  E_5011() {
    const error: TErrorMessage = {
      message: 'Leadtime của NCC bắt buộc',
      errorCode: 'E_5011',
    };

    return error;
  },
  E_5012() {
    const error: TErrorMessage = {
      message: 'Số lượng thường mua hàng bắt buộc',
      errorCode: 'E_5010',
    };

    return error;
  },
  E_5013() {
    const error: TErrorMessage = {
      message: 'Số lượng đặt hàng tối thiểu bắt buộc',
      errorCode: 'E_5013',
    };

    return error;
  },
  E_5014() {
    const error: TErrorMessage = {
      message: 'Dung sai trên theo % bắt buộc',
      errorCode: 'E_5014',
    };

    return error;
  },
  E_5015() {
    const error: TErrorMessage = {
      message: 'Dung sai dưới theo % bắt buộc',
      errorCode: 'E_5015',
    };

    return error;
  },
  E_5016() {
    const error: TErrorMessage = {
      message: 'Giá tiền mua hàng bắt buộc',
      errorCode: 'E_5016',
    };

    return error;
  },
  E_5017() {
    const error: TErrorMessage = {
      message: 'Trên bao nhiêu đơn vị mua bắt buộc',
      errorCode: 'E_5017',
    };

    return error;
  },
  E_5018() {
    const error: TErrorMessage = {
      message: 'Thời gian có hiệu lực bắt buộc',
      errorCode: 'E_5018',
    };

    return error;
  },
  E_5019() {
    const error: TErrorMessage = {
      message: 'Thời gian hết hiệu lực bắt buộc',
      errorCode: 'E_5019',
    };

    return error;
  },
  E_5020() {
    const error: TErrorMessage = {
      message: 'Ngày không hợp lệ',
      errorCode: 'E_5020',
    };

    return error;
  },
  E_5021() {
    const error: TErrorMessage = {
      message: 'Người mua hàng không tồn tại',
      errorCode: 'E_5021',
    };

    return error;
  },
  E_5022() {
    const error: TErrorMessage = {
      message: 'Không được mua quá số lượng yêu cầu',
      errorCode: 'E_5022',
    };

    return error;
  },
  E_5023() {
    const error: TErrorMessage = {
      message: 'Phải có cost center hoặc mã vật tư hoặc mã ngân sách',
      errorCode: 'E_5023',
    };

    return error;
  },
  E_5024() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy mã ngân sách',
      errorCode: 'E_5024',
    };

    return error;
  },
  E_5025() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy cost center',
      errorCode: 'E_5025',
    };

    return error;
  },
  E_5026() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy nhóm vật tư',
      errorCode: 'E_5026',
    };

    return error;
  },

  E_5027() {
    const error: TErrorMessage = {
      message: 'Nhà cung cấp không tìm thấy',
      errorCode: 'E_5027',
    };

    return error;
  },
  E_5028() {
    const error: TErrorMessage = {
      message: 'Đơn vị tiền tệ không tìm thấy',
      errorCode: 'E_5028',
    };

    return error;
  },
  E_5029() {
    const error: TErrorMessage = {
      message: 'Cost Center không hiệu lực',
      errorCode: 'E_5029',
    };

    return error;
  },
  E_5030() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy loại quy trình',
      errorCode: 'E_5030',
    };

    return error;
  },
  E_5031() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy trại',
      errorCode: 'E_5031',
    };

    return error;
  },
  E_5032() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy phòng ban',
      errorCode: 'E_5032',
    };

    return error;
  },
  E_5033() {
    const error: TErrorMessage = {
      message: 'Không tìm thấy khối chức năng',
      errorCode: 'E_5033',
    };

    return error;
  },
  E_6055() {
    const error: TErrorMessage = {
      message: 'PO chưa được duyệt',
      errorCode: 'E_6055',
    };

    return error;
  },
  E_6056() {
    const error: TErrorMessage = {
      message: 'PO đã được đồng bộ SAP',
      errorCode: 'E_6056',
    };
    return error;
  },
  E_6057() {
    const error: TErrorMessage = {
      message: 'PO đã được đồng bộ SOLOMON',
      errorCode: 'E_6057',
    };
    return error;
  },
  E_6058() {
    const error: TErrorMessage = {
      message: 'PO phải có company code 3600',
      errorCode: 'E_6058',
    };
    return error;
  },
  E_6059() {
    const error: TErrorMessage = {
      message: 'Integrated Solomon Failed',
      errorCode: 'E_6059',
    };
    return error;
  },
  E_6060() {
    const error: TErrorMessage = {
      message: 'Bị trùng lặp Mã và Mã SAP trong Vật tư',
      errorCode: 'E_6060',
    };
    return error;
  },
};

export const getErrorMessage = (
  error: TErrorMessage,
  detail?: any,
): TErrorMessage => {
  return { ...error, detail };
};

export const buildErrorDetail = (
  errorCode: string,
  message: string = null,
  detail: string = null,
): TErrorMessage => {
  const e: TErrorMessage = {
    errorCode,
    message,
  };

  if (detail) e.detail = detail;

  return e;
};
