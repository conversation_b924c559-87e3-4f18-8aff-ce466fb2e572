import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { json, urlencoded } from 'express';
import * as moment from 'moment-timezone';
import { AppModule } from './app.module';
import { HttpErrorFilter } from './utils/exception/http-exception.filter';

async function bootstrap() {
  // Thiết lập timezone mặc định cho Moment Timezone
  moment.tz.setDefault('UTC');

  const app = await NestFactory.create(AppModule);
  // const redisIoAdapter = new RedisIoAdapter();

  // app.connectMicroservice<MicroserviceOptions>({
  //   transport: Transport.REDIS,
  //   options: redisIoAdapter.redisOptions(),
  // });

  app.setGlobalPrefix('purchase-service');
  app.enableCors({
    origin: true,
    credentials: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  });
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ limit: '50mb', extended: true }));

  const config = new DocumentBuilder()
    .setTitle('Purchase API')
    .setDescription('The API description')
    .setVersion('1.0')
    .setBasePath('purchase-service')
    .addBearerAuth(
      {
        description: ``,
        name: 'Authorization',
        bearerFormat: 'Bearer',
        scheme: 'Bearer',
        type: 'http',
        in: 'Header',
      },
      'Authorization',
    )
    .addBasicAuth(
      {
        description: 'Enter Basic Auth credentials',
        name: 'Authorization',
        scheme: 'basic',
        type: 'http',
        in: 'header',
      },
      'BasicAuth',
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('purchase-service-doc/swagger', app, document, {
    swaggerOptions: {
      operationsSorter: 'method',
    },
  });
  //const rabbitConfigService = app.get(RabbitConfigService);
  app.useGlobalFilters(new HttpErrorFilter());
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  await app.listen(process.env.PORT || 8000);
}
bootstrap();
